1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,160 --> 00:00:15,970
之前我们说过咱们这个cocos creator里面的animation就是这个动画系统啊

3
00:00:15,970 --> 00:00:18,219
它有时候是有问题的啊

4
00:00:18,219 --> 00:00:21,160
就是说比如说我们让他去播放一个动画啊

5
00:00:21,160 --> 00:00:23,859
默认情况下它肯定是可以正常播放的

6
00:00:23,879 --> 00:00:26,100
但是我们让它在播放的时候

7
00:00:26,100 --> 00:00:31,649
比如说让它去产生呃移动啊或者旋转啊等等啊

8
00:00:31,649 --> 00:00:32,759
这些操作的时候

9
00:00:32,759 --> 00:00:36,509
有时候他的动画就不会执行了啊

10
00:00:36,509 --> 00:00:38,240
它就会产生一些bug

11
00:00:38,320 --> 00:00:41,710
那么如果你的项目遇到了这样的问题怎么办

12
00:00:41,710 --> 00:00:45,039
那你就需要自己去写一个脚本啊

13
00:00:45,039 --> 00:00:49,079
自己去写一个自己的动画组件好

14
00:00:49,079 --> 00:00:53,850
那么咱们这节课就来简单写一个啊自己的动画组件啊

15
00:00:53,850 --> 00:00:56,939
其实所谓的自己的动画组件呀

16
00:00:57,679 --> 00:01:01,100
animation啊呆吗

17
00:01:02,299 --> 00:01:05,340
其实所谓自己的动画组件是什么意思啊

18
00:01:05,579 --> 00:01:07,560
动画是怎样的啊

19
00:01:07,560 --> 00:01:10,680
动画其实就是很多张图片啊

20
00:01:10,680 --> 00:01:13,709
比如说这三个啊

21
00:01:13,709 --> 00:01:17,700
这个小鸟飞翔的图片其实就是让它一直循环播放

22
00:01:17,700 --> 00:01:18,180
对不对

23
00:01:18,180 --> 00:01:19,980
其实就是一直让它循环播放了

24
00:01:19,980 --> 00:01:23,159
那么系统自己的animation就是做到了这样的事

25
00:01:23,159 --> 00:01:25,170
那我们可以自己写脚本

26
00:01:25,170 --> 00:01:26,939
我们通过自己的脚本啊

27
00:01:26,939 --> 00:01:28,140
自己通过代码

28
00:01:28,140 --> 00:01:31,959
然后来进行这个图片的更换啊

29
00:01:31,959 --> 00:01:34,659
那就达到了这个动画的效果啊

30
00:01:34,659 --> 00:01:36,010
那就达到动画的效果

31
00:01:36,010 --> 00:01:37,780
我们在这里嗯

32
00:01:37,780 --> 00:01:39,159
咱们一起来写一下啊

33
00:01:39,159 --> 00:01:40,459
大家来看一下

34
00:01:48,200 --> 00:01:50,079
比如说就用这个小鸟吧

35
00:01:54,819 --> 00:01:55,959
那正常啊

36
00:01:55,959 --> 00:01:57,579
我们有这样一个小鸟的精灵

37
00:01:57,579 --> 00:01:58,959
想让它播放动画怎么办

38
00:01:58,959 --> 00:02:01,599
是不是我们就要添加animation组件啊

39
00:02:01,599 --> 00:02:02,859
开始去做动画了

40
00:02:02,859 --> 00:02:03,519
对不对

41
00:02:03,540 --> 00:02:07,079
那我们现在不使用系统的animation了啊

42
00:02:07,079 --> 00:02:09,360
我们是我们创建一个脚本啊

43
00:02:09,360 --> 00:02:11,460
我们自己来给他做动画

44
00:02:11,460 --> 00:02:13,439
其实做动画就是改变这个属性

45
00:02:13,439 --> 00:02:14,120
对不对

46
00:02:14,120 --> 00:02:15,710
这是第一个图片

47
00:02:15,710 --> 00:02:17,599
然后这是第二个图片

48
00:02:17,599 --> 00:02:19,159
这是第三个图片

49
00:02:19,159 --> 00:02:21,719
我们只需要通过代码

50
00:02:21,740 --> 00:02:23,900
然后来改变这个属性

51
00:02:23,900 --> 00:02:27,460
那它就有这样的一个动画效果了啊

52
00:02:27,460 --> 00:02:31,400
那我们在这里创建一个脚本叫my animation

53
00:02:33,599 --> 00:02:35,340
当然你如果写项目

54
00:02:35,340 --> 00:02:37,379
你发你发现你的项目比较简单

55
00:02:37,379 --> 00:02:39,120
用系统的animation没问题

56
00:02:39,120 --> 00:02:40,860
你就直接用系统的就可以了

57
00:02:40,860 --> 00:02:42,419
如果有问题啊

58
00:02:42,419 --> 00:02:45,680
那你就可以自己来去写这个id没审

59
00:02:51,780 --> 00:02:52,949
哎你没事

60
00:02:52,949 --> 00:02:54,639
那么在这里

61
00:03:00,780 --> 00:03:02,460
我们来想一想啊

62
00:03:02,460 --> 00:03:03,719
对于外界的控制

63
00:03:03,719 --> 00:03:05,099
首先既然播放动画

64
00:03:05,099 --> 00:03:06,419
动画有什么呀

65
00:03:06,419 --> 00:03:11,520
每秒播放的一个速度啊

66
00:03:11,520 --> 00:03:14,849
就是说多长时间切换一帧啊

67
00:03:14,849 --> 00:03:17,039
多长时间切换一个图片

68
00:03:17,039 --> 00:03:19,219
那这个就影响我们播放的速度

69
00:03:19,219 --> 00:03:23,240
那这个我们要在外界啊去显示出来

70
00:03:23,240 --> 00:03:24,379
所以at property

71
00:03:24,379 --> 00:03:29,090
然后speed number等于0.1

72
00:03:29,090 --> 00:03:30,680
比如说默认给个0.1

73
00:03:30,680 --> 00:03:36,759
意思就是每隔0.1秒我们可以切换一张图片啊

74
00:03:36,759 --> 00:03:41,080
那么这样的话大家去想一秒0.1去切换一张图片

75
00:03:41,080 --> 00:03:44,240
一秒就可以切换十张啊

76
00:03:44,240 --> 00:03:48,639
也就是说默认情况下我们这个是十帧啊

77
00:03:48,639 --> 00:03:51,259
一秒给它十帧这样的一个播放速度

78
00:03:52,580 --> 00:03:55,159
然后你播放哪些图片呀

79
00:03:55,159 --> 00:03:58,370
啊我们现在是不是有三个飞翔的图片呀

80
00:03:58,370 --> 00:04:02,740
那我们在这里给它一个播放帧的一个数组

81
00:04:06,500 --> 00:04:09,530
property呃

82
00:04:09,530 --> 00:04:11,780
数组就要用中括号

83
00:04:11,780 --> 00:04:14,599
然后在里面写上类型

84
00:04:15,099 --> 00:04:17,319
spirit frame啊

85
00:04:17,319 --> 00:04:18,338
就是一针

86
00:04:19,240 --> 00:04:23,589
然后在下面我们创建这样一个数组

87
00:04:23,589 --> 00:04:29,160
cret frame默认是个空数组啊

88
00:04:29,160 --> 00:04:31,259
那这就是一个播放帧数组啊

89
00:04:31,259 --> 00:04:33,329
播放帧数组呃

90
00:04:33,329 --> 00:04:35,720
这样的话我们可以看一下这边

91
00:04:37,560 --> 00:04:39,180
现在已经有个0.1

92
00:04:39,180 --> 00:04:41,579
然后有个数组数组里面默认是零

93
00:04:41,579 --> 00:04:42,899
我们给他的三

94
00:04:43,959 --> 00:04:45,519
然后现在就有三项

95
00:04:45,519 --> 00:04:47,259
每一项都是spirit frame

96
00:04:47,259 --> 00:04:51,160
然后我们把这个动画每一帧图片拖过来

97
00:04:54,240 --> 00:04:57,610
诶就可以了就可以了

98
00:04:57,610 --> 00:05:03,620
那么接下来我们要一个属性是是否播放动画

99
00:05:04,180 --> 00:05:07,079
那这个外界也是可以控制的

100
00:05:08,220 --> 00:05:13,399
默认比如说我们让它为false好啊

101
00:05:13,399 --> 00:05:14,120
如果为tru

102
00:05:14,120 --> 00:05:15,920
我们就让它自动播放了啊

103
00:05:15,920 --> 00:05:16,699
如果为false

104
00:05:16,699 --> 00:05:18,439
就让他不去播放了啊

105
00:05:18,439 --> 00:05:22,360
就一个布尔值来控制当前这个动画是否播放啊

106
00:05:22,360 --> 00:05:23,649
很简单

107
00:05:23,649 --> 00:05:27,220
那么大家注意啊

108
00:05:27,220 --> 00:05:28,810
我们要通过代码

109
00:05:28,810 --> 00:05:32,779
我们要通过代码让这个数组里面啊

110
00:05:32,779 --> 00:05:36,750
这个比如说隔0.1秒先播放第一个图片

111
00:05:36,750 --> 00:05:37,860
再给0.1秒播放

112
00:05:37,860 --> 00:05:39,660
第二个再停0.1秒播放

113
00:05:39,660 --> 00:05:41,100
第三个暂停

114
00:05:41,100 --> 00:05:41,850
0.1秒

115
00:05:41,850 --> 00:05:43,920
我们就让它再继续播放第一个

116
00:05:43,920 --> 00:05:46,240
这样是不是就变成循环动画了啊

117
00:05:46,240 --> 00:05:47,649
这就成了循环动画了

118
00:05:47,649 --> 00:05:49,540
那么当前播放到第几帧

119
00:05:49,540 --> 00:05:53,319
我们需要保存一下当前播放真

120
00:05:55,980 --> 00:05:58,110
number等于零

121
00:05:58,110 --> 00:05:59,790
当前播放帧

122
00:05:59,790 --> 00:06:03,139
那么ok我们先这么多啊

123
00:06:03,139 --> 00:06:04,160
我们一会缺了什么

124
00:06:04,160 --> 00:06:05,699
我们再去说

125
00:06:05,720 --> 00:06:08,240
那首先我们这个播放啊

126
00:06:08,240 --> 00:06:09,259
我们在这边啊

127
00:06:09,259 --> 00:06:11,269
up data里面实现好的啊

128
00:06:11,269 --> 00:06:15,740
如果this day is player为true

129
00:06:15,740 --> 00:06:17,740
我们在这里就可以怎样了

130
00:06:17,740 --> 00:06:19,060
播放动画了啊

131
00:06:19,060 --> 00:06:20,529
就这么要播放动画了

132
00:06:20,529 --> 00:06:21,519
那么多动画

133
00:06:21,519 --> 00:06:22,899
我们现在要去判断了

134
00:06:22,899 --> 00:06:24,560
如果够0.1秒

135
00:06:24,560 --> 00:06:26,300
如果够0.1秒

136
00:06:26,300 --> 00:06:27,740
我们就切换一张图片

137
00:06:27,740 --> 00:06:29,240
够0.1秒切换一张图片

138
00:06:29,240 --> 00:06:29,980
对不对

139
00:06:30,040 --> 00:06:32,860
那我们现在需要一个计时器

140
00:06:32,860 --> 00:06:36,199
代表当前过去了多长时间

141
00:06:37,160 --> 00:06:38,540
默认也是零

142
00:06:38,540 --> 00:06:41,019
这个计时器怎样去增加呢

143
00:06:43,759 --> 00:06:45,980
大家还记着这个dt是什么意思吧

144
00:06:45,980 --> 00:06:48,540
就是每一帧之间的间隔时间

145
00:06:48,540 --> 00:06:53,639
所以在这里我们只需要让它自加上一个dt

146
00:06:53,639 --> 00:06:59,279
那这样的话this.time就代表当前已经过去的时间啊

147
00:06:59,279 --> 00:07:01,699
this.time就代表已经过去的时间

148
00:07:01,899 --> 00:07:04,660
那么如果

149
00:07:04,660 --> 00:07:11,860
this.time已经超过了我们的播放速度

150
00:07:13,360 --> 00:07:14,800
超过了我们的播放速度

151
00:07:14,800 --> 00:07:15,459
就是0.1

152
00:07:15,459 --> 00:07:16,420
默认是0.1

153
00:07:16,420 --> 00:07:17,680
就证明怎样

154
00:07:17,680 --> 00:07:20,050
我们目前已经过了0.1秒了

155
00:07:20,050 --> 00:07:21,980
我们就在这里应该干嘛了

156
00:07:22,060 --> 00:07:23,920
切换帧了

157
00:07:23,920 --> 00:07:25,259
切换帧了

158
00:07:25,560 --> 00:07:29,100
那么切换帧之前我们要做一件事

159
00:07:29,100 --> 00:07:31,980
this.time给他置为零啊

160
00:07:31,980 --> 00:07:33,759
重新把计时器置为零

161
00:07:35,019 --> 00:07:36,759
那么我们切换帧的话

162
00:07:36,759 --> 00:07:38,410
怎样切换啊

163
00:07:38,410 --> 00:07:39,579
非常简单

164
00:07:39,579 --> 00:07:41,560
当前的索引

165
00:07:41,560 --> 00:07:43,720
比如说默认显示的是第零帧

166
00:07:43,720 --> 00:07:44,379
对不对

167
00:07:44,379 --> 00:07:47,620
我们在这里让它加加就可以了

168
00:07:48,079 --> 00:07:51,000
但是要做一个判断啊

169
00:07:51,000 --> 00:07:57,339
如果当前的这个帧数大于了我们这个精灵数组的长度

170
00:07:57,439 --> 00:08:00,360
大于等于大于等于了

171
00:08:01,019 --> 00:08:02,459
如果超出范围了

172
00:08:02,459 --> 00:08:06,519
我们就应该怎样让它当前的帧数等于零

173
00:08:06,519 --> 00:08:11,399
也就是说这一串的意思就是比如目前我们显示的是第零帧

174
00:08:11,399 --> 00:08:12,360
index是零

175
00:08:12,360 --> 00:08:16,060
那么这一串代码就让它变成一了啊

176
00:08:16,060 --> 00:08:18,459
如果这一串如果是一

177
00:08:18,459 --> 00:08:19,240
现在是一

178
00:08:19,240 --> 00:08:23,189
那么执行这一串就变成二了啊

179
00:08:23,189 --> 00:08:27,029
那么再往后如果他现在是二的话

180
00:08:27,029 --> 00:08:28,110
他想变成三

181
00:08:28,110 --> 00:08:29,939
发现我这个长度一共就是三

182
00:08:29,939 --> 00:08:33,379
那这个大于等于了哎满足这个条件了

183
00:08:33,379 --> 00:08:35,539
那么它就会变成一了啊

184
00:08:35,539 --> 00:08:36,320
又会变成零了

185
00:08:36,320 --> 00:08:37,820
我们又给它赋值成零了

186
00:08:37,820 --> 00:08:40,220
所以实际上这一串啊

187
00:08:40,220 --> 00:08:47,080
这一串就是*********就变成一个循环了啊

188
00:08:47,080 --> 00:08:49,179
就让这个index变成一个循环了

189
00:08:49,960 --> 00:08:52,870
然后我们既然有这个循环了

190
00:08:52,870 --> 00:08:53,740
循环索引了

191
00:08:53,740 --> 00:08:58,659
我们只需要去更换帧图片就可以了

192
00:09:02,559 --> 00:09:10,500
热点get company cc.surprise

193
00:09:10,500 --> 00:09:12,720
surprise frame

194
00:09:12,940 --> 00:09:15,700
这是我们获取当前这个精灵组件

195
00:09:15,700 --> 00:09:17,320
然后拿到他的spit frame

196
00:09:17,320 --> 00:09:20,580
这个属性直接从这个数组里面

197
00:09:22,460 --> 00:09:27,200
去拿拿到当前这个this down index这一帧就可以了

198
00:09:27,720 --> 00:09:30,779
因为这个帧数已经在这里被我们改变了

199
00:09:30,779 --> 00:09:31,519
对不对

200
00:09:32,080 --> 00:09:32,769
嗯

201
00:09:32,769 --> 00:09:33,220
ok啊

202
00:09:33,220 --> 00:09:33,879
就这么简单

203
00:09:33,879 --> 00:09:34,539
就这么简单

204
00:09:34,539 --> 00:09:35,320
几行代码

205
00:09:35,320 --> 00:09:37,460
然后我们现在运行看一下效果啊

206
00:09:39,440 --> 00:09:40,460
运行

207
00:09:40,460 --> 00:09:43,659
那么我们就可以看到这有一只鸟

208
00:09:43,659 --> 00:09:44,799
但是没有播放动画

209
00:09:44,799 --> 00:09:45,159
对不对

210
00:09:45,159 --> 00:09:46,629
我们把它关了

211
00:09:46,629 --> 00:09:48,340
在这边上来

212
00:09:48,340 --> 00:09:49,539
我们把这个勾勾上

213
00:09:49,539 --> 00:09:51,580
就是is player啊

214
00:09:51,580 --> 00:09:53,860
这应该不叫e子play啊

215
00:09:53,860 --> 00:09:55,240
这e普列尔错了

216
00:09:55,240 --> 00:09:56,200
是成了师傅

217
00:09:56,200 --> 00:09:59,179
是玩家的ez play啊

218
00:09:59,179 --> 00:10:00,740
或者应该加explain

219
00:10:00,740 --> 00:10:01,440
是不是

220
00:10:02,019 --> 00:10:04,059
那player肯定是不对的

221
00:10:04,059 --> 00:10:05,700
稍微修改下好了

222
00:10:06,240 --> 00:10:10,470
ok我们在这里把这个勾给他勾上运行

223
00:10:10,470 --> 00:10:13,419
那大家可以看这个鸟是不是就飞了

224
00:10:13,419 --> 00:10:14,320
你要觉得速度快

225
00:10:14,320 --> 00:10:17,519
你在这里去修改它的这个播放速度就可以了

226
00:10:17,519 --> 00:10:19,799
那这样的话是不是做了同样的效果

227
00:10:19,799 --> 00:10:22,080
和这个系统的这个动画组件完全一样

228
00:10:22,080 --> 00:10:22,779
对不对

229
00:10:23,340 --> 00:10:27,539
那么在这里你也可以不通过这个面板来控制

230
00:10:27,539 --> 00:10:31,779
你也可以通过呃脚本来控制

231
00:10:31,779 --> 00:10:33,799
比如说我这个脚本

232
00:10:36,019 --> 00:10:37,549
ok啊我们继续

233
00:10:37,549 --> 00:10:43,220
比如说呃我们这个我们这个组件

234
00:10:43,220 --> 00:10:45,500
我们这个脚本是纯播放动画的

235
00:10:45,500 --> 00:10:46,639
而是纯播放动画的

236
00:10:46,639 --> 00:10:48,740
太里面不处理任何其他逻辑

237
00:10:48,740 --> 00:10:51,620
比如说我们这个小鸟本身有一些逻辑

238
00:10:51,620 --> 00:10:56,279
那么你还是需要去创建一个新的脚本给这个小鸟

239
00:10:59,440 --> 00:11:01,299
比如说在这个脚本里面啊

240
00:11:01,299 --> 00:11:03,500
你去你去写代码啊

241
00:11:03,500 --> 00:11:05,480
这个小鸟应该怎么操作啊

242
00:11:05,480 --> 00:11:06,700
应该怎样操作

243
00:11:06,700 --> 00:11:11,200
那么这时候我们如果希望通过这个脚本来控制当前动画的话

244
00:11:11,200 --> 00:11:11,879
怎么办

245
00:11:11,879 --> 00:11:13,379
那么一般在动画这边

246
00:11:13,379 --> 00:11:15,029
我们需要给他两个方法

247
00:11:15,029 --> 00:11:18,190
第一个方法叫做play

248
00:11:18,190 --> 00:11:20,559
第二个叫做stop

249
00:11:22,840 --> 00:11:28,440
然后play的话就是this is play等于true啊

250
00:11:28,440 --> 00:11:34,309
stop就是is this is play等于stop啊

251
00:11:34,309 --> 00:11:36,759
啊等于等于w等于fs

252
00:11:38,419 --> 00:11:42,080
然后我们这时候就可以在这边在小鸟这边

253
00:11:42,080 --> 00:11:43,309
大家注意啊

254
00:11:43,309 --> 00:11:48,120
目前我这个小鸟这边我是没有给它画上的

255
00:11:48,120 --> 00:11:49,500
它按道理是不会播放的

256
00:11:49,500 --> 00:11:50,159
对不对

257
00:11:50,159 --> 00:11:52,779
但是我现在通过这个脚本啊

258
00:11:52,779 --> 00:11:53,740
通过小鸟的脚本

259
00:11:53,740 --> 00:11:57,620
我在start的时候得到我自己的组件

260
00:11:57,740 --> 00:11:59,690
就是my animation

261
00:11:59,690 --> 00:12:00,740
得到这个组件

262
00:12:00,740 --> 00:12:02,200
然后调play方法

263
00:12:02,220 --> 00:12:04,440
我要播放的动画停止的话

264
00:12:04,440 --> 00:12:06,120
就是播放掉这个点

265
00:12:06,120 --> 00:12:07,580
stop这个方法对不对

266
00:12:07,919 --> 00:12:10,049
然后在这边再次运行

267
00:12:10,049 --> 00:12:14,710
那么这样的话就ok了啊

268
00:12:14,710 --> 00:12:21,899
那么这就是一个呃基本的一个动画的一个播放器啊

269
00:12:21,899 --> 00:12:24,389
就是一个基本的动画播放器呃

270
00:12:24,389 --> 00:12:27,720
如果你这个比如说一个精灵

271
00:12:27,720 --> 00:12:29,620
它不只有一个飞翔动画

272
00:12:30,259 --> 00:12:32,879
就是它还包括其他动画的话

273
00:12:33,299 --> 00:12:35,220
你可以有两种方案做

274
00:12:35,220 --> 00:12:38,240
一种是每一个动画给它一个animation组件

275
00:12:38,259 --> 00:12:41,080
另外一种就是这里有一个数组啊

276
00:12:41,080 --> 00:12:43,059
你可以给它对应的数组

277
00:12:43,059 --> 00:12:43,960
比如说有三个动画

278
00:12:43,960 --> 00:12:45,519
你这里就不要一个数组了

279
00:12:45,519 --> 00:12:47,139
你给三个数组啊

280
00:12:47,139 --> 00:12:50,289
然后把这三个动画呢分别附到三个数组里面

281
00:12:50,289 --> 00:12:53,960
然后播放的话去切换三个数组就可以了啊

282
00:12:54,000 --> 00:12:56,639
那么这两种方案都是可以的啊

283
00:12:56,639 --> 00:12:58,840
这个就看你怎样去实现了

284
00:12:59,240 --> 00:13:00,320
嗯ok啊

285
00:13:00,320 --> 00:13:01,879
那么我们这节课啊

286
00:13:01,879 --> 00:13:03,120
就说这么多内容


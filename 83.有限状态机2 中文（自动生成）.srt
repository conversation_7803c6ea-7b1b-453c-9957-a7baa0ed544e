1
00:00:09,599 --> 00:00:12,660
咱们这节课拿一个小事例

2
00:00:13,239 --> 00:00:14,759
然后咱们来试一下

3
00:00:14,759 --> 00:00:17,039
咱们这个状态机能不能去用啊

4
00:00:17,039 --> 00:00:19,588
看一下它跟普通使用有什么区别啊

5
00:00:19,588 --> 00:00:23,980
比如说我们就拿之前的这个小鸟啊

6
00:00:23,980 --> 00:00:25,120
这样的一个小鸟

7
00:00:25,120 --> 00:00:27,550
我们来用它去做啊

8
00:00:27,550 --> 00:00:32,399
那么我们在这里创建一个新的场景啊

9
00:00:32,399 --> 00:00:33,659
使用新的场景

10
00:00:34,100 --> 00:00:36,380
然后在新的场景这里面

11
00:00:36,380 --> 00:00:39,799
首先我们这个拖过来一个小鸟

12
00:00:42,079 --> 00:00:43,240
一个小鸟啊

13
00:00:44,439 --> 00:00:47,350
那么对于这个小鸟而言

14
00:00:47,350 --> 00:00:50,020
比如说我们要做一个很简单的事情啊

15
00:00:50,020 --> 00:00:51,759
我们现在要做一个很简单的事情

16
00:00:51,759 --> 00:00:54,259
给他的上面加上两个button

17
00:00:55,200 --> 00:00:56,509
加上两个button

18
00:00:56,509 --> 00:00:58,250
那么这时候大家去想一想啊

19
00:00:58,250 --> 00:01:00,058
我们来先去写

20
00:01:00,240 --> 00:01:02,098
我们先把这个bug修好

21
00:01:02,240 --> 00:01:05,540
第一个我们这个小鸟有两个情况

22
00:01:05,540 --> 00:01:07,280
一个是正常的飞翔状态

23
00:01:07,280 --> 00:01:08,390
一个是死亡状态

24
00:01:08,390 --> 00:01:11,539
所以第一个就叫飞翔状态

25
00:01:13,260 --> 00:01:15,019
还有一个是死亡状态

26
00:01:15,019 --> 00:01:15,920
对不对

27
00:01:15,920 --> 00:01:19,299
那么我们把它按钮先改小一些

28
00:01:24,739 --> 00:01:26,480
比如说这是飞翔

29
00:01:28,120 --> 00:01:31,679
再来一个死亡

30
00:01:33,859 --> 00:01:38,329
那么我现在就是要让它在飞翔的时候播放飞翔的动画

31
00:01:38,329 --> 00:01:40,780
死亡的时候就播放这个死亡的

32
00:01:40,780 --> 00:01:43,840
那么这个死亡的其实我们就不用让它播放动画了

33
00:01:43,840 --> 00:01:45,879
我们直接让动画停止播放

34
00:01:45,879 --> 00:01:48,269
然后加载这个图片就可以了啊

35
00:01:48,269 --> 00:01:51,739
那或者说我们把它做成一个动画都是可以的啊

36
00:01:51,920 --> 00:01:54,560
那我们在这里就就做动画吧

37
00:01:54,560 --> 00:01:55,899
啊做动画吧

38
00:01:56,420 --> 00:01:58,640
呃我们还是用这个系统的啊

39
00:01:58,640 --> 00:02:05,399
我们用系统的这个动画编辑器在这边首先给这个小鸟添加组件

40
00:02:06,260 --> 00:02:09,639
添加一个animation啊

41
00:02:09,639 --> 00:02:12,759
然后我们新建动画

42
00:02:13,840 --> 00:02:16,610
一个是fly啊

43
00:02:16,610 --> 00:02:18,379
然后再加一个

44
00:02:18,379 --> 00:02:24,479
我们再加一个叫做大死亡飞翔的动画

45
00:02:24,479 --> 00:02:27,599
我们在这里给它加上一个

46
00:02:28,599 --> 00:02:31,650
加上这三个图片加载上来

47
00:02:31,650 --> 00:02:32,759
加上来

48
00:02:32,759 --> 00:02:35,860
把这个60帧改成每秒八帧

49
00:02:35,959 --> 00:02:39,229
然后循环这个动画就搞定了

50
00:02:39,229 --> 00:02:42,199
然后到死亡这边一样的

51
00:02:45,219 --> 00:02:47,580
ok有一个死亡的就可以了

52
00:02:47,580 --> 00:02:48,960
因为死亡就一针直接放

53
00:02:48,960 --> 00:02:49,620
这就可以了

54
00:02:49,620 --> 00:02:51,139
保存关闭

55
00:02:51,139 --> 00:02:53,719
那么当前这个小鸟就已经有这两种状态

56
00:02:53,719 --> 00:02:54,710
一种是飞翔的

57
00:02:54,710 --> 00:02:55,490
一种是死亡的

58
00:02:55,490 --> 00:02:56,389
两种动画啊

59
00:02:56,389 --> 00:02:57,900
两种动画都有了

60
00:02:58,379 --> 00:03:03,219
呃在这里我默认没有让他去播放任何动画

61
00:03:03,219 --> 00:03:06,389
我们通过写脚本来让他播放啊

62
00:03:06,389 --> 00:03:08,789
那我们在这里创建一个新的脚本

63
00:03:08,789 --> 00:03:11,598
比如说叫做bird ctrl啊

64
00:03:11,598 --> 00:03:17,419
小鸟的控制器给他bug上来打开它

65
00:03:18,919 --> 00:03:21,129
那么在这里啊

66
00:03:21,129 --> 00:03:27,439
在这边首先啊呃我们按正常的思路去写

67
00:03:27,439 --> 00:03:29,419
正常思路怎样去写啊

68
00:03:29,419 --> 00:03:30,860
正常思路怎样去写

69
00:03:30,860 --> 00:03:31,819
非常简单

70
00:03:31,819 --> 00:03:33,860
首先我要获取animation

71
00:03:35,459 --> 00:03:38,058
所以我这里cc.animation

72
00:03:39,199 --> 00:03:41,500
我在start里面获取一次好了

73
00:03:41,618 --> 00:03:44,408
你就等于一个

74
00:03:44,408 --> 00:03:46,538
而不是你是this点

75
00:03:46,538 --> 00:03:47,019
any

76
00:03:47,019 --> 00:03:49,179
先获取我自己的动画播放组件

77
00:03:49,179 --> 00:03:49,979
对不对

78
00:03:54,819 --> 00:03:57,689
然后我现在需要有两个方法

79
00:03:57,689 --> 00:03:59,379
一个方法呢

80
00:04:02,240 --> 00:04:03,939
一个方法叫做fly

81
00:04:03,939 --> 00:04:04,930
就是飞

82
00:04:04,930 --> 00:04:07,778
还有一个叫做带啊

83
00:04:07,778 --> 00:04:08,949
我按一下按钮

84
00:04:08,949 --> 00:04:10,479
飞的按钮就让它执行飞

85
00:04:10,479 --> 00:04:12,218
按一下死亡的就执行死亡

86
00:04:12,218 --> 00:04:12,788
对不对

87
00:04:12,788 --> 00:04:15,400
那我们在这里先关联一下这个事件

88
00:04:15,740 --> 00:04:17,569
在飞翔状态这里

89
00:04:17,569 --> 00:04:19,680
我们给它加一个状态

90
00:04:22,100 --> 00:04:24,160
然后把小鸟拖过来

91
00:04:27,060 --> 00:04:30,860
是非我们看一下在哪里飞

92
00:04:32,180 --> 00:04:35,699
然后第二个加一个状态

93
00:04:42,779 --> 00:04:45,420
是死亡而是死亡

94
00:04:45,740 --> 00:04:52,040
那么这样的话两个按钮关联了我们这个小鸟里面的两个事件啊

95
00:04:52,040 --> 00:04:53,959
关联了里面的两个事件就ok了

96
00:04:53,959 --> 00:04:54,839
没问题

97
00:04:55,399 --> 00:04:58,029
那么在fa里面我们应该会怎样去写

98
00:04:58,029 --> 00:04:59,920
而我们应该就会做这样的事

99
00:04:59,920 --> 00:05:05,759
就是this.any.play fly

100
00:05:06,579 --> 00:05:14,449
然后在死亡这里this.any.play带

101
00:05:14,449 --> 00:05:15,430
对不对

102
00:05:15,430 --> 00:05:16,449
那么注意啊

103
00:05:16,449 --> 00:05:18,129
这里只是代码比较简单

104
00:05:18,129 --> 00:05:20,350
所以就是一个播放动画的一行代码

105
00:05:20,350 --> 00:05:23,019
实际上比如说在我们小鸟的飞行当中

106
00:05:23,019 --> 00:05:25,480
这里面应该还有很多代码代码

107
00:05:25,480 --> 00:05:27,399
比如说这个小鸟怎么飞行啊

108
00:05:27,399 --> 00:05:29,180
飞行速度是多少啊

109
00:05:29,180 --> 00:05:29,959
死亡的话

110
00:05:29,959 --> 00:05:31,639
这里实际上也有很多代码

111
00:05:31,639 --> 00:05:33,490
除了更改动画呃

112
00:05:33,490 --> 00:05:35,199
除了播放这个死亡动画

113
00:05:35,199 --> 00:05:37,759
它还要有怎么下落呀

114
00:05:37,759 --> 00:05:38,598
下落到哪里

115
00:05:38,598 --> 00:05:39,678
我们让它销毁了

116
00:05:39,678 --> 00:05:40,218
对不对

117
00:05:40,218 --> 00:05:41,088
这才是死亡

118
00:05:41,088 --> 00:05:44,189
所以实际上每一个它的动画啊

119
00:05:44,189 --> 00:05:45,149
就每一个状态

120
00:05:45,149 --> 00:05:49,259
我们现在其实是给它放到了一个方法的里面啊

121
00:05:49,259 --> 00:05:51,000
我们给它放到了一个方法里面

122
00:05:51,000 --> 00:05:52,079
就是飞翔的代码

123
00:05:52,079 --> 00:05:53,759
我们全写在这个方法里面

124
00:05:53,759 --> 00:05:56,139
死亡的代码全写在这个方法里面

125
00:05:56,500 --> 00:05:58,060
就这正常情况下

126
00:05:58,060 --> 00:05:59,560
我们最多也就是这样去想

127
00:05:59,560 --> 00:06:00,379
对不对

128
00:06:01,480 --> 00:06:04,600
那么在这里我们看一下效果和目前的效果

129
00:06:10,579 --> 00:06:12,110
哦我们点飞翔

130
00:06:12,110 --> 00:06:13,009
他开始飞

131
00:06:13,009 --> 00:06:14,540
点死亡变成死亡了

132
00:06:14,540 --> 00:06:15,680
飞翔飞死亡

133
00:06:15,680 --> 00:06:16,939
死亡没有问题

134
00:06:16,939 --> 00:06:17,449
对不对

135
00:06:17,449 --> 00:06:18,370
没有问题

136
00:06:18,370 --> 00:06:20,740
那么这是正常的情况下啊

137
00:06:20,740 --> 00:06:27,569
接下来如果啊这就是咱们说的这只是两种状态

138
00:06:27,569 --> 00:06:30,629
而且实际上它每个状态里面有很多行代码

139
00:06:30,629 --> 00:06:31,889
我们这里是省略了啊

140
00:06:31,889 --> 00:06:34,730
因为我们现在只需要让它播放个动画啊

141
00:06:34,730 --> 00:06:37,279
其他的什么真正的飞翔呀或者真正的死亡

142
00:06:37,279 --> 00:06:38,759
咱们代码就省略了

143
00:06:38,759 --> 00:06:41,699
当他的状态啊太多的时候

144
00:06:41,699 --> 00:06:45,158
而且每个状态里面的代码量也太多的时候

145
00:06:45,158 --> 00:06:46,838
那就会比较乱了啊

146
00:06:46,838 --> 00:06:48,639
全写在一个脚本里面就比较乱了

147
00:06:48,639 --> 00:06:50,199
这时候我们就要用状态机

148
00:06:50,199 --> 00:06:51,298
就会好一些

149
00:06:51,298 --> 00:06:53,098
怎样去使用状态机呢

150
00:06:53,098 --> 00:06:54,449
咱们写好的状态机

151
00:06:54,449 --> 00:06:56,519
首先我们要确定有几个状态

152
00:06:56,519 --> 00:06:59,740
我们在这里给它创建一个枚举

153
00:07:02,139 --> 00:07:04,240
你第一个状态比如说叫做fly

154
00:07:04,240 --> 00:07:05,470
第二个状态叫做蛋

155
00:07:05,470 --> 00:07:07,420
那么实际上大家看一下

156
00:07:07,420 --> 00:07:10,379
每个枚举其实都是一个数字

157
00:07:10,478 --> 00:07:12,218
比如说非就是零死亡

158
00:07:12,218 --> 00:07:12,819
就是一

159
00:07:12,819 --> 00:07:16,088
那么刚好我们知道每个状态它有一个对应的i d对不对

160
00:07:16,088 --> 00:07:19,899
那我们就用这个枚举来代替它的这个i d啊

161
00:07:19,899 --> 00:07:22,079
枚举来代替他的id啊

162
00:07:22,079 --> 00:07:24,478
因为你光说0号状态是不是不太好听

163
00:07:24,478 --> 00:07:26,579
到最后你也忘了0号状态是什么了

164
00:07:26,579 --> 00:07:29,310
你要是说bird state点上fly状态

165
00:07:29,310 --> 00:07:31,410
那你一下就知道这个状态是干嘛的

166
00:07:31,410 --> 00:07:31,889
对不对

167
00:07:33,089 --> 00:07:33,629
嗯

168
00:07:33,629 --> 00:07:37,899
ok那么如果我们要用状态

169
00:07:37,899 --> 00:07:40,860
首先这里我们要创建一个状态机

170
00:07:43,879 --> 00:07:47,540
manager fm manager

171
00:07:48,600 --> 00:07:50,990
这这这里有一个状态机啊

172
00:07:50,990 --> 00:07:57,879
那么状态机的话我们要在start里面把他new一个

173
00:07:59,120 --> 00:08:01,360
其次我们有两种状态

174
00:08:01,360 --> 00:08:04,329
这两种状态我们都要让它继承于state

175
00:08:04,329 --> 00:08:07,029
所以我们在这边还要创建两个新的脚本

176
00:08:07,029 --> 00:08:09,310
每一个状态都是一个新的脚本

177
00:08:10,509 --> 00:08:12,779
第一个状态叫做flad

178
00:08:14,300 --> 00:08:18,399
第二个脚本叫做带state

179
00:08:22,379 --> 00:08:25,819
那么这里啊这个状态的话

180
00:08:25,819 --> 00:08:27,439
我们继承已经说了

181
00:08:27,439 --> 00:08:30,519
继承就是继承于fsm state

182
00:08:31,778 --> 00:08:35,938
然后名字呢就叫fly state

183
00:08:37,779 --> 00:08:41,620
在它的里面呢我们要实现这两个方法

184
00:08:45,799 --> 00:08:47,440
实现这两个方法

185
00:08:47,440 --> 00:08:49,299
那么这是飞翔的

186
00:08:49,299 --> 00:08:50,590
飞翔的状态

187
00:08:50,590 --> 00:08:53,139
你也可以把它的70复方法啊

188
00:08:53,139 --> 00:08:56,320
就是你在这里可以把这个写上

189
00:08:56,320 --> 00:08:57,519
当然写不写无所谓

190
00:08:57,519 --> 00:08:59,019
因为他的这个父类里面

191
00:08:59,019 --> 00:09:00,759
实际上这方法是空的

192
00:09:04,679 --> 00:09:06,679
那么这是飞翔的状态

193
00:09:06,679 --> 00:09:09,259
然后我们再来死亡的状态

194
00:09:11,860 --> 00:09:13,139
这个就没有用了啊

195
00:09:13,139 --> 00:09:14,599
这个start就没用了

196
00:09:16,240 --> 00:09:18,659
然后死亡死亡状态

197
00:09:18,659 --> 00:09:20,700
死亡状态一样的

198
00:09:20,700 --> 00:09:22,200
里面有这两个方法

199
00:09:22,200 --> 00:09:23,279
然后继承的话

200
00:09:23,279 --> 00:09:27,470
我们就要从fsm state继承过来

201
00:09:27,470 --> 00:09:29,659
这里呢就叫die

202
00:09:31,419 --> 00:09:31,980
state

203
00:09:34,200 --> 00:09:35,299
就搞定了

204
00:09:35,600 --> 00:09:39,259
那么这两个状态我们就都算完事儿了啊

205
00:09:39,259 --> 00:09:39,860
都算完事了

206
00:09:39,860 --> 00:09:43,259
然后这两个状态我们需要做什么操作

207
00:09:43,259 --> 00:09:44,879
比如说每一帧要做什么操作

208
00:09:44,879 --> 00:09:46,980
你就写在这个里面啊

209
00:09:46,980 --> 00:09:48,179
如果是进来的时候

210
00:09:48,179 --> 00:09:49,620
只需要做一次的操作

211
00:09:49,620 --> 00:09:50,879
你就写在这个里面

212
00:09:50,879 --> 00:09:52,610
比如说我们更改动画

213
00:09:52,610 --> 00:09:53,990
更改动画这件事情

214
00:09:53,990 --> 00:09:56,330
实际上进入就是切换状态的时候

215
00:09:56,330 --> 00:09:57,409
我们改一次就行了

216
00:09:57,409 --> 00:10:00,899
所以我们比如说哎飞翔动画

217
00:10:00,899 --> 00:10:03,360
飞翔状态里面我们要播放飞翔动画

218
00:10:03,360 --> 00:10:05,370
我们就写在on enter里面就行了

219
00:10:05,370 --> 00:10:09,980
在这里面我们就可以先得到热点啊

220
00:10:09,980 --> 00:10:13,370
competent就是谁拥有我这个状态

221
00:10:13,370 --> 00:10:17,099
然后在他的身上得到组件

222
00:10:17,559 --> 00:10:19,080
得到animation组件

223
00:10:19,080 --> 00:10:20,279
然后播放动画

224
00:10:21,960 --> 00:10:26,840
fla敢跟刚才无非就是麻烦了一些播放动画

225
00:10:26,840 --> 00:10:27,679
对不对

226
00:10:27,679 --> 00:10:30,339
那么在这个死亡这边呢

227
00:10:30,799 --> 00:10:32,929
我们就是只要进入到死亡

228
00:10:32,929 --> 00:10:33,679
死亡状态

229
00:10:33,679 --> 00:10:37,080
我们就播放死亡的动画就可以了

230
00:10:37,080 --> 00:10:40,799
那么这边这两个方法虽然没写东西

231
00:10:40,799 --> 00:10:41,820
但是很有用啊

232
00:10:41,820 --> 00:10:45,279
就是每一帧比如说当前是死亡状态的话

233
00:10:45,279 --> 00:10:48,519
那么死亡状态里面这个方法每一帧都会执行啊

234
00:10:48,519 --> 00:10:49,960
当前要是飞翔状态的话

235
00:10:49,960 --> 00:10:52,919
飞翔状态里面的这个方法每一帧都会执行

236
00:10:54,279 --> 00:10:56,019
那么这时候有这两个状态了

237
00:10:56,019 --> 00:10:57,399
怎样去用啊

238
00:10:57,399 --> 00:10:58,899
用起来就非常简单了

239
00:10:58,899 --> 00:11:03,299
在这里我们要回到这个bd control这边

240
00:11:03,799 --> 00:11:08,159
在这边创建两个状态

241
00:11:09,100 --> 00:11:10,419
一个是飞翔

242
00:11:12,759 --> 00:11:17,090
new fly d bird state

243
00:11:17,090 --> 00:11:18,299
点人fly

244
00:11:19,720 --> 00:11:23,370
谁拥有我们这个状态就是自己this拥有

245
00:11:23,370 --> 00:11:30,578
然后第二个呃属于的这个状态机就是this.fm manager

246
00:11:31,580 --> 00:11:32,509
对不对

247
00:11:32,509 --> 00:11:34,419
同样第二个带

248
00:11:42,340 --> 00:11:45,820
他的id我们也是给他就是1号i d

249
00:11:45,820 --> 00:11:46,600
这个是零

250
00:11:46,600 --> 00:11:47,929
这个就是一对不对

251
00:11:47,929 --> 00:11:50,450
但是我们这样写的话就会清晰很多啊

252
00:11:50,450 --> 00:11:53,059
用枚举这种方式来写就会清晰很多

253
00:11:53,259 --> 00:11:54,700
然后谁拥有呢

254
00:11:54,700 --> 00:11:57,720
就是this属于哪个状态机呢

255
00:11:57,720 --> 00:12:00,620
就是this改fm manager就可以了

256
00:12:01,039 --> 00:12:02,600
创建好这两个状态以后

257
00:12:02,600 --> 00:12:07,340
我们让当前manager状态机状态列表啊

258
00:12:07,340 --> 00:12:10,899
我们就更新为fly跟di这两个状态

259
00:12:10,899 --> 00:12:17,649
然后我们就可以开始执行状态了

260
00:12:17,649 --> 00:12:19,299
那么开始执行状态以后

261
00:12:19,299 --> 00:12:20,379
我们就要做什么操作

262
00:12:20,379 --> 00:12:27,100
就是this.f s f s m manager点切换状态啊

263
00:12:27,100 --> 00:12:29,799
就默认我们让它执行一个状态

264
00:12:29,799 --> 00:12:31,240
就是飞翔啊

265
00:12:31,240 --> 00:12:33,669
就是你这个你不能说没有状态

266
00:12:33,669 --> 00:12:35,019
你默认也有一个状态

267
00:12:35,019 --> 00:12:36,339
默认是飞翔

268
00:12:36,820 --> 00:12:41,068
然后在这边我们就是只要点一下飞翔的按钮

269
00:12:41,068 --> 00:12:44,188
那么这里本来如果你要写飞行飞翔的话

270
00:12:44,188 --> 00:12:45,269
可能有很多代码

271
00:12:45,269 --> 00:12:49,839
那么但是在这里你只需要一行就是飞翔在这里死亡

272
00:12:49,839 --> 00:12:53,279
就是一行切换到死亡状态

273
00:12:53,279 --> 00:12:58,919
这样的话实际上就是我们在这边在我们主要的小鸟脚本里面

274
00:12:59,120 --> 00:13:01,789
无非做的就是状态之间的切换了

275
00:13:01,789 --> 00:13:04,490
那么这个状态具体执行什么代码

276
00:13:04,490 --> 00:13:08,269
就要到这个状态对应的脚本里面去修改

277
00:13:08,269 --> 00:13:09,710
这样的话是不是就很好了

278
00:13:09,710 --> 00:13:11,629
把这个代码给抽出来了啊

279
00:13:11,629 --> 00:13:14,450
每一个状态的代码抽成一个脚本啊

280
00:13:14,450 --> 00:13:20,230
那这样这样的话对于一个很复杂的角色而言就会好很多啊

281
00:13:20,230 --> 00:13:21,220
就会好很多

282
00:13:21,220 --> 00:13:24,220
那在update这边一定要记着做一件事

283
00:13:24,220 --> 00:13:25,419
做一个判断

284
00:13:25,419 --> 00:13:31,240
如果当前这个状态管理器当前的执行状态不是-1的话

285
00:13:31,240 --> 00:13:33,340
就证明当前已经有状态了

286
00:13:33,340 --> 00:13:34,219
对不对

287
00:13:34,240 --> 00:13:37,000
如果当前有状态啊

288
00:13:37,000 --> 00:13:38,740
就是说当前已经在执行状态了

289
00:13:38,740 --> 00:13:42,200
我们就让他执行on update

290
00:13:43,639 --> 00:13:51,179
这样的话我们其实就是通过这个update调用了状态里面的update啊

291
00:13:51,179 --> 00:13:56,139
这样的话就可以做到每一帧都会调用里面的啊update对不对

292
00:13:58,320 --> 00:14:00,320
ok啊那这样的话我们再来试一下

293
00:14:00,320 --> 00:14:03,979
看看效果跟刚才有没有这个区别

294
00:14:04,519 --> 00:14:06,120
我们再运行一下

295
00:14:06,820 --> 00:14:12,379
默认是你看是在飞死亡死亡飞翔飞死亡死亡飞翔飞

296
00:14:12,379 --> 00:14:15,379
所以实际上我们运行起来跟刚才没有任何区别

297
00:14:15,379 --> 00:14:18,489
但是代码结构比刚才好很多啊

298
00:14:18,489 --> 00:14:22,208
就是你哪怕当前我这个小鸟18般武艺

299
00:14:22,208 --> 00:14:23,019
18个状态

300
00:14:23,019 --> 00:14:23,979
对不对

301
00:14:24,019 --> 00:14:26,360
你在这里无非就是一个状态

302
00:14:26,360 --> 00:14:27,620
给它创建一个对象

303
00:14:27,620 --> 00:14:29,710
一个状态创建一个对象就完了

304
00:14:29,710 --> 00:14:33,549
你不需要把18个状态的各种逻辑全写到一个脚本里面

305
00:14:33,549 --> 00:14:34,730
那要乱死了

306
00:14:34,730 --> 00:14:36,590
可能甚至可能几百行都写不完

307
00:14:36,590 --> 00:14:37,159
对不对

308
00:14:37,159 --> 00:14:40,549
但是你把这18个状态分成18个脚本啊

309
00:14:40,549 --> 00:14:42,240
一个脚本里面写一个状态

310
00:14:42,240 --> 00:14:43,980
这样的话是不是效果会很好

311
00:14:43,980 --> 00:14:45,059
好很多呀

312
00:14:45,059 --> 00:14:46,799
会清晰很多啊

313
00:14:46,799 --> 00:14:49,870
那么这就是这个状态机啊

314
00:14:49,870 --> 00:14:51,719
有限状态机带来的好处

315
00:14:52,980 --> 00:14:56,960
ok那么这节课我们就这么多


1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,740 --> 00:00:12,099
ok这节课我们还是来讲一个思想啊

3
00:00:12,099 --> 00:00:14,519
就是做游戏很重要的一个思想

4
00:00:14,519 --> 00:00:16,649
那么就是有限状态机啊

5
00:00:16,649 --> 00:00:19,199
那么这个东西是干嘛的啊

6
00:00:19,199 --> 00:00:20,460
这个东西是干嘛的

7
00:00:20,460 --> 00:00:22,320
比如说咱们有一个主角啊

8
00:00:22,320 --> 00:00:23,879
比如说咱们有一个主角

9
00:00:23,879 --> 00:00:28,239
这个主角呢会做很多事情啊

10
00:00:28,239 --> 00:00:29,289
会做很多事情

11
00:00:29,289 --> 00:00:31,480
如果我们真正去写代码啊

12
00:00:31,480 --> 00:00:33,399
让这个主角会做这么多事情的话

13
00:00:33,399 --> 00:00:35,200
怎样去怎样去写

14
00:00:35,219 --> 00:00:36,179
正常情况下

15
00:00:36,179 --> 00:00:37,799
比如说啊我们这个主角会走路

16
00:00:37,799 --> 00:00:38,700
会跳跃

17
00:00:38,700 --> 00:00:41,189
你肯定就就这样去写在代码里面

18
00:00:41,189 --> 00:00:42,299
如果我们按了

19
00:00:42,299 --> 00:00:45,460
比如说左键或者右键让主角走路啊

20
00:00:45,460 --> 00:00:46,899
朝左边或者朝右边走

21
00:00:46,899 --> 00:00:49,240
同时播放走路的动画

22
00:00:49,240 --> 00:00:50,159
对不对

23
00:00:50,320 --> 00:00:52,600
如果我们按了空格

24
00:00:52,600 --> 00:00:55,170
然后让这个主角哎飞起来

25
00:00:55,170 --> 00:00:56,820
首先位置改变对不对

26
00:00:56,820 --> 00:00:57,899
做一个跳跃的

27
00:00:57,899 --> 00:01:02,479
比如说咱们之前的jump to啊这样的一个动画嗯

28
00:01:02,479 --> 00:01:05,659
同时我们让这个角色也播放跳跃的动画

29
00:01:05,659 --> 00:01:06,500
对不对

30
00:01:06,680 --> 00:01:11,000
那么也就是说我们之前写代码其实都是按流程来的啊

31
00:01:11,000 --> 00:01:12,260
就是按流程来的

32
00:01:12,260 --> 00:01:13,280
就是我们怎么想

33
00:01:13,280 --> 00:01:16,060
我们直接就把我们的想法给写成代码了

34
00:01:16,060 --> 00:01:20,439
那这样的话呃有一个问题

35
00:01:20,439 --> 00:01:25,689
就是当这个主角他要做的动作啊比较多的时候

36
00:01:25,689 --> 00:01:27,519
比如说这个主角不光会走路

37
00:01:27,519 --> 00:01:28,959
他还会跑步啊

38
00:01:28,959 --> 00:01:29,980
不光会跳跃

39
00:01:29,980 --> 00:01:30,980
他会打人

40
00:01:30,980 --> 00:01:33,140
打人里面会轻击啊

41
00:01:33,140 --> 00:01:35,599
会重击还会踢啊

42
00:01:35,599 --> 00:01:36,920
那么它还有技能

43
00:01:36,920 --> 00:01:38,090
各种技能等等

44
00:01:38,090 --> 00:01:41,930
那么当这个主角他会做的动作量太大的时候

45
00:01:41,930 --> 00:01:46,200
你会发现按我们的思路啊去写代码的代码量

46
00:01:46,200 --> 00:01:47,879
首先代码量肯定会上去

47
00:01:47,879 --> 00:01:51,090
那么其次就是会非常的乱啊

48
00:01:51,090 --> 00:01:52,799
非常非常的乱啊

49
00:01:52,799 --> 00:01:54,000
当它越复杂

50
00:01:54,000 --> 00:01:56,040
当这个主角的逻辑越复杂的时候

51
00:01:56,040 --> 00:01:57,500
我们写的代码越乱

52
00:01:57,500 --> 00:01:58,609
那么怎么办

53
00:01:58,609 --> 00:02:04,920
那这时候我们就要有一个我们就需要用这个有限状态机来解决了

54
00:02:05,680 --> 00:02:07,480
那么什么是有限状态机啊

55
00:02:07,480 --> 00:02:11,439
首先有限在这里意思就是当然它就不是无限的啊

56
00:02:11,439 --> 00:02:12,879
它是有限的啊

57
00:02:12,879 --> 00:02:14,319
这个就是很顾名思义了

58
00:02:14,319 --> 00:02:14,919
对不对

59
00:02:14,919 --> 00:02:15,639
状态

60
00:02:15,639 --> 00:02:17,439
那我们现在就要理解一下了

61
00:02:17,439 --> 00:02:22,159
那么其实他就是把主角不同的行为分成了不同的状态

62
00:02:22,180 --> 00:02:24,780
比如说我们的主角有什么状态

63
00:02:26,439 --> 00:02:31,939
比如说他会有一个这个这个唉站立的一个状态

64
00:02:33,860 --> 00:02:35,930
那么它还会有什么状态

65
00:02:35,930 --> 00:02:38,580
还会有跑步的状态

66
00:02:39,219 --> 00:02:43,030
跑步的状态还会有什么状态

67
00:02:43,030 --> 00:02:46,780
还会有这个攻击的状态啊

68
00:02:46,780 --> 00:02:48,319
还会有攻击的状态

69
00:02:53,039 --> 00:03:00,340
那么也就是说我们把主角啊他的各种事情都给它分成不同的状态了

70
00:03:00,360 --> 00:03:01,919
分成不同的状态以后

71
00:03:01,919 --> 00:03:06,979
然后我们只需要给某一个状态里面去填写它自己的代码就行了

72
00:03:06,979 --> 00:03:09,710
比如说如果这个主角处于站立的时候

73
00:03:09,710 --> 00:03:11,719
他应该播放什么动画啊

74
00:03:11,719 --> 00:03:13,340
他当前应该是什么行为

75
00:03:13,340 --> 00:03:16,479
把所有的代码全都写在站立的里面

76
00:03:16,780 --> 00:03:20,620
他的跑步相关的代码全写在跑步的状态里面

77
00:03:20,620 --> 00:03:23,979
他攻击所有的代码全写到攻击这个状态里面

78
00:03:23,979 --> 00:03:27,280
那这样的话好处就是非常清晰啊

79
00:03:27,280 --> 00:03:28,240
结构很清晰

80
00:03:28,240 --> 00:03:32,259
其次状态之间也不会产生影响啊

81
00:03:32,259 --> 00:03:33,099
不会产生影响

82
00:03:33,099 --> 00:03:35,080
比如说你攻击有问题了啊

83
00:03:35,080 --> 00:03:37,419
你上来就知道那是不是攻击状态啊

84
00:03:37,419 --> 00:03:39,039
里面的代码有问题了啊

85
00:03:39,039 --> 00:03:42,319
那就会考虑i在这里面怎样去修改

86
00:03:42,819 --> 00:03:45,520
当这个尤其是状态越多的时候

87
00:03:45,520 --> 00:03:47,919
我们这种写法那是越好的啊

88
00:03:47,919 --> 00:03:48,900
是越好的

89
00:03:49,719 --> 00:03:53,639
那么这时候我们大家要想一下

90
00:03:53,639 --> 00:03:57,389
首先我这个主角啊不能是没有状态的

91
00:03:57,389 --> 00:04:00,780
它默认一定处于某一种状态啊

92
00:04:00,780 --> 00:04:03,599
比如说他默认他的状态是什么

93
00:04:03,599 --> 00:04:09,699
比如说它的默认他的状态就是这个啊站立

94
00:04:10,000 --> 00:04:12,460
那么我们在这个逻辑代码里面

95
00:04:12,460 --> 00:04:14,020
比如说我们按了一个键啊

96
00:04:14,020 --> 00:04:15,460
按了左键或者右键

97
00:04:15,460 --> 00:04:18,160
无非就是让它状态之间去进行什么呀

98
00:04:18,160 --> 00:04:19,319
进行切换

99
00:04:19,579 --> 00:04:21,019
进行切换的话

100
00:04:21,019 --> 00:04:23,059
比如说把状态切换到跑步

101
00:04:23,059 --> 00:04:25,480
那么主角就会执行跑步的代码

102
00:04:25,480 --> 00:04:27,850
把状态执行切换到攻击

103
00:04:27,850 --> 00:04:30,449
它就会执行工具的代码啊

104
00:04:30,449 --> 00:04:32,160
这就是一个状态模式

105
00:04:32,160 --> 00:04:34,500
它一定有某一种状态啊

106
00:04:34,500 --> 00:04:37,319
它不能是无没有状态的啊

107
00:04:37,319 --> 00:04:37,620
对不对

108
00:04:37,620 --> 00:04:41,399
这个其实就也是一个面向对象的这样的一套思想

109
00:04:41,399 --> 00:04:42,360
大家想一想

110
00:04:42,360 --> 00:04:44,920
比如说你拿你自身啊来去想

111
00:04:44,939 --> 00:04:46,620
你当前一定也有一个状态

112
00:04:46,620 --> 00:04:48,879
你的状态现在是听课啊

113
00:04:48,879 --> 00:04:49,750
你还能睡觉

114
00:04:49,750 --> 00:04:51,339
你还有吃饭的状态啊

115
00:04:51,339 --> 00:04:52,569
你每天还有各种

116
00:04:52,569 --> 00:04:53,560
你要是有上班

117
00:04:53,560 --> 00:04:54,519
还有上班的状态

118
00:04:54,519 --> 00:04:56,019
有上学还有读书的状态

119
00:04:56,019 --> 00:04:56,680
对不对

120
00:04:56,680 --> 00:04:59,620
所以其实就是每个人都可以分出不同的状态啊

121
00:04:59,620 --> 00:05:01,279
每个状态里面我们体验东西

122
00:05:01,300 --> 00:05:04,899
最后做的就是把每个状态的代码填完以后啊

123
00:05:04,899 --> 00:05:07,180
他要做的要做的事填完以后

124
00:05:07,199 --> 00:05:11,490
我们就是让这个主角在这些状态之间进行干嘛

125
00:05:11,490 --> 00:05:13,829
进行切换就行了啊

126
00:05:13,829 --> 00:05:15,480
那么谁去进行切换呢

127
00:05:15,480 --> 00:05:17,009
我们就需要一个管理者

128
00:05:17,009 --> 00:05:19,139
管理者就叫做状态机啊

129
00:05:19,139 --> 00:05:21,060
就叫做状态机啊

130
00:05:21,060 --> 00:05:24,600
那么也就是说如果我们要实现这样的一个逻辑

131
00:05:24,600 --> 00:05:27,360
我们需要去编写两个基本的脚本

132
00:05:27,360 --> 00:05:29,019
来实现一个脚本

133
00:05:30,579 --> 00:05:34,680
就是首先这三个啊每一个都是一个脚本

134
00:05:34,680 --> 00:05:36,639
比如攻击是一个单独的脚本

135
00:05:36,660 --> 00:05:38,160
跑步是个单独的脚本

136
00:05:38,160 --> 00:05:39,839
站立都是个单独的脚本

137
00:05:39,839 --> 00:05:41,759
但是他们都属于状态

138
00:05:41,759 --> 00:05:47,430
所以我们要写一个共同的父类f l s m state

139
00:05:47,430 --> 00:05:50,379
那么这三个状态啊

140
00:05:50,379 --> 00:05:54,639
甚至以后写的更多的全都是继承于这样的一个类的

141
00:05:54,639 --> 00:05:55,660
只要继承于他

142
00:05:55,660 --> 00:05:58,620
就证明你当前这个这个类啊

143
00:05:58,620 --> 00:06:00,779
这个脚本它就是一个状态啊

144
00:06:00,779 --> 00:06:03,060
这个脚本不是什么其他的啊

145
00:06:03,060 --> 00:06:03,839
没有什么其他的

146
00:06:03,839 --> 00:06:05,740
用这个脚本就是一个状态

147
00:06:05,839 --> 00:06:07,189
那么除此之外

148
00:06:07,189 --> 00:06:09,139
我还需要一个人去进行管理

149
00:06:09,139 --> 00:06:10,879
在这些状态之间进行切换

150
00:06:10,879 --> 00:06:11,959
谁去管理

151
00:06:13,680 --> 00:06:17,060
我们还要写第二个脚本fm manag

152
00:06:17,639 --> 00:06:20,160
这个其实就是我们的状态机啊

153
00:06:20,160 --> 00:06:21,839
或者叫状态管理器啊

154
00:06:21,839 --> 00:06:22,879
这样的一个东西

155
00:06:23,819 --> 00:06:27,139
也就是说我们写两个脚本

156
00:06:27,139 --> 00:06:28,399
一个是状态啊

157
00:06:28,399 --> 00:06:29,779
一个是状态机

158
00:06:29,779 --> 00:06:31,699
那么他们到最后只有一个啊

159
00:06:31,699 --> 00:06:34,279
比如说主角身上就带了一个状态机

160
00:06:34,279 --> 00:06:36,079
然后他呢有很多状态

161
00:06:36,079 --> 00:06:38,480
每个状态父类都是它对不对

162
00:06:38,480 --> 00:06:42,800
然后状态机的作用就是在这些状态之间去进行切换就可以了

163
00:06:42,800 --> 00:06:47,939
你切换到哪个状态就执行哪个状态里面的代码啊

164
00:06:47,939 --> 00:06:51,360
也就是说你看主角比如说有站立还是一个脚本对吧

165
00:06:51,360 --> 00:06:53,040
都是点t脚本啊

166
00:06:53,040 --> 00:06:54,199
都是ts脚本

167
00:06:54,220 --> 00:06:57,639
但是这些脚本里面的代码并不会同时执行它

168
00:06:57,639 --> 00:07:01,750
同一时间只能执行一个脚本里面的执行哪个脚本

169
00:07:01,750 --> 00:07:03,939
就看我当前是什么状态了啊

170
00:07:03,939 --> 00:07:05,319
如果我现在是站立状态

171
00:07:05,319 --> 00:07:08,560
这个脚本里面的代码执行这两个代码是不执行的

172
00:07:08,579 --> 00:07:09,180
对不对

173
00:07:09,180 --> 00:07:10,620
如果切换到跑步了

174
00:07:10,620 --> 00:07:11,699
站立代码就不执行了

175
00:07:11,699 --> 00:07:12,720
攻击也不执行了

176
00:07:12,720 --> 00:07:14,139
跑步就开始执行了

177
00:07:14,139 --> 00:07:16,480
对不对啊

178
00:07:16,480 --> 00:07:19,180
ok啊逻辑就是这样的一个逻辑

179
00:07:19,180 --> 00:07:22,920
那么我们现在来写代码来实现一下啊

180
00:07:24,120 --> 00:07:26,670
呃我还拿上节课这个吧

181
00:07:26,670 --> 00:07:31,029
在这里面我们来创建我们两个脚本

182
00:07:31,029 --> 00:07:34,240
就是我们的第一个是状态脚本

183
00:07:37,360 --> 00:07:38,620
第二个

184
00:07:41,120 --> 00:07:42,889
是我们的状态机

185
00:07:42,889 --> 00:07:45,139
然后我们打开这两个脚本

186
00:07:46,600 --> 00:07:48,399
首先改下名

187
00:07:53,100 --> 00:07:55,829
然后这个也改下名

188
00:07:55,829 --> 00:07:57,199
两个都改下名

189
00:07:59,360 --> 00:08:00,860
ok我们开始去写

190
00:08:00,860 --> 00:08:02,569
首先先编写状态

191
00:08:02,569 --> 00:08:03,680
状态的话

192
00:08:03,680 --> 00:08:08,899
我们他是不准备挂到我们的这个呃组件上的啊

193
00:08:08,899 --> 00:08:10,500
它不是挂到物体上的

194
00:08:12,879 --> 00:08:18,100
那么我们是把它当做一个单独的一个对象来使用的啊

195
00:08:18,100 --> 00:08:22,089
也就是说我们最后这三个都是单独的一个对象

196
00:08:22,089 --> 00:08:23,379
然后生成对象以后

197
00:08:23,379 --> 00:08:25,120
我们会放到一个数组里面

198
00:08:25,139 --> 00:08:29,100
数组就是归我们这个状态机管理啊

199
00:08:29,100 --> 00:08:30,339
我们是这样去做的

200
00:08:30,339 --> 00:08:33,580
所以他呢是不需要继承我们的组件的啊

201
00:08:34,700 --> 00:08:35,990
那么在它里面

202
00:08:35,990 --> 00:08:42,610
首先我既然我们当前这个这个这个class代表的是个状态

203
00:08:42,610 --> 00:08:43,360
对不对

204
00:08:43,360 --> 00:08:45,419
它代表的是个状态的积累

205
00:08:45,440 --> 00:08:48,440
那么首先我要知道当前

206
00:08:48,440 --> 00:08:52,570
状态的一个编码啊

207
00:08:52,570 --> 00:08:55,779
这个状态我们要用一个东西去区分啊

208
00:08:55,779 --> 00:08:57,279
那么什么去区分呢

209
00:08:57,279 --> 00:08:58,960
我们用编码去区分

210
00:08:58,960 --> 00:09:02,019
比如说当年是0号状态还是1号状态还是2号状态

211
00:09:02,019 --> 00:09:04,720
所以每个状态我们给它不同的编码啊

212
00:09:04,720 --> 00:09:06,179
这样的话会好一些

213
00:09:06,320 --> 00:09:10,519
state id number啊

214
00:09:10,519 --> 00:09:12,529
就是数字类型的状态i d

215
00:09:12,529 --> 00:09:19,080
那么第二个状态的一个拥有者

216
00:09:20,960 --> 00:09:22,940
是谁拥有这个状态啊

217
00:09:22,940 --> 00:09:24,860
也就是说我跑步

218
00:09:24,860 --> 00:09:27,919
我跑步的拥有者就是这个主角战力的拥有者

219
00:09:27,919 --> 00:09:28,639
也是这个主角

220
00:09:28,639 --> 00:09:29,899
我都知道是谁

221
00:09:29,899 --> 00:09:31,759
我这个状态是为谁服务的

222
00:09:31,759 --> 00:09:32,480
对不对

223
00:09:32,500 --> 00:09:35,679
所以在这里有个状态拥有者拥有者的话

224
00:09:35,679 --> 00:09:38,870
那咱们一般就是咱们的这个组件了啊

225
00:09:38,870 --> 00:09:41,480
所以咱们就叫component

226
00:09:46,559 --> 00:09:51,600
啊是哪一个这个这个组件的这个子类啊

227
00:09:51,600 --> 00:09:53,419
是拥有我这些状态的

228
00:09:54,500 --> 00:10:01,580
那么再往下当前我这个状态是属于哪一个管理器来管理的

229
00:10:01,580 --> 00:10:07,159
是属于哪个状态机里面的这个我们也要也要让他知道一下啊

230
00:10:07,159 --> 00:10:08,360
也要让他知道一下

231
00:10:08,360 --> 00:10:10,580
那么如果你这些不写的话

232
00:10:10,580 --> 00:10:12,590
其实也可以啊

233
00:10:12,590 --> 00:10:14,799
就是说下面这两个id一定要有

234
00:10:14,820 --> 00:10:20,279
那么状态拥有者和这个所属管理器啊

235
00:10:20,279 --> 00:10:22,039
或者所属状态机

236
00:10:23,100 --> 00:10:25,080
这两个要没有的话也可以

237
00:10:25,080 --> 00:10:30,039
但是经过很多次的这个实验啊

238
00:10:30,039 --> 00:10:32,440
你这个写上的话会好用很多啊

239
00:10:32,440 --> 00:10:35,360
不写的话后面写代码最麻烦很多啊

240
00:10:35,799 --> 00:10:38,529
所以在这里大家写上就可以了

241
00:10:38,529 --> 00:10:41,500
然后我们给他写一个初始化的方法

242
00:10:42,100 --> 00:10:43,240
初始化的时候

243
00:10:43,240 --> 00:10:47,620
我们就要让外界怎样把这三个内容传过来

244
00:10:47,620 --> 00:10:50,039
一个是我们的状态

245
00:10:54,139 --> 00:10:57,740
然后还有一个是所属者啊

246
00:10:57,740 --> 00:10:59,159
就是这个拥有者

247
00:11:00,679 --> 00:11:04,460
然后还有一个就是用呃所属的这个状态机

248
00:11:09,340 --> 00:11:13,720
然后在这边呢我们直接给它赋值就行了

249
00:11:25,039 --> 00:11:28,639
然后呢每一个状态有两个方法

250
00:11:28,639 --> 00:11:29,299
回调方法

251
00:11:29,299 --> 00:11:31,019
一个是进入状态

252
00:11:31,659 --> 00:11:36,320
一个是状态更新中

253
00:11:36,379 --> 00:11:38,840
那么这两个实际上就是什么

254
00:11:38,840 --> 00:11:43,100
实际上就是咱们正常脚本里面有两个方法叫做start和update

255
00:11:43,100 --> 00:11:44,759
是不是一个start方法

256
00:11:44,759 --> 00:11:48,700
一个update start是就是进入这个脚本的时候调用一次

257
00:11:48,700 --> 00:11:50,259
update是每一帧调用一次

258
00:11:50,259 --> 00:11:51,019
对不对

259
00:11:51,019 --> 00:11:55,820
那么咱们的这个进入状态和状态更更新中

260
00:11:55,820 --> 00:11:59,960
实际上啊和和n的两个方法是一样的啊

261
00:11:59,960 --> 00:12:04,559
就是我们这里有一个on enter方法啊

262
00:12:04,559 --> 00:12:07,539
under这是一个进入状态啊

263
00:12:07,539 --> 00:12:09,879
只要进入切换到一个状态

264
00:12:09,879 --> 00:12:13,059
那么它们就会执行一次这个方法之后

265
00:12:13,059 --> 00:12:17,059
它就会一直执行状态更新中这个方法

266
00:12:17,059 --> 00:12:18,879
on update

267
00:12:22,120 --> 00:12:25,840
啊也就是说只要我们切换为哪个动画啊

268
00:12:25,840 --> 00:12:28,820
比如说我们切换成跑步动画

269
00:12:28,820 --> 00:12:30,980
那么跑步的这个状态啊

270
00:12:30,980 --> 00:12:32,179
我们切换到跑步状态

271
00:12:32,179 --> 00:12:35,450
那么跑步状态里面先会掉一次这个方法

272
00:12:35,450 --> 00:12:38,539
然后就会每一帧都会调用这个方法就会一直在调用

273
00:12:38,539 --> 00:12:39,460
一直在调用

274
00:12:40,960 --> 00:12:48,539
ok啊也就是说每一个这就是每个状态里面拥有的东西其实很简单啊

275
00:12:48,539 --> 00:12:51,059
他这个东西主要就是这套思想啊

276
00:12:51,059 --> 00:12:53,690
代码很简单呃

277
00:12:53,690 --> 00:12:54,980
仔细看一下啊

278
00:12:54,980 --> 00:12:55,700
非常简单

279
00:12:55,700 --> 00:12:56,899
三个属性对不对

280
00:12:56,899 --> 00:12:57,820
三个属性

281
00:12:57,820 --> 00:12:59,139
然后两个方法

282
00:12:59,139 --> 00:13:01,179
三个属性最重要的其实就是一个id

283
00:13:01,179 --> 00:13:03,549
也就是说每个状态其实就是一个类

284
00:13:03,549 --> 00:13:05,080
这个类有一个自己的id

285
00:13:05,080 --> 00:13:06,299
就是几号状态

286
00:13:06,299 --> 00:13:08,940
然后有两个回调方法啊

287
00:13:08,940 --> 00:13:12,350
这两个方法是咱们自己给它加上的啊

288
00:13:12,350 --> 00:13:14,899
当然我们想法是好的

289
00:13:14,899 --> 00:13:16,879
但是现在这个还没有实现是吧

290
00:13:16,879 --> 00:13:18,080
什么时候掉on enter

291
00:13:18,080 --> 00:13:18,679
什么时候掉

292
00:13:18,679 --> 00:13:19,340
on update

293
00:13:19,340 --> 00:13:20,480
我们还没有去写啊

294
00:13:20,480 --> 00:13:21,740
这个大家先不用管

295
00:13:21,740 --> 00:13:26,700
你现在想的就是我们需要让它按enter是进入状态会调用一次

296
00:13:26,720 --> 00:13:29,919
而update就是每次状态更新啊

297
00:13:29,919 --> 00:13:33,100
就是每一次这个这个这个每一帧都会这样一次啊

298
00:13:33,100 --> 00:13:34,379
就这样的一个意思

299
00:13:35,399 --> 00:13:41,440
那接下来我们实现这个manager manager这边也是一个自己的一个类

300
00:13:43,080 --> 00:13:44,620
单独的一个类

301
00:13:46,019 --> 00:13:47,399
这个类里面

302
00:13:47,399 --> 00:13:49,860
首先我要有一个状态列表

303
00:13:51,720 --> 00:13:56,080
代表我当前我拥有哪些状态

304
00:13:58,720 --> 00:14:01,659
那默认我们可以给它一个空的啊

305
00:14:01,659 --> 00:14:03,519
我们可以给它一个空档

306
00:14:03,620 --> 00:14:05,960
那么这个就是我们的一个状态列表

307
00:14:05,960 --> 00:14:09,179
然后再来一个就是当前的一个状态

308
00:14:12,419 --> 00:14:13,860
当前的状态

309
00:14:13,860 --> 00:14:16,700
当前状态我们知道呃

310
00:14:16,700 --> 00:14:19,759
它就代表我们当前执行的是哪一块状态

311
00:14:19,759 --> 00:14:21,860
因为每个状态不都有一个i d嘛

312
00:14:21,860 --> 00:14:26,779
当前状态就是当前是哪一个状态的一个id

313
00:14:26,779 --> 00:14:28,639
默认我们给他一个-1啊

314
00:14:28,639 --> 00:14:31,039
因为你最开始他是没有执行的啊

315
00:14:31,039 --> 00:14:33,840
没有状态的啊

316
00:14:33,840 --> 00:14:36,480
那么在这里作为状态机

317
00:14:36,480 --> 00:14:37,559
它有两个功能

318
00:14:37,559 --> 00:14:39,580
一个就是改变状态

319
00:14:42,639 --> 00:14:44,860
呃改变状态的话很简单

320
00:14:44,860 --> 00:14:49,600
就是我们给他传来一个传来一个i d

321
00:14:51,399 --> 00:14:53,230
传来一个状态的id

322
00:14:53,230 --> 00:14:56,779
然后呢它就会改变为这个状态啊

323
00:14:56,779 --> 00:14:57,620
那非常简单

324
00:14:57,620 --> 00:14:58,039
怎么做

325
00:14:58,039 --> 00:15:05,720
就是让当前的状态就等于这个状态啊

326
00:15:07,200 --> 00:15:10,440
改变状态id要改变状态了

327
00:15:10,440 --> 00:15:10,860
很简单

328
00:15:10,860 --> 00:15:12,559
就是改变一下状态

329
00:15:12,980 --> 00:15:14,960
然后呢

330
00:15:14,960 --> 00:15:17,419
我们要做一件事儿

331
00:15:17,419 --> 00:15:23,919
调用新状态的enter方法

332
00:15:23,919 --> 00:15:27,610
那我们就要调用一下当前这个状态

333
00:15:27,610 --> 00:15:32,559
数组里面已经切换完的这个新的状态

334
00:15:32,559 --> 00:15:34,179
你看新的这个状态

335
00:15:34,860 --> 00:15:37,889
它的按enter方法就可以了

336
00:15:37,889 --> 00:15:40,320
那么这个状态我们就要从零开始命名了

337
00:15:40,320 --> 00:15:41,220
012啊

338
00:15:41,220 --> 00:15:42,360
一定要按这样的顺序

339
00:15:42,360 --> 00:15:43,659
01234

340
00:15:44,440 --> 00:15:45,879
那么调用完以后

341
00:15:45,879 --> 00:15:47,019
我们还有一个方法

342
00:15:47,019 --> 00:15:50,740
就是我们知道现在只要切换切换新的状态

343
00:15:50,740 --> 00:15:52,240
我们都会调这个方法

344
00:15:52,240 --> 00:15:52,820
对不对

345
00:15:52,820 --> 00:15:55,039
但是这个on update每一帧都要调用

346
00:15:55,039 --> 00:15:56,299
这个什么时候都要用

347
00:15:56,299 --> 00:16:00,259
我们在这里写一个更新调用啊

348
00:16:00,259 --> 00:16:04,039
每一帧更新调用的方法也叫on update

349
00:16:05,659 --> 00:16:07,580
这个方法里面我们做一个判断

350
00:16:07,580 --> 00:16:12,139
就是只要当前的这个呃状态啊

351
00:16:12,139 --> 00:16:15,409
当前的状态不等于应该是不等于-1

352
00:16:15,409 --> 00:16:18,500
就证明我当前肯定有状态在执行了

353
00:16:18,519 --> 00:16:19,659
有状态在执行

354
00:16:19,659 --> 00:16:21,240
我就怎样调用

355
00:16:23,120 --> 00:16:27,480
当前状态的update方法

356
00:16:29,100 --> 00:16:31,529
this state list

357
00:16:31,529 --> 00:16:33,690
this.kind index

358
00:16:33,690 --> 00:16:36,950
点on update就可以了

359
00:16:36,950 --> 00:16:39,019
那么这个方法也是还没执行

360
00:16:39,019 --> 00:16:39,559
对不对啊

361
00:16:39,559 --> 00:16:40,279
这个不用急

362
00:16:40,279 --> 00:16:43,700
这个咱们在最外层让它去执行啊

363
00:16:43,700 --> 00:16:46,399
就是这个现在还没人调用它啊

364
00:16:46,399 --> 00:16:48,440
但是这个enter方法已经没有问题了

365
00:16:48,440 --> 00:16:51,019
只要我们外界有人来切换状态啊

366
00:16:51,019 --> 00:16:53,450
它就会掉这个安特方法啊

367
00:16:53,450 --> 00:16:57,500
ok这个就是咱们的状态机啊

368
00:16:57,500 --> 00:17:00,360
这个就是状态机非常简单非常简单

369
00:17:00,360 --> 00:17:02,159
状态机里面其实就是一个数组

370
00:17:02,159 --> 00:17:04,859
这个数组里面放了很多状态的

371
00:17:04,859 --> 00:17:06,470
就是它的子类啊

372
00:17:06,470 --> 00:17:07,858
放了很多他的子类

373
00:17:07,858 --> 00:17:10,979
然后同时我只能执行一个状态

374
00:17:10,979 --> 00:17:12,179
哪一个状态呢

375
00:17:12,179 --> 00:17:15,420
就看我当前这个current index是几

376
00:17:15,420 --> 00:17:17,579
它是零就执行0号状态

377
00:17:17,579 --> 00:17:19,289
他是一就执行1号状态

378
00:17:19,289 --> 00:17:20,759
那么就是这样的啊

379
00:17:20,759 --> 00:17:22,700
就是这样的嗯

380
00:17:22,700 --> 00:17:24,799
ok啊我们这节课啊

381
00:17:24,799 --> 00:17:25,579
就先这么多

382
00:17:25,579 --> 00:17:27,259
大家先把这个搞清楚啊

383
00:17:27,259 --> 00:17:30,200
先把这个搞清楚嗯


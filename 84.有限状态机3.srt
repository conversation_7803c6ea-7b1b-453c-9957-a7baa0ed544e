1
00:00:09,140 --> 00:00:14,429
ok那么前两节课咱们讲了一下这个有限状态机

2
00:00:14,429 --> 00:00:16,230
那么这个状态机的话

3
00:00:16,230 --> 00:00:18,170
咱们其实做了一个例子

4
00:00:18,170 --> 00:00:20,929
咱们是使用一个主角啊

5
00:00:20,929 --> 00:00:22,730
他可能有的这种状态

6
00:00:22,730 --> 00:00:23,920
然后来去做的

7
00:00:23,920 --> 00:00:25,839
比如说一个主角有站立状态

8
00:00:25,839 --> 00:00:26,739
有死亡状态

9
00:00:26,739 --> 00:00:27,850
有飞翔状态

10
00:00:27,850 --> 00:00:29,460
跳跃状态啊

11
00:00:29,460 --> 00:00:30,719
攻击状态等等

12
00:00:30,719 --> 00:00:33,299
那么每一个状态我们都都知道啊

13
00:00:33,299 --> 00:00:38,759
他就可以当做我们状态机里面的一个状态类来去这样封装

14
00:00:39,119 --> 00:00:46,460
那么实际上啊有限状态机不光能不光是在这种情况下能用

15
00:00:46,460 --> 00:00:48,710
它在很多地儿都可以派得上用场

16
00:00:48,710 --> 00:00:50,750
比如说这里举一个例子啊

17
00:00:50,750 --> 00:00:53,829
就是因为这是刚好我在写了一个东西

18
00:00:53,829 --> 00:00:58,039
就是呃这个东西啊不是一个游戏

19
00:00:58,039 --> 00:00:59,509
它只是一个应用

20
00:00:59,509 --> 00:01:02,659
那么这个应用我也用到了状态机

21
00:01:02,659 --> 00:01:05,090
而且它的主要架构就是状态机

22
00:01:05,090 --> 00:01:06,079
我们来看一下

23
00:01:06,079 --> 00:01:09,000
这是一个手游的一个外挂

24
00:01:10,140 --> 00:01:12,179
这个外挂呢是什么意思啊

25
00:01:12,359 --> 00:01:15,659
上面有这七个功能啊

26
00:01:15,659 --> 00:01:17,790
你勾选上的功能点开始

27
00:01:17,790 --> 00:01:21,290
然后这个外挂呢就会帮助我们自己去玩这个游戏

28
00:01:21,290 --> 00:01:22,609
然后他会挨着顺序

29
00:01:22,609 --> 00:01:23,870
挨着你勾选的顺序

30
00:01:23,870 --> 00:01:24,899
一个一个去玩

31
00:01:25,019 --> 00:01:29,099
那么这个东西怎样去做合适一些啊

32
00:01:29,099 --> 00:01:31,260
这个东西怎样去做合适一些

33
00:01:31,260 --> 00:01:37,780
那么我在这里呢就用到了咱们的状态机啊

34
00:01:37,780 --> 00:01:39,219
就用到了咱们的状态机

35
00:01:39,219 --> 00:01:42,259
大家可以看一下这里面是不是有非常多的状态

36
00:01:42,259 --> 00:01:46,039
每一个这里面的一个选项啊

37
00:01:46,039 --> 00:01:47,808
这里面的一个任务选项

38
00:01:47,808 --> 00:01:50,539
我在这里都做一个状态来使用的

39
00:01:50,719 --> 00:01:53,959
也就是说我把家园林修当作一个状态

40
00:01:53,959 --> 00:01:55,519
大理寺当成一个状态

41
00:01:55,519 --> 00:01:57,159
竞技场当成一个状态

42
00:01:57,340 --> 00:02:02,709
那么为什么像这个应用我们就可以用这种状态机的思想

43
00:02:02,709 --> 00:02:04,209
因为这个状态机啊

44
00:02:04,209 --> 00:02:05,230
大家注意它

45
00:02:05,230 --> 00:02:07,870
首先第一个特点就是它可以有很多状态

46
00:02:07,870 --> 00:02:10,558
那么我们这个应用符合诶

47
00:02:10,558 --> 00:02:12,718
他有这个这七种状态

48
00:02:12,718 --> 00:02:13,019
对不对

49
00:02:13,019 --> 00:02:15,360
他同他可以做这七七种事

50
00:02:15,399 --> 00:02:19,300
第二个特点就是它的状态同时只能执行一个

51
00:02:19,719 --> 00:02:23,020
就是当时同时只能执行一个状态

52
00:02:23,020 --> 00:02:26,590
就是你比如说主角他不可能边跑

53
00:02:26,590 --> 00:02:32,998
然后诶跑和死亡这两个状态一起执行并行执行

54
00:02:32,998 --> 00:02:33,959
这个是不允许的

55
00:02:33,959 --> 00:02:36,419
所以就是这个主角要不然就是正常状态

56
00:02:36,419 --> 00:02:37,378
要不然就是跑步状态

57
00:02:37,378 --> 00:02:38,459
要不然就是死亡状态

58
00:02:38,459 --> 00:02:40,729
他同一时间只能有一个状态执行

59
00:02:40,729 --> 00:02:43,289
那么这个应用也是一样的

60
00:02:43,289 --> 00:02:45,030
他帮着我们玩游戏啊

61
00:02:45,030 --> 00:02:46,750
他作为一个外挂帮我们玩游戏

62
00:02:46,750 --> 00:02:47,949
他同一时间

63
00:02:47,949 --> 00:02:49,810
要不然你去做家园零星任务

64
00:02:49,810 --> 00:02:51,009
要不然做大理寺任务

65
00:02:51,009 --> 00:02:52,360
要不然做竞技场任务

66
00:02:52,360 --> 00:02:54,729
你不可能把这些任务同时去做的

67
00:02:54,729 --> 00:02:55,319
对不对

68
00:02:55,319 --> 00:02:59,340
所以你同一同一时间只能有一个任务在执行

69
00:02:59,359 --> 00:03:01,609
同一时间只能有一个任务在执行

70
00:03:01,609 --> 00:03:07,598
所以所以在这里我们这个既符合了有多个状态啊

71
00:03:07,598 --> 00:03:10,729
又符合了同一时间只有一个状态执行啊

72
00:03:10,729 --> 00:03:12,889
那么他们就可以用状态机来做了

73
00:03:12,889 --> 00:03:17,169
所以我把它每一个任务都做成了一个状态机啊

74
00:03:17,169 --> 00:03:18,610
然后有状态管理器

75
00:03:18,610 --> 00:03:21,020
你看一下这是不是还是咱们的两个类啊

76
00:03:21,020 --> 00:03:23,180
当然这是用c shop写c shop写的

77
00:03:23,180 --> 00:03:25,639
所以这个语言不一样啊

78
00:03:25,639 --> 00:03:27,379
但是内容是一样的啊

79
00:03:27,379 --> 00:03:31,379
大家其实看一下里面你就可以感觉出来是不是

80
00:03:31,379 --> 00:03:33,840
其实内容基本上都是一样的啊

81
00:03:33,840 --> 00:03:34,860
就是语法不同

82
00:03:36,060 --> 00:03:36,860
那么

83
00:03:38,539 --> 00:03:39,879
我们再看一下啊

84
00:03:39,879 --> 00:03:41,319
当他一开始的时候

85
00:03:41,319 --> 00:03:44,120
他首先只要点一下这个开始按钮

86
00:03:44,120 --> 00:03:45,680
它就开始执行状态机

87
00:03:45,680 --> 00:03:46,759
状态机的话

88
00:03:46,759 --> 00:03:48,740
它在这里用户会点功

89
00:03:48,740 --> 00:03:50,780
用户点哪个哪个就会有勾

90
00:03:50,780 --> 00:03:53,120
那么它就会按顺序去判断状态

91
00:03:53,120 --> 00:03:55,250
如果比如说家园林学有勾

92
00:03:55,250 --> 00:03:58,090
那么我们就进入家园林休状态

93
00:03:58,090 --> 00:04:00,310
当这个任务执行完以后

94
00:04:00,310 --> 00:04:03,849
他就会挨着顺序判断第二个任务有没有勾

95
00:04:03,849 --> 00:04:04,750
如果有勾的话

96
00:04:04,750 --> 00:04:05,949
去执行第二个任务

97
00:04:05,949 --> 00:04:08,060
第二个任务如果没有勾

98
00:04:08,060 --> 00:04:09,560
他直接去判断第三个

99
00:04:09,560 --> 00:04:10,699
直到判断哪一个

100
00:04:10,699 --> 00:04:12,169
比如说帮派任务有勾了

101
00:04:12,169 --> 00:04:16,129
那么它呢就把当前的状态切换成帮派去执行

102
00:04:16,129 --> 00:04:17,209
帮派里面的内容

103
00:04:17,209 --> 00:04:18,350
这样就ok了

104
00:04:18,350 --> 00:04:19,189
我们可以看一下

105
00:04:19,189 --> 00:04:21,560
比如说他的第一个家园林修任务

106
00:04:22,560 --> 00:04:24,860
那么这个是家园林修的状态

107
00:04:25,800 --> 00:04:27,259
那么大家就可以看一下

108
00:04:27,259 --> 00:04:29,060
这是家园林修里面要做的事

109
00:04:29,060 --> 00:04:30,339
这是are update

110
00:04:30,339 --> 00:04:32,199
是不是在里面要做什么事

111
00:04:32,199 --> 00:04:33,579
打开活动窗口啊

112
00:04:33,579 --> 00:04:34,959
啊点哪个按钮啊

113
00:04:34,959 --> 00:04:35,970
怎么去做

114
00:04:35,970 --> 00:04:38,250
然后如果这个将灵修做完了

115
00:04:38,250 --> 00:04:41,730
他判断大理寺如果画上勾切换到大理寺状态

116
00:04:41,730 --> 00:04:43,439
就不执行这里面的内容了

117
00:04:43,439 --> 00:04:46,339
就执行大理寺这个里面的内容了

118
00:04:46,339 --> 00:04:48,050
那这个也是比较简单的啊

119
00:04:48,050 --> 00:04:52,649
也是在这里面去做一些大理寺这个任务要执行的事啊

120
00:04:52,649 --> 00:04:55,290
比如说要怎么打开哪个窗口啊

121
00:04:55,290 --> 00:04:57,519
打开窗口以后是吧

122
00:04:57,519 --> 00:04:59,759
点击什么东西啊等等啊

123
00:05:01,660 --> 00:05:03,899
ok那么在这里啊

124
00:05:03,899 --> 00:05:05,819
呃咱们举个例子啊

125
00:05:05,819 --> 00:05:07,959
其实就是为了让大家明白一下

126
00:05:07,959 --> 00:05:11,819
不光是你写游戏可以用得上状态机

127
00:05:11,819 --> 00:05:14,519
所有满足咱们刚才说的那两个条件

128
00:05:14,519 --> 00:05:15,779
一个就是有多种状态

129
00:05:15,779 --> 00:05:18,000
一个是同一时间只有一个状态执行

130
00:05:18,000 --> 00:05:19,470
那么这种情况下

131
00:05:19,470 --> 00:05:21,000
你不管是游戏也好

132
00:05:21,000 --> 00:05:21,720
软件也好

133
00:05:21,720 --> 00:05:23,490
都是可以使用状态机的

134
00:05:23,490 --> 00:05:26,300
那么这个像这种嗯外挂的话

135
00:05:26,300 --> 00:05:29,269
其实很多反而真正去写外挂的很多人

136
00:05:29,269 --> 00:05:30,620
他反而不会用状态机

137
00:05:30,620 --> 00:05:34,740
因为他编程水平反而到不了这个这个这个程度啊

138
00:05:34,740 --> 00:05:38,529
它就是按照顺序一个一个一个用最简单的代码去写的

139
00:05:38,529 --> 00:05:39,490
大家就会发现

140
00:05:39,490 --> 00:05:41,350
如果你见过他们的这个代码的话

141
00:05:41,350 --> 00:05:43,839
你会发现它的代码就很乱啊

142
00:05:43,839 --> 00:05:45,430
他自己去改的话

143
00:05:45,430 --> 00:05:46,899
就是出现一个问题

144
00:05:46,899 --> 00:05:47,620
他要去修改

145
00:05:47,620 --> 00:05:50,319
他自己都得找半天改半天啊

146
00:05:50,319 --> 00:05:54,139
所以说如果啊写个外挂用状态机来写啊

147
00:05:54,139 --> 00:05:56,509
这个就是非常非常非常好用的啊

148
00:05:56,509 --> 00:05:58,160
这就是再举个例子

149
00:05:58,160 --> 00:06:01,110
让大家对这个状态机更加有所了解

150
00:06:01,110 --> 00:06:06,339
那接下来呃之前咱们这里还有一套课程是unity的课程啊

151
00:06:06,339 --> 00:06:09,259
也就是另外一套3d游戏引擎的课程

152
00:06:09,259 --> 00:06:11,600
这个课程里面最后给大家呃

153
00:06:11,600 --> 00:06:14,579
其实给unity的同学扩展了一个

154
00:06:14,579 --> 00:06:16,220
扩展了两个内容

155
00:06:16,560 --> 00:06:20,060
那么主要就是扩展了一个python啊

156
00:06:20,060 --> 00:06:22,639
还有一个就是白鹭游戏情

157
00:06:22,639 --> 00:06:25,819
那白鹭游戏情用的也是tap script啊

158
00:06:25,819 --> 00:06:30,928
那所以这个白露跟他cos creator其实很相似啊

159
00:06:30,928 --> 00:06:32,069
所以在这里

160
00:06:32,069 --> 00:06:37,490
然后我我就把这个unity这边的这几节课啊也传上来

161
00:06:37,490 --> 00:06:39,110
然后大家如果有兴趣的话

162
00:06:39,110 --> 00:06:40,220
也可以学习一下

163
00:06:40,220 --> 00:06:41,089
那python的话

164
00:06:41,089 --> 00:06:43,300
我是推荐大家嗯

165
00:06:43,339 --> 00:06:46,189
最起码把python这个语法给好好学一下

166
00:06:46,189 --> 00:06:47,899
因为python现在非常火啊

167
00:06:47,899 --> 00:06:48,470
非常火

168
00:06:48,470 --> 00:06:50,660
你出去你要说我不光会有心情

169
00:06:50,660 --> 00:06:52,350
我还会python的话啊

170
00:06:52,350 --> 00:06:53,980
这个是有加分的

171
00:06:54,579 --> 00:06:56,459
当然那个白鹭游戏引擎的话

172
00:06:56,459 --> 00:06:58,168
这个就看大家兴趣了啊

173
00:06:58,168 --> 00:07:01,399
ok那么我们这节课就这么多

174
00:07:08,978 --> 00:07:09,720
略略略


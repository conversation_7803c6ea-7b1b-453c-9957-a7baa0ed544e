1
00:00:09,039 --> 00:00:14,099
ok我们这节课来说一下呃脚本的生命周期啊

2
00:00:14,099 --> 00:00:15,538
什么叫生命周期呢

3
00:00:15,538 --> 00:00:16,798
就是这个脚本里面啊

4
00:00:16,798 --> 00:00:18,120
我们把它打开

5
00:00:25,559 --> 00:00:29,000
这个脚本里面大家发现除了我们之前说的

6
00:00:29,000 --> 00:00:30,199
比如说属性啊

7
00:00:30,199 --> 00:00:32,359
以及其他东西之外啊

8
00:00:32,359 --> 00:00:33,829
我们之前说的啊

9
00:00:33,829 --> 00:00:34,850
千万不要忘了啊

10
00:00:34,850 --> 00:00:37,459
就最起码简单的就是创建完以后

11
00:00:37,459 --> 00:00:39,018
前面这一片不用管它

12
00:00:39,018 --> 00:00:43,219
从class后面第一步把这个类名最好改成和文件名一样的

13
00:00:43,219 --> 00:00:44,359
然后你需要什么属性

14
00:00:44,359 --> 00:00:45,719
加什么属性就行了

15
00:00:45,719 --> 00:00:47,399
需要在面板上显示

16
00:00:47,399 --> 00:00:49,210
就加at the property啊

17
00:00:49,210 --> 00:00:50,350
基本类型就ok了

18
00:00:50,350 --> 00:00:52,090
如果不是基本类型

19
00:00:52,090 --> 00:00:55,359
你就要把类名用个括号给它括起来

20
00:00:56,240 --> 00:00:58,149
那么除了这些东西以外

21
00:00:58,149 --> 00:01:00,280
它的下面就是生命周期了

22
00:01:00,280 --> 00:01:03,079
所谓生命周期就是一堆函数啊

23
00:01:03,079 --> 00:01:08,299
这样的这些函数呢是默认这个类就存在的

24
00:01:08,299 --> 00:01:12,629
而且这个函数这些函数是不需要我们调用的

25
00:01:12,629 --> 00:01:14,969
就说这些函数是它自己会调用的

26
00:01:14,969 --> 00:01:16,290
所以叫生命周期啊

27
00:01:16,290 --> 00:01:19,859
你把它想成一个他自己就会活着的一个类啊

28
00:01:19,859 --> 00:01:22,109
它的这些函数是不需要我们调用的

29
00:01:22,109 --> 00:01:23,310
它自己调用的

30
00:01:23,310 --> 00:01:26,239
我们我们只需要在它里面写东西

31
00:01:26,239 --> 00:01:30,560
然后他就会在合适的时候去帮我们调这些代码就ok了

32
00:01:30,560 --> 00:01:34,189
那么我们来看一下它有哪些啊

33
00:01:34,189 --> 00:01:35,679
生命周期函数

34
00:01:36,459 --> 00:01:40,399
unload start

35
00:01:41,780 --> 00:01:43,480
to update

36
00:01:45,400 --> 00:01:49,390
然后还有个late update啊

37
00:01:49,390 --> 00:01:50,739
late update

38
00:01:50,739 --> 00:01:51,939
然后

39
00:01:55,439 --> 00:01:59,019
lt update后面有个dundisable

40
00:02:00,799 --> 00:02:03,379
有个on destiny

41
00:02:05,599 --> 00:02:09,379
然后在这中间有个on enable

42
00:02:14,759 --> 00:02:17,900
ok那么我们来看一下啊

43
00:02:17,900 --> 00:02:20,379
我们来看一下一个一个来看

44
00:02:21,099 --> 00:02:24,300
首先这两个on enable on dable

45
00:02:24,300 --> 00:02:25,259
大家刚才写完以后

46
00:02:25,259 --> 00:02:27,620
大家发现这个明显就是一组

47
00:02:27,899 --> 00:02:28,739
对不对

48
00:02:28,739 --> 00:02:31,199
这个明显就是一组ra不on d c b

49
00:02:31,199 --> 00:02:34,139
所以这一组我们单独拿出来说啊

50
00:02:34,139 --> 00:02:36,120
我们单独拿出来说呃

51
00:02:36,120 --> 00:02:39,870
我们先从头把除了他们两个的啊

52
00:02:39,870 --> 00:02:40,560
先说了

53
00:02:40,560 --> 00:02:42,459
首先第一个叫unload

54
00:02:42,459 --> 00:02:44,859
这个是当脚本啊

55
00:02:44,859 --> 00:02:50,799
只要这个脚本这个这个这个创建就是这个脚本所在的对象创建出来

56
00:02:50,799 --> 00:02:52,359
这个脚本被激活了

57
00:02:52,359 --> 00:02:54,879
那么他就会被第一个调用

58
00:02:56,539 --> 00:02:59,740
所以算是初始化调用

59
00:03:00,498 --> 00:03:03,579
那么这个是这个方法啊

60
00:03:03,579 --> 00:03:05,079
是最开始会调用的

61
00:03:05,079 --> 00:03:08,650
所以我们可以在它里面做一些初始化的操作啊

62
00:03:08,650 --> 00:03:10,240
做一些初始化的操作

63
00:03:10,240 --> 00:03:13,658
那么同时只要这个方法完了

64
00:03:13,658 --> 00:03:19,088
我们第二个start方法也会跟着进行一个初始化调用

65
00:03:19,088 --> 00:03:22,840
也就是说他们俩他们俩其实作用一样的

66
00:03:22,840 --> 00:03:25,569
都是当只要这个脚本啊

67
00:03:25,569 --> 00:03:28,139
你看这个脚本挂在了啊

68
00:03:28,139 --> 00:03:31,080
比如说挂在了我哪一个物体上面

69
00:03:31,080 --> 00:03:31,620
对不对啊

70
00:03:31,620 --> 00:03:32,759
现在哪个都没挂

71
00:03:32,759 --> 00:03:34,020
比如说我有一个节点

72
00:03:34,020 --> 00:03:35,840
我挂在了test

73
00:03:36,259 --> 00:03:38,629
那么只要这个游戏运行

74
00:03:38,629 --> 00:03:41,900
这个节点被创建出来以后啊

75
00:03:41,900 --> 00:03:45,519
那么unload瞬间被执行啊

76
00:03:45,519 --> 00:03:46,778
他是第一个被执行的

77
00:03:46,778 --> 00:03:48,500
然后start在被执行

78
00:03:48,500 --> 00:03:50,389
那么他俩都是做初始化的

79
00:03:50,389 --> 00:03:53,368
那为什么把初始化分成两个了啊

80
00:03:53,368 --> 00:03:55,468
为什么把初始化分成两个函数了

81
00:03:55,468 --> 00:03:56,800
那么其实是这样的

82
00:03:58,679 --> 00:04:01,639
比如说我们有两个类

83
00:04:01,639 --> 00:04:03,199
一个类是子弹类

84
00:04:03,199 --> 00:04:05,139
一个类是枪类

85
00:04:05,560 --> 00:04:08,560
在枪的枪的脚本里面

86
00:04:08,759 --> 00:04:10,379
枪里面有初始化

87
00:04:10,379 --> 00:04:10,860
对不对

88
00:04:10,860 --> 00:04:14,210
它的初始化是需要用到子弹的

89
00:04:14,210 --> 00:04:16,009
它是需要用到子弹的

90
00:04:16,009 --> 00:04:21,668
所以这时候我们就要保证子弹的初始化在枪怎样在枪之前

91
00:04:21,668 --> 00:04:24,848
所以虽然比如说我们可能会写很多类啊

92
00:04:24,848 --> 00:04:32,158
但是有时候我们是需要确保某个类的初始化一定要在另外哪个类之前

93
00:04:32,158 --> 00:04:35,139
就是他这个初始化也是可以有先后顺序的

94
00:04:35,399 --> 00:04:37,639
那这时候怎么办

95
00:04:37,639 --> 00:04:41,269
哎我们就可以用利用这两个初始化函数了

96
00:04:41,269 --> 00:04:44,720
那么首先我们先说一下它的这个调用方法啊

97
00:04:44,720 --> 00:04:46,660
比如说我们有十个脚本

98
00:04:46,680 --> 00:04:47,879
十个脚本

99
00:04:47,879 --> 00:04:52,199
那么它并不是说把一个脚本的onload start执行完了再执行

100
00:04:52,199 --> 00:04:52,860
第二个

101
00:04:52,860 --> 00:04:55,339
他是执行完第一个脚本的unload

102
00:04:55,379 --> 00:04:57,420
然后再执行第二个脚本unload

103
00:04:57,420 --> 00:04:58,860
然后执行到第十个脚本

104
00:04:58,860 --> 00:05:00,300
unload都执行完了

105
00:05:00,300 --> 00:05:02,069
再执行第一个脚本的start

106
00:05:02,069 --> 00:05:03,699
它是这样去执行的

107
00:05:03,699 --> 00:05:05,259
所以这样的话我就可以怎么样

108
00:05:05,259 --> 00:05:07,120
比如说子弹子弹的初始化

109
00:05:07,120 --> 00:05:08,740
我就可以放在onload里面

110
00:05:08,740 --> 00:05:11,540
然后如果我们做那个枪枪的初始化

111
00:05:11,540 --> 00:05:12,860
我就放在start里面

112
00:05:12,860 --> 00:05:16,009
那么这时候只要枪在调初始化的时候

113
00:05:16,009 --> 00:05:20,000
我们是不是肯定能保证这个子弹已经初始化完成了啊

114
00:05:20,000 --> 00:05:21,860
所以就是为了防止这种情况发生

115
00:05:21,860 --> 00:05:25,480
我们这里有两个方法跟我们初始化啊

116
00:05:25,480 --> 00:05:28,259
如果我们比如说当前初始化无所谓啊

117
00:05:28,259 --> 00:05:29,399
在哪里初始化都行

118
00:05:29,399 --> 00:05:32,670
那么默认情况下大家发现它unload是被注释起来的

119
00:05:32,670 --> 00:05:35,269
那我们就在start里面直接去写就好了

120
00:05:35,269 --> 00:05:38,449
除非我们就要就是要确定哪个初始化

121
00:05:38,449 --> 00:05:41,500
一定要在最开始他就被初始化

122
00:05:41,500 --> 00:05:44,079
那么你就把它写在unload里面就可以了

123
00:05:45,220 --> 00:05:46,519
嗯ok啊

124
00:05:47,279 --> 00:05:56,199
那么接下来我们看这个update update是每帧每一帧都会调用的啊

125
00:05:56,199 --> 00:05:57,639
每一帧都会调用的

126
00:05:57,639 --> 00:06:01,779
我们知道我们这个游戏其实就是运行起来就是一帧一帧的

127
00:06:01,779 --> 00:06:02,860
这样和动画片一样的

128
00:06:02,860 --> 00:06:03,430
对不对

129
00:06:03,430 --> 00:06:05,800
那么update就是每一帧会掉一次啊

130
00:06:05,800 --> 00:06:08,560
每次刷新刷新一次掉一次

131
00:06:08,560 --> 00:06:11,490
那么这里面有个参数是dt dt呢

132
00:06:11,490 --> 00:06:15,300
其实就代表每每一帧他的一个执行时间

133
00:06:15,300 --> 00:06:18,860
说白了就是从上一帧到这帧中间有多长时间啊

134
00:06:18,860 --> 00:06:21,350
就是它啊它代表了一个间隔时间

135
00:06:21,350 --> 00:06:23,720
那么每帧调用完了以后啊

136
00:06:23,720 --> 00:06:25,370
这个同样有个lt update

137
00:06:25,370 --> 00:06:28,069
它是在每帧调用完之后紧跟着会执行啊

138
00:06:28,069 --> 00:06:32,670
就是每帧呃刷新完之后他跟着执行一次啊

139
00:06:32,670 --> 00:06:36,160
就是呃比如说每一帧我们要做一些事

140
00:06:36,160 --> 00:06:39,579
做完以后可能这一帧完成之后还要做一些什么收尾啊

141
00:06:39,579 --> 00:06:41,149
或者其他工作啊

142
00:06:41,149 --> 00:06:44,529
我可以在late update里面去做啊

143
00:06:44,529 --> 00:06:46,240
当然我们可能在写代码的时候

144
00:06:46,240 --> 00:06:48,879
大部分情况下用的都是这个update啊

145
00:06:48,879 --> 00:06:50,259
所以有这样一个东西啊

146
00:06:50,259 --> 00:06:51,519
大家知道就ok

147
00:06:51,519 --> 00:06:54,399
那么我们用到的时候再说啊

148
00:06:54,538 --> 00:06:57,509
再往下就是这个on mystery

149
00:06:57,509 --> 00:07:00,119
我们知道每个对象都是可以被销毁的

150
00:07:00,119 --> 00:07:02,560
这个就是如果销毁

151
00:07:03,540 --> 00:07:08,660
如果我当前比如说我这个test这个这个这个这个组件啊

152
00:07:08,660 --> 00:07:10,800
他要被remove了

153
00:07:10,800 --> 00:07:11,759
也就是被销毁了

154
00:07:11,759 --> 00:07:13,470
或者他所在的这个节点

155
00:07:13,470 --> 00:07:17,149
整个这个new node要被我们删除了啊

156
00:07:17,149 --> 00:07:18,500
当然是在运行过程中

157
00:07:18,500 --> 00:07:20,449
如果运行运行过程当中

158
00:07:20,449 --> 00:07:22,439
这个节点被我们删除掉

159
00:07:22,538 --> 00:07:24,608
那么它就会调用这个方法

160
00:07:24,608 --> 00:07:27,399
那比如说有些物体删除掉以后

161
00:07:27,399 --> 00:07:28,899
我要恢复一些

162
00:07:29,579 --> 00:07:31,278
恢复一些数值啊

163
00:07:31,278 --> 00:07:34,098
那么你就把恢复数值的代码写到这个里面啊

164
00:07:34,098 --> 00:07:36,230
就总之就是如果要销毁它了

165
00:07:36,230 --> 00:07:38,329
那么它就会先掉这个方法啊

166
00:07:38,329 --> 00:07:41,579
我们在这做一些销毁前的一些操作啊

167
00:07:41,579 --> 00:07:45,389
操作完了以后他自己就会销毁了啊

168
00:07:45,389 --> 00:07:49,759
那这就是他的这几个生命周期啊

169
00:07:49,759 --> 00:07:53,720
我们可以在这里输出一下啊

170
00:07:53,720 --> 00:07:54,860
我们可以输出一下

171
00:07:54,860 --> 00:07:56,519
我们来看一下这个效果

172
00:07:57,839 --> 00:08:01,939
那么在这里我们输出就不能用我们之前的那个document.right了

173
00:08:01,939 --> 00:08:04,699
我们就要用console.debug啊

174
00:08:04,699 --> 00:08:06,230
我们就要用这个输出了

175
00:08:06,230 --> 00:08:07,300
那么

176
00:08:10,160 --> 00:08:14,319
在这个里面我们就可以输出任何类型的这样的一个内容了

177
00:08:14,319 --> 00:08:18,329
我们比如说在这就输一个unload啊

178
00:08:18,329 --> 00:08:21,209
然后在这个这两个还没说

179
00:08:21,209 --> 00:08:21,600
对不对

180
00:08:21,600 --> 00:08:23,000
我们先不写他

181
00:08:23,819 --> 00:08:27,718
然后再start这两个我们就不写了

182
00:08:27,718 --> 00:08:30,119
因为一写的话它会一直调用啊

183
00:08:30,119 --> 00:08:33,298
每一帧调用一次打小他就一直打印太多东西了啊

184
00:08:33,298 --> 00:08:35,479
然后这个up

185
00:08:42,960 --> 00:08:45,960
那么我们来运行一下

186
00:08:45,960 --> 00:08:50,899
看一下这这三个会不会按我们正常的这样的一个顺序去执行

187
00:08:50,899 --> 00:08:54,289
首先我现在如果用的是模拟器的话

188
00:08:54,289 --> 00:08:55,500
运行

189
00:08:57,860 --> 00:09:00,220
那么这边会产生一个模拟器

190
00:09:00,220 --> 00:09:02,259
然后我们的输出大家可以看

191
00:09:02,259 --> 00:09:04,360
在这里就有了有个unload

192
00:09:04,360 --> 00:09:05,850
有个start

193
00:09:05,850 --> 00:09:07,110
有没有啊

194
00:09:07,110 --> 00:09:08,429
这个就是模拟器啊

195
00:09:08,429 --> 00:09:14,259
模拟器我们可以在上面右键隐藏f p s或者显示fps

196
00:09:14,259 --> 00:09:17,938
或者这个做一些其他操作啊

197
00:09:18,080 --> 00:09:19,970
还有最大化最小化是吧

198
00:09:19,970 --> 00:09:21,230
就是在上面去做

199
00:09:21,230 --> 00:09:26,600
你看你可以选择当前是用呃什么什么类型的模拟器

200
00:09:26,600 --> 00:09:31,200
横屏还是竖屏还是缩放是怎样的

201
00:09:31,200 --> 00:09:32,159
对吧啊

202
00:09:32,159 --> 00:09:33,639
这个你都可以去做

203
00:09:35,940 --> 00:09:37,059
然后

204
00:09:39,279 --> 00:09:40,379
输出啊

205
00:09:40,379 --> 00:09:43,590
输出是在刚才咱们看到的这个位置啊

206
00:09:43,590 --> 00:09:44,759
我我现在关了

207
00:09:44,759 --> 00:09:46,379
我不应该关了啊

208
00:09:46,379 --> 00:09:47,100
我们在运行

209
00:09:47,100 --> 00:09:48,960
大家可以看这两个debug是不是就出来了

210
00:09:48,960 --> 00:09:49,379
unload

211
00:09:49,379 --> 00:09:50,129
一个start

212
00:09:50,129 --> 00:09:54,019
这时候如果我们的new node被删除了啊

213
00:09:54,019 --> 00:09:55,460
就是在游戏运行的时候

214
00:09:55,460 --> 00:09:58,059
比如说就被删除了

215
00:09:58,100 --> 00:10:00,320
那么我这里是手动删除啊

216
00:10:00,320 --> 00:10:03,779
你在游戏里面需要用用代码的方式删除才可以

217
00:10:03,779 --> 00:10:05,639
如果我们用代码方式把它删除了

218
00:10:05,639 --> 00:10:08,590
那么这里就会输出下一个on history啊

219
00:10:08,590 --> 00:10:10,299
ok我们知道这些就行了

220
00:10:10,299 --> 00:10:12,700
那么这是我们要知道的几个

221
00:10:12,700 --> 00:10:16,350
那么如果

222
00:10:16,350 --> 00:10:17,879
那么接下来啊

223
00:10:17,879 --> 00:10:19,169
还有这两个

224
00:10:19,169 --> 00:10:21,779
这两个这两个分别是什么时候调用

225
00:10:21,779 --> 00:10:24,740
首先on enable在最开始也会调用一次

226
00:10:26,940 --> 00:10:29,340
而且他就在onload和start中间

227
00:10:29,340 --> 00:10:32,779
所以我放在这个位置就on enable

228
00:10:34,299 --> 00:10:36,279
我们先看一下是不是啊

229
00:10:37,980 --> 00:10:40,559
我们到这边运行一下

230
00:10:40,559 --> 00:10:41,970
看一下它的输出

231
00:10:41,970 --> 00:10:44,080
ctrl shift加诶

232
00:10:44,080 --> 00:10:45,429
我们发现没有打印

233
00:10:45,429 --> 00:10:46,809
那么这里注意啊

234
00:10:46,809 --> 00:10:49,720
我们选择这个如果没打印的话

235
00:10:49,720 --> 00:10:51,210
大家可以看一下层级

236
00:10:51,210 --> 00:10:54,210
他这边可能输出的信息并不完整啊

237
00:10:54,210 --> 00:10:56,580
我们把这个没有勾选的给它勾选上

238
00:10:56,580 --> 00:10:58,779
大家就发现诶打印出来了

239
00:10:58,779 --> 00:11:01,600
那大家发现我们的脚本打印了个upload

240
00:11:01,600 --> 00:11:02,559
打印了个ua吧

241
00:11:02,559 --> 00:11:03,309
打印了个start

242
00:11:03,309 --> 00:11:04,899
所以他们的顺序是没错的

243
00:11:04,899 --> 00:11:05,779
对不对

244
00:11:05,860 --> 00:11:10,779
但是这里注意able跟他俩最大的区别就是它会多次执行

245
00:11:10,840 --> 00:11:13,120
他跟on disable是一对

246
00:11:13,120 --> 00:11:18,340
on disable呢他就是当这个当那个组件啊

247
00:11:18,340 --> 00:11:25,110
就当那个脚本如果我要无效的失效的情况下啊

248
00:11:25,110 --> 00:11:26,190
就是禁用的情况下

249
00:11:26,190 --> 00:11:27,269
我会调用

250
00:11:27,269 --> 00:11:30,690
启用的情况下会调用on enable啊

251
00:11:30,690 --> 00:11:32,190
准确来说大家可以看一下啊

252
00:11:32,190 --> 00:11:36,719
我往on disable里面也写个on第四able

253
00:11:38,000 --> 00:11:39,379
那么两个都会输出

254
00:11:39,379 --> 00:11:39,950
对不对

255
00:11:39,950 --> 00:11:42,230
那这时候我重新运行一下

256
00:11:42,230 --> 00:11:43,789
我重新运行一下

257
00:11:43,789 --> 00:11:45,318
ctrl shift加i

258
00:11:45,860 --> 00:11:47,240
那么大家可以看啊

259
00:11:48,259 --> 00:11:49,460
on运行了

260
00:11:49,460 --> 00:11:50,120
对不对

261
00:11:50,120 --> 00:11:53,769
那我在这里选中这个组件

262
00:11:53,769 --> 00:11:55,269
我把他这个勾去掉

263
00:11:55,269 --> 00:11:56,110
勾去掉

264
00:11:56,110 --> 00:11:57,580
其实就是把它禁用了

265
00:11:57,580 --> 00:12:00,879
这时候其实如果我们用代码去写啊

266
00:12:00,879 --> 00:12:03,279
那这里面undisable就会执行

267
00:12:03,279 --> 00:12:06,360
如果我把它勾上

268
00:12:06,360 --> 00:12:08,779
然后我们再看这边

269
00:12:09,340 --> 00:12:10,830
然后我们再看这边

270
00:12:10,830 --> 00:12:13,080
on enable就是会执行的啊

271
00:12:13,080 --> 00:12:14,820
所以实际上它是会多次执行的

272
00:12:14,820 --> 00:12:17,710
就是你这个只要默认情况下是勾上的

273
00:12:17,710 --> 00:12:20,230
所以它默认会执行一次unable啊

274
00:12:20,230 --> 00:12:22,049
因为它默认已经是启用的

275
00:12:22,049 --> 00:12:24,210
当我们每一次这个状态改变的时候

276
00:12:24,210 --> 00:12:27,639
比如说勾取了on the undisable就可以执行

277
00:12:27,639 --> 00:12:30,519
再勾上unable就会执行on diable

278
00:12:30,519 --> 00:12:31,960
执行able执行

279
00:12:31,960 --> 00:12:33,279
同样的

280
00:12:33,419 --> 00:12:36,190
因为它是属于这个最大的这个组件的

281
00:12:36,190 --> 00:12:38,830
如果整个这个节点被禁用

282
00:12:38,830 --> 00:12:41,049
那么它也相当于被禁用啊

283
00:12:41,049 --> 00:12:42,610
它也会掉on dc过啊

284
00:12:42,610 --> 00:12:45,259
勾上以后调on a不是这样的啊

285
00:12:45,259 --> 00:12:47,359
所以一定要注意这样的一点啊

286
00:12:47,359 --> 00:12:50,399
所以一定要注意这一点啊

287
00:12:52,200 --> 00:12:56,220
那么这个就是unenable和undisable的这个作用啊

288
00:12:56,220 --> 00:13:01,059
目前而言这两个可能我们用不太上啊

289
00:13:01,059 --> 00:13:02,230
目前而言用不太上

290
00:13:02,230 --> 00:13:03,759
等我们用用上的时候

291
00:13:03,759 --> 00:13:05,698
然后再详细去看他

292
00:13:05,899 --> 00:13:07,820
那么这就是整体而言

293
00:13:07,820 --> 00:13:10,190
这个就是我们的生命周期函数

294
00:13:10,190 --> 00:13:14,240
大家要知道它们有哪些顺序是什么

295
00:13:14,240 --> 00:13:16,399
分别是什么时候调用的啊

296
00:13:16,399 --> 00:13:17,419
就可以了啊

297
00:13:17,419 --> 00:13:18,110
就可以了

298
00:13:18,110 --> 00:13:19,938
我们的要求就完成了

299
00:13:21,200 --> 00:13:26,539
那么我们这节课就这么多内容啊


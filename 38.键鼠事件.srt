1
00:00:09,480 --> 00:00:16,750
ok这节课我们来讲一下我们这个cos creator怎样去监听这个键鼠操作的

2
00:00:16,750 --> 00:00:17,079
啊

3
00:00:17,079 --> 00:00:18,019
键鼠操作

4
00:00:18,019 --> 00:00:22,920
那么在这里我们比如说随便放来放上来一个内容

5
00:00:23,339 --> 00:00:23,850
呃

6
00:00:23,850 --> 00:00:31,339
比如说比如放一个图片吧啊那么放上来这个图片了啊

7
00:00:31,339 --> 00:00:37,219
那么我们就在这个图片上去监听一下我们的键鼠操作

8
00:00:37,219 --> 00:00:38,359
那一个一个来说

9
00:00:38,359 --> 00:00:40,280
首先我们先说鼠标啊

10
00:00:40,280 --> 00:00:41,359
我们先来说鼠标

11
00:00:41,359 --> 00:00:43,439
鼠标怎样去监听这个操作

12
00:00:43,439 --> 00:00:46,320
那么首先我们选择在这个精灵上面

13
00:00:46,320 --> 00:00:48,918
在这个节点上面去进行一个监听

14
00:00:48,920 --> 00:00:53,359
那么所以我们就要在它的上面去添加一个脚本

15
00:00:53,359 --> 00:00:55,418
然后打开我们的脚本

16
00:00:58,020 --> 00:01:00,020
那么在脚本这边啊

17
00:01:00,020 --> 00:01:02,920
我们写什么内容呃

18
00:01:02,920 --> 00:01:04,719
监听鼠标事件啊

19
00:01:04,719 --> 00:01:05,459
其实

20
00:01:07,340 --> 00:01:09,700
比较简单其实是比较简单的

21
00:01:09,700 --> 00:01:11,859
我们在这个在star的方法里面

22
00:01:11,859 --> 00:01:15,420
我们直接去写node啊

23
00:01:15,420 --> 00:01:18,310
this.node.on

24
00:01:18,310 --> 00:01:21,280
那么就开始进行了一个监听啊

25
00:01:21,280 --> 00:01:23,329
就开开始进行一个监听

26
00:01:23,329 --> 00:01:25,069
那么在右边啊

27
00:01:25,069 --> 00:01:25,909
在括号里面

28
00:01:25,909 --> 00:01:26,750
在参数部分

29
00:01:26,750 --> 00:01:29,590
其实我们最重要的就是两个内容

30
00:01:29,590 --> 00:01:33,189
第一个参数就是你监听的一个事件名称

31
00:01:33,189 --> 00:01:34,679
它是个字符串

32
00:01:34,819 --> 00:01:41,310
第二个是个function function呢传来一个参数是事件参数

33
00:01:41,310 --> 00:01:45,219
那么只要你监听的这个内容被调用了啊

34
00:01:45,219 --> 00:01:46,359
这就是监听的内容

35
00:01:46,359 --> 00:01:47,680
比如说是鼠标点击啊

36
00:01:47,680 --> 00:01:49,239
还是什么被调用了

37
00:01:49,239 --> 00:01:51,819
这个函数就会被执行啊

38
00:01:51,819 --> 00:01:53,230
这个函数就会被执行

39
00:01:53,230 --> 00:01:56,879
那么这里如果让我们去填写英文的话

40
00:01:56,879 --> 00:02:00,468
可能比较呃麻烦大家也可能常常出错

41
00:02:00,468 --> 00:02:02,709
比如说most done啊

42
00:02:02,709 --> 00:02:04,689
其实就对应鼠标的按下方法

43
00:02:04,689 --> 00:02:06,159
但是大家还得记对不对

44
00:02:06,159 --> 00:02:08,610
所以它在这里给了我们一个枚举

45
00:02:08,610 --> 00:02:15,169
cc.node点问他tap啊

46
00:02:15,169 --> 00:02:15,979
invent type

47
00:02:15,979 --> 00:02:18,560
这就是事件的一个类型点

48
00:02:18,560 --> 00:02:19,699
这里面有很多

49
00:02:19,699 --> 00:02:21,020
我们选择mouse

50
00:02:21,020 --> 00:02:22,500
mouse开头的

51
00:02:22,939 --> 00:02:26,750
那么大家就可以看到关于鼠标相关的事件

52
00:02:26,750 --> 00:02:28,289
一共有六个

53
00:02:28,289 --> 00:02:29,909
我们先试一个

54
00:02:29,909 --> 00:02:33,240
当鼠标按下的时候触发的这个事件

55
00:02:33,319 --> 00:02:39,080
我们在这里可以console.debug

56
00:02:40,118 --> 00:02:42,539
鼠标按下了

57
00:02:43,740 --> 00:02:45,199
我们来试一下啊

58
00:02:45,199 --> 00:02:46,780
看看它能不能触发

59
00:02:49,539 --> 00:02:51,419
嗯我们在这个位置

60
00:02:51,419 --> 00:02:54,449
当然我们需要打开调试台

61
00:02:54,449 --> 00:02:56,460
在这里按按按这里

62
00:02:56,460 --> 00:02:57,120
大家注意啊

63
00:02:57,120 --> 00:02:58,439
如果同样的内容

64
00:02:58,439 --> 00:03:02,569
它在前面就是会增加这个数字四五

65
00:03:02,569 --> 00:03:04,280
当我们鼠标出去的时候

66
00:03:04,280 --> 00:03:05,479
你发现啊就不管用了

67
00:03:05,479 --> 00:03:06,810
就必须在这个之内

68
00:03:06,810 --> 00:03:09,150
如果你希望全屏都有用

69
00:03:09,150 --> 00:03:12,319
你可以把脚本加到我们的这个canvas上面

70
00:03:14,020 --> 00:03:15,419
加在这个canvas上面

71
00:03:15,419 --> 00:03:17,759
因为canvas就是刚好是我们这个全屏

72
00:03:17,759 --> 00:03:21,879
那么在这里鼠标按下雨用了

73
00:03:21,879 --> 00:03:24,370
但是我现在并不知道鼠标的位置

74
00:03:24,370 --> 00:03:27,219
我现在有可能希望知道我鼠标的一个位置

75
00:03:27,219 --> 00:03:28,039
对不对

76
00:03:28,199 --> 00:03:29,520
能不能知道

77
00:03:29,520 --> 00:03:32,819
那这时候我们就需要用到这个事件了

78
00:03:33,000 --> 00:03:35,310
它里面其实有很多东西啊

79
00:03:35,310 --> 00:03:37,439
比如鼠标按下了位置

80
00:03:37,979 --> 00:03:39,080
加上一个

81
00:03:40,340 --> 00:03:43,120
它里面就有一个get location

82
00:03:43,338 --> 00:03:46,460
get event点

83
00:03:48,080 --> 00:03:50,650
我们这边他是啊

84
00:03:50,650 --> 00:03:52,780
他这边因为是任意类型

85
00:03:52,780 --> 00:03:54,039
所以它不提示了啊

86
00:03:54,039 --> 00:03:55,919
这就是说如果你不写类型

87
00:03:55,919 --> 00:03:57,180
他判断不了类型

88
00:03:57,180 --> 00:03:58,740
他代码也没法给你提示

89
00:03:58,740 --> 00:03:59,819
如果没法给你提示

90
00:03:59,819 --> 00:04:01,500
你只能自己去输点

91
00:04:01,500 --> 00:04:05,218
get location啊

92
00:04:05,218 --> 00:04:07,240
你只能这样去写了啊

93
00:04:07,240 --> 00:04:09,159
那么当然你也能get location

94
00:04:09,159 --> 00:04:11,020
就是只获取到其中一个

95
00:04:11,020 --> 00:04:16,250
比如说是x或者是y那都是可以的啊

96
00:04:16,250 --> 00:04:17,300
都是可以的

97
00:04:17,300 --> 00:04:18,740
那么在这里

98
00:04:22,759 --> 00:04:23,858
我们来试一下

99
00:04:23,858 --> 00:04:26,879
我们先来运行一下诶

100
00:04:26,879 --> 00:04:27,600
没保存吧

101
00:04:28,740 --> 00:04:29,800
保存一下

102
00:04:31,939 --> 00:04:36,939
点一下ctrl shift加i i i

103
00:04:36,939 --> 00:04:38,560
这个有时候不太管用啊

104
00:04:38,560 --> 00:04:39,500
像我这

105
00:04:44,060 --> 00:04:46,860
那这时候我们按下大家就会注意

106
00:04:47,300 --> 00:04:48,860
我每次一个地儿

107
00:04:48,860 --> 00:04:50,540
大家可以看诶

108
00:04:50,540 --> 00:04:54,120
按到这里位置是哪里是吧

109
00:04:54,120 --> 00:04:55,600
是24啊

110
00:04:55,600 --> 00:04:58,660
这里300多明显就是不同的位置啊

111
00:04:58,660 --> 00:05:00,430
不同的坐标是不是就打出来了

112
00:05:00,430 --> 00:05:06,670
那这时候我们就可以通过这个属性来得到我们的鼠标的一个位置啊

113
00:05:06,670 --> 00:05:08,279
来得到鼠标的位置

114
00:05:09,180 --> 00:05:11,319
那么继续

115
00:05:14,339 --> 00:05:15,759
返回来

116
00:05:15,839 --> 00:05:17,759
那么这只是鼠标按下

117
00:05:17,759 --> 00:05:21,540
其实鼠标它的这个事件是蛮多的啊

118
00:05:21,540 --> 00:05:23,250
其实它的事件是蛮多的

119
00:05:23,250 --> 00:05:25,100
那么我们在这里

120
00:05:28,399 --> 00:05:29,410
most done

121
00:05:29,410 --> 00:05:31,269
我们一个一个说一下

122
00:05:31,269 --> 00:05:32,560
有个most done

123
00:05:32,560 --> 00:05:35,040
还有个mouse enter enter

124
00:05:35,040 --> 00:05:38,939
其实跟这个后面这两个都是一组方法啊

125
00:05:38,939 --> 00:05:41,279
他这三个算是一组方法

126
00:05:41,379 --> 00:05:48,899
但首先这个啊第一个第一个是当鼠标进入目标区域的时候啊

127
00:05:48,899 --> 00:05:50,279
进入区域的时候

128
00:05:50,279 --> 00:05:51,560
你可以试一下

129
00:05:53,740 --> 00:05:54,930
什么时候出发

130
00:05:54,930 --> 00:05:58,740
当我鼠标从外面不点击啊

131
00:05:58,740 --> 00:06:01,139
我一进来只要你看一进来

132
00:06:01,139 --> 00:06:04,279
一进来的瞬间它就会调用啊

133
00:06:04,279 --> 00:06:06,170
一进来的瞬间就会调用

134
00:06:06,170 --> 00:06:11,478
那么还有第二个就是移动啊

135
00:06:11,478 --> 00:06:12,588
这是第二个

136
00:06:12,588 --> 00:06:15,459
只要我鼠标在里面移动就会调用

137
00:06:15,718 --> 00:06:16,920
试一下

138
00:06:18,779 --> 00:06:21,480
哎你看因为在上面一直移动

139
00:06:21,480 --> 00:06:24,120
所以它一直调用上移动一点就会调用啊

140
00:06:24,120 --> 00:06:27,259
这个注意再来

141
00:06:31,500 --> 00:06:35,149
还有一个当然就是我们的这个这个离开

142
00:06:35,149 --> 00:06:38,000
就是当鼠标离开我们这个节点的时候

143
00:06:38,000 --> 00:06:38,899
会调用

144
00:06:40,899 --> 00:06:43,959
离开离开的时候会调用

145
00:06:45,399 --> 00:06:49,439
离开离开你离开的时候会调用啊

146
00:06:49,439 --> 00:06:50,790
离开的时候会调用

147
00:06:50,790 --> 00:06:53,819
所以这三个就是按下呃

148
00:06:53,819 --> 00:06:56,319
就是进来进了节点

149
00:06:56,319 --> 00:06:59,110
然后在节点上面和移除啊

150
00:06:59,110 --> 00:07:00,360
这一组方法

151
00:07:00,980 --> 00:07:03,540
这组方法可以帮你做到什么事儿

152
00:07:04,158 --> 00:07:05,119
仔细想想

153
00:07:05,119 --> 00:07:07,189
比如说我可以让鼠标进来

154
00:07:07,189 --> 00:07:10,819
然后让这个图片和红色混合啊

155
00:07:10,819 --> 00:07:12,959
就是把它颜色设置为红色啊

156
00:07:12,959 --> 00:07:14,160
我们用户一看就知道诶

157
00:07:14,160 --> 00:07:15,810
我鼠标现在移动进来了

158
00:07:15,810 --> 00:07:17,879
鼠标一出去再给它变成白色

159
00:07:17,879 --> 00:07:18,959
就恢复原来颜色

160
00:07:18,959 --> 00:07:20,910
是不是就可以用这个东西来去做

161
00:07:20,910 --> 00:07:21,389
对不对

162
00:07:21,389 --> 00:07:22,920
就可以用这个东西来去做

163
00:07:22,920 --> 00:07:25,649
当然还有一些其他的我们全给他说完

164
00:07:25,649 --> 00:07:28,259
先有down

165
00:07:28,259 --> 00:07:29,970
按下up

166
00:07:29,970 --> 00:07:31,620
这俩也是一对up

167
00:07:31,620 --> 00:07:33,240
就是松开的时候啊

168
00:07:33,240 --> 00:07:34,240
比如说

169
00:07:38,939 --> 00:07:40,339
我按下大家注意

170
00:07:40,339 --> 00:07:41,120
我已经按下了

171
00:07:41,120 --> 00:07:42,860
我怎么样动它都没反应

172
00:07:42,860 --> 00:07:44,420
只有我松开的时候

173
00:07:44,420 --> 00:07:46,139
它就会调用啊

174
00:07:46,139 --> 00:07:50,879
如果我在外面按下移进来再松开也是有用的啊

175
00:07:50,879 --> 00:07:53,160
只要你在这个节点上面松开

176
00:07:53,160 --> 00:07:54,180
它就会调用啊

177
00:07:54,180 --> 00:07:55,000
是这样的

178
00:07:57,199 --> 00:08:00,218
那么这两这两个一组方法大家想可以做什么

179
00:08:00,218 --> 00:08:03,439
刚才的可以我们设置一个高亮颜色啊

180
00:08:03,439 --> 00:08:04,250
鼠标移进来

181
00:08:04,250 --> 00:08:05,449
让它变个红色

182
00:08:05,449 --> 00:08:06,829
这个可以设置什么

183
00:08:06,829 --> 00:08:08,490
当你按下的时候

184
00:08:08,490 --> 00:08:10,949
给他一个绿色用户就知道按下了

185
00:08:10,949 --> 00:08:13,029
松开的时候恢复哎

186
00:08:13,029 --> 00:08:14,410
是不是就可以做到这个情况

187
00:08:14,410 --> 00:08:17,170
所以这就可以做到一个很完美的按钮了对吧

188
00:08:17,170 --> 00:08:19,449
你进来的时候提示提示一下

189
00:08:19,449 --> 00:08:19,689
诶

190
00:08:19,689 --> 00:08:20,910
进来了啊

191
00:08:20,910 --> 00:08:21,779
出去的时候

192
00:08:21,779 --> 00:08:24,329
然后这个按钮又恢复原本的这个颜色

193
00:08:24,329 --> 00:08:26,560
然后你在按钮上面按一下

194
00:08:26,639 --> 00:08:27,540
可以给他

195
00:08:27,540 --> 00:08:28,620
比如说再变个颜色

196
00:08:28,620 --> 00:08:29,879
或者直接换个图片

197
00:08:29,879 --> 00:08:31,740
换一个按下去按钮的图片

198
00:08:31,740 --> 00:08:33,690
松开的时候再给它换回来

199
00:08:33,690 --> 00:08:34,798
是不是

200
00:08:36,820 --> 00:08:38,399
还有一个啊

201
00:08:38,399 --> 00:08:39,240
这个不怎么用

202
00:08:39,240 --> 00:08:41,100
是滚轮啊

203
00:08:41,100 --> 00:08:41,958
滚轮

204
00:08:45,179 --> 00:08:46,620
我们在上面

205
00:08:48,399 --> 00:08:49,440
滚动啊

206
00:08:49,440 --> 00:08:51,000
只要我中间滚轮一滚动

207
00:08:51,000 --> 00:08:51,960
大家可以看啊

208
00:08:51,960 --> 00:08:53,519
滚动滚动滚动滚动滚动

209
00:08:53,519 --> 00:08:54,960
它就一直会调用啊

210
00:08:54,960 --> 00:08:57,460
它一直会调用按键已经不管用了

211
00:08:57,460 --> 00:09:00,019
这个就是滚轮滚轮那个监听事件

212
00:09:04,779 --> 00:09:06,859
那么ok啊

213
00:09:08,240 --> 00:09:10,240
在这里有些同学说

214
00:09:10,240 --> 00:09:11,379
那我怎么判断

215
00:09:11,379 --> 00:09:12,460
比如说我按下啊

216
00:09:12,460 --> 00:09:13,360
我这滚轮什么的

217
00:09:13,360 --> 00:09:13,960
无所谓

218
00:09:13,960 --> 00:09:17,139
我怎么知道我按下的是左键还是右键

219
00:09:17,139 --> 00:09:18,100
那么注意啊

220
00:09:18,100 --> 00:09:19,360
如果判断左右键

221
00:09:19,360 --> 00:09:20,950
我们需要这样去判断

222
00:09:20,950 --> 00:09:24,379
如果event啊

223
00:09:24,379 --> 00:09:27,198
点get button

224
00:09:27,919 --> 00:09:28,929
得到一下

225
00:09:28,929 --> 00:09:31,299
得到你按到的那个按键

226
00:09:31,299 --> 00:09:33,460
然后他呢其实也是一个枚举

227
00:09:33,460 --> 00:09:39,519
你可以判断点英文mouse

228
00:09:41,340 --> 00:09:48,340
点这里注意就有这个这个这个button底盖有个left

229
00:09:48,340 --> 00:09:50,049
有没有有个left

230
00:09:50,049 --> 00:09:51,070
有没有left

231
00:09:51,070 --> 00:09:53,440
就是左边rt

232
00:09:53,440 --> 00:09:54,970
就是右边还有个中间

233
00:09:54,970 --> 00:09:57,120
比如说右边

234
00:09:57,120 --> 00:09:59,039
那就证明我们按了右边的按键

235
00:09:59,039 --> 00:09:59,399
对不对

236
00:09:59,399 --> 00:10:02,379
我们输出一下debug

237
00:10:03,299 --> 00:10:04,039
右键

238
00:10:06,620 --> 00:10:08,059
啊这就是证明按的右键

239
00:10:08,059 --> 00:10:09,620
我们再来一个左键

240
00:10:13,320 --> 00:10:13,820
left

241
00:10:16,820 --> 00:10:22,198
左键运行一下就就判断这俩啊

242
00:10:24,360 --> 00:10:30,399
这个这个这个左键右键啊

243
00:10:30,399 --> 00:10:32,019
我们这时候就错了

244
00:10:32,019 --> 00:10:34,360
我们监听的事件就不能用这个滚轮了

245
00:10:34,360 --> 00:10:37,779
我们就应该用这个滚轮鉴定不出来

246
00:10:37,779 --> 00:10:40,659
我们应该用down down或者up

247
00:10:40,659 --> 00:10:42,639
就是只要带点击的都行

248
00:10:47,779 --> 00:10:52,100
点左键左键右键右键右键左键左键左键

249
00:10:52,100 --> 00:10:53,840
是不是这时候左右键就分开了

250
00:10:53,840 --> 00:10:56,990
当然还有一个中间你也可以去进行一个区分啊

251
00:10:56,990 --> 00:10:59,240
这个就是鼠标的一个事件啊

252
00:10:59,240 --> 00:11:04,068
你通过这个就可以去来点不同的鼠标去做不同的事情啊

253
00:11:04,068 --> 00:11:06,458
点不同的鼠标做不同的事情

254
00:11:09,620 --> 00:11:10,120
ok

255
00:11:13,240 --> 00:11:15,740
那么我们看一下

256
00:11:16,379 --> 00:11:17,940
这是我们基本属性啊

257
00:11:17,940 --> 00:11:20,399
这是我们鼠标的基本属性

258
00:11:20,399 --> 00:11:23,788
那么当然这个像事件里面肯定还有很多这个函数

259
00:11:23,788 --> 00:11:25,940
只是我们目前也用不着啊

260
00:11:25,940 --> 00:11:27,710
这是我们足够基础的属性

261
00:11:27,710 --> 00:11:29,269
就是我们用处最大的

262
00:11:29,269 --> 00:11:30,379
我们先把这个会了

263
00:11:30,379 --> 00:11:32,539
基本上功能就没什么问题了啊

264
00:11:32,539 --> 00:11:36,120
等如果我们后面碰到了这个呃

265
00:11:36,120 --> 00:11:37,919
就是比较高级的用法啊

266
00:11:37,919 --> 00:11:40,679
然后我们再去解析里面的其他的这个函数

267
00:11:40,679 --> 00:11:43,200
那么其他的基本上就是比较冷门了啊

268
00:11:43,200 --> 00:11:45,639
这是基本上用它啊

269
00:11:45,639 --> 00:11:48,220
就判断一个左右键和一个鼠标的位置

270
00:11:48,220 --> 00:11:50,399
get location啊就ok了

271
00:11:53,019 --> 00:11:55,710
那么这个就是鼠标的一个事件

272
00:11:55,710 --> 00:12:00,759
那么接下来我们来说我们来说这个键盘

273
00:12:00,759 --> 00:12:02,179
我们来说键盘

274
00:12:02,700 --> 00:12:06,919
那么呃在说这个按键盘之前突然想起来

275
00:12:06,919 --> 00:12:07,820
大家注意注意啊

276
00:12:07,820 --> 00:12:08,659
这里为什么叫on

277
00:12:08,659 --> 00:12:12,210
因为还有个of this.node

278
00:12:12,210 --> 00:12:13,259
还有个of of

279
00:12:13,259 --> 00:12:16,200
就是如果执行了这个方法就不去监听了啊

280
00:12:16,200 --> 00:12:19,080
但是我现在怎么样让他不去监听这个东西呢

281
00:12:19,080 --> 00:12:19,879
所以

282
00:12:21,399 --> 00:12:23,279
其实最好的方式啊

283
00:12:23,279 --> 00:12:25,779
就是啊我们可以直接这样

284
00:12:26,440 --> 00:12:30,759
这样的话就是我现在不去监听这个鼠标按下了

285
00:12:30,759 --> 00:12:36,559
一般的话这个就是用的可能可能不是特别多

286
00:12:36,559 --> 00:12:39,620
我们常常就是比如说就是让他去监开

287
00:12:39,620 --> 00:12:40,850
开启监听就行了

288
00:12:40,850 --> 00:12:43,469
有时候我们会为了以防万一

289
00:12:43,469 --> 00:12:47,539
我们会在啊destiny当在当前这个节点

290
00:12:47,539 --> 00:12:49,159
比如说当年这个节点要死掉了

291
00:12:49,159 --> 00:12:55,500
销毁前我们把它这个事件啊给这个停止停止监听就可以了

292
00:12:58,320 --> 00:12:59,879
但是这里注意啊

293
00:12:59,879 --> 00:13:04,740
如果我们就是要指定就是监听这个这个事件

294
00:13:04,740 --> 00:13:07,230
我不去监听了怎么办啊

295
00:13:07,230 --> 00:13:11,690
你其实正常而言还能往这儿去写这么一遍啊

296
00:13:11,690 --> 00:13:12,590
那这个就很麻烦

297
00:13:12,590 --> 00:13:15,110
就是说我现在不去监听按下的这个事件

298
00:13:15,110 --> 00:13:16,490
那这个就很不好

299
00:13:16,490 --> 00:13:18,049
所以说其实为了方便

300
00:13:18,049 --> 00:13:20,229
你完全可以把它该怎样

301
00:13:20,229 --> 00:13:22,269
你不要写成这种的啊

302
00:13:22,269 --> 00:13:25,809
你给他写成一个外面的一个方法

303
00:13:25,809 --> 00:13:28,059
然后在这里把把方法名字写上

304
00:13:28,059 --> 00:13:32,590
那这样的话比如这个方法名s s s这个方法名ss

305
00:13:32,590 --> 00:13:37,250
这样的话就方便我们的注册和销毁啊

306
00:13:37,250 --> 00:13:38,750
如果你当前的事件

307
00:13:38,750 --> 00:13:39,950
比如说就没准备销毁

308
00:13:39,950 --> 00:13:41,740
就没准备做这个东西

309
00:13:41,860 --> 00:13:43,240
那就无所谓了啊

310
00:13:43,240 --> 00:13:46,059
那你就直接用这个匿名函数啊

311
00:13:46,059 --> 00:13:47,919
匿名的这个事件就可以了

312
00:13:49,039 --> 00:13:52,820
如果你希望对对这个事件还要进行一个销毁啊

313
00:13:52,820 --> 00:13:57,080
点of还要把这个在某一个时刻把这个事件停止监听

314
00:13:57,080 --> 00:14:01,340
那你尽量把这个事件不要用匿名函数单独写个函数

315
00:14:01,340 --> 00:14:03,139
这里填函数名字关联啊

316
00:14:03,139 --> 00:14:05,539
这样的这种方式啊

317
00:14:05,840 --> 00:14:07,940
ok啊那么这是鼠标

318
00:14:07,940 --> 00:14:09,139
然后键盘也是一样的

319
00:14:09,139 --> 00:14:12,450
但是键盘监听就不是用这个node监听了啊

320
00:14:12,450 --> 00:14:15,870
因为它不是说针对于某个节点上面去按鼠标的

321
00:14:15,870 --> 00:14:17,600
它是整个全局的

322
00:14:17,600 --> 00:14:21,340
所以他呢就是cc.system啊

323
00:14:21,340 --> 00:14:23,019
它是这个system

324
00:14:23,019 --> 00:14:26,200
因为它就是你看是一个全局的一个系统事件

325
00:14:26,200 --> 00:14:28,720
然后在这个里面也是一样的

326
00:14:28,720 --> 00:14:36,458
on 2里面就填写你监听的这个呃事件名称system event

327
00:14:36,779 --> 00:14:40,519
然后点tap再点

328
00:14:40,659 --> 00:14:42,039
这时候大家注意啊

329
00:14:42,039 --> 00:14:46,149
你看就有很多这个k2 k down

330
00:14:46,149 --> 00:14:53,129
这就是监听键盘的上键盘的下啊

331
00:14:53,129 --> 00:14:56,279
我们这里就可以进行一个监听了啊

332
00:14:56,279 --> 00:14:58,059
就可以进行一个监听了

333
00:14:58,580 --> 00:14:59,960
但是在这里有问题

334
00:14:59,960 --> 00:15:06,620
就是说呃这里面只会监听两个键盘事件啊

335
00:15:06,620 --> 00:15:09,080
就是我们刚才看到的一个kdown kup

336
00:15:09,080 --> 00:15:13,720
就是只会按下键盘和松开键盘的时候调用啊

337
00:15:13,720 --> 00:15:18,940
那么我们在这里怎么样知道我们按下的是哪一个啊

338
00:15:18,940 --> 00:15:24,190
怎样得知我们按下的是哪一个东西啊

339
00:15:24,190 --> 00:15:26,639
其实很简单啊

340
00:15:26,639 --> 00:15:27,480
我们先试一下

341
00:15:27,480 --> 00:15:29,039
看看能不能监听出来啊

342
00:15:29,039 --> 00:15:31,479
kdown function

343
00:15:34,440 --> 00:15:36,600
我们先试一下consolute

344
00:15:36,600 --> 00:15:40,559
点debug这手写按键

345
00:15:42,039 --> 00:15:44,759
ok我们运行一下

346
00:15:48,559 --> 00:15:52,610
比如说按e e啊

347
00:15:52,610 --> 00:15:54,320
一定要交点在这里啊

348
00:15:54,320 --> 00:15:55,159
有时候你交点

349
00:15:55,159 --> 00:15:56,059
比如运行完了以后

350
00:15:56,059 --> 00:15:57,350
你按键盘没反应

351
00:15:57,350 --> 00:15:59,240
你第一下一定要点一下屏幕啊

352
00:15:59,240 --> 00:16:03,450
确保焦点在这里叫什么q w e r t y

353
00:16:03,450 --> 00:16:07,029
你看我随便按上下左右各种键

354
00:16:07,029 --> 00:16:09,669
我按下以后它都是可以触发的

355
00:16:09,669 --> 00:16:11,830
只是现在区分不出来是什么键而已

356
00:16:11,830 --> 00:16:12,909
对不对啊

357
00:16:12,909 --> 00:16:14,240
那么这个注意

358
00:16:15,320 --> 00:16:17,779
那么我们肯定是不光是这样

359
00:16:17,779 --> 00:16:21,500
我们肯定有时候希望知道我们按下的是哪一个键

360
00:16:21,500 --> 00:16:22,039
对不对

361
00:16:22,039 --> 00:16:25,110
那在这里我怎么知道我按下了哪个键

362
00:16:25,110 --> 00:16:27,509
我们可以这样后面有事件

363
00:16:27,509 --> 00:16:29,299
那么和鼠标一样

364
00:16:29,299 --> 00:16:32,629
键盘的英文里面肯定也有一些他的东西

365
00:16:32,629 --> 00:16:34,279
那么在对于键盘而言

366
00:16:34,279 --> 00:16:37,159
我们需要知道的就一个叫做k扣子

367
00:16:37,620 --> 00:16:39,879
k code啊

368
00:16:39,879 --> 00:16:43,299
就是你按的是哪一个键啊

369
00:16:43,299 --> 00:16:44,799
你按的是哪一个键

370
00:16:45,039 --> 00:16:47,559
那么在这里我们来k q的

371
00:16:47,559 --> 00:16:50,099
我们直接给它打印出来看一下啊

372
00:16:55,259 --> 00:16:56,460
希望大家哎哎呀

373
00:16:56,460 --> 00:16:57,419
有时候又不管用

374
00:16:57,419 --> 00:16:58,578
真的很烦

375
00:17:00,700 --> 00:17:01,720
那大家注意啊

376
00:17:01,720 --> 00:17:04,900
你看我按q w e啊

377
00:17:04,900 --> 00:17:08,500
比如说我先要交点全在这q w e r t y

378
00:17:08,500 --> 00:17:10,059
大家发现我按每一个键

379
00:17:10,059 --> 00:17:12,039
这里可扣的都是一个数字

380
00:17:12,039 --> 00:17:15,479
也就是说每一个键其实它是有一个编码的啊

381
00:17:15,479 --> 00:17:16,499
它是有一个编码的

382
00:17:16,499 --> 00:17:18,239
所以在这里我也不知道我按了哪个键

383
00:17:18,239 --> 00:17:20,220
但是我知道我按不同的键

384
00:17:21,759 --> 00:17:23,619
那么其实非常简单啊

385
00:17:23,619 --> 00:17:24,700
其实和鼠标一样

386
00:17:24,700 --> 00:17:28,200
鼠标我们是不是在上面去判断这个左右键了啊

387
00:17:28,200 --> 00:17:28,950
哪个是左

388
00:17:28,950 --> 00:17:29,490
哪个是右

389
00:17:29,490 --> 00:17:30,119
对不对

390
00:17:30,119 --> 00:17:31,170
键盘也一样

391
00:17:31,170 --> 00:17:33,630
我们也是可以通过if或者switch

392
00:17:33,630 --> 00:17:34,829
如果你监听的特别多

393
00:17:34,829 --> 00:17:36,089
就用switch少的话

394
00:17:36,089 --> 00:17:37,109
if就够了

395
00:17:37,109 --> 00:17:40,750
我们来判断一下英文的点k code

396
00:17:40,750 --> 00:17:42,819
它其实呢每一个数字啊

397
00:17:42,819 --> 00:17:44,200
他都给你做了一个红

398
00:17:44,200 --> 00:17:45,940
那么我们可以直接调这个红

399
00:17:45,940 --> 00:17:50,269
红就是在红就是k点

400
00:17:50,269 --> 00:17:51,890
这里面大家可以看

401
00:17:51,890 --> 00:17:55,059
你就看到了键盘上面的所有的键了吧

402
00:17:55,680 --> 00:17:59,190
是不是什么上啊

403
00:17:59,190 --> 00:18:01,049
左啊右啊a啊

404
00:18:01,049 --> 00:18:05,539
w s d是不是比如说我监听是不是按了w

405
00:18:06,000 --> 00:18:07,670
然后如果只有按了w

406
00:18:07,670 --> 00:18:08,539
我才怎样

407
00:18:08,539 --> 00:18:11,259
我才输出w

408
00:18:11,259 --> 00:18:12,940
对不对啊

409
00:18:12,940 --> 00:18:15,609
如果再想先听别的啊

410
00:18:15,609 --> 00:18:19,980
如果按了这个这个这个什么q

411
00:18:19,980 --> 00:18:23,140
那么我下面输出一个q

412
00:18:23,140 --> 00:18:25,779
是不是就q w就监听这两个键

413
00:18:25,779 --> 00:18:28,019
这时候我们来运行一下

414
00:18:30,500 --> 00:18:35,599
那么我们在这里选择更多工具里面的开发者工具

415
00:18:35,599 --> 00:18:42,878
然后在这里面大家就可以看q q q q w w w w诶

416
00:18:45,940 --> 00:18:50,799
我怎么按了每一个他都会调用呢啊我现在这里写错了

417
00:18:50,799 --> 00:18:53,529
云注意啊

418
00:18:53,529 --> 00:18:54,940
比较是双等号

419
00:18:54,940 --> 00:18:56,019
不是一个等号啊

420
00:18:56,019 --> 00:18:58,299
我晕竟然会犯这种错误

421
00:18:58,299 --> 00:18:59,638
也是醉了

422
00:19:00,160 --> 00:19:01,539
保存一下

423
00:19:02,619 --> 00:19:06,420
所以谁会犯犯这种像这种低级错误啊

424
00:19:06,420 --> 00:19:09,839
所以说你这个写多久犯了都正常是吧

425
00:19:09,839 --> 00:19:11,039
别说写代码了

426
00:19:11,039 --> 00:19:12,839
我从1年开始讲课到现在

427
00:19:12,839 --> 00:19:15,119
你说还出现这种错误是吧

428
00:19:16,599 --> 00:19:19,200
呵呵重新来重新来

429
00:19:21,799 --> 00:19:30,839
然后在这里我们开发者工具开发者工具q w q w w w w q q q q q

430
00:19:30,839 --> 00:19:31,650
ok啊

431
00:19:31,650 --> 00:19:34,200
那所以这个是没有任何问题的啊

432
00:19:34,200 --> 00:19:35,349
没有任何问题

433
00:19:35,349 --> 00:19:39,490
那这个就是我们的键盘鼠标它的一个事件监听啊

434
00:19:39,490 --> 00:19:41,960
首先键鼠一定要搞清楚

435
00:19:43,079 --> 00:19:46,759
那我们这节课就先这么多吧


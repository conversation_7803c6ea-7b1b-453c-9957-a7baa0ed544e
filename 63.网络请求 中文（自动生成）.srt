1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,699 --> 00:00:10,679
这节课我们来讲一个东西啊

3
00:00:10,679 --> 00:00:13,099
网络请求啊

4
00:00:15,060 --> 00:00:17,309
那么这个东西也是比较重要的

5
00:00:17,309 --> 00:00:19,079
只要你做的应用啊

6
00:00:19,079 --> 00:00:23,039
只要你做的这个游戏稍微有一点功能啊

7
00:00:23,039 --> 00:00:23,940
涉及到网络

8
00:00:23,940 --> 00:00:26,260
那么就会用到这样的一个技术

9
00:00:26,260 --> 00:00:29,320
比如说你的游戏可能完全单机啊

10
00:00:29,320 --> 00:00:33,200
但是你的游戏可能有排行榜啊

11
00:00:33,200 --> 00:00:36,619
这个排行榜呢是要传到网络的啊

12
00:00:36,619 --> 00:00:37,909
那么这种情况下

13
00:00:37,909 --> 00:00:39,859
你这个也是有这个网络功能

14
00:00:39,859 --> 00:00:46,060
我们知道很多这种小游戏他都有这个呃排行榜啊

15
00:00:46,060 --> 00:00:47,140
很多小游戏都有

16
00:00:47,140 --> 00:00:48,729
大家可以去看一下那种

17
00:00:48,729 --> 00:00:50,380
比如说小游戏的那种网站

18
00:00:50,380 --> 00:00:54,090
很多那种网页的消息都会有这种功能啊

19
00:00:54,090 --> 00:00:56,700
你这个诶玩了多少分儿

20
00:00:56,700 --> 00:00:58,740
玩了多少分以后让你输个昵称

21
00:00:58,759 --> 00:01:01,399
然后这时候你的这个信息就保存下来了

22
00:01:01,399 --> 00:01:03,020
那么别人如果玩这个游戏

23
00:01:03,020 --> 00:01:05,719
就可以在排行榜上看到你的这样的一个名次

24
00:01:05,719 --> 00:01:06,340
对不对

25
00:01:06,340 --> 00:01:10,900
那所以这个东西这些功能肯定还是通过网络来进行的啊

26
00:01:10,900 --> 00:01:14,819
所以说呃网络请求并不一定说啊

27
00:01:14,819 --> 00:01:17,879
你要做一个很大的一个网络游戏才会用得到啊

28
00:01:17,879 --> 00:01:19,640
哪怕你是一个单机游戏

29
00:01:19,659 --> 00:01:23,079
只要稍微有一点功能会用到和网络相关的

30
00:01:23,079 --> 00:01:26,060
那么就得用这个这个功能

31
00:01:26,799 --> 00:01:29,409
那么我们来说一下什么是网络请求啊

32
00:01:29,409 --> 00:01:31,239
那么网络请求是这样的

33
00:01:31,239 --> 00:01:32,560
首先正常而言

34
00:01:32,560 --> 00:01:34,180
我们先先想啊

35
00:01:34,180 --> 00:01:36,500
比如说有两个客户端

36
00:01:38,239 --> 00:01:40,040
啊有两个客户端啊

37
00:01:40,040 --> 00:01:41,060
或者说是这样吧

38
00:01:41,060 --> 00:01:42,640
左边这个是客户端

39
00:01:42,760 --> 00:01:44,829
我们好分一下客户端

40
00:01:44,829 --> 00:01:46,780
我们知道对于网游而言啊

41
00:01:46,780 --> 00:01:49,359
我们就拿正常的这个网游网游来举例

42
00:01:49,359 --> 00:01:49,599
子

43
00:01:49,599 --> 00:01:51,180
右边那个叫服务端

44
00:01:52,060 --> 00:01:55,540
那么服务端呢是在网络当中的一个位置啊

45
00:01:55,540 --> 00:01:58,439
比如说你玩的游戏是呃腾讯的

46
00:01:58,439 --> 00:02:00,420
那这个服务端就是腾讯的服务器

47
00:02:00,420 --> 00:02:00,959
对不对

48
00:02:00,959 --> 00:02:04,719
这个客户端呢就是指你的这个玩游戏的设备

49
00:02:04,739 --> 00:02:07,079
比如说他可能代表你的手机是个手游

50
00:02:07,079 --> 00:02:08,879
也可能代表你的电脑是个端游

51
00:02:08,879 --> 00:02:10,370
对不对啊

52
00:02:10,370 --> 00:02:13,539
那么在这里这两个之间啊

53
00:02:13,539 --> 00:02:15,699
我们知道他肯定是都在网络当中

54
00:02:15,699 --> 00:02:16,120
对不对

55
00:02:16,120 --> 00:02:17,620
正常都在网络当中

56
00:02:17,620 --> 00:02:20,689
但是他们俩之间肯定是没有一个联系的啊

57
00:02:20,689 --> 00:02:22,340
大家只是都在网络当中

58
00:02:22,340 --> 00:02:24,530
只是没有一个完全直接的联系

59
00:02:24,530 --> 00:02:26,659
那当我玩你的游戏的时候

60
00:02:26,659 --> 00:02:30,000
我就会和你怎样哎产生联系了

61
00:02:30,000 --> 00:02:32,009
这个联系是怎么产生的啊

62
00:02:32,009 --> 00:02:36,180
那么我们先不说这种嗯

63
00:02:37,520 --> 00:02:39,560
大型的这种网游啊

64
00:02:39,560 --> 00:02:42,259
我们先先拿什么来举例子啊

65
00:02:42,259 --> 00:02:44,599
我们先从简单的来说起啊

66
00:02:44,599 --> 00:02:45,919
我们先从简单的来说起

67
00:02:45,919 --> 00:02:47,340
我们来说网页好了

68
00:02:48,680 --> 00:02:51,449
比如说这是一个网页啊

69
00:02:51,449 --> 00:02:53,280
那我们知道我们在客户端上面

70
00:02:53,280 --> 00:02:55,800
我们在电脑上面是可以打开这个网页的

71
00:02:55,800 --> 00:02:56,490
对不对

72
00:02:56,490 --> 00:02:58,879
那比如说我们打开ie浏览器

73
00:02:58,900 --> 00:03:02,289
在浏览器上我们输入一个地址就可以打开这个网页

74
00:03:02,289 --> 00:03:04,520
那么实际上这个地址呢

75
00:03:04,879 --> 00:03:11,180
他这个地址就会指引我们找到这个网页对应的这个服务器

76
00:03:11,180 --> 00:03:18,219
这个服务器呢就会把这个网页html网页代码给我们发送回来

77
00:03:18,219 --> 00:03:19,870
那发送回来以后

78
00:03:19,870 --> 00:03:22,020
我们收到这个代码以后

79
00:03:22,020 --> 00:03:25,560
然后这个代码通过这个ie浏览器的解析

80
00:03:25,560 --> 00:03:28,400
它就变成了这种带布局的

81
00:03:28,400 --> 00:03:32,199
而且图文混排的这种漂亮的这样的一个样式啊

82
00:03:32,199 --> 00:03:36,860
所以实际上我们每一次我们每次看网页内容都是最新的

83
00:03:36,860 --> 00:03:37,610
对不对啊

84
00:03:37,610 --> 00:03:40,550
之所以最新的就是每一次我们预览网页

85
00:03:40,550 --> 00:03:44,919
我们都是哎告诉网页服务端说我要看这个网页

86
00:03:44,919 --> 00:03:48,000
然后网页服务端把最新的网页代码给你发回来

87
00:03:48,020 --> 00:03:52,240
然后你的ie浏览器或者接到浏览器把这个代码翻译成网页

88
00:03:52,240 --> 00:03:54,610
你就能看到这个网页内容了

89
00:03:54,610 --> 00:03:56,860
那么这两个操作啊

90
00:03:56,939 --> 00:04:01,139
第一个操作叫做请求啊

91
00:04:01,139 --> 00:04:02,280
就是我要发送一个请求

92
00:04:02,280 --> 00:04:03,449
请求是干嘛的

93
00:04:03,449 --> 00:04:05,039
我要获取什么信息的

94
00:04:05,039 --> 00:04:06,330
我要获取什么内容的

95
00:04:06,330 --> 00:04:07,500
是一个网页内容啊

96
00:04:07,500 --> 00:04:08,520
还是什么内容

97
00:04:08,539 --> 00:04:11,419
然后返回来的叫做什么

98
00:04:11,419 --> 00:04:12,500
叫做响应

99
00:04:14,400 --> 00:04:16,199
叫做响应唉

100
00:04:16,199 --> 00:04:18,180
也就是说我这边可以发送请求

101
00:04:18,180 --> 00:04:21,040
然后这边服务端呢给我们返回响应

102
00:04:21,120 --> 00:04:23,699
那么在这里我们请求响应

103
00:04:23,699 --> 00:04:25,319
我们详细来说一下啊

104
00:04:25,319 --> 00:04:26,699
我们来详细说一下

105
00:04:28,120 --> 00:04:30,879
首先对于响应而言啊

106
00:04:30,879 --> 00:04:32,079
我们先说响应啊

107
00:04:32,079 --> 00:04:34,339
响应是服务端给我们传回来的内容

108
00:04:34,360 --> 00:04:36,519
那么响应呢由两部分组成

109
00:04:36,519 --> 00:04:37,660
一部分叫响应

110
00:04:37,660 --> 00:04:39,740
头一部分叫响应体

111
00:04:43,819 --> 00:04:45,639
响应头

112
00:04:49,000 --> 00:04:50,540
响应体

113
00:04:51,079 --> 00:04:54,920
那么想像头和小题里面分别装了什么消什么东西啊

114
00:04:54,920 --> 00:04:56,060
他给我们发个消息

115
00:04:56,060 --> 00:04:57,579
为什么还分两部分

116
00:04:57,680 --> 00:05:01,819
那么响应头里面就包含了我们这个响应体的什么东西

117
00:05:01,819 --> 00:05:06,519
类型长度等信息

118
00:05:06,519 --> 00:05:08,379
那么这个响应体是什么

119
00:05:08,379 --> 00:05:11,170
它它里面可以装任何内容

120
00:05:11,170 --> 00:05:12,579
可以装任何内容

121
00:05:12,600 --> 00:05:16,680
那比如说我们大家知道这个我们常常用迅雷下载

122
00:05:16,680 --> 00:05:18,139
是不是下载

123
00:05:18,139 --> 00:05:20,000
其实也是我们发了个请求

124
00:05:20,000 --> 00:05:21,620
说我们要从服务端

125
00:05:21,620 --> 00:05:23,660
比如说获取一个rr压缩包

126
00:05:23,660 --> 00:05:26,319
这个压缩包可能几个大几个g的大小

127
00:05:26,339 --> 00:05:29,040
那这时候我们就要持续获取这个响应了

128
00:05:29,040 --> 00:05:29,579
对不对

129
00:05:29,579 --> 00:05:32,040
然后来获取这个压缩包的大小

130
00:05:32,040 --> 00:05:34,839
那么但是这里有一点大家注意啊

131
00:05:35,500 --> 00:05:38,620
我们从网上下载内容是一点一点下载的

132
00:05:38,620 --> 00:05:43,540
比如说哎我们这个响应体内容有实行啊

133
00:05:43,540 --> 00:05:45,100
我们先下载第一行又下载

134
00:05:45,100 --> 00:05:46,959
第二行就逐步逐行去下载

135
00:05:46,959 --> 00:05:47,699
对不对

136
00:05:47,860 --> 00:05:53,120
那么你的迅雷怎么能知道当前进度下载了百分之多少呢

137
00:05:53,180 --> 00:05:56,220
那么其实就是因为响应头是很小的

138
00:05:56,220 --> 00:05:58,829
我们在下载任何东西的时候啊

139
00:05:58,829 --> 00:06:01,620
准确来说是我们接收响应的时候

140
00:06:01,639 --> 00:06:03,800
第一下都是把这个响应拿到

141
00:06:03,800 --> 00:06:05,000
只要响应拿到

142
00:06:05,000 --> 00:06:08,160
你就知道你下载的内容是什么类型

143
00:06:08,220 --> 00:06:09,930
然后长度是多少

144
00:06:09,930 --> 00:06:10,980
知道了长度

145
00:06:10,980 --> 00:06:12,300
所以我们就可以知道哎

146
00:06:12,300 --> 00:06:13,439
那每次下载完以后

147
00:06:13,439 --> 00:06:15,939
把下载完的长度除以一个总长度

148
00:06:15,959 --> 00:06:17,959
那么就是我当前下载进度

149
00:06:17,980 --> 00:06:20,079
而且类型就是有时候我们发现

150
00:06:20,079 --> 00:06:23,300
比如说我们下载迅雷下载一个内容的时候

151
00:06:23,300 --> 00:06:25,730
它默认是识别不了这个格式的

152
00:06:25,730 --> 00:06:28,000
但是只要我们一点开始下载

153
00:06:28,720 --> 00:06:31,600
过一会儿突然格式就能识别了

154
00:06:31,759 --> 00:06:35,120
比如说就识别成ra或者ex e等等

155
00:06:35,120 --> 00:06:36,899
就就识别出来这个格式了

156
00:06:36,899 --> 00:06:39,480
这就是因为最开始他不知道什么格式诶

157
00:06:39,480 --> 00:06:41,889
过一会儿他拿到这个tp诶

158
00:06:41,889 --> 00:06:43,959
他就知道了你是什么格式了

159
00:06:43,959 --> 00:06:47,680
所以说实际上我们每次拿到响应这个东西

160
00:06:47,680 --> 00:06:51,100
首先想用头里面就是一些描述什么的描述

161
00:06:51,100 --> 00:06:55,139
对响应体的描述告诉我们想体里面装的是什么内容

162
00:06:55,139 --> 00:06:56,069
然后有多

163
00:06:56,069 --> 00:06:57,720
然后它的长度有多长

164
00:06:57,720 --> 00:07:00,060
这是响应响应部分

165
00:07:00,060 --> 00:07:03,300
那么同样请求其实也是一样的

166
00:07:03,740 --> 00:07:05,959
那么请求的话啊

167
00:07:05,959 --> 00:07:06,680
这里有个请求

168
00:07:06,680 --> 00:07:07,339
对不对

169
00:07:07,339 --> 00:07:08,720
那么我们知道啊

170
00:07:08,720 --> 00:07:11,579
我们从浏览器上输入内容

171
00:07:11,579 --> 00:07:12,990
一般是这种格式

172
00:07:12,990 --> 00:07:14,100
htt

173
00:07:15,699 --> 00:07:18,860
那么为什么是这个这种格式啊

174
00:07:18,920 --> 00:07:20,959
为什么永远是http开头

175
00:07:20,959 --> 00:07:23,060
因为我们现在网络浏览啊

176
00:07:23,060 --> 00:07:24,920
遵守的都是http协议

177
00:07:24,920 --> 00:07:26,300
遵循了http协议

178
00:07:26,300 --> 00:07:31,449
你才能在网络当中去和去在不同终端之间去进行沟通啊

179
00:07:31,449 --> 00:07:36,040
所以我们所有的这个网址前面都是http啊

180
00:07:36,040 --> 00:07:38,100
这是一个协议网络协议

181
00:07:38,120 --> 00:07:40,519
那么再往后面一般后面就跟着域名了

182
00:07:40,519 --> 00:07:41,209
对不对啊

183
00:07:41,209 --> 00:07:43,180
比如abc.com

184
00:07:44,220 --> 00:07:47,379
那么再往后面可能还有很多东西啊

185
00:07:47,379 --> 00:07:49,959
可能还有很多东西到最后啊

186
00:07:49,959 --> 00:07:54,279
然后可能后面还有什么a b c等于一一是吧

187
00:07:55,079 --> 00:07:58,730
等雨哥333等等

188
00:07:58,730 --> 00:08:01,220
这就是一串正常的一个地址啊

189
00:08:01,220 --> 00:08:03,439
那么我们现在来逐渐解释一下

190
00:08:03,439 --> 00:08:06,199
像这一类的地址分别代表什么东西啊

191
00:08:06,579 --> 00:08:08,680
首先h t t p冒号双斜杠

192
00:08:08,680 --> 00:08:11,740
它代表了一个呃通讯协议啊

193
00:08:11,740 --> 00:08:13,480
h t p协议我们就不多说了

194
00:08:13,480 --> 00:08:17,029
在这里域名代表什么

195
00:08:17,029 --> 00:08:19,680
域名就代表了一个ip地址

196
00:08:19,699 --> 00:08:21,350
它就代表了个ip地址

197
00:08:21,350 --> 00:08:25,560
说白了一个域名就代表了一个服务器啊

198
00:08:25,560 --> 00:08:27,000
它就代表了一个服务器

199
00:08:27,000 --> 00:08:32,299
比如说你现在啊呃我们这里比如说我们常用百度

200
00:08:32,299 --> 00:08:32,809
对不对

201
00:08:32,809 --> 00:08:36,559
那百度实际上我们预览百度这个网址

202
00:08:36,559 --> 00:08:38,860
它对应的就是百度的一个ip地址

203
00:08:39,038 --> 00:08:40,719
所以如果你知道ip地址

204
00:08:40,719 --> 00:08:42,219
你在这直接不用百度

205
00:08:42,219 --> 00:08:44,139
你直接在这用这个哎

206
00:08:44,139 --> 00:08:47,100
比如说他的这个ip地址啊

207
00:08:47,100 --> 00:08:48,360
也是可以的啊

208
00:08:48,360 --> 00:08:49,379
也是可以的

209
00:08:49,399 --> 00:08:51,230
那么这个注意一下啊

210
00:08:51,230 --> 00:08:54,320
也就是说实际上这里都是ip地址

211
00:08:54,320 --> 00:08:57,860
一个ip地址我们在这里对应一台设备

212
00:08:57,860 --> 00:09:00,440
只要你的设备连上网

213
00:09:00,440 --> 00:09:01,519
不管你手机也好

214
00:09:01,519 --> 00:09:02,299
电脑也好

215
00:09:02,299 --> 00:09:05,399
我们都知道他会给我们分配一个ip啊

216
00:09:05,399 --> 00:09:07,019
ip就相当于你的编号

217
00:09:07,019 --> 00:09:08,039
对不对啊

218
00:09:08,039 --> 00:09:09,779
就相当于你的一个编号啊

219
00:09:09,779 --> 00:09:11,639
每个设备独一无二的编号啊

220
00:09:11,639 --> 00:09:13,409
这个世界上不会有两台设备

221
00:09:13,409 --> 00:09:15,269
它的ip是一样的啊

222
00:09:15,269 --> 00:09:15,960
对不对

223
00:09:15,980 --> 00:09:19,730
那么我们知道这个以后啊

224
00:09:19,730 --> 00:09:24,100
那么ip地址啊既然都是独一无二的

225
00:09:24,100 --> 00:09:25,809
但是有一个问题啊

226
00:09:25,809 --> 00:09:28,240
就是首先我们在浏览网站的时候

227
00:09:28,240 --> 00:09:31,220
如果比如说我们知道一个网站是搜索的

228
00:09:31,220 --> 00:09:32,179
而这个网站很好

229
00:09:32,179 --> 00:09:33,409
我想记录一下

230
00:09:33,409 --> 00:09:34,399
如果大家看啊

231
00:09:34,399 --> 00:09:35,480
用这种域名的方式

232
00:09:35,480 --> 00:09:36,320
你很好记

233
00:09:36,320 --> 00:09:37,940
但是你要记一个ip地址

234
00:09:37,940 --> 00:09:38,980
像这种东西

235
00:09:38,980 --> 00:09:39,879
那太麻烦了

236
00:09:39,879 --> 00:09:40,389
对不对

237
00:09:40,389 --> 00:09:42,039
比如说我有五个经典的网站

238
00:09:42,039 --> 00:09:43,620
我这五个网站常上

239
00:09:43,700 --> 00:09:47,090
那我在这里每个网站我都要把它的域名背下来

240
00:09:47,090 --> 00:09:48,350
这个就太坑了

241
00:09:48,350 --> 00:09:53,340
而我们在这里用这种唉用这种域名的方式

242
00:09:53,340 --> 00:09:54,419
不用ip的方式

243
00:09:54,419 --> 00:09:55,799
我们去记诶

244
00:09:55,799 --> 00:09:57,980
我们去记录它就很好记

245
00:09:57,980 --> 00:09:58,490
对不对

246
00:09:58,490 --> 00:10:00,080
哎可能看一眼就记住啊

247
00:10:00,080 --> 00:10:01,379
这个网站我记住了

248
00:10:01,779 --> 00:10:03,399
那么这是一个优点啊

249
00:10:03,399 --> 00:10:04,659
这是域名的第一个优点

250
00:10:04,659 --> 00:10:09,320
域名的第二个优点就是域名是可以换ip绑定的啊

251
00:10:09,320 --> 00:10:12,980
就是说比如说这个百度域名现在指向了第一个ip啊

252
00:10:12,980 --> 00:10:14,919
这个ip是百度的服务器

253
00:10:14,940 --> 00:10:19,080
但是百度可能比如说诶过了2年服务器更新了

254
00:10:19,080 --> 00:10:21,100
他换了一个i啊

255
00:10:21,100 --> 00:10:22,360
比如说他换了个p

256
00:10:22,360 --> 00:10:23,879
当他换了p后

257
00:10:24,200 --> 00:10:28,429
那么如果我们之前是用ip这种方式去访问网站的

258
00:10:28,429 --> 00:10:30,440
那就太麻烦了啊

259
00:10:30,440 --> 00:10:31,460
那就太麻烦了

260
00:10:31,460 --> 00:10:32,809
那我们就还要换

261
00:10:32,809 --> 00:10:35,639
我们好不容易把百度的这个ip地址记住了

262
00:10:35,659 --> 00:10:38,120
那接下来百度说我们服务器换ip了

263
00:10:38,120 --> 00:10:40,000
你是不是还要记住新的ip

264
00:10:40,019 --> 00:10:41,250
而这个就很坑

265
00:10:41,250 --> 00:10:43,590
所以我们这时候如果记录一个域名

266
00:10:43,590 --> 00:10:45,899
那么百度如果他自己换了ip以后

267
00:10:45,899 --> 00:10:49,679
他只需要让他的域名指向新的ip就可以了啊

268
00:10:49,679 --> 00:10:51,059
指向新的ip就可以了

269
00:10:51,059 --> 00:10:55,940
所以说每个设备每个设备都有一个ip

270
00:10:55,940 --> 00:10:59,340
但是对于服务器这种服务器属于什么呀

271
00:10:59,519 --> 00:11:02,159
他是很多客户端会访问它

272
00:11:02,159 --> 00:11:03,419
所以作为他而言

273
00:11:03,419 --> 00:11:04,259
他太长

274
00:11:04,259 --> 00:11:05,730
太容易被人访问了

275
00:11:05,730 --> 00:11:08,539
那么他直接用ip来进行访问

276
00:11:08,539 --> 00:11:09,740
太不好了啊

277
00:11:09,740 --> 00:11:10,460
太不好了

278
00:11:10,460 --> 00:11:13,100
所以在这里后面出来了域名

279
00:11:13,100 --> 00:11:17,200
域名和和ip其实就是一一对应的啊

280
00:11:17,200 --> 00:11:19,539
那么他就可以不对外公布这个ip了

281
00:11:19,539 --> 00:11:21,129
他对外公布域名就行了

282
00:11:21,129 --> 00:11:24,360
那么大家访问他就可以通过域名访问啊

283
00:11:24,360 --> 00:11:26,679
实际上我们在访问的时候

284
00:11:26,679 --> 00:11:29,740
他会把域名解析成ip还是通过ip访问的

285
00:11:29,740 --> 00:11:30,460
对不对

286
00:11:30,659 --> 00:11:32,100
那么这个指导下

287
00:11:32,100 --> 00:11:36,559
所以这个3~6点百度点com这个内容就是属于一个呃

288
00:11:36,559 --> 00:11:38,210
其实就属于一个域名

289
00:11:38,210 --> 00:11:42,759
它就属于一个指向ip的这样的一个域名嗯

290
00:11:42,759 --> 00:11:45,559
说白了就好像一个字符串儿变量一样

291
00:11:45,559 --> 00:11:48,620
这个是变量名称变量的值就是一个ip地址

292
00:11:48,620 --> 00:11:49,820
你可以随意去换它

293
00:11:49,820 --> 00:11:51,320
这样的话我们去好记录

294
00:11:51,320 --> 00:11:51,740
对不对

295
00:11:51,740 --> 00:11:52,500
好记录

296
00:11:52,960 --> 00:11:56,950
那么再往后面一定会有这样的一串内容

297
00:11:56,950 --> 00:12:00,059
他如果后面啊到此结束了

298
00:12:00,860 --> 00:12:03,019
那么就正常就是这一串内容

299
00:12:03,019 --> 00:12:05,179
如果后面还有很多乱七八糟啊

300
00:12:05,179 --> 00:12:06,769
就是什么问号什么什么的

301
00:12:06,769 --> 00:12:08,399
如果你见了问号

302
00:12:08,539 --> 00:12:11,899
那么从问号前面到域名后面

303
00:12:11,899 --> 00:12:13,320
这是一组

304
00:12:15,200 --> 00:12:16,789
这一组代表什么

305
00:12:16,789 --> 00:12:19,139
这一组就代表访问的

306
00:12:21,659 --> 00:12:24,000
服务器文件

307
00:12:24,000 --> 00:12:27,919
那么在这里这里实际上啊为什么会有这个路径

308
00:12:27,919 --> 00:12:30,440
这个路径实际上是服务器里面的路径

309
00:12:30,440 --> 00:12:32,600
就是前面你已经防到服务器了

310
00:12:32,600 --> 00:12:34,419
那服务器我们知道也是台电脑

311
00:12:34,440 --> 00:12:37,919
电脑里面肯定有个文件夹叫aa bb啊

312
00:12:37,919 --> 00:12:40,860
电脑里面服务器的电脑里面有个文件夹叫a p p

313
00:12:40,860 --> 00:12:45,360
这个文件夹里面有个呃有个文件啊

314
00:12:45,360 --> 00:12:47,340
这个文件就适合这个程序啊

315
00:12:47,340 --> 00:12:50,840
叫aa点菲律宾比索或者点gs或者点别的

316
00:12:50,879 --> 00:12:52,919
那么这个就是一个程序啊

317
00:12:52,919 --> 00:12:54,750
就是一个服务器上的程序

318
00:12:54,750 --> 00:12:59,549
所以实际上我我们啊看着我们在浏览器上输了这串内容

319
00:12:59,549 --> 00:13:02,629
实际上我们是找到了这个服务器

320
00:13:02,629 --> 00:13:05,210
然后运行了服务器的这个程序

321
00:13:05,210 --> 00:13:10,000
也就是说我们是从客户端运行了远程服务器上的一个程序

322
00:13:10,000 --> 00:13:11,139
当运行完以后

323
00:13:11,139 --> 00:13:14,519
他会把运行完的结果怎样返回给我们

324
00:13:14,759 --> 00:13:16,500
也就是说我们每次浏览网页

325
00:13:16,500 --> 00:13:18,240
实际上我们都做了一件很伟大的事情

326
00:13:18,240 --> 00:13:22,779
就是我们其实不是说我们在我们的电脑上操作的

327
00:13:22,779 --> 00:13:26,200
而是我们访问了远程的一个文件

328
00:13:26,200 --> 00:13:28,179
远程的文件被执行了

329
00:13:28,179 --> 00:13:31,360
只是他把最后执行完的结果返回给我们了

330
00:13:31,360 --> 00:13:34,389
那返回给我们的如果是个html代码

331
00:13:34,389 --> 00:13:36,399
那么我们就可以看到一个网页了

332
00:13:36,659 --> 00:13:41,570
但是有些他的这个到这里还没结束

333
00:13:41,570 --> 00:13:42,860
它会有一个问号

334
00:13:42,860 --> 00:13:45,679
问号后面他会以建直对啊

335
00:13:45,679 --> 00:13:48,779
他会以建直对来存放内容

336
00:13:48,779 --> 00:13:50,370
比如说a等于一

337
00:13:50,370 --> 00:13:51,659
b等于二

338
00:13:51,659 --> 00:13:53,850
那么这些代表参数

339
00:13:53,850 --> 00:13:55,320
这个参数是给谁用的

340
00:13:55,320 --> 00:13:56,759
是给这个文件用的

341
00:13:56,960 --> 00:13:59,059
这个文件也是一个编程代码

342
00:13:59,059 --> 00:14:00,200
这个编程代码的语言

343
00:14:00,200 --> 00:14:02,279
比如说咱们这举例子是菲律

344
00:14:02,299 --> 00:14:04,159
那么它既然是编程语言

345
00:14:04,159 --> 00:14:06,500
它也是也是可以传参数的

346
00:14:06,519 --> 00:14:10,929
那么后面我们就可以通过这种键值对的方式给他传两个参数

347
00:14:10,929 --> 00:14:12,759
就是ab两个参数

348
00:14:12,759 --> 00:14:14,860
所以如果我们见问号隔开

349
00:14:14,860 --> 00:14:17,620
那么问号后面肯定是键值对

350
00:14:17,620 --> 00:14:20,799
然后很多参数之间拿这个符号隔开

351
00:14:20,799 --> 00:14:22,639
那么这就是参数部分

352
00:14:25,320 --> 00:14:28,860
啊那么这整体就是一个访问地址啊

353
00:14:28,860 --> 00:14:30,720
我们可以叫它u r l啊

354
00:14:30,720 --> 00:14:32,340
这就是一个访问地址啊

355
00:14:32,340 --> 00:14:33,820
这就是一个访问地址

356
00:14:33,860 --> 00:14:35,659
那么拿到这样的一个地址

357
00:14:35,659 --> 00:14:37,399
我们就可以去进行访问了

358
00:14:37,399 --> 00:14:41,000
那么请求啊所谓的请求有两种

359
00:14:41,000 --> 00:14:43,000
首先我们来说第一种请求

360
00:14:44,980 --> 00:14:46,120
第一种请求

361
00:14:46,120 --> 00:14:47,980
网络请求叫做get请求

362
00:14:47,980 --> 00:14:49,779
最常用的请求啊

363
00:14:49,779 --> 00:14:51,820
大部分的请求都是get请求

364
00:14:52,639 --> 00:14:55,919
get的请求的话只有个请求头啊

365
00:14:55,919 --> 00:14:57,539
就他也是有这个头有体

366
00:14:57,539 --> 00:14:58,860
但是对于get的请求而言

367
00:14:58,860 --> 00:14:59,980
他只有一个头

368
00:14:59,980 --> 00:15:01,360
他的头里面有什么

369
00:15:01,360 --> 00:15:03,279
比如说你访问的是这个内容

370
00:15:03,279 --> 00:15:06,879
那么它的头里面就有这个内容啊

371
00:15:06,879 --> 00:15:07,799
就有这个内容

372
00:15:07,799 --> 00:15:10,200
那么也就是说如果我们用get请求的话

373
00:15:10,200 --> 00:15:11,220
如果我们用get请求

374
00:15:11,220 --> 00:15:13,080
我们就发了个请求投出来

375
00:15:13,100 --> 00:15:15,259
然后这个请求头发出来以后

376
00:15:15,259 --> 00:15:18,620
它就会根据这个域名所指向的ip地址

377
00:15:18,620 --> 00:15:19,879
比如说你这里是百度

378
00:15:19,879 --> 00:15:20,519
对不对

379
00:15:20,519 --> 00:15:22,860
它就会根据这个域名所指向的ip

380
00:15:22,860 --> 00:15:26,720
地址找到这个对应的客户服务端

381
00:15:26,720 --> 00:15:27,679
找到服务端以后

382
00:15:27,679 --> 00:15:30,830
怎样运行它里面对应的这个服务端的文件

383
00:15:30,830 --> 00:15:33,019
并且给它传递对应的参数

384
00:15:33,039 --> 00:15:35,500
然后就可以接收响应啊

385
00:15:35,500 --> 00:15:37,879
就可以接收服务端给我们传回来的响应

386
00:15:37,879 --> 00:15:40,279
也就是请求响应肯定是一对的啊

387
00:15:40,279 --> 00:15:42,230
我们先发请求才有响应

388
00:15:42,230 --> 00:15:43,460
那么这是get请求

389
00:15:43,460 --> 00:15:44,179
非常简单

390
00:15:44,179 --> 00:15:46,279
get请求就是一个地址就行了

391
00:15:46,299 --> 00:15:49,090
还有一种请求叫做post请求

392
00:15:49,090 --> 00:15:51,139
什么叫post请求

393
00:15:51,159 --> 00:15:55,240
有时候我们不光是获取从服务端获取信息

394
00:15:55,240 --> 00:15:58,029
有时候我们也要上传很大量的信息

395
00:15:58,029 --> 00:15:59,720
比如说现在的网盘

396
00:15:59,720 --> 00:16:04,080
或者说我们我们很多论坛有这个头像啊

397
00:16:04,080 --> 00:16:05,519
我们可以上传个头像

398
00:16:05,519 --> 00:16:06,179
对不对

399
00:16:06,179 --> 00:16:10,620
基本上我们认为所有的上传大部分都是post请求

400
00:16:10,620 --> 00:16:12,269
那么post请求而言

401
00:16:12,269 --> 00:16:16,990
他也有请请求头和请求体啊

402
00:16:16,990 --> 00:16:19,539
我们可以把我们比如说要上传的内容

403
00:16:19,539 --> 00:16:20,500
包括头像啊

404
00:16:20,500 --> 00:16:22,000
放到这个请求体里面

405
00:16:22,000 --> 00:16:24,340
然后把这个题里面的内容

406
00:16:24,340 --> 00:16:28,840
而把他的type跟ns都放到我们最上面了啊

407
00:16:28,840 --> 00:16:31,519
都放到我们请求头里面了啊

408
00:16:31,519 --> 00:16:35,480
那么一般它的名字会叫content type和content dance

409
00:16:35,480 --> 00:16:38,720
意思就是我内容的类型和内容的长度啊

410
00:16:38,720 --> 00:16:40,129
然后我们把它发出去

411
00:16:40,129 --> 00:16:41,580
然后我们把它发出去

412
00:16:41,580 --> 00:16:43,679
那这个请求就叫post请求

413
00:16:43,679 --> 00:16:45,840
所以实际上正常而言有两种请求

414
00:16:45,840 --> 00:16:47,159
大部分请求都是get

415
00:16:47,159 --> 00:16:50,899
因为我们大部分情况是要获取信息的啊

416
00:16:50,899 --> 00:16:55,179
只有很小部分时候我们会传递信息

417
00:16:55,179 --> 00:16:56,379
传递大量的信息

418
00:16:56,379 --> 00:16:57,940
那么传递大量信息的时候

419
00:16:57,940 --> 00:16:59,700
我们就需要用到post请求了

420
00:16:59,720 --> 00:17:01,490
那这时候注意一下

421
00:17:01,490 --> 00:17:03,379
也就是说这里分两种请求

422
00:17:03,379 --> 00:17:05,278
get for post请求啊

423
00:17:05,278 --> 00:17:06,719
但是响应我们就一种啊

424
00:17:06,719 --> 00:17:09,440
就正常的响应啊

425
00:17:09,640 --> 00:17:12,460
那么这是我们正常的一个理解啊

426
00:17:12,460 --> 00:17:13,720
就是网络请求啊

427
00:17:13,720 --> 00:17:14,740
肯定有客户端

428
00:17:14,740 --> 00:17:15,460
有服务端的

429
00:17:15,460 --> 00:17:16,180
对不对

430
00:17:17,259 --> 00:17:19,538
但是对于我们这个啊

431
00:17:19,538 --> 00:17:22,778
我们这个还得我们这个cos creator这单独拿出来说

432
00:17:22,778 --> 00:17:25,328
因为他做的游戏大部分是网页游戏

433
00:17:25,328 --> 00:17:28,720
所以实际上这个游戏最后你比如说做完以后

434
00:17:28,720 --> 00:17:29,650
你会放到哪

435
00:17:29,650 --> 00:17:32,380
你这个游戏也会放到服务服务器里面

436
00:17:32,380 --> 00:17:34,539
比如说这是我们的服务器啊

437
00:17:34,539 --> 00:17:37,950
比如说这个是我们的嗯

438
00:17:37,950 --> 00:17:40,359
服务器我们看一下啊

439
00:17:41,519 --> 00:17:42,839
就写个名字吧

440
00:17:42,839 --> 00:17:46,700
服务器服务器这整个都是我们的服务器

441
00:17:46,700 --> 00:17:51,440
在服务器里面需要保存数据的一层啊

442
00:17:51,440 --> 00:17:53,380
需要保存数据的一层

443
00:17:54,759 --> 00:17:56,109
我们可以叫它

444
00:17:56,109 --> 00:17:58,480
比如说数据库啊

445
00:17:58,980 --> 00:18:01,829
在服务器里面数据库是用来存储数据的

446
00:18:01,829 --> 00:18:04,289
那么谁和数据库进行交互

447
00:18:04,289 --> 00:18:07,119
一般会有一个服务器语言啊

448
00:18:07,119 --> 00:18:11,400
这个服务器语言会编写很多服务器代码啊

449
00:18:11,400 --> 00:18:13,779
服务器语言代码

450
00:18:14,700 --> 00:18:17,759
这个服务器语言代码或者叫程序啊

451
00:18:17,759 --> 00:18:18,960
叫还是叫程序好点

452
00:18:18,960 --> 00:18:21,720
服务器语言程序它的目的是干嘛

453
00:18:21,720 --> 00:18:28,250
它的目的是和数据库产生真正的一个交互的啊

454
00:18:28,250 --> 00:18:30,380
它们之间会产生交互的

455
00:18:30,460 --> 00:18:33,579
那么这个服务器编程语言是我们常用的

456
00:18:33,579 --> 00:18:37,400
比如说菲律宾比java c sharp啊

457
00:18:37,400 --> 00:18:39,140
那这些都可以去做啊

458
00:18:39,140 --> 00:18:41,210
这些都可以去编写啊

459
00:18:41,210 --> 00:18:43,519
那么这些代码会写很多程序

460
00:18:43,519 --> 00:18:47,740
然后这些程序然后就可以和数据库进行交互了

461
00:18:47,740 --> 00:18:50,160
那么我们cocos creator

462
00:18:51,160 --> 00:18:55,420
cocos creator我们写的叫做前端游戏啊

463
00:18:55,420 --> 00:19:02,400
所以实际上我们这个游戏如果比如说会有这个嗯数据交互的话

464
00:19:02,400 --> 00:19:08,099
我们往往会和我们自己服务器里面的服务器语言程序进行交互

465
00:19:11,039 --> 00:19:13,380
啊会和他进行交互啊

466
00:19:13,380 --> 00:19:16,059
这就是我们这个cos creator特殊的地方

467
00:19:16,059 --> 00:19:18,640
因为实际上我们知道端游正常的端游

468
00:19:18,640 --> 00:19:21,339
比如说你每个游戏都有一个客户端啊

469
00:19:21,339 --> 00:19:22,660
比如说这是端游的话

470
00:19:22,660 --> 00:19:23,880
这是一个客户端

471
00:19:26,019 --> 00:19:28,150
如果是正常端游而言

472
00:19:28,150 --> 00:19:31,039
我们做一个端游是没有这一部分的啊

473
00:19:31,039 --> 00:19:34,339
端游的话我们有个服务器里面有个服务器语言与数据库

474
00:19:34,339 --> 00:19:37,220
然后我们客户端之间和服务器之间去进行沟通

475
00:19:37,220 --> 00:19:38,740
是不是就是这种模式了

476
00:19:38,900 --> 00:19:41,420
但是我们现在做的又是一个网页游戏

477
00:19:41,420 --> 00:19:42,500
网页游戏的话

478
00:19:42,500 --> 00:19:47,339
它本身就会把这个游戏部署在我们的服务器里面啊

479
00:19:47,339 --> 00:19:51,599
所以在这里我们的这个服务

480
00:19:51,599 --> 00:19:52,859
我们的这个游戏啊

481
00:19:52,859 --> 00:19:55,200
cocos creator里面写的游戏啊

482
00:19:55,200 --> 00:20:00,119
前端游戏网页游戏代码是存放在服务器里的

483
00:20:00,119 --> 00:20:02,579
也就是说实际上我们的客户端代码

484
00:20:02,579 --> 00:20:05,039
因为我们编写的这个游戏啊

485
00:20:05,039 --> 00:20:06,000
叫客户端

486
00:20:06,559 --> 00:20:09,319
客户端代码和服务器代码啊

487
00:20:09,319 --> 00:20:10,759
这个就叫服务器代码

488
00:20:10,759 --> 00:20:13,559
实际上都是放在我们服务器里的啊

489
00:20:13,559 --> 00:20:15,539
实际上都是放在我们服务器里的

490
00:20:15,539 --> 00:20:18,190
这就是我们这个cos creator嗯

491
00:20:18,190 --> 00:20:19,779
他编写网页游戏以后

492
00:20:19,779 --> 00:20:21,789
和其他这个正常的这个网页

493
00:20:21,789 --> 00:20:24,599
正常的这个呃网络游戏的区别

494
00:20:24,599 --> 00:20:25,920
正常网络游戏是客户端

495
00:20:25,920 --> 00:20:26,799
是独立的

496
00:20:26,799 --> 00:20:27,279
对不对

497
00:20:27,279 --> 00:20:28,359
你不管是手机也好

498
00:20:28,359 --> 00:20:29,019
电脑也好

499
00:20:29,019 --> 00:20:30,730
你都是要下载客户端的

500
00:20:30,730 --> 00:20:33,319
然后你和真正的服务器去进行沟通

501
00:20:33,319 --> 00:20:35,799
而你编写网页游戏的话

502
00:20:35,799 --> 00:20:39,460
网页游戏的特点就是你这个你看你有网页都能玩游戏

503
00:20:39,460 --> 00:20:40,720
那这个游戏放到哪了

504
00:20:40,720 --> 00:20:43,299
游戏其实也放在服务器上了啊

505
00:20:43,299 --> 00:20:45,819
所以实际上就变成这样的一个效果了

506
00:20:45,819 --> 00:20:48,190
就相当于把外面的那个光诶

507
00:20:48,190 --> 00:20:53,779
客户端的那个代码给它移到我们服务器里面了啊

508
00:20:53,779 --> 00:20:57,720
然后他们之间去展产生一些交流

509
00:21:00,880 --> 00:21:02,799
他们之间去产生一些交流

510
00:21:02,799 --> 00:21:05,619
也就是说我们如果想调数据库

511
00:21:05,619 --> 00:21:07,119
并不是直接调数据库

512
00:21:07,119 --> 00:21:09,220
我们往往会调服务器啊

513
00:21:09,220 --> 00:21:10,519
编写完的这个代码

514
00:21:10,519 --> 00:21:13,130
然后这个代码再和数据库进行沟通

515
00:21:13,130 --> 00:21:15,440
他拿到从数据库里面拿到数据以后

516
00:21:15,440 --> 00:21:18,119
他再把获得的数据返回给我们

517
00:21:18,119 --> 00:21:21,839
那我们知道在这个不管是什么什么编程啊

518
00:21:21,839 --> 00:21:25,680
前端是单独的一个人员或者说多个人员去做的

519
00:21:25,920 --> 00:21:29,369
然后服务器可能有一个或多个人员去做的啊

520
00:21:29,369 --> 00:21:32,160
那么数据库基本上也是属于服务器人员去做的

521
00:21:32,160 --> 00:21:35,299
也就是说这边基本上就属于一个角色啊

522
00:21:35,299 --> 00:21:37,460
很多人专门去学这个数据库

523
00:21:37,460 --> 00:21:41,440
而不是数据库学这个后台编程或者说网络编程

524
00:21:41,460 --> 00:21:43,180
他们学的就是这块内容

525
00:21:43,180 --> 00:21:47,680
而我们学做游戏或者说学做软件的啊

526
00:21:47,680 --> 00:21:49,599
大部分都属于这个前端的

527
00:21:49,599 --> 00:21:52,609
那就属于这一部分啊

528
00:21:52,609 --> 00:21:54,680
这个东西啊我们不去多说啊

529
00:21:54,680 --> 00:21:57,619
我在这里就是想给大家尽量把这个画出来

530
00:21:57,619 --> 00:22:00,200
让大家能能理解一点是一点啊

531
00:22:00,200 --> 00:22:02,000
其实你理解不了也无所谓啊

532
00:22:02,000 --> 00:22:03,440
就不跟大家去说也没关系

533
00:22:03,440 --> 00:22:06,009
但是我还是想简单提一下

534
00:22:06,009 --> 00:22:07,750
能理解一点是一点啊

535
00:22:07,750 --> 00:22:09,960
因为这个你到工作的时候

536
00:22:09,960 --> 00:22:11,220
你早晚会接触啊

537
00:22:11,220 --> 00:22:12,660
早晚会明白啊

538
00:22:12,660 --> 00:22:14,579
提前跟大家就是说一下

539
00:22:15,740 --> 00:22:18,200
那么这时候啊有一个问题啊

540
00:22:18,200 --> 00:22:18,980
我跟大家去说

541
00:22:18,980 --> 00:22:21,410
这个也是有一个问题要跟大家说的

542
00:22:21,410 --> 00:22:24,680
就是这里注意啊

543
00:22:24,680 --> 00:22:26,509
既然我们写的网页游戏

544
00:22:26,509 --> 00:22:28,220
我们都在一个服务器里面

545
00:22:28,220 --> 00:22:31,019
所以我们的ip地址属于一个

546
00:22:31,019 --> 00:22:34,819
所以我们的域名也属于一个啊

547
00:22:34,819 --> 00:22:36,980
所以我们的域名是属于一个的

548
00:22:36,980 --> 00:22:39,079
这一点一定要注意啊

549
00:22:39,079 --> 00:22:40,460
这点一定要注意啊

550
00:22:40,460 --> 00:22:41,480
为什么要注意呢

551
00:22:41,480 --> 00:22:46,160
一会儿等我们学到这个网络请求代码的时候

552
00:22:46,160 --> 00:22:47,480
你就知道了啊

553
00:22:47,480 --> 00:22:50,269
但是现在我还没说代码的前提之下

554
00:22:50,269 --> 00:22:51,680
你先记住这个啊

555
00:22:51,680 --> 00:22:52,740
先记着我说的

556
00:22:52,759 --> 00:22:53,839
我们现在有一个特点

557
00:22:53,839 --> 00:22:55,099
就是客户端啊

558
00:22:55,099 --> 00:22:55,700
就这个网页

559
00:22:55,700 --> 00:22:56,599
尤其这个客户端

560
00:22:56,599 --> 00:23:00,869
我们编写的客户端跟服务器它的域名是相同的

561
00:23:00,869 --> 00:23:02,490
ip地址是一样的

562
00:23:02,490 --> 00:23:05,460
而我们正常如果去写那种端游的话啊

563
00:23:05,460 --> 00:23:07,920
就是你电脑游戏或者手机游戏的话

564
00:23:07,920 --> 00:23:10,140
由于你你是客户端啊

565
00:23:10,140 --> 00:23:12,660
然后人家是服务器的ip肯定是不一样的

566
00:23:12,660 --> 00:23:14,029
对不对啊

567
00:23:14,029 --> 00:23:16,220
那么这有什么哎

568
00:23:16,220 --> 00:23:17,359
那这有什么用啊

569
00:23:17,359 --> 00:23:18,500
你先记着就行了

570
00:23:18,500 --> 00:23:20,920
咱们写代码的时候你就明白了

571
00:23:21,279 --> 00:23:25,240
那么这节课就先跟大家去聊了一下这些东西啊

572
00:23:25,240 --> 00:23:26,920
先跟大家聊了一些这些东西

573
00:23:26,920 --> 00:23:29,200
那么下节课我们再来说代码啊

574
00:23:29,200 --> 00:23:30,400
我们这节课先这么多


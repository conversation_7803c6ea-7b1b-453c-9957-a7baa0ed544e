1
00:00:09,480 --> 00:00:12,419
ok这节课咱们把这个ui加一下啊

2
00:00:12,419 --> 00:00:13,740
其实非常简单啊

3
00:00:13,740 --> 00:00:15,900
首先我们这个是个游戏场景

4
00:00:15,900 --> 00:00:18,449
所以我们应该把它当做第二个场景

5
00:00:18,449 --> 00:00:21,390
除此之外我们还应该有一个场景啊

6
00:00:21,390 --> 00:00:23,039
我们创建一个新的场景

7
00:00:23,940 --> 00:00:25,079
取个名

8
00:00:25,379 --> 00:00:26,250
然后呢

9
00:00:26,250 --> 00:00:29,460
首先它的这个大小我们也一样一样

10
00:00:29,460 --> 00:00:30,960
改一下啊

11
00:00:30,960 --> 00:00:34,259
那么大小是250x190

12
00:00:34,560 --> 00:00:36,759
所以我们到它里面也改一下

13
00:00:41,399 --> 00:00:43,640
那么在这个新的场景里面

14
00:00:43,640 --> 00:00:45,619
我们只需要做一件事儿

15
00:00:45,619 --> 00:00:48,359
创建一个单色的精灵

16
00:00:49,000 --> 00:00:52,259
然后把它填充满

17
00:00:55,859 --> 00:00:57,779
来点灰色的颜色

18
00:00:58,219 --> 00:01:00,740
然后我们给他写个标题啊

19
00:01:00,740 --> 00:01:03,359
我们给他来个label去写个标题

20
00:01:06,000 --> 00:01:08,060
比如说叫做打小鸟

21
00:01:10,159 --> 00:01:11,640
这个名字很不好

22
00:01:12,099 --> 00:01:15,719
这个爱护动物的这个看不过去了

23
00:01:15,719 --> 00:01:16,799
是不是啊哈哈

24
00:01:16,799 --> 00:01:19,280
咱们咱们这个就忽视了啊

25
00:01:19,340 --> 00:01:22,819
然后在这里我们比如说开始界面一个标题

26
00:01:22,819 --> 00:01:24,280
我们再来一个button

27
00:01:26,480 --> 00:01:28,930
啊这个button我们就叫它开始游戏

28
00:01:28,930 --> 00:01:30,700
当然这里我们是没有素材的

29
00:01:30,700 --> 00:01:32,409
实际上大家可以找一些素材

30
00:01:32,409 --> 00:01:34,599
然后比如来个背景图片啊

31
00:01:34,599 --> 00:01:36,030
按钮什么的是吧

32
00:01:36,030 --> 00:01:38,969
我们都可以搞一些好看的素材啊

33
00:01:38,969 --> 00:01:40,319
看起来就比较有效果

34
00:01:42,099 --> 00:01:43,920
那我们在这里给它改下名

35
00:01:46,019 --> 00:01:47,920
开始游戏

36
00:01:49,700 --> 00:01:52,500
文字大小的话我们小一点

37
00:01:53,260 --> 00:01:56,560
button的话我们也给它小一点

38
00:01:59,560 --> 00:02:03,299
ok我们都放到canvas下面

39
00:02:04,819 --> 00:02:07,599
然后在这里我们点开始游戏

40
00:02:07,599 --> 00:02:10,960
我们让我们要让它跳转到我们的这个界面啊

41
00:02:10,960 --> 00:02:12,558
游戏界面啊

42
00:02:12,558 --> 00:02:13,399
这个简陋点

43
00:02:13,399 --> 00:02:14,449
这个没关系啊

44
00:02:14,449 --> 00:02:16,579
啊这个逻辑是这样一个逻辑

45
00:02:16,579 --> 00:02:19,439
大家自己去找其他素材就可以了啊

46
00:02:21,459 --> 00:02:24,058
或者是我们也可以嗯

47
00:02:24,058 --> 00:02:26,900
我们可以把这个背景放上来

48
00:02:31,519 --> 00:02:34,459
那是不是有这样一个背景点

49
00:02:34,459 --> 00:02:35,960
开始游戏就进去了

50
00:02:36,539 --> 00:02:39,900
然后我们创建一个脚本

51
00:02:41,219 --> 00:02:43,039
开始的脚本

52
00:02:44,959 --> 00:02:46,658
挂在这个卡洛斯上面

53
00:02:48,699 --> 00:02:50,240
在这个脚本里面

54
00:02:52,379 --> 00:02:54,539
我们需要有什么内容

55
00:02:55,479 --> 00:02:56,280
很简单啊

56
00:02:56,280 --> 00:03:01,819
在这个里面就需要一个start game

57
00:03:02,919 --> 00:03:05,159
一个方法叫做开始游戏

58
00:03:05,159 --> 00:03:06,360
只要开始游戏

59
00:03:06,360 --> 00:03:08,039
我们就让它加载场景

60
00:03:08,039 --> 00:03:09,629
加载我们的游戏场景

61
00:03:09,629 --> 00:03:11,558
game场景啊

62
00:03:11,558 --> 00:03:12,669
就这一个方法

63
00:03:12,669 --> 00:03:19,080
然后在这边关联一下button事件事件加一个事件

64
00:03:20,400 --> 00:03:21,960
把cos拖过来

65
00:03:22,139 --> 00:03:23,969
找到咱们的这个脚本

66
00:03:23,969 --> 00:03:25,259
start control

67
00:03:25,259 --> 00:03:31,099
在里面找到咱们的这个start game这个回调关联上

68
00:03:31,759 --> 00:03:33,580
然后我们运行一下

69
00:03:34,899 --> 00:03:38,399
这时候我们来看它就显示这个主场景了

70
00:03:38,399 --> 00:03:41,879
点开始游戏就进入到我们的游戏场景了

71
00:03:41,879 --> 00:03:44,879
当游戏场景结束的时候

72
00:03:45,539 --> 00:03:48,860
我们再让他跳出来一个ui界面啊

73
00:03:48,860 --> 00:03:51,479
那我们回来继续来继续来做

74
00:03:53,778 --> 00:03:56,900
嗯回到game里面

75
00:03:59,399 --> 00:04:05,938
在这边我们canvas上面创建一个单色的节点啊

76
00:04:05,938 --> 00:04:10,639
我们给它起个名字叫做这个返回视图

77
00:04:10,639 --> 00:04:11,639
bank you

78
00:04:13,020 --> 00:04:17,160
然后我们把它的宽高稍微改一改

79
00:04:19,399 --> 00:04:21,680
颜色稍微变一变

80
00:04:24,579 --> 00:04:27,480
然后我们这里加一个label

81
00:04:29,939 --> 00:04:33,500
叫做返回主视图

82
00:04:36,120 --> 00:04:37,800
啊我们叫游戏结束吧

83
00:04:37,800 --> 00:04:39,160
游戏结束

84
00:04:42,199 --> 00:04:43,838
放到这里

85
00:04:43,838 --> 00:04:47,579
然后在这个视图上面还有两个button

86
00:04:50,420 --> 00:04:51,879
我们加一个好了

87
00:04:51,879 --> 00:04:53,319
加一个就行

88
00:04:53,319 --> 00:04:56,779
button的话我们也是给它改一下

89
00:04:58,839 --> 00:05:06,240
文字改一下叫做返回主页

90
00:05:06,240 --> 00:05:08,759
或者就要返回就好了啊

91
00:05:08,759 --> 00:05:11,160
一返回就返回到这个主页面了

92
00:05:13,639 --> 00:05:14,499
然后啊

93
00:05:16,778 --> 00:05:18,579
这边完成以后啊

94
00:05:18,579 --> 00:05:19,980
这边完成以后

95
00:05:20,399 --> 00:05:23,040
我们希望啊让他有这个返回

96
00:05:23,040 --> 00:05:27,569
点击返回会有返回到这个主持主持主这个场景的功能

97
00:05:27,569 --> 00:05:30,639
那我们找到我们的代码

98
00:05:30,639 --> 00:05:35,740
到我们这个小鸟管理类里面添加一个方法

99
00:05:36,899 --> 00:05:39,740
方法就是bk

100
00:05:42,560 --> 00:05:43,220
六

101
00:05:45,399 --> 00:05:47,379
啊bk view就是返回

102
00:05:47,379 --> 00:05:50,500
返回的话我们直接cc.direct

103
00:05:50,500 --> 00:05:52,720
点load加载场景

104
00:05:52,720 --> 00:05:56,300
加载这个start场景就可以了

105
00:05:56,300 --> 00:05:59,819
然后在这边我们也是要关联一下这个按钮

106
00:06:01,279 --> 00:06:02,740
加一个事件

107
00:06:05,220 --> 00:06:06,920
小鸟管理类拖过来

108
00:06:06,920 --> 00:06:08,560
找到咱们的脚本

109
00:06:09,079 --> 00:06:11,588
把qq关联一下就好了

110
00:06:11,588 --> 00:06:13,869
那这样的话我们运行一下

111
00:06:13,869 --> 00:06:20,238
我们试一下返回你看就返回到我们的最开始的场景了

112
00:06:20,238 --> 00:06:23,389
但是这个界面默认是不显示的

113
00:06:23,389 --> 00:06:24,339
对不对

114
00:06:27,439 --> 00:06:31,759
那么我们在这里给他来写一下啊

115
00:06:35,319 --> 00:06:36,629
我们看一下啊

116
00:06:36,629 --> 00:06:37,740
我们要让他上来

117
00:06:37,740 --> 00:06:39,240
就是隐藏状态

118
00:06:39,459 --> 00:06:42,220
我们先给它把这个激活关掉

119
00:06:42,220 --> 00:06:44,860
我们控制让它隐藏还是显示

120
00:06:44,860 --> 00:06:48,639
就是来控制当前这个节点的激活状态啊

121
00:06:48,639 --> 00:06:50,779
是激活的还是非激活的啊

122
00:06:50,779 --> 00:06:51,439
他就一会儿开

123
00:06:51,439 --> 00:06:52,220
一会儿关了

124
00:06:52,220 --> 00:06:54,050
默认应该是非激活的

125
00:06:54,050 --> 00:06:55,699
怎样让它变成激活的呢

126
00:06:55,699 --> 00:06:57,560
游戏结束就让它变成激活的

127
00:06:57,560 --> 00:07:02,009
所以在这里我们应该写这样一个东西

128
00:07:02,009 --> 00:07:03,178
除了一个分数标签

129
00:07:03,178 --> 00:07:04,939
有一个返回视图

130
00:07:10,259 --> 00:07:16,300
返回视图是cc.node叫bank view

131
00:07:17,680 --> 00:07:18,360
cc

132
00:07:18,360 --> 00:07:19,079
两人note

133
00:07:21,600 --> 00:07:25,259
本科没有啊

134
00:07:25,259 --> 00:07:26,699
下面那个重名了

135
00:07:26,699 --> 00:07:27,899
下面已经关联了

136
00:07:27,899 --> 00:07:29,779
那我们直接改这个好了

137
00:07:31,139 --> 00:07:33,678
will加个几个好的

138
00:07:35,658 --> 00:07:42,379
然后我们过来关联一下fq到这边把这个视图关联一下

139
00:07:44,660 --> 00:07:46,839
然后在这边只要我们死了

140
00:07:46,839 --> 00:07:47,680
游戏结束

141
00:07:47,680 --> 00:07:49,500
这里死亡了

142
00:07:49,500 --> 00:07:49,920
对不对

143
00:07:49,920 --> 00:07:51,420
我们就不输出死亡了

144
00:07:51,420 --> 00:07:53,699
我们就要让它显示这个视图

145
00:07:53,699 --> 00:07:57,759
bank will.active

146
00:07:57,759 --> 00:07:59,560
就把它设置为true啊

147
00:07:59,560 --> 00:08:00,519
本来是force

148
00:08:00,519 --> 00:08:02,480
我们就应该把它设置为true就可以了

149
00:08:02,480 --> 00:08:04,490
那现在我们来运行看一下效果

150
00:08:04,490 --> 00:08:06,920
首先我们回到这个开始界面

151
00:08:09,120 --> 00:08:11,180
大家可以看开始游戏对不对

152
00:08:11,180 --> 00:08:12,740
然后大家就可以玩游戏了

153
00:08:14,360 --> 00:08:15,579
有这个分数

154
00:08:17,360 --> 00:08:20,839
当然这个比如说你哪次没点着死亡了

155
00:08:23,459 --> 00:08:26,290
死亡的时候就会弹出这个界面

156
00:08:26,290 --> 00:08:27,879
然后就会弹出这个界面

157
00:08:27,879 --> 00:08:29,410
弹出这个界面以后

158
00:08:29,410 --> 00:08:31,990
我们就点返回返回就到这个界面了

159
00:08:31,990 --> 00:08:33,879
你就可以开始新一轮的游戏了

160
00:08:34,460 --> 00:08:35,659
对不对

161
00:08:35,659 --> 00:08:36,859
ok啊

162
00:08:37,639 --> 00:08:41,500
那么这个啊就是大概是这样的一个游戏啊

163
00:08:41,500 --> 00:08:43,179
很简单啊很简单

164
00:08:43,179 --> 00:08:47,480
但是它里面用到了需要大家理解的东西啊

165
00:08:47,480 --> 00:08:49,399
就是这个回调啊

166
00:08:49,399 --> 00:08:51,740
其实你就算是很复杂的大项目

167
00:08:51,740 --> 00:08:53,779
它无非就是代码量大啊

168
00:08:53,779 --> 00:08:58,419
他男的他在代码上面绕的男的其实没多少

169
00:08:58,419 --> 00:09:01,480
主要也就是这个毁掉了啊

170
00:09:01,480 --> 00:09:05,289
所以你当然第一第一件事就是要把这个回调完全搞清楚

171
00:09:05,289 --> 00:09:06,279
就在这里

172
00:09:06,279 --> 00:09:07,599
为什么用回调

173
00:09:07,759 --> 00:09:09,139
如果你不知道

174
00:09:09,139 --> 00:09:11,419
如果你在这怎么样都理解不了这个回调啊

175
00:09:11,419 --> 00:09:12,889
在这儿为什么用回调

176
00:09:12,889 --> 00:09:16,440
最简单的方式就是你不要看我的代码

177
00:09:16,440 --> 00:09:17,879
你自己去实现啊

178
00:09:17,879 --> 00:09:19,440
就是你自己去写这两个脚本

179
00:09:19,440 --> 00:09:20,100
一个管理类

180
00:09:20,100 --> 00:09:22,379
一个是小鸟这样的一个类

181
00:09:22,379 --> 00:09:24,318
然后你自己去实现代码

182
00:09:24,318 --> 00:09:25,818
你自己实现的实现

183
00:09:25,818 --> 00:09:31,190
你就发现诶在写这个游戏结束和这个加分的时候就不太好写了

184
00:09:31,190 --> 00:09:33,440
你自己就会去想怎样去解决

185
00:09:33,440 --> 00:09:38,000
然后你再去再再来听一下咱们这个课程啊

186
00:09:38,000 --> 00:09:38,899
再来听一下课程

187
00:09:38,899 --> 00:09:41,419
再来听一下这个回调到底是用来做什么的

188
00:09:41,419 --> 00:09:43,210
应该就会了解了啊

189
00:09:43,210 --> 00:09:46,450
所以实际上最好的方式还是自己去动手啊

190
00:09:46,450 --> 00:09:49,889
很多同学就是光听就傻愣着听一遍啊

191
00:09:49,889 --> 00:09:52,169
就是这个视频里面都敲了一遍代码了

192
00:09:52,169 --> 00:09:54,210
自己只是听一遍是吧

193
00:09:54,210 --> 00:09:57,460
这种情况下你这太懒了啊

194
00:09:57,460 --> 00:09:59,859
这个学习方法就是不对的

195
00:10:00,879 --> 00:10:01,649
ok啊

196
00:10:01,649 --> 00:10:04,679
那么我们这个项目就先先到这里啊

197
00:10:04,679 --> 00:10:05,460
这个项目完了

198
00:10:05,460 --> 00:10:10,389
我们后面也可以拿这个项目来做一些这个呃其他的东西啊

199
00:10:10,389 --> 00:10:12,850
其他的事比如说咱们的打包啊

200
00:10:12,850 --> 00:10:15,249
咱们就拿这个项目来做例子了啊

201
00:10:15,249 --> 00:10:17,889
所以这个项目大家自己都要写一下啊

202
00:10:17,889 --> 00:10:18,958
都要写一下

203
00:10:20,600 --> 00:10:21,600
略略略


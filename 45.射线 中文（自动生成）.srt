1
00:00:09,779 --> 00:00:13,919
ok这节课我们继续我们的物理引擎部分

2
00:00:14,640 --> 00:00:16,879
我们上节课说了碰撞啊

3
00:00:16,879 --> 00:00:20,719
但是我们这里大家记住我们有一个属性啊

4
00:00:20,719 --> 00:00:23,420
有一个选项没有去说

5
00:00:23,539 --> 00:00:25,489
那么这个选项啊

6
00:00:25,489 --> 00:00:26,660
这个选项是什么

7
00:00:26,660 --> 00:00:29,140
它是传感器选项啊

8
00:00:29,140 --> 00:00:30,760
如果我把它勾上了

9
00:00:30,760 --> 00:00:32,079
那么从现在开始

10
00:00:32,079 --> 00:00:34,689
这个碰撞体就变成了一个传感器了

11
00:00:34,689 --> 00:00:35,380
注意啊

12
00:00:35,380 --> 00:00:38,299
是完全的就是变成传感器了

13
00:00:38,299 --> 00:00:40,310
也就是说我不勾选它

14
00:00:40,310 --> 00:00:43,170
它呢这一项叫做碰撞体

15
00:00:43,170 --> 00:00:45,990
我勾选上它这一项就不叫碰撞体了

16
00:00:45,990 --> 00:00:46,890
它就叫传感器

17
00:00:46,890 --> 00:00:48,338
传感器是什么意思啊

18
00:00:48,460 --> 00:00:51,340
呃我们或者把它叫做触发器啊

19
00:00:51,340 --> 00:00:53,350
传感器触发器都可以啊

20
00:00:53,350 --> 00:00:55,518
那比如说我把它勾上

21
00:00:56,560 --> 00:00:59,159
勾上以后特点是什么啊

22
00:00:59,159 --> 00:01:01,229
这个传感器的特点是什么

23
00:01:01,229 --> 00:01:06,549
特点就是本来我这个小鸟掉下来会和它产生碰撞

24
00:01:06,549 --> 00:01:07,328
对不对

25
00:01:07,328 --> 00:01:11,650
但是一旦我这个地面变成了这个触发器了

26
00:01:11,650 --> 00:01:14,590
或者或者说小鸟有一个变成触发器了

27
00:01:14,590 --> 00:01:17,459
那么触发器它是可以被穿过去的

28
00:01:17,459 --> 00:01:19,230
它是可以被穿过去的

29
00:01:19,230 --> 00:01:21,629
也就是说你看现在地面是触发器

30
00:01:21,629 --> 00:01:25,670
那么我这个小鸟实际上是可以穿过它的

31
00:01:25,670 --> 00:01:27,569
就是完全忽视它的存在

32
00:01:27,569 --> 00:01:29,069
但然后有同学说

33
00:01:29,069 --> 00:01:30,150
那这有什么用

34
00:01:30,150 --> 00:01:31,650
那这不等于白加他了

35
00:01:31,650 --> 00:01:32,489
我就直接我

36
00:01:32,489 --> 00:01:33,989
那我还不如把他也删了

37
00:01:33,989 --> 00:01:35,079
钢铁也删了

38
00:01:35,079 --> 00:01:35,980
那得了

39
00:01:35,980 --> 00:01:36,280
是不是

40
00:01:36,280 --> 00:01:37,840
那我要这个有什么用

41
00:01:37,840 --> 00:01:40,439
那么这里啊这个特别有用

42
00:01:40,439 --> 00:01:41,969
比如说我选中地面

43
00:01:41,969 --> 00:01:42,959
我把它勾上

44
00:01:44,099 --> 00:01:47,659
这时候这时候我运行一下

45
00:01:47,659 --> 00:01:49,579
首先我们看一下那个碰撞啊

46
00:01:49,579 --> 00:01:53,439
碰撞的这个方法还会不会发生碰撞

47
00:01:53,439 --> 00:01:55,679
那结束碰撞我们也可以写出来

48
00:01:56,379 --> 00:02:00,780
constl点是debug结束碰撞

49
00:02:03,019 --> 00:02:04,439
我们运行一下

50
00:02:05,859 --> 00:02:08,250
我们看小鸟是不是直接穿过它了

51
00:02:08,250 --> 00:02:10,259
由于它的类型变成传感器了

52
00:02:16,159 --> 00:02:20,189
但是它实际上是可以被其他缸体穿过的

53
00:02:20,189 --> 00:02:24,280
但是我们再看发生碰撞

54
00:02:24,280 --> 00:02:25,210
结束碰撞

55
00:02:25,210 --> 00:02:28,060
也就是说实际上虽然我们能穿过去

56
00:02:28,060 --> 00:02:32,349
但是它的碰撞方法依然是会被调用的啊

57
00:02:32,349 --> 00:02:34,389
碰撞方法依然会被调用的

58
00:02:34,389 --> 00:02:38,409
而且它的这个点是得不到的了啊

59
00:02:38,409 --> 00:02:39,250
就是你碰撞的话

60
00:02:39,250 --> 00:02:40,990
你会得到碰撞点和碰撞法线

61
00:02:40,990 --> 00:02:41,469
对不对

62
00:02:41,469 --> 00:02:43,389
但是如果你变成传感器了

63
00:02:43,389 --> 00:02:45,250
它只会调用这两个方法

64
00:02:45,250 --> 00:02:48,338
但是这个内容就没有了啊

65
00:02:48,338 --> 00:02:50,558
你顶多能获取一下这两个内容啊

66
00:02:50,558 --> 00:02:53,259
就是我穿过的传感器啊

67
00:02:53,259 --> 00:02:55,659
他是是是谁啊

68
00:02:55,659 --> 00:02:57,939
可以通过他的tag值判断一些东西

69
00:02:57,939 --> 00:03:00,039
但是具体的点呀

70
00:03:00,039 --> 00:03:02,069
法线呀你就获取不了了啊

71
00:03:02,069 --> 00:03:05,430
你只能知道我发生碰撞和结束碰撞

72
00:03:05,430 --> 00:03:07,770
那这个为什么说这个特别有用的

73
00:03:07,770 --> 00:03:09,689
做游戏太多这种东西了

74
00:03:09,689 --> 00:03:11,939
你比如说小鸟往下走

75
00:03:11,939 --> 00:03:14,719
那我可以把这个渲染精灵渲染关了

76
00:03:15,539 --> 00:03:20,419
注意现在我们在这儿知道这儿有一个方形的一个碰撞器

77
00:03:20,419 --> 00:03:21,639
对不对啊

78
00:03:21,639 --> 00:03:23,620
而且它是一个传感器类型的啊

79
00:03:23,620 --> 00:03:24,729
它就是一个传感器

80
00:03:24,729 --> 00:03:26,379
但是我们游戏运行起来

81
00:03:26,379 --> 00:03:27,610
这是什么也没有的

82
00:03:27,610 --> 00:03:30,159
看似鸟是直接掉了下去

83
00:03:30,159 --> 00:03:34,199
实际上他触发了这个碰撞方法啊

84
00:03:34,199 --> 00:03:35,879
他触发了这个碰撞方法

85
00:03:35,879 --> 00:03:38,368
那么大部分游戏啊

86
00:03:38,368 --> 00:03:43,189
它的嗯大部分的事件都是通过这个传感器做的啊

87
00:03:43,189 --> 00:03:44,030
举一些例子啊

88
00:03:44,030 --> 00:03:44,990
举一些小例子

89
00:03:44,990 --> 00:03:46,370
比如说我们玩一个rpg游戏

90
00:03:46,370 --> 00:03:47,800
一个人啊

91
00:03:47,800 --> 00:03:49,120
一个人走走走

92
00:03:49,120 --> 00:03:50,680
走到一个区域啊

93
00:03:50,680 --> 00:03:52,509
突然这个区域就开始刷怪了

94
00:03:52,509 --> 00:03:53,889
你不走到这不刷怪

95
00:03:53,889 --> 00:03:55,150
走到这就刷怪了

96
00:03:55,150 --> 00:03:56,689
他怎么知道你走到这儿了

97
00:03:56,689 --> 00:03:58,789
就是这里这个位置啊

98
00:03:58,789 --> 00:04:00,800
这个区域有这样一个传感器

99
00:04:00,800 --> 00:04:03,270
你进来就发生这个碰撞了啊

100
00:04:03,270 --> 00:04:05,099
但是你又可以穿过它

101
00:04:05,099 --> 00:04:06,330
所以在游戏世界里

102
00:04:06,330 --> 00:04:08,039
你感觉不到你碰到东西了

103
00:04:08,039 --> 00:04:10,189
可实际上你碰到这个碰撞器了

104
00:04:10,189 --> 00:04:12,770
那么在代码里面我们就可以判断诶

105
00:04:12,770 --> 00:04:13,969
有玩家进来了

106
00:04:13,969 --> 00:04:15,860
玩家进来我们这里就刷怪

107
00:04:16,139 --> 00:04:17,240
对不对

108
00:04:18,218 --> 00:04:20,738
那很多这个怪都是这样去做的啊

109
00:04:20,738 --> 00:04:21,579
都是通过这个东西

110
00:04:21,579 --> 00:04:23,319
我们走到一个位置就开始刷怪啊

111
00:04:23,319 --> 00:04:24,009
都是这样的

112
00:04:24,009 --> 00:04:28,449
也有可能比如说很多以前的rpg游戏有那个隐藏宝箱

113
00:04:28,449 --> 00:04:29,680
隐藏那个宝物

114
00:04:29,680 --> 00:04:30,040
对不对

115
00:04:30,040 --> 00:04:31,930
我们比如说走到一棵树的下面

116
00:04:31,930 --> 00:04:33,230
按下空格

117
00:04:33,230 --> 00:04:35,939
唉我们就获得一个苹果啊

118
00:04:35,939 --> 00:04:38,100
难道真是这个树给了我们一颗苹果吗

119
00:04:38,100 --> 00:04:38,850
肯定不是

120
00:04:38,850 --> 00:04:42,860
实际上就是这个树的周围有一个这个传感器

121
00:04:42,860 --> 00:04:45,050
我们人走到这个传感器里面

122
00:04:45,050 --> 00:04:46,100
我们按空格

123
00:04:46,100 --> 00:04:50,269
这时候空格它就会判断我们在不在这个传感器里面

124
00:04:50,269 --> 00:04:54,540
也就是说实际上就是有没有进入到这个发生碰撞

125
00:04:54,540 --> 00:04:56,009
如果发生碰撞了

126
00:04:56,009 --> 00:04:57,420
我们还按了空格了

127
00:04:57,420 --> 00:04:58,560
他就给我们一个苹果

128
00:04:58,560 --> 00:05:00,959
那就证明我们是在树的周围按的苹果

129
00:05:00,959 --> 00:05:02,220
对不对啊

130
00:05:02,220 --> 00:05:06,220
所以说这个特别有用啊

131
00:05:06,538 --> 00:05:09,178
那么大家现在先思考思考是吧

132
00:05:09,178 --> 00:05:11,158
如果你想不出来哪里有用啊

133
00:05:11,158 --> 00:05:15,040
咱们后面还是呃用它写一些东西啊

134
00:05:15,040 --> 00:05:16,660
大家就会逐渐清楚了啊

135
00:05:16,660 --> 00:05:18,000
也不用太纠结

136
00:05:18,718 --> 00:05:21,259
那我们先继续往后面去走

137
00:05:21,259 --> 00:05:23,060
除了他还物理

138
00:05:23,060 --> 00:05:26,629
在物理这个世界里面还有一个东西很有用啊

139
00:05:26,629 --> 00:05:27,980
什么什么东西很有用

140
00:05:27,980 --> 00:05:30,889
就叫法线呃

141
00:05:30,889 --> 00:05:32,120
呃不是不是什么法线

142
00:05:32,120 --> 00:05:33,379
就叫射线啊

143
00:05:33,379 --> 00:05:34,579
射线也是很有用的

144
00:05:34,579 --> 00:05:36,079
我们来这画个图吧

145
00:05:36,759 --> 00:05:38,519
射线是做什么的

146
00:05:38,519 --> 00:05:41,038
比如说有两个物体它会产生碰撞

147
00:05:41,038 --> 00:05:42,399
比如说这是第一个物体

148
00:05:43,560 --> 00:05:45,660
比如说这是第二个物体

149
00:05:46,759 --> 00:05:50,019
这两个物体如果比如说碰到一起了

150
00:05:50,019 --> 00:05:51,098
他们会产生碰撞

151
00:05:51,098 --> 00:05:51,668
对不对

152
00:05:51,668 --> 00:05:53,738
哎我们这个方法是可以检测出来

153
00:05:53,738 --> 00:05:54,930
不管怎样碰

154
00:05:54,930 --> 00:05:59,550
哎我们这个方法都可以检测出来这两个物体产生碰撞了啊

155
00:05:59,550 --> 00:06:00,329
这是没问题的

156
00:06:00,329 --> 00:06:01,139
对不对

157
00:06:01,139 --> 00:06:04,259
那么如果这两个物体现在本身都有物理特性

158
00:06:04,259 --> 00:06:06,399
但是他俩没挨在一起的话

159
00:06:06,699 --> 00:06:09,100
我们可以打一条线啊

160
00:06:09,100 --> 00:06:10,540
我们可以定两个点

161
00:06:10,540 --> 00:06:12,339
第一个点是起始点

162
00:06:12,740 --> 00:06:14,720
第二个点是结束点

163
00:06:14,720 --> 00:06:16,259
比如说这个是结束点

164
00:06:16,300 --> 00:06:21,279
我们可以从起始点到结束点打出一条射线啊

165
00:06:21,279 --> 00:06:23,230
其实就是打出一条这样的线

166
00:06:23,230 --> 00:06:25,019
这个线是看不见的

167
00:06:25,019 --> 00:06:26,339
就是玩家看不见的

168
00:06:26,339 --> 00:06:28,369
但是我们知道啊

169
00:06:28,369 --> 00:06:32,019
我们从这个点到上面那个点打出了一条射线

170
00:06:32,259 --> 00:06:34,360
这时候我们就可以怎样

171
00:06:35,379 --> 00:06:40,548
我们可以得到这个线有没有穿过哪些钢铁

172
00:06:40,548 --> 00:06:42,709
如果穿穿穿过了

173
00:06:42,709 --> 00:06:44,028
我们甚至可以得到

174
00:06:44,028 --> 00:06:46,980
比如说穿过去的这一点的信息啊

175
00:06:46,980 --> 00:06:49,019
比如说我我把我把这一点信息拿出来

176
00:06:49,019 --> 00:06:49,920
我就知道诶

177
00:06:49,920 --> 00:06:53,990
从这点到这一点中间穿过的这样的一个点啊

178
00:06:53,990 --> 00:06:55,220
穿过了这样的一个点

179
00:06:55,220 --> 00:07:01,439
那么射线射线用来检测这个碰撞也是比较有用的啊

180
00:07:01,439 --> 00:07:02,399
也是比较有用的

181
00:07:02,399 --> 00:07:05,500
这个估计咱很多同学更更不太好想的

182
00:07:05,500 --> 00:07:05,860
对不对

183
00:07:05,860 --> 00:07:07,379
这个东西哪里有用

184
00:07:09,160 --> 00:07:10,560
举这样一个例子啊

185
00:07:10,560 --> 00:07:11,899
举这样一个例子吧

186
00:07:13,360 --> 00:07:14,939
我们知道做游戏的时候

187
00:07:14,939 --> 00:07:16,410
很多时候我们会做敌人

188
00:07:16,410 --> 00:07:17,189
对不对

189
00:07:17,189 --> 00:07:19,050
比如说我们做一个

190
00:07:19,050 --> 00:07:20,939
我们做一个敌人

191
00:07:21,899 --> 00:07:23,600
我们先说游戏场景啊

192
00:07:23,600 --> 00:07:25,009
先做好一个游戏场景

193
00:07:25,009 --> 00:07:27,740
比如说我们这一个游戏场景是这样

194
00:07:32,459 --> 00:07:34,279
首先四面都是墙

195
00:07:34,279 --> 00:07:35,339
对不对

196
00:07:42,740 --> 00:07:46,779
哎首先这是我们的一个游戏场景啊

197
00:07:46,779 --> 00:07:48,360
这是我们一个游戏场景

198
00:07:48,819 --> 00:07:51,819
可能我们游戏场景里面是个迷宫啊

199
00:07:51,819 --> 00:07:52,600
是个迷宫

200
00:07:52,600 --> 00:07:54,759
然后我们在这个迷宫里面打怪

201
00:07:54,759 --> 00:07:55,779
我这是一个最简单的

202
00:07:55,779 --> 00:07:56,970
就是举个例子啊

203
00:07:56,970 --> 00:07:58,470
比如说这里有个敌人

204
00:08:00,089 --> 00:08:01,350
这里有个敌人

205
00:08:01,350 --> 00:08:03,000
那么敌人

206
00:08:05,639 --> 00:08:07,459
敌人眼睛在哪里

207
00:08:07,459 --> 00:08:08,718
眼睛在这里

208
00:08:08,718 --> 00:08:14,160
也就是说他的前方是在这里的啊

209
00:08:14,160 --> 00:08:15,839
它的前方是在这里的

210
00:08:18,060 --> 00:08:20,000
那么注意啊

211
00:08:20,000 --> 00:08:21,220
我们作为一个玩家

212
00:08:21,220 --> 00:08:23,079
玩家是不是我们自己控制的啊

213
00:08:23,079 --> 00:08:24,100
我们是个玩家

214
00:08:24,100 --> 00:08:27,160
我们这个前后左右移动全是我们自己控制的

215
00:08:27,160 --> 00:08:28,610
我们可能在这吃东西

216
00:08:28,610 --> 00:08:30,860
然后怪物在这儿巡逻

217
00:08:30,860 --> 00:08:32,149
找我找我们

218
00:08:32,149 --> 00:08:32,899
对不对

219
00:08:32,899 --> 00:08:34,580
那么这时候有一个问题啊

220
00:08:34,580 --> 00:08:35,779
我们要做一个算法

221
00:08:35,779 --> 00:08:36,710
算法是什么

222
00:08:36,710 --> 00:08:41,389
让怪物在这个场景里面前后左右自动走啊

223
00:08:41,389 --> 00:08:42,259
就是自动走

224
00:08:42,259 --> 00:08:44,870
比如说默认在这儿我们写个脚本

225
00:08:44,870 --> 00:08:47,830
写个这个怪物的这个ai脚本

226
00:08:47,830 --> 00:08:49,990
就是要给他去写一些逻辑了

227
00:08:49,990 --> 00:08:50,529
对不对

228
00:08:50,529 --> 00:08:53,419
让这个怪物有了ai啊

229
00:08:53,419 --> 00:08:55,220
有了b就是有了这个智能了

230
00:08:55,220 --> 00:08:55,980
对不对

231
00:08:55,980 --> 00:08:58,769
那么当他ai系统写完以后

232
00:08:58,769 --> 00:09:00,899
我们的逻辑ai的逻辑是什么

233
00:09:00,899 --> 00:09:03,019
让怪物向前方移动

234
00:09:03,179 --> 00:09:05,519
如果前方无比

235
00:09:05,519 --> 00:09:11,339
如说前方100像素以内有一堵墙

236
00:09:11,700 --> 00:09:15,039
我们就让它转向另外一个方向

237
00:09:15,279 --> 00:09:17,139
如果另外一个方向还有墙

238
00:09:17,139 --> 00:09:18,340
我们就让他再转

239
00:09:18,340 --> 00:09:20,649
这样的话这个怪物就可以怎样了

240
00:09:20,649 --> 00:09:22,889
就可以不会碰到墙了啊

241
00:09:22,889 --> 00:09:24,090
如果没有这个检测

242
00:09:24,090 --> 00:09:25,590
我们这个怪物让他走走走

243
00:09:25,590 --> 00:09:27,750
是不是最后直接碰墙就挤到墙边了

244
00:09:27,750 --> 00:09:29,059
一直在这对吧

245
00:09:29,059 --> 00:09:30,259
那这个游戏肯定不好

246
00:09:30,259 --> 00:09:33,379
我们最简单的要做的这个ai就是往前走走走

247
00:09:33,379 --> 00:09:33,620
诶

248
00:09:33,620 --> 00:09:35,360
发现前面一堵墙还没到墙

249
00:09:35,360 --> 00:09:37,700
那呢发现一堵墙就转

250
00:09:37,700 --> 00:09:39,980
然后他去检测这这边啊

251
00:09:39,980 --> 00:09:41,480
比如说他从这个方向转

252
00:09:41,480 --> 00:09:43,279
它检测先转90度

253
00:09:43,279 --> 00:09:45,379
检测这边发现这边也是墙

254
00:09:45,379 --> 00:09:47,710
再再转转到这边

255
00:09:47,710 --> 00:09:48,789
检测这边没墙

256
00:09:48,789 --> 00:09:50,440
他就诶继续走

257
00:09:50,440 --> 00:09:52,219
走到这里以后

258
00:09:52,559 --> 00:09:53,789
他发现怎样

259
00:09:53,789 --> 00:09:54,990
这里有墙了

260
00:09:54,990 --> 00:09:57,299
他是往往这个方向转的对吧

261
00:09:57,299 --> 00:09:58,620
转过来这边没墙

262
00:09:58,620 --> 00:09:59,220
他就走了

263
00:09:59,220 --> 00:09:59,980
走了走

264
00:09:59,980 --> 00:10:01,480
然后走到这儿又有墙了

265
00:10:01,480 --> 00:10:03,669
再转转到这边没钱了

266
00:10:03,669 --> 00:10:04,570
走走走走走

267
00:10:04,570 --> 00:10:06,100
走到这边了

268
00:10:06,100 --> 00:10:08,360
先检测右边有墙了

269
00:10:08,360 --> 00:10:10,220
再往下检测下面没墙

270
00:10:10,220 --> 00:10:12,919
所以他可能诶转到下面又开始走走走

271
00:10:12,919 --> 00:10:14,539
所以他最后就在这个区域走了

272
00:10:14,539 --> 00:10:16,460
那这个可能就是怪物的一个流程

273
00:10:16,460 --> 00:10:18,620
这样的话最起码怪物就是一个智能的

274
00:10:18,620 --> 00:10:21,700
会在这个区域内行走的一个怪物啊

275
00:10:21,700 --> 00:10:23,740
会在这个区域内行走的怪物

276
00:10:23,740 --> 00:10:27,059
那么呃怎么做啊

277
00:10:27,059 --> 00:10:28,139
我们想法是好的

278
00:10:28,139 --> 00:10:29,759
怎么做做

279
00:10:29,759 --> 00:10:35,519
就是从他持续地往前方去打出这样的一条射线啊

280
00:10:35,519 --> 00:10:38,159
就是从怪物的这一点往前方

281
00:10:38,159 --> 00:10:40,110
比如说打出这样一条射线

282
00:10:40,110 --> 00:10:46,960
然后这个射线我们知道它是会和这个缸体产生碰撞的啊

283
00:10:46,960 --> 00:10:49,240
比如我们这个墙我们都给它设置为刚体

284
00:10:49,240 --> 00:10:51,399
那我们就诶往前走走走

285
00:10:51,399 --> 00:10:54,460
当这个射线和墙碰到的时候

286
00:10:54,460 --> 00:10:56,320
我们就判断检测到

287
00:10:56,320 --> 00:10:59,750
检测到了就知道诶现在前方就是墙

288
00:10:59,750 --> 00:11:00,759
我们在转

289
00:11:00,759 --> 00:11:02,919
如果转过来这边还碰到了

290
00:11:02,919 --> 00:11:04,870
就证明右边还是墙

291
00:11:04,870 --> 00:11:06,370
我们再转往下转

292
00:11:06,370 --> 00:11:07,240
往下一转

293
00:11:07,240 --> 00:11:09,529
射线没有检测到任何物体

294
00:11:09,529 --> 00:11:11,399
我们就可以让它往下走啊

295
00:11:11,399 --> 00:11:11,938
走走走

296
00:11:11,938 --> 00:11:14,038
射线碰到再让他往这边转

297
00:11:14,038 --> 00:11:16,918
但是由于我们玩家在游戏里看不到这个射线

298
00:11:16,918 --> 00:11:19,220
所以玩家只能看到一个怪物

299
00:11:19,220 --> 00:11:21,958
很智能的在这个场景里面行走啊

300
00:11:22,360 --> 00:11:25,059
那么这个就是射线的含义啊

301
00:11:25,059 --> 00:11:27,879
射线的意义呃

302
00:11:27,879 --> 00:11:29,799
如果你现在听不懂不懂啊

303
00:11:29,799 --> 00:11:31,480
也是这个没关系啊

304
00:11:31,480 --> 00:11:33,309
还是我说的这个也是没关系

305
00:11:33,309 --> 00:11:35,409
这个就是等你啊

306
00:11:35,409 --> 00:11:37,179
把后面的这个

307
00:11:39,019 --> 00:11:41,379
后面的这个比如说射线部分呀

308
00:11:41,379 --> 00:11:42,460
其他动画啊

309
00:11:42,460 --> 00:11:45,059
这个这块重点内容都学完了以后啊

310
00:11:45,059 --> 00:11:49,480
你自己可以尝试自己去写一个类似于我这样的一个游戏啊

311
00:11:49,480 --> 00:11:51,549
就是类似于我刚才说的这个游戏

312
00:11:51,549 --> 00:11:55,960
然后你用这个射线去进行一下判断啊

313
00:11:55,960 --> 00:11:56,559
判断一下

314
00:11:56,559 --> 00:12:00,820
让这个敌人会智能的去在这个迷宫里面行走啊

315
00:12:00,820 --> 00:12:02,649
智能的在这个迷宫里面行走

316
00:12:02,649 --> 00:12:06,269
那么这个咱们之后也会去做一下啊

317
00:12:06,269 --> 00:12:07,799
之后咱们也会去做一下

318
00:12:07,799 --> 00:12:12,029
那么我们这节课先来看一下这个射线怎样去打

319
00:12:12,029 --> 00:12:13,859
怎样去打出这个射线

320
00:12:19,460 --> 00:12:20,899
那么在这里

321
00:12:24,899 --> 00:12:25,860
我们看一下啊

322
00:12:25,860 --> 00:12:29,578
我们看一下我们这个射线在哪里去给它打出来啊

323
00:12:29,578 --> 00:12:31,499
射线方法其实非常简单

324
00:12:31,499 --> 00:12:33,899
比如说我们从这个是小鸟对吧

325
00:12:33,899 --> 00:12:37,950
我就从小鸟这个位置开始往附近去打一条射线啊

326
00:12:37,950 --> 00:12:39,330
我们就把代码写出来好了

327
00:12:39,330 --> 00:12:40,799
大家就知道什么意思了

328
00:12:40,799 --> 00:12:42,938
怎样打出一条射线

329
00:12:46,600 --> 00:12:48,360
首先打出这些很简单

330
00:12:48,360 --> 00:12:50,340
cc点找到导演

331
00:12:50,340 --> 00:12:53,639
得到我们的还是这个啊

332
00:12:53,639 --> 00:12:57,480
物理管理类点直接一个射线检测

333
00:12:57,480 --> 00:12:59,580
射线检测里面有两个点

334
00:12:59,580 --> 00:13:00,960
第一个点可以填写

335
00:13:00,960 --> 00:13:06,440
我们比如说this.transform node

336
00:13:06,440 --> 00:13:07,698
点transform

337
00:13:11,720 --> 00:13:14,289
this点嗯

338
00:13:14,289 --> 00:13:16,938
this.node点

339
00:13:18,259 --> 00:13:19,219
就是这样

340
00:13:24,000 --> 00:13:25,559
get position啊

341
00:13:25,559 --> 00:13:26,879
get position

342
00:13:27,039 --> 00:13:29,799
我们第一个点就得到我们自己的位置啊

343
00:13:29,799 --> 00:13:30,159
晕了

344
00:13:30,159 --> 00:13:31,120
直接用这个position

345
00:13:31,120 --> 00:13:32,679
它这个里面你要得到这个位置

346
00:13:32,679 --> 00:13:33,970
用get position

347
00:13:37,779 --> 00:13:41,349
那么第二个位置我们就要填另外一个点

348
00:13:41,349 --> 00:13:42,548
另外一个点

349
00:13:42,548 --> 00:13:45,759
比如说我这个小鸟就向天上打一条射线

350
00:13:45,759 --> 00:13:48,580
那这样的话你就可以怎样啊

351
00:13:48,580 --> 00:13:52,870
就可以i这个这个这个cc.v我创建一个点

352
00:13:52,870 --> 00:13:56,359
这个点x和我小鸟一样

353
00:13:56,700 --> 00:13:58,620
点x和我小鸟一样

354
00:13:58,620 --> 00:14:03,318
y的话就是我小鸟的y向天上打100

355
00:14:03,740 --> 00:14:06,799
那这样的话实际上实际上就是打了一条射线

356
00:14:06,799 --> 00:14:07,519
是怎样的

357
00:14:07,519 --> 00:14:13,099
是从小鸟的中心向天上打了100的100长度的这样的一个射线

358
00:14:14,259 --> 00:14:17,139
那么这个射线最后怎样了

359
00:14:17,139 --> 00:14:18,220
我们加了呃

360
00:14:18,220 --> 00:14:19,509
这个这个这个打完以后

361
00:14:19,509 --> 00:14:21,399
最后总得有这个返回结果吧

362
00:14:21,399 --> 00:14:24,059
你这个射线到底打完以后是什么情况

363
00:14:24,059 --> 00:14:25,019
先别急

364
00:14:25,019 --> 00:14:28,059
还有一项这一项很重要

365
00:14:28,059 --> 00:14:31,120
射线检测有四种检测类型啊

366
00:14:31,120 --> 00:14:33,610
首先它返回的结果是依据类型的

367
00:14:33,610 --> 00:14:36,139
那么我们知道既然是一个线

368
00:14:36,379 --> 00:14:39,440
他有可能穿过一个钢铁

369
00:14:39,440 --> 00:14:42,720
也有可能穿过怎样多个缸体啊

370
00:14:42,720 --> 00:14:44,879
一个射线可能穿过多个多个缸体

371
00:14:44,879 --> 00:14:45,480
对不对

372
00:14:45,480 --> 00:14:48,269
那么这里的类型它有四个

373
00:14:48,269 --> 00:14:49,759
我们先分一下

374
00:14:49,759 --> 00:14:52,950
下面这两个都是返回一个点啊

375
00:14:52,950 --> 00:14:54,990
上面的都是多个的啊

376
00:14:54,990 --> 00:14:56,519
检测多个物体的

377
00:14:56,519 --> 00:14:59,399
那么再说下面这俩单个的区别

378
00:14:59,860 --> 00:15:03,909
这个any它是会比如说一个射线

379
00:15:03,909 --> 00:15:06,070
射线检测了好多物体

380
00:15:06,070 --> 00:15:09,090
它是会给你返回其中一点啊

381
00:15:09,090 --> 00:15:10,350
它只会给你返回一点

382
00:15:10,350 --> 00:15:12,360
哪一点我们也不确定

383
00:15:12,360 --> 00:15:14,610
而这个就是返回最近的那一点

384
00:15:14,610 --> 00:15:16,679
所以一般我们就可以用这个

385
00:15:16,679 --> 00:15:19,779
那么也就是说用他返回最近的那一点

386
00:15:19,779 --> 00:15:20,860
比如射线这样打的

387
00:15:20,860 --> 00:15:22,759
他就会给我们返回这一点

388
00:15:23,860 --> 00:15:26,440
只会给我们返回第一个物体的这一点

389
00:15:26,440 --> 00:15:29,219
第二个物体什么信息都不会返回

390
00:15:29,840 --> 00:15:31,909
那么这是这个单体的

391
00:15:31,909 --> 00:15:35,600
然后多个呢就是它会返回多个物体的这个信息啊

392
00:15:35,600 --> 00:15:38,578
多个和多个钢铁碰撞的信息

393
00:15:38,578 --> 00:15:41,038
那么区别还是or的话

394
00:15:41,038 --> 00:15:44,938
它会返回一个路径上所有碰撞体的信息

395
00:15:44,938 --> 00:15:48,578
但是有一个问题是什么

396
00:15:48,740 --> 00:15:51,860
它一个物体它可能给你返回多个碰撞点

397
00:15:51,860 --> 00:15:54,710
比如说这里才给你当一个碰撞体碰撞点

398
00:15:54,710 --> 00:15:56,090
这里也给你返回一个点

399
00:15:56,090 --> 00:15:57,049
这里你返回一点

400
00:15:57,049 --> 00:15:58,370
所以你看似穿过两个了

401
00:15:58,370 --> 00:16:01,320
他可能他可能给你返回四个点啊

402
00:16:01,320 --> 00:16:02,399
这样就不是很好

403
00:16:02,399 --> 00:16:03,960
所以这个or就不是很好

404
00:16:03,960 --> 00:16:07,879
还有一个是这个这一项啊

405
00:16:07,879 --> 00:16:10,039
就和和最下面这一项类似

406
00:16:10,039 --> 00:16:13,070
它呢如果如果你穿过多个物体

407
00:16:13,070 --> 00:16:20,519
它会把每个物体的这个最最开始的这个碰撞点给你返回回来啊

408
00:16:20,519 --> 00:16:23,320
就这个这一点的信息反馈回来啊

409
00:16:23,320 --> 00:16:25,389
那么这样的话其实是最好的

410
00:16:25,389 --> 00:16:28,279
所以如果你要做多个检测的话

411
00:16:28,659 --> 00:16:30,820
推荐用这个单个检测的话

412
00:16:30,820 --> 00:16:33,129
一般就用这个啊就ok了

413
00:16:33,129 --> 00:16:35,169
那么它呢是有一个返回值的

414
00:16:35,169 --> 00:16:36,429
你检测完了

415
00:16:36,429 --> 00:16:38,049
检测的结果是什么

416
00:16:38,049 --> 00:16:39,610
你有一个结果啊

417
00:16:39,610 --> 00:16:40,698
返回值

418
00:16:41,220 --> 00:16:43,679
那么这个结果的话啊

419
00:16:43,679 --> 00:16:47,820
结果的话我们就可以通过这个结果拿到很多信息

420
00:16:47,820 --> 00:16:51,599
比如说首先如果

421
00:16:54,980 --> 00:16:56,419
他应该是个数组

422
00:16:56,419 --> 00:16:59,899
所以我们i s我们用数组吧

423
00:17:00,019 --> 00:17:01,220
那么它是一个数组

424
00:17:01,220 --> 00:17:02,539
为什么说他是个数组

425
00:17:02,539 --> 00:17:05,029
因为它可能穿过多个缸体

426
00:17:05,029 --> 00:17:05,750
多少个缸体

427
00:17:05,750 --> 00:17:07,609
它就可能可能给我们返回多个点

428
00:17:07,609 --> 00:17:08,029
对不对

429
00:17:08,029 --> 00:17:09,598
所以它这边是个数组

430
00:17:09,598 --> 00:17:13,799
那我们就可以去产生一个便利啊

431
00:17:13,799 --> 00:17:15,269
比如说i等于零

432
00:17:15,269 --> 00:17:17,759
i小于数组

433
00:17:17,759 --> 00:17:18,960
如果有多个点

434
00:17:18,960 --> 00:17:21,940
我们就遍历一下啊

435
00:17:21,940 --> 00:17:22,839
如果有单个的话

436
00:17:22,839 --> 00:17:24,130
那它就是一个

437
00:17:24,130 --> 00:17:29,589
那么然后我们可以从这个数组里面准确的拿到每一个结果

438
00:17:29,589 --> 00:17:31,539
每一个结果就是result

439
00:17:32,299 --> 00:17:34,160
然后resource唉

440
00:17:34,160 --> 00:17:36,079
这就是每一点的结果

441
00:17:36,079 --> 00:17:40,789
或者我们直接用那个four of去辨率也是一样的啊

442
00:17:40,789 --> 00:17:43,480
然后在这里拿到这个信息以后

443
00:17:44,480 --> 00:17:45,890
我们就可以怎样了

444
00:17:45,890 --> 00:17:47,240
这个是结果信息

445
00:17:47,240 --> 00:17:50,690
从结果信息里面我们就可以获得很多内容了

446
00:17:55,619 --> 00:17:59,759
这个就是射线碰到的

447
00:18:01,980 --> 00:18:03,779
碰撞器啊

448
00:18:03,779 --> 00:18:06,569
你就可以通过它点tag值

449
00:18:06,569 --> 00:18:09,119
你就知道哎我碰到的是哪一个碰撞器

450
00:18:09,119 --> 00:18:14,319
因为每个碰撞器我们都可以给它不同的这个tag值

451
00:18:14,319 --> 00:18:14,740
对不对

452
00:18:14,740 --> 00:18:16,869
每个碰撞器你都可以给它不同的tag值

453
00:18:16,869 --> 00:18:21,329
所以在这边你就可以通过clid.tag值去判断啊

454
00:18:21,329 --> 00:18:24,269
我这个射线碰到的是哪一个碰撞啊

455
00:18:24,269 --> 00:18:25,240
碰撞器

456
00:18:25,240 --> 00:18:26,680
那么还有什么呀

457
00:18:26,680 --> 00:18:29,490
i s.point

458
00:18:29,490 --> 00:18:32,180
还有一个点normal

459
00:18:33,160 --> 00:18:37,329
这个就是我们的碰到的点啊

460
00:18:37,329 --> 00:18:38,940
碰到的这个点

461
00:18:39,240 --> 00:18:42,619
而这个就是碰到的法线

462
00:18:44,759 --> 00:18:46,380
啊这两个不用多说了

463
00:18:46,380 --> 00:18:47,960
咱们之前说过啊

464
00:18:48,839 --> 00:18:50,940
也就是说如果返回的话

465
00:18:50,940 --> 00:18:54,078
其实比如说他比如说一个啊

466
00:18:54,078 --> 00:18:55,338
我们就说每次for循环

467
00:18:55,338 --> 00:18:56,659
比如说穿过了第一个物体

468
00:18:56,659 --> 00:18:58,068
那么它会返回这个点

469
00:18:58,068 --> 00:19:00,679
然后返回这个点处的一个法线

470
00:19:00,679 --> 00:19:04,819
法线应该就是这样的一个垂直的一个法线

471
00:19:04,819 --> 00:19:05,809
对不对啊

472
00:19:05,809 --> 00:19:07,779
垂直于这个面的一个法线

473
00:19:08,599 --> 00:19:13,339
那么这就是它的射线的这样的一个用法啊

474
00:19:13,339 --> 00:19:14,898
射线的一个用法

475
00:19:17,119 --> 00:19:17,900
怎么说啊

476
00:19:17,900 --> 00:19:19,460
射线别看简单啊

477
00:19:19,460 --> 00:19:20,240
用处也挺大

478
00:19:20,240 --> 00:19:22,759
如果你要做一个很高级的游戏

479
00:19:22,759 --> 00:19:24,019
很高级的游戏

480
00:19:24,019 --> 00:19:25,579
2d游戏啊

481
00:19:25,680 --> 00:19:28,140
就是或者说逻辑性强一点的

482
00:19:28,140 --> 00:19:29,460
常常会用到射线

483
00:19:29,460 --> 00:19:31,079
如果一些很简单的游戏

484
00:19:31,079 --> 00:19:34,670
可能你就做一些碰撞呀或者传感器啊

485
00:19:34,670 --> 00:19:36,259
就就搞定了啊

486
00:19:36,460 --> 00:19:37,599
如果麻烦一点的

487
00:19:37,599 --> 00:19:41,950
像刚才咱们说都要做个智能的敌人在那巡逻了啊对吧

488
00:19:41,950 --> 00:19:42,730
不要碰到墙

489
00:19:42,730 --> 00:19:45,039
就让它会自动转转弯啊

490
00:19:45,039 --> 00:19:45,910
那怎么办

491
00:19:45,910 --> 00:19:47,940
我们就用射线去做

492
00:19:51,500 --> 00:19:52,269
ok啊

493
00:19:52,269 --> 00:19:56,140
那么这节课这节课咱们就先这么多啊

494
00:19:56,140 --> 00:20:04,000
我们就把最后的这个物理的这一块内容给他说了一下啊


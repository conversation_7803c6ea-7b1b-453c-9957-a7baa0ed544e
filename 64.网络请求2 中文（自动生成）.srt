1
00:00:09,599 --> 00:00:13,948
ok这节课咱们来再看一下嗯

2
00:00:13,948 --> 00:00:15,179
接着上一节课呢

3
00:00:15,179 --> 00:00:16,649
我们继续去说

4
00:00:16,649 --> 00:00:19,919
上一节课咱们说了一下这个发起请求

5
00:00:19,919 --> 00:00:20,640
对不对

6
00:00:20,640 --> 00:00:22,170
比如说我们有个客户端

7
00:00:22,170 --> 00:00:24,480
我们要发起请求到服务端

8
00:00:24,480 --> 00:00:27,390
实际上就是请求到这个位置了啊

9
00:00:27,390 --> 00:00:29,789
只是我们这个cos creator比较特殊

10
00:00:29,789 --> 00:00:31,559
它的它作为一个客户端

11
00:00:31,559 --> 00:00:35,948
它的这个程序也是放在我们的这个呃服务器里的啊

12
00:00:35,948 --> 00:00:37,418
也是放在服务器里的

13
00:00:37,418 --> 00:00:42,899
那么我们现在就来看一下这一层到底长什么样啊

14
00:00:42,899 --> 00:00:44,759
就是服务器程序啊

15
00:00:44,759 --> 00:00:47,100
语言程序这一层到底长什么样

16
00:00:47,100 --> 00:00:50,899
那么在这里我们打开了一个

17
00:00:50,899 --> 00:00:52,159
这是豆瓣网啊

18
00:00:52,159 --> 00:00:55,679
它提供了一个排行前250名啊

19
00:00:55,679 --> 00:00:56,850
这样的一个数据

20
00:00:56,850 --> 00:01:02,200
那么它给我们这个数据就是给了我们一个json格式的数据

21
00:01:02,200 --> 00:01:06,930
我们可以看一下它这个域名到了这里代表的是豆瓣啊

22
00:01:06,930 --> 00:01:09,870
豆瓣的域名从这里开始代表的是路径

23
00:01:09,870 --> 00:01:10,728
对不对

24
00:01:10,728 --> 00:01:12,409
这里代表的是参数

25
00:01:12,409 --> 00:01:15,030
参数有两个start等于25

26
00:01:15,030 --> 00:01:17,120
就证明从第25名开始看

27
00:01:17,120 --> 00:01:18,379
count等于25

28
00:01:18,379 --> 00:01:19,909
就证明看25个

29
00:01:19,909 --> 00:01:24,259
也就是说实际上我们现在就是从25名往后看看25个

30
00:01:24,340 --> 00:01:28,599
那么所以现在啊我们就是看了25个这样的电影啊

31
00:01:28,599 --> 00:01:29,859
就是前250名

32
00:01:29,859 --> 00:01:31,739
然后我们看了250个电影

33
00:01:31,739 --> 00:01:36,659
那么他返回的结果就是这样的一个呃json字符串

34
00:01:36,659 --> 00:01:37,560
大家可以看一下

35
00:01:37,560 --> 00:01:39,450
这就是它返回的json字符串

36
00:01:39,450 --> 00:01:47,840
那实际上这个接口这个接口啊就是我们的这一层啊

37
00:01:47,840 --> 00:01:49,228
就是我们的这一层

38
00:01:49,228 --> 00:01:52,259
那么我们现在我们的客户端啊

39
00:01:52,259 --> 00:01:55,450
我们的客户端就是我们的这个浏览器啊

40
00:01:55,450 --> 00:01:57,189
现在这个浏览器就是我们的客户端

41
00:01:57,189 --> 00:01:59,260
我们去发了个请求哎

42
00:01:59,260 --> 00:02:00,819
往这个地方发了一个请求

43
00:02:00,819 --> 00:02:02,769
然后得到了一个杰森结果

44
00:02:02,769 --> 00:02:07,599
所以实际上现在我们嗯并不适用于这张图啊

45
00:02:07,599 --> 00:02:09,580
这张图只适用于cos creator

46
00:02:09,580 --> 00:02:11,379
他做的这个网页的消息

47
00:02:11,379 --> 00:02:16,338
我们现在唉其实可以说用的是这张图啊

48
00:02:16,338 --> 00:02:18,340
我们可以说用的是这张图

49
00:02:18,340 --> 00:02:20,469
那么在这张图的话

50
00:02:20,469 --> 00:02:22,659
我们现在这个客户端就是网页

51
00:02:22,659 --> 00:02:23,919
我们发了一个请求

52
00:02:23,919 --> 00:02:26,909
就是刚才那一串地址发给网页服务器

53
00:02:26,909 --> 00:02:30,180
它给我们返回来的响应就是这个杰森啊

54
00:02:30,180 --> 00:02:31,759
就是我们这个杰森

55
00:02:31,859 --> 00:02:34,899
那么这时候哎我们就可以看到啊

56
00:02:34,899 --> 00:02:36,639
这个json里面有什么信息了

57
00:02:36,639 --> 00:02:38,919
那么这里大家发现他把杰森给我们转了啊

58
00:02:38,919 --> 00:02:39,998
看起来很好看

59
00:02:39,998 --> 00:02:43,860
是因为咱们chrome里面有个扩展程序啊

60
00:02:43,860 --> 00:02:46,419
你可以装一个jason插件就可以了

61
00:02:46,739 --> 00:02:50,218
那么他就可以给我们把杰森哎转成这样的形式

62
00:02:50,218 --> 00:02:51,088
非常好看

63
00:02:51,088 --> 00:02:53,218
首先最外层大家可以看是个大花

64
00:02:53,218 --> 00:02:57,239
括号对应的是不是有里面有五个键值对啊

65
00:02:57,239 --> 00:02:58,679
证明它是一个对象

66
00:02:58,679 --> 00:03:00,919
对象里面有五个属性啊

67
00:03:00,919 --> 00:03:01,699
三个字

68
00:03:01,699 --> 00:03:02,870
三个数字

69
00:03:02,870 --> 00:03:03,740
一个字符串

70
00:03:03,740 --> 00:03:04,800
还有一个数组

71
00:03:05,639 --> 00:03:06,860
这个数组展开

72
00:03:06,860 --> 00:03:10,219
我们发现数组里面每一个都是一个对象

73
00:03:10,219 --> 00:03:13,590
大家可以看这边数组里面紧跟着大括号

74
00:03:13,590 --> 00:03:18,139
所以里面每一项都是一个呃对象

75
00:03:19,659 --> 00:03:23,219
那么我们在这里把每一个对象展开

76
00:03:23,219 --> 00:03:23,819
我们看一下

77
00:03:23,819 --> 00:03:25,259
因为实际上作为对象

78
00:03:25,259 --> 00:03:27,849
大家可以看它里面的内容肯定是一样的

79
00:03:27,849 --> 00:03:28,539
对不对

80
00:03:28,539 --> 00:03:31,750
那么我们可以看一下它每一每一个电影啊

81
00:03:31,750 --> 00:03:33,009
这个对象里面有什么东西

82
00:03:33,009 --> 00:03:34,030
你看这里也行

83
00:03:34,030 --> 00:03:34,759
一样的

84
00:03:34,759 --> 00:03:35,539
对不对

85
00:03:35,539 --> 00:03:38,800
这里就是每一个呃每一部电影啊

86
00:03:38,800 --> 00:03:40,000
他这样的一个对象

87
00:03:40,000 --> 00:03:41,259
这个对象我们在这儿看

88
00:03:41,259 --> 00:03:42,699
其实里面也很复杂

89
00:03:42,699 --> 00:03:43,900
有很多属性

90
00:03:43,900 --> 00:03:45,460
有这个普通的键值

91
00:03:45,460 --> 00:03:46,120
对对不对

92
00:03:46,120 --> 00:03:48,229
有这个number类型的这个属性

93
00:03:48,229 --> 00:03:50,240
有这个字符串类型的属性

94
00:03:50,240 --> 00:03:52,568
还有什么还可以展开证明

95
00:03:52,568 --> 00:03:54,128
这又是个对象啊

96
00:03:54,128 --> 00:03:55,359
这个也是个对象

97
00:03:55,359 --> 00:04:02,819
那么在这里你就可以看到你看比如说这里是我们看哪里

98
00:04:02,819 --> 00:04:04,469
这是我们看这里

99
00:04:04,469 --> 00:04:05,400
这是数组

100
00:04:05,400 --> 00:04:05,879
对不对

101
00:04:05,879 --> 00:04:09,039
数组里面这是第一个对象

102
00:04:09,039 --> 00:04:11,680
你看对象里面一个属性

103
00:04:11,680 --> 00:04:13,840
这个属性对应的又是一个对象

104
00:04:13,840 --> 00:04:16,839
第二个属性对应的这个就不是一个对象了

105
00:04:16,839 --> 00:04:18,290
这是一个数组啊

106
00:04:18,290 --> 00:04:19,129
这是一个数组

107
00:04:19,129 --> 00:04:21,050
然后这个就是普通的一个属性

108
00:04:21,050 --> 00:04:22,079
字符串属性

109
00:04:22,079 --> 00:04:24,509
再往下面又是一个数组

110
00:04:24,509 --> 00:04:26,639
这个数组还是个对象数组啊

111
00:04:26,639 --> 00:04:30,310
所以实际上大家可以看到jason就是这样的一层嵌套一层

112
00:04:30,310 --> 00:04:33,370
那么这个网页的jason很复杂

113
00:04:33,370 --> 00:04:33,970
是不是

114
00:04:33,970 --> 00:04:35,529
但是正常而言

115
00:04:35,529 --> 00:04:37,870
就是我们在这个做游戏过程当中

116
00:04:39,670 --> 00:04:40,949
就一点就完了

117
00:04:40,949 --> 00:04:42,569
这个是特殊的

118
00:04:42,569 --> 00:04:50,569
是这个我们从豆瓣网上获取它的一个呃电影排名的这样的一个呃api啊

119
00:04:50,569 --> 00:04:51,560
这样的一个地址

120
00:04:51,560 --> 00:04:54,139
那这个地址返回的json格式

121
00:04:54,759 --> 00:04:58,180
那么就是呃比较复杂的啊

122
00:04:58,180 --> 00:04:59,170
比较复杂的

123
00:04:59,170 --> 00:05:01,600
那么在这里咱们说一下啊

124
00:05:03,680 --> 00:05:06,720
一般正常比如说我们说一个网址

125
00:05:07,079 --> 00:05:10,500
网址返回的一般都是这个html

126
00:05:10,500 --> 00:05:12,209
所以我们能给大家解析出来

127
00:05:12,209 --> 00:05:16,160
那么第二个网网网上还有接口啊

128
00:05:16,160 --> 00:05:17,240
api接口

129
00:05:17,240 --> 00:05:20,990
如果api接口一般返回回来的格式就是jason

130
00:05:20,990 --> 00:05:24,319
或者是比如说其他的像xml

131
00:05:25,180 --> 00:05:28,800
那所以说如果大家想从网上去查看json格式的话

132
00:05:28,800 --> 00:05:31,560
你就可以去查查搜一下啊

133
00:05:31,560 --> 00:05:35,920
比如说api api接口这样的东西搜出来以后

134
00:05:35,920 --> 00:05:41,350
他可能有一些就是那么搜出来它就是这个json或xml

135
00:05:41,350 --> 00:05:45,920
所以如果你想从网上去看一下真正的这个json和xml

136
00:05:45,920 --> 00:05:47,480
它的消息一般是什么样

137
00:05:47,480 --> 00:05:50,399
你就可以在百度上面去搜一下api接口

138
00:05:50,399 --> 00:05:52,980
你看一下能不能搜搜索出来这个消息

139
00:05:52,980 --> 00:05:57,620
比如说我们在这里尝试搜一下api接口

140
00:05:59,160 --> 00:06:01,339
那么搜出来以后啊

141
00:06:01,339 --> 00:06:04,279
那么大家可以看免费使用的api接口

142
00:06:05,620 --> 00:06:06,759
大家可以看这里

143
00:06:06,759 --> 00:06:09,009
他就给我们提供了好多接口

144
00:06:09,009 --> 00:06:10,959
这些接口你看像这个啊

145
00:06:10,959 --> 00:06:12,040
就是提供xml的

146
00:06:12,040 --> 00:06:13,798
一看就是只提供x m l的

147
00:06:13,798 --> 00:06:16,108
然后其他的有什么查ip的呀

148
00:06:16,108 --> 00:06:17,158
地图接口啊

149
00:06:17,158 --> 00:06:22,699
音乐接口啊啊这些接口你都可以去尝试啊

150
00:06:22,699 --> 00:06:23,749
你都可以去尝试

151
00:06:23,749 --> 00:06:26,178
然后里面打开以后都和这个豆瓣一样

152
00:06:26,178 --> 00:06:29,139
就是一个json格式或者一个xml格式啊

153
00:06:29,139 --> 00:06:33,980
你就可以看一下商业化真正的这样的一个嗯jason xml长什么样啊

154
00:06:33,980 --> 00:06:35,000
但是还是我说的啊

155
00:06:35,000 --> 00:06:38,000
这个咱们做游戏一般用不到这么复杂的

156
00:06:38,000 --> 00:06:43,009
一般咱们都是嗯比较简单的json或者比较简单的xml啊

157
00:06:43,009 --> 00:06:44,930
当然对于咱cos creator而言

158
00:06:44,930 --> 00:06:46,819
一般都是这个jason啊

159
00:06:49,540 --> 00:06:50,160
行啊

160
00:06:50,160 --> 00:06:54,800
那么现在我们就说比如说我们要从这个地址啊

161
00:06:54,800 --> 00:06:59,430
我们要从这个地址把里面的jason下载下来

162
00:06:59,430 --> 00:07:01,740
因为这个我们知道它是在服务器上啊

163
00:07:01,740 --> 00:07:04,428
这个jason默认都是在服务器上的

164
00:07:04,428 --> 00:07:07,170
我们拿到接口就是为了在程序里面用

165
00:07:07,170 --> 00:07:09,269
那么我们怎样写代码

166
00:07:09,269 --> 00:07:12,000
通过代码把里面的内容获取下来

167
00:07:12,339 --> 00:07:14,740
当我们在这里创建一个

168
00:07:17,100 --> 00:07:18,139
脚本

169
00:07:18,139 --> 00:07:19,939
比如说叫请求

170
00:07:22,120 --> 00:07:23,759
我们也创建个节点

171
00:07:23,759 --> 00:07:25,560
request请求

172
00:07:27,000 --> 00:07:29,689
把脚本挂上来打开它

173
00:07:29,689 --> 00:07:34,980
比如说我们要获取这个url里面的信息了

174
00:07:34,980 --> 00:07:36,779
那我们先创建一个url

175
00:07:37,899 --> 00:07:39,959
这就是我们的接口啊

176
00:07:39,959 --> 00:07:40,829
api接口

177
00:07:40,829 --> 00:07:43,860
我们想通过程序把里面的json拿到

178
00:07:43,860 --> 00:07:44,699
拿到以后

179
00:07:44,699 --> 00:07:46,439
我们就可以去翻译里面的东西了

180
00:07:46,439 --> 00:07:50,059
这样其实我们这个程序就是网络营网络应用了

181
00:07:50,059 --> 00:07:50,718
对不对

182
00:07:50,718 --> 00:07:51,740
那么

183
00:07:54,459 --> 00:07:56,220
正常而言咱们说了啊

184
00:07:56,220 --> 00:07:58,259
我们这个杰森可能存到本地啊

185
00:07:58,259 --> 00:07:59,279
可能做这样的事

186
00:07:59,279 --> 00:08:02,569
但是我们接下来做的就是比如说从网上获取

187
00:08:05,449 --> 00:08:07,910
我们怎样去怎样去获得它

188
00:08:07,910 --> 00:08:09,170
主要是获得它解析

189
00:08:09,170 --> 00:08:10,610
我们已经说过了

190
00:08:10,610 --> 00:08:16,680
那么在这里怎样怎样去诶啊

191
00:08:16,680 --> 00:08:19,560
怎样去对他发起这个请求呢

192
00:08:19,560 --> 00:08:22,120
那我们先来创建一个请求

193
00:08:24,079 --> 00:08:30,860
这个请求还是loader里面有个get xml http request啊

194
00:08:30,860 --> 00:08:32,860
这个就是我们的网络请求

195
00:08:32,860 --> 00:08:37,779
网络请求的话一定要设置它当前用的哪种方式

196
00:08:37,779 --> 00:08:39,220
是get还是post

197
00:08:39,220 --> 00:08:40,779
我们这里就用get

198
00:08:40,899 --> 00:08:42,340
然后把url填

199
00:08:42,340 --> 00:08:44,559
这意思就是我们要用这个请求

200
00:08:44,559 --> 00:08:47,599
第三项代表我们是否要用异步

201
00:08:47,779 --> 00:08:50,029
我们现在先用true啊

202
00:08:50,029 --> 00:08:54,570
那么这个是否使用异步是什么意思啊

203
00:08:54,570 --> 00:08:56,279
那么我们一会儿再说啊

204
00:08:56,279 --> 00:08:59,059
我们先先在这把异步这个放下啊

205
00:08:59,580 --> 00:09:00,840
这个布尔值放下

206
00:09:00,840 --> 00:09:02,559
我们先把这个请求说完

207
00:09:03,440 --> 00:09:04,899
然后再往下走

208
00:09:06,039 --> 00:09:08,099
这里我们给它一个回调

209
00:09:09,419 --> 00:09:13,580
这个回调的意思就是当我从网上请求完成以后

210
00:09:13,580 --> 00:09:16,639
它就会调用我们这个回调

211
00:09:17,779 --> 00:09:23,200
然后最后一行我们就可以开始请求send

212
00:09:23,200 --> 00:09:24,399
就是开始请求了

213
00:09:24,399 --> 00:09:27,129
那么这一长串就是发送一个get请求

214
00:09:27,129 --> 00:09:29,259
你要post这边要放post

215
00:09:29,259 --> 00:09:30,929
但是它相对而言麻烦点

216
00:09:30,929 --> 00:09:33,730
还能往里面去创建一个请求体

217
00:09:33,730 --> 00:09:36,009
但是一般而言我们都是get多啊

218
00:09:36,009 --> 00:09:38,169
尤其是自己公司写程序

219
00:09:38,169 --> 00:09:39,818
一般都是get多一些

220
00:09:39,919 --> 00:09:46,019
那么在这里我们知道发起请求有很多种问题啊

221
00:09:46,019 --> 00:09:48,659
比如说当前这个请求可能请求了一半

222
00:09:48,659 --> 00:09:50,730
请求中或者请求结束

223
00:09:50,730 --> 00:09:52,259
有时候还会请求失败

224
00:09:52,259 --> 00:09:54,179
因为网络只要涉及到网络

225
00:09:54,179 --> 00:09:55,818
就有各种各样的问题

226
00:09:55,860 --> 00:10:00,549
所以在这里面并不是说并不是说代表进入到这个回调里面

227
00:10:00,549 --> 00:10:02,049
就代表请求完成了啊

228
00:10:02,049 --> 00:10:02,799
并不是这样

229
00:10:02,799 --> 00:10:06,220
他只是每次请求状态一改变啊

230
00:10:06,220 --> 00:10:09,850
请求状态改变都会调用这个回调

231
00:10:09,850 --> 00:10:11,440
但是我们一般而言

232
00:10:11,440 --> 00:10:13,419
中间的状态改变我们不关心

233
00:10:13,419 --> 00:10:18,610
我们只关心怎样请求结束后啊

234
00:10:18,610 --> 00:10:20,539
我们来获取信息

235
00:10:22,360 --> 00:10:25,330
怎么判断当前是请求结束后呢

236
00:10:25,330 --> 00:10:26,379
很简单

237
00:10:26,379 --> 00:10:31,120
request里面有一个当前的一个状态码

238
00:10:31,120 --> 00:10:34,480
这个状态码10123都代表是进行中

239
00:10:34,480 --> 00:10:38,139
只有四代表当前请求完成了

240
00:10:38,139 --> 00:10:39,159
就请求结束了

241
00:10:39,159 --> 00:10:40,509
我们获取到信息了

242
00:10:40,509 --> 00:10:42,309
所以我们一定要判断一下

243
00:10:47,259 --> 00:10:48,059
对不对

244
00:10:48,059 --> 00:10:49,799
美只要我们浏览网页

245
00:10:49,799 --> 00:10:51,809
总浏览这个接口啊

246
00:10:51,809 --> 00:10:54,009
只要是htp协议啊

247
00:10:54,009 --> 00:10:55,330
他的这个状态啊

248
00:10:55,330 --> 00:10:56,710
就是当他请求完成以后

249
00:10:56,710 --> 00:10:58,299
它会返回一个状态嘛

250
00:10:58,299 --> 00:10:59,559
只要请求完成

251
00:10:59,559 --> 00:11:00,970
也就是说变成四以后

252
00:11:00,970 --> 00:11:02,259
它就会有一个状态嘛

253
00:11:02,259 --> 00:11:06,568
这个状态码代表当前请求的结果是否是正确的

254
00:11:06,568 --> 00:11:08,729
一般200就是正确的啊

255
00:11:11,068 --> 00:11:14,668
那个就证明是客户端是我们这边请求的

256
00:11:14,668 --> 00:11:16,019
有问题了啊

257
00:11:17,980 --> 00:11:22,360
那么在这里如果我们都能进来了

258
00:11:22,360 --> 00:11:25,779
这里的输出肯定是怎样的请求

259
00:11:25,779 --> 00:11:26,799
完成了

260
00:11:28,860 --> 00:11:33,979
那么请求完成以后的内容啊是什么

261
00:11:35,240 --> 00:11:37,179
其实就是我们那个json字符串了

262
00:11:37,179 --> 00:11:37,720
对不对

263
00:11:37,720 --> 00:11:41,259
因为我们知道它它里面的内容就是那个json字符串

264
00:11:41,259 --> 00:11:46,059
json字符串怎样获取request.response text

265
00:11:46,519 --> 00:11:51,139
这个就是我们的请求完成以后的json字符串啊

266
00:11:51,139 --> 00:11:51,830
注意一下

267
00:11:51,830 --> 00:11:53,720
这整个而言就是我们的请求

268
00:11:53,720 --> 00:11:54,500
我们来运行一下

269
00:11:54,500 --> 00:11:55,519
看一下效果

270
00:11:58,779 --> 00:11:59,879
打开它

271
00:11:59,879 --> 00:12:02,979
这时候我们发现它报错了

272
00:12:03,759 --> 00:12:04,679
情绪完成以后

273
00:12:04,679 --> 00:12:05,850
他直接报错了

274
00:12:05,850 --> 00:12:08,220
这个错误是什么意思啊

275
00:12:08,220 --> 00:12:09,839
这个错误是什么意思

276
00:12:10,720 --> 00:12:12,789
我告诉大家很简单

277
00:12:12,789 --> 00:12:14,740
这个错误和大家的代码没关系

278
00:12:14,740 --> 00:12:19,899
这就是咱们上节课跟大家去说的这种网页啊

279
00:12:19,899 --> 00:12:21,519
由于网页的特性

280
00:12:21,519 --> 00:12:23,509
网页是不允许啊

281
00:12:23,509 --> 00:12:24,799
不允许做到这样的式子

282
00:12:24,799 --> 00:12:27,710
就是从一个服务器调另外一个服务器

283
00:12:27,710 --> 00:12:30,620
也就是说如果现在不是客户端之间了

284
00:12:30,620 --> 00:12:31,919
是两个服务器

285
00:12:32,000 --> 00:12:33,620
我这里重新画一下啊

286
00:12:36,679 --> 00:12:41,799
比如说这是服务器服务器一

287
00:12:43,360 --> 00:12:44,799
这是服务器二

288
00:12:45,600 --> 00:12:49,070
它是不允许从服务器一调服务器二的内容的

289
00:12:49,070 --> 00:12:51,629
这个就叫做跨域调用了啊

290
00:12:51,629 --> 00:12:52,590
跨域名啊

291
00:12:52,590 --> 00:12:54,149
你就可以把那个域想成域名

292
00:12:54,149 --> 00:12:56,250
就是跨域啊来调用了

293
00:12:56,250 --> 00:12:59,789
那么什么时候才不跨域调用的

294
00:12:59,789 --> 00:13:01,139
就是这种情况

295
00:13:01,139 --> 00:13:06,230
当我们把网页呃网页游戏布局到我们这个服务器里面的时候

296
00:13:06,230 --> 00:13:11,639
这时候我们的域名和我们的服务器的这个接口域名就会一样

297
00:13:11,639 --> 00:13:13,979
那这时候就可以正常调用了

298
00:13:14,220 --> 00:13:16,679
而我们现在大家想一想啊

299
00:13:17,039 --> 00:13:18,419
我们的这个游戏

300
00:13:18,419 --> 00:13:20,879
现在其实我们现在运行起来

301
00:13:20,879 --> 00:13:23,159
它是布局在本机的local host

302
00:13:23,159 --> 00:13:24,649
就代表本机ip

303
00:13:24,649 --> 00:13:27,938
所以实际上现在我们的域名是这个local host

304
00:13:27,940 --> 00:13:32,099
而我们请求的域名是api点豆瓣点com

305
00:13:32,200 --> 00:13:35,259
这两个域名大家可以看就不在一个域名

306
00:13:35,259 --> 00:13:37,599
所以它才会调用出现这样的错误

307
00:13:37,599 --> 00:13:40,519
如果比如说我们这个游戏就是给豆瓣做的

308
00:13:40,519 --> 00:13:42,139
当我们把它做完以后

309
00:13:42,139 --> 00:13:43,610
传到豆瓣的网站上

310
00:13:43,610 --> 00:13:46,580
我们也可以通过这个豆瓣点com访问的时候

311
00:13:46,580 --> 00:13:50,460
这时候我们就可以访问这个域名了啊

312
00:13:50,460 --> 00:13:51,600
我们就可以访问域名了

313
00:13:51,600 --> 00:13:52,559
这这里就注意啊

314
00:13:52,559 --> 00:13:54,539
同域名之间才可以访问

315
00:13:54,539 --> 00:13:56,279
跨域名是会报错的

316
00:13:56,279 --> 00:13:59,639
除非跨域名有一个方法可以正常访问

317
00:13:59,639 --> 00:14:04,500
就是比如说你现在这个我要跨域访问了

318
00:14:04,500 --> 00:14:09,029
那这个服务器的这个语言也必须是你去编写

319
00:14:09,029 --> 00:14:10,889
在它里面我们可以做一些操作

320
00:14:10,889 --> 00:14:12,068
操作完了以后

321
00:14:12,068 --> 00:14:16,279
这个服务器才可以被跨域名调用啊

322
00:14:16,279 --> 00:14:17,120
当然不管怎样

323
00:14:17,120 --> 00:14:18,080
就是对大家而言

324
00:14:18,080 --> 00:14:20,000
大家一下接受不了这么多啊

325
00:14:20,000 --> 00:14:21,519
肯定接受不了这么多

326
00:14:21,519 --> 00:14:22,899
那就是我跟他去说的

327
00:14:22,899 --> 00:14:24,639
大家就是尽量去明白

328
00:14:24,639 --> 00:14:26,100
尽量去想啊

329
00:14:26,100 --> 00:14:26,820
能理解多少

330
00:14:26,820 --> 00:14:27,659
尽量理解

331
00:14:27,659 --> 00:14:31,320
但是这个失败一定要知道失败之所以失败

332
00:14:31,320 --> 00:14:32,940
就是我们跨域名调用了啊

333
00:14:32,940 --> 00:14:34,818
跨域名调用的原因很简单

334
00:14:34,818 --> 00:14:39,629
那其实就是我们去访问的这个域名啊

335
00:14:39,629 --> 00:14:44,129
访问的域名一定要和我们在这里运行的这个域名一样的啊

336
00:14:44,129 --> 00:14:45,440
这是最简单的理解

337
00:14:45,440 --> 00:14:47,000
我们现在之所以错误

338
00:14:47,000 --> 00:14:49,039
就是因为我们现在还没传到网站上

339
00:14:49,039 --> 00:14:52,350
我们现在是在本地尝试运用这个网页游戏

340
00:14:52,350 --> 00:14:53,549
当我们这个网页游戏做完

341
00:14:53,549 --> 00:14:55,710
最后肯定要发展发布到网站上

342
00:14:55,710 --> 00:14:57,690
如果我们给它发布到豆瓣

343
00:14:57,690 --> 00:14:59,850
我们现在是豆瓣点com去访问的

344
00:14:59,850 --> 00:15:05,639
比如说我们这里是a p i点豆瓣点com

345
00:15:05,940 --> 00:15:07,769
然后后面跟着什么都行

346
00:15:07,769 --> 00:15:10,740
比如说我们上传的这个这个网站上了

347
00:15:10,740 --> 00:15:12,899
这时候我们再去调用这个地址

348
00:15:12,899 --> 00:15:16,129
这时候它会检测a你这个域名是一样的

349
00:15:16,129 --> 00:15:18,740
这就是正常的预了啊

350
00:15:18,740 --> 00:15:23,360
这时候你再去呃看看一下它打印出来的内容

351
00:15:23,360 --> 00:15:28,620
就是我们刚才的那一大串这个json字符串了啊

352
00:15:28,620 --> 00:15:30,960
而现在报错就是因为这个域名问题

353
00:15:30,960 --> 00:15:32,039
这个注意一下啊

354
00:15:32,039 --> 00:15:34,720
所以这时候呃大家要明白啊

355
00:15:34,720 --> 00:15:36,820
我们这个代码已经正确了啊

356
00:15:36,820 --> 00:15:38,019
之所以会报错

357
00:15:38,019 --> 00:15:39,740
是跨域问题啊

358
00:15:41,539 --> 00:15:44,859
那我们现在再来说一下这个异步代表什么意思啊

359
00:15:47,120 --> 00:15:48,080
异步啊

360
00:15:48,080 --> 00:15:50,600
其实就是如果我们不使用异步

361
00:15:50,600 --> 00:15:54,080
比如说它开始下载了这一行是真正开始下载

362
00:15:54,080 --> 00:15:54,839
对不对

363
00:15:54,839 --> 00:15:56,729
比如说下面这里还有代码

364
00:15:56,729 --> 00:15:58,558
如果我们不使用异步

365
00:15:58,558 --> 00:16:01,059
那么它默认使用的方式就是同步

366
00:16:01,539 --> 00:16:02,919
如果是用同步下载

367
00:16:02,919 --> 00:16:04,720
意思是这行开始下载

368
00:16:04,720 --> 00:16:07,808
那么这时候程序不会执行下面的代码

369
00:16:07,808 --> 00:16:09,908
它会直接等等等等

370
00:16:09,908 --> 00:16:11,948
当你下载完成以后才会往下执行

371
00:16:11,948 --> 00:16:15,438
这样的话实际上这个程序就算是卡死在这个位置了

372
00:16:15,438 --> 00:16:17,839
直到下载完成以后才会往下运行

373
00:16:17,839 --> 00:16:19,129
那如果网络不好

374
00:16:19,129 --> 00:16:21,440
比如说下载需要三四秒下完

375
00:16:21,440 --> 00:16:23,779
那你这程序直接就卡了三四秒

376
00:16:23,779 --> 00:16:24,859
对不对

377
00:16:24,859 --> 00:16:28,278
异步的意思就是如果我们这里给它处用异步下载

378
00:16:28,278 --> 00:16:30,139
就是这里开始下载

379
00:16:30,139 --> 00:16:31,399
只是开始了

380
00:16:31,399 --> 00:16:34,450
然后他他后台默默的下载啊

381
00:16:34,450 --> 00:16:36,279
确实它就放在后台了啊

382
00:16:36,279 --> 00:16:38,109
然后默默的自己去下载了

383
00:16:38,109 --> 00:16:41,470
然后我们的代码紧接着继续往后走走走走

384
00:16:41,470 --> 00:16:42,100
往后走

385
00:16:42,100 --> 00:16:43,659
直到这边下载完了

386
00:16:43,659 --> 00:16:44,980
只要下载完了哎

387
00:16:44,980 --> 00:16:47,230
代码会立刻进入到这个里面处理

388
00:16:47,230 --> 00:16:49,370
我们下载完成的逻辑啊

389
00:16:49,370 --> 00:16:50,090
就是这样的

390
00:16:50,090 --> 00:16:52,480
所以对于异步处理而言

391
00:16:52,480 --> 00:16:55,779
我们这个程序是不会进入到那个假死状态啊

392
00:16:55,779 --> 00:16:58,210
卡死状态你要用同步的话

393
00:16:58,210 --> 00:17:00,549
由于他这行执行完了就开始下载了

394
00:17:00,549 --> 00:17:01,389
它不下载完

395
00:17:01,389 --> 00:17:02,409
它不往下执行

396
00:17:02,409 --> 00:17:03,850
那么它就会卡在这行

397
00:17:03,850 --> 00:17:05,059
一直卡啊

398
00:17:05,059 --> 00:17:07,579
翻译成程序就是未响应啊

399
00:17:07,579 --> 00:17:09,559
我们这个windows程序常常未响应是吧

400
00:17:09,559 --> 00:17:10,578
未响应

401
00:17:10,779 --> 00:17:11,980
如果你想未响应

402
00:17:11,980 --> 00:17:13,539
你就可以做这种同步操作

403
00:17:13,539 --> 00:17:16,700
那就未响应了哈哈那很简单

404
00:17:16,700 --> 00:17:19,400
所以我们在这里尽量用一步

405
00:17:21,388 --> 00:17:25,650
ok啊那么这两节网络请求的这个课程啊

406
00:17:25,650 --> 00:17:27,720
内容信息非常多

407
00:17:27,720 --> 00:17:29,700
网络是个很复杂的东西

408
00:17:29,700 --> 00:17:31,650
那么对于大家现在而言

409
00:17:31,650 --> 00:17:35,369
大家只需要了解什么是接口

410
00:17:35,369 --> 00:17:39,329
接口里面会会给我们返回这个json和xml

411
00:17:39,329 --> 00:17:40,410
对不对啊

412
00:17:40,410 --> 00:17:42,690
ok这是第一个了解什么是接口接口

413
00:17:42,690 --> 00:17:44,400
会返回什么样的内容

414
00:17:44,400 --> 00:17:49,160
第二个就是如果我们想从网上获取接口里面的内容

415
00:17:49,160 --> 00:17:50,240
我们怎样获取

416
00:17:50,240 --> 00:17:51,829
就用这个方式获取

417
00:17:51,829 --> 00:17:53,390
然后我们还要用异步获取

418
00:17:53,390 --> 00:17:55,369
要知道什么是异步获取就可以了

419
00:17:56,630 --> 00:18:00,640
那么咱们这节课就这么多啊

420
00:18:00,640 --> 00:18:02,799
一定要把刚才咱们说的那几点啊

421
00:18:02,799 --> 00:18:03,759
一个是什么是接口

422
00:18:03,759 --> 00:18:06,690
接口里面会给我们返回json或xml

423
00:18:08,670 --> 00:18:10,170
就用这种方式去获取

424
00:18:10,170 --> 00:18:13,589
这样的话我们就可以获取服务器上的数据了啊

425
00:18:13,589 --> 00:18:15,299
那ok啊

426
00:18:15,299 --> 00:18:17,099
其实我们讲这么多就行了啊

427
00:18:17,099 --> 00:18:21,319
给大家去说的还是有点嗯有点多啊

428
00:18:21,319 --> 00:18:23,000
因为我觉得是视频课

429
00:18:23,000 --> 00:18:24,440
所以给大家去说的多点

430
00:18:24,440 --> 00:18:25,640
大家如果一遍不懂

431
00:18:25,640 --> 00:18:26,869
可以来回听

432
00:18:26,869 --> 00:18:28,250
来回重复听

433
00:18:28,250 --> 00:18:33,119
之前我讲了这个好多年线下的啊

434
00:18:33,119 --> 00:18:34,619
其实我们讲线下的时候

435
00:18:34,619 --> 00:18:36,109
因为只能讲一遍

436
00:18:36,109 --> 00:18:37,609
然后又怕大家听不懂

437
00:18:37,609 --> 00:18:42,970
所以其实线下我们花一天时间就就讲这个什么是接口

438
00:18:42,970 --> 00:18:44,829
接口会返回一个这个数据

439
00:18:44,829 --> 00:18:47,630
然后这个数据拿到数据怎样去请求

440
00:18:47,630 --> 00:18:50,390
然后把这段代码只要大家练会就行了

441
00:18:50,390 --> 00:18:51,980
很多东西没有去说

442
00:18:51,980 --> 00:18:56,759
那就是因为线下其实和线上对比有优点也有缺点

443
00:18:56,759 --> 00:18:58,259
缺点就在于只能听一遍

444
00:18:58,259 --> 00:18:59,579
线上你可以来回去听

445
00:18:59,579 --> 00:19:00,539
对不对啊

446
00:19:00,539 --> 00:19:01,619
所以讲的多了一点

447
00:19:01,619 --> 00:19:03,499
大家自己可以啊

448
00:19:03,499 --> 00:19:04,699
多听两遍啊

449
00:19:04,699 --> 00:19:06,559
感受一下这个网络请求

450
00:19:06,559 --> 00:19:08,059
实在不懂也没关系啊

451
00:19:08,059 --> 00:19:09,200
实在不懂也没关系

452
00:19:09,200 --> 00:19:11,180
因为如果等你遇到的时候

453
00:19:11,180 --> 00:19:12,299
你会发现

454
00:19:14,279 --> 00:19:15,119
怎么说啊

455
00:19:15,119 --> 00:19:16,980
就是这个如果你遇到这种东西

456
00:19:16,980 --> 00:19:18,480
实际上用起来很简单

457
00:19:18,480 --> 00:19:19,779
就这么简单啊

458
00:19:20,079 --> 00:19:24,869
只是你要想对他就是说内部理解得更深的话

459
00:19:24,869 --> 00:19:27,569
你就要去看一看我这边画的这些图啊

460
00:19:27,569 --> 00:19:28,710
这些是怎么回事啊

461
00:19:28,710 --> 00:19:31,069
其实你这些图什么完全不了解

462
00:19:31,069 --> 00:19:33,799
你只需要知道这边啊给你个地址

463
00:19:33,799 --> 00:19:37,549
通过这个地址能拿到这个地址里面的数据就ok了啊

464
00:19:37,549 --> 00:19:40,739
这就是我们最基础的一个需求就好了

465
00:19:40,739 --> 00:19:43,419
那行那我们这节课就这么多了

466
00:19:43,839 --> 00:19:44,759
略略略


1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,779 --> 00:00:13,460
ok我们这节课来说一下预设体的一个使用啊

3
00:00:13,460 --> 00:00:15,279
那什么是预设体啊

4
00:00:16,059 --> 00:00:19,329
嗯预设体是这样的一个东西啊

5
00:00:19,329 --> 00:00:22,719
首先我们直接这样去写吧

6
00:00:22,719 --> 00:00:25,030
那我把这个这个内容删了

7
00:00:25,030 --> 00:00:27,120
那比如说我这里有个地面

8
00:00:27,399 --> 00:00:30,339
那么地面上面去挂载了一个脚本啊

9
00:00:30,339 --> 00:00:32,439
可能这个脚本是对于这个地面的一个控制

10
00:00:32,439 --> 00:00:33,200
对不对

11
00:00:33,439 --> 00:00:38,990
然后这时候如果有一个需求说场景里面还要再创建一个这样的地面

12
00:00:38,990 --> 00:00:42,520
还要而且这个地面也需要这个脚本怎么办

13
00:00:42,740 --> 00:00:44,570
它们的功能完全一样啊

14
00:00:44,570 --> 00:00:45,740
只是位置不一样

15
00:00:45,740 --> 00:00:49,299
比如说第二个地面需要放在这个位置唉

16
00:00:49,299 --> 00:00:51,969
但是他也需要这个脚本

17
00:00:51,969 --> 00:00:57,990
啊o那比如说现在我要创建第三个地面

18
00:00:57,990 --> 00:00:59,579
第三个地面也是一样的

19
00:00:59,579 --> 00:01:00,840
屏幕上还需要一个地面

20
00:01:00,840 --> 00:01:02,130
也需要这个脚本怎么办

21
00:01:02,130 --> 00:01:03,780
你是不是又得来一次

22
00:01:03,920 --> 00:01:05,900
ok那么现在好了

23
00:01:05,900 --> 00:01:08,280
你看比如说我又拖上来一个

24
00:01:08,959 --> 00:01:11,120
又挂载上了咱们的这个脚本

25
00:01:11,120 --> 00:01:13,640
而现在达到我们这个游戏的一个需求了

26
00:01:13,640 --> 00:01:16,519
但是这时候我们脚本有一个属性叫做类

27
00:01:16,519 --> 00:01:17,420
叫做这个text

28
00:01:17,420 --> 00:01:17,989
对不对

29
00:01:17,989 --> 00:01:19,459
就输入文本的

30
00:01:19,700 --> 00:01:21,920
那么默认所有的都是hello

31
00:01:21,920 --> 00:01:22,489
对不对

32
00:01:22,489 --> 00:01:27,859
我现在要求的是把所有的这个文本全改成一怎么办

33
00:01:28,680 --> 00:01:30,120
全改成一怎么办

34
00:01:30,120 --> 00:01:32,430
你是不是一改一下

35
00:01:32,430 --> 00:01:34,459
二改一下

36
00:01:34,459 --> 00:01:35,810
三改一下

37
00:01:35,810 --> 00:01:37,879
你是不是必须一个一个去改啊

38
00:01:37,879 --> 00:01:38,719
很麻烦

39
00:01:38,719 --> 00:01:43,140
而且这时候突然人家说哎这个地面可以不用脚本控制啊

40
00:01:43,140 --> 00:01:44,159
发现这个脚本多余

41
00:01:44,159 --> 00:01:44,969
把它删了吧

42
00:01:44,969 --> 00:01:46,859
你是不是这扇一下

43
00:01:46,859 --> 00:01:48,620
这扇一下

44
00:01:48,799 --> 00:01:49,969
这杀一下

45
00:01:49,969 --> 00:01:52,640
唉觉得这种事情就很多余

46
00:01:52,640 --> 00:01:53,420
对不对

47
00:01:53,459 --> 00:01:56,700
那这时候怎么解决这个问题啊

48
00:01:56,700 --> 00:01:57,840
这个用起来比较麻烦

49
00:01:57,840 --> 00:02:00,040
怎么解决预设题就出来了

50
00:02:01,459 --> 00:02:03,500
那么首先啊预设体是什么

51
00:02:03,500 --> 00:02:08,240
比如说我们场景里面已经有一个呃成品存在的时候啊

52
00:02:08,240 --> 00:02:10,560
已经有一个成品存在的时候

53
00:02:10,560 --> 00:02:17,500
比如说嗯这个地面上面就是挂了这个脚本啊

54
00:02:17,500 --> 00:02:18,800
这就是我的成品

55
00:02:18,819 --> 00:02:21,580
那么现在我希望把它做成一个预设题

56
00:02:21,580 --> 00:02:22,150
怎么做

57
00:02:22,150 --> 00:02:25,659
直接把它拖拽到我们下面的这个目录里面

58
00:02:25,879 --> 00:02:28,879
那么它呢就会生成这样一个预设题

59
00:02:28,879 --> 00:02:30,840
仔细看一下啊

60
00:02:31,780 --> 00:02:33,580
这个预测题的样子是吧

61
00:02:33,580 --> 00:02:35,860
这个图标一个方块儿

62
00:02:35,919 --> 00:02:39,580
那么这个预设体怎么去用

63
00:02:39,620 --> 00:02:42,379
当我把它拖到场景里面的时候

64
00:02:42,379 --> 00:02:43,400
大家可以看一下

65
00:02:43,400 --> 00:02:45,770
它就会生成这样的一个地面

66
00:02:45,770 --> 00:02:48,560
并且上面直接挂在了我们的脚本

67
00:02:48,919 --> 00:02:50,539
直接挂在了我们的脚本

68
00:02:50,539 --> 00:02:55,280
也就是说他们俩现在都是预设体生出呃

69
00:02:55,280 --> 00:02:56,419
产生出来的了

70
00:02:56,419 --> 00:02:57,419
怎么看呢

71
00:02:57,719 --> 00:02:58,500
很简单

72
00:02:58,500 --> 00:03:00,330
你看上面是白色的

73
00:03:00,330 --> 00:03:01,860
现在变成蓝色的了

74
00:03:01,860 --> 00:03:05,180
如果我们比如普通正常把地面拖上来

75
00:03:05,180 --> 00:03:06,379
应该是白色的

76
00:03:06,379 --> 00:03:07,879
当文字是蓝色的

77
00:03:07,879 --> 00:03:13,039
就证明这个节点是由预设体而产生的

78
00:03:13,039 --> 00:03:15,169
它是和预设体有关联的

79
00:03:15,169 --> 00:03:17,699
那么这个预设体你可以怎样理解啊

80
00:03:19,539 --> 00:03:21,819
我们玩游戏啊

81
00:03:21,819 --> 00:03:22,659
你可以存档

82
00:03:22,659 --> 00:03:23,229
对不对

83
00:03:23,229 --> 00:03:25,090
预设体就相当于一个存档

84
00:03:25,090 --> 00:03:26,500
你做好了一个成品

85
00:03:26,500 --> 00:03:28,099
把这个成品存了档

86
00:03:28,099 --> 00:03:30,199
然后你可以无限地读取它

87
00:03:30,199 --> 00:03:32,340
读取一个哪一个读取一个哪一个

88
00:03:32,360 --> 00:03:34,340
在生活中创建物品

89
00:03:34,340 --> 00:03:35,000
也是的

90
00:03:35,000 --> 00:03:38,319
你不管创建一个新的汽车还是新的手机

91
00:03:38,319 --> 00:03:42,340
那么它都是先是创造一台一台出来了

92
00:03:42,340 --> 00:03:42,879
怎么办

93
00:03:42,879 --> 00:03:44,020
把它调试好

94
00:03:44,020 --> 00:03:45,849
属性参数需要什么调试好

95
00:03:45,849 --> 00:03:51,020
把它呢呃通过它来制造一个模具啊

96
00:03:51,020 --> 00:03:51,860
哈是不是

97
00:03:51,860 --> 00:03:56,120
然后再通过这个模具产生出无数和他完全一样的东西

98
00:03:56,120 --> 00:03:56,860
对不对

99
00:03:56,860 --> 00:03:59,590
那么其实就是这样的一个思想啊

100
00:03:59,590 --> 00:04:04,240
那么当它啊当从这个预设体我们拖出来

101
00:04:04,240 --> 00:04:06,020
产生这个物体以后

102
00:04:06,020 --> 00:04:07,699
它跟普通物体的区别

103
00:04:07,699 --> 00:04:09,500
第一个就是颜色变了

104
00:04:09,500 --> 00:04:11,719
那但有些同学说你不能光这一个区别

105
00:04:11,719 --> 00:04:14,629
是不是第二个区别是什么

106
00:04:14,629 --> 00:04:16,860
它上面有这一栏

107
00:04:17,079 --> 00:04:18,338
有这一栏

108
00:04:18,338 --> 00:04:21,038
那么这是干嘛的啊

109
00:04:21,038 --> 00:04:22,800
那么这时候注意啊

110
00:04:23,220 --> 00:04:27,660
呃这里首先有个这个图标啊

111
00:04:27,660 --> 00:04:29,339
这个图标是干嘛的

112
00:04:29,660 --> 00:04:33,740
那么其实这个功能就是做自动同步了啊

113
00:04:33,740 --> 00:04:35,720
我们知道比如说一个预测题

114
00:04:35,720 --> 00:04:37,019
我们把它拖出来以后

115
00:04:37,040 --> 00:04:39,259
虽然他们的各个属性都是同步

116
00:04:39,259 --> 00:04:41,420
但是有一些属性是不同步的

117
00:04:41,420 --> 00:04:41,959
比如说什么

118
00:04:41,959 --> 00:04:43,019
大家可以看一下

119
00:04:43,040 --> 00:04:43,850
这三个

120
00:04:43,850 --> 00:04:45,259
虽然都是他的预测题

121
00:04:45,259 --> 00:04:47,480
但是很明显它们的位置不同步

122
00:04:48,180 --> 00:04:49,980
比如说位置旋转缩放

123
00:04:49,980 --> 00:04:51,220
我都可以不同步

124
00:04:51,600 --> 00:04:55,019
如果比如说当我把一个调完以后啊

125
00:04:55,019 --> 00:04:59,180
比如说我把一个哎我把一个大小调了以后

126
00:04:59,480 --> 00:05:03,110
我现在需要让别的和他同步了

127
00:05:03,110 --> 00:05:05,480
那一般我们会做手动同步

128
00:05:05,480 --> 00:05:08,439
就是诶我们调到合适的情况了啊

129
00:05:08,439 --> 00:05:10,240
你不就太大啊

130
00:05:10,240 --> 00:05:11,529
比如调到这个程度了

131
00:05:11,529 --> 00:05:17,319
我希望所有的从现在开始预设题里面保存的数据变成这个新的数据

132
00:05:17,319 --> 00:05:19,720
这样的话剩下的是不是也会跟着同步了

133
00:05:19,720 --> 00:05:20,939
就变成长的了

134
00:05:20,959 --> 00:05:23,959
但是这个操作往往是往往是怎样的

135
00:05:23,959 --> 00:05:26,420
往往是手动去同步的

136
00:05:26,420 --> 00:05:28,759
啊就是你不能说随便调一下

137
00:05:28,779 --> 00:05:30,519
这边预设题就跟着改变

138
00:05:30,519 --> 00:05:31,360
你要是那样的话

139
00:05:31,360 --> 00:05:32,139
很容易挑错

140
00:05:32,139 --> 00:05:32,680
对不对

141
00:05:32,680 --> 00:05:34,839
但是这个东西就是你如果把它点上的话

142
00:05:34,839 --> 00:05:36,279
就会变成自动同步模式

143
00:05:36,279 --> 00:05:37,360
那这个一般我们不点它

144
00:05:37,360 --> 00:05:38,180
不管它

145
00:05:38,180 --> 00:05:40,490
然后在后面有三个按钮

146
00:05:40,490 --> 00:05:42,639
第一个按钮的作用是诶

147
00:05:42,660 --> 00:05:44,399
选中这个蓝色物体

148
00:05:44,399 --> 00:05:45,480
再点选择

149
00:05:45,480 --> 00:05:48,319
它会给你跳到这个预设体的位置

150
00:05:48,319 --> 00:05:50,870
它会告诉你我的预设体在哪里

151
00:05:50,870 --> 00:05:52,920
回退是什么意思

152
00:05:53,319 --> 00:05:56,680
比如说你设置好了一个位置啊

153
00:05:56,680 --> 00:05:59,259
或者你对预设体的内容进行修改了

154
00:05:59,259 --> 00:06:00,699
你现在觉得不好了

155
00:06:00,699 --> 00:06:01,740
你点回退

156
00:06:01,759 --> 00:06:05,779
那么他呢就会回退到之前的一个形态啊

157
00:06:05,779 --> 00:06:07,459
回退回退到之前那个形态

158
00:06:07,459 --> 00:06:09,660
当然这个必须和保存一块去用

159
00:06:09,660 --> 00:06:10,290
嗯

160
00:06:10,290 --> 00:06:13,110
我们先把回退放到这儿啊

161
00:06:13,110 --> 00:06:17,579
那比如说我们看保存这个保存是什么意思啊

162
00:06:18,040 --> 00:06:19,360
你看这个预测题

163
00:06:19,360 --> 00:06:20,620
这是最开始那个预测题

164
00:06:20,620 --> 00:06:21,160
对不对

165
00:06:21,160 --> 00:06:23,120
我们现在只要拖出来

166
00:06:23,379 --> 00:06:24,610
只要拖出来

167
00:06:24,610 --> 00:06:26,680
现在预设体就已经在这个位置了

168
00:06:26,680 --> 00:06:27,100
对不对

169
00:06:27,100 --> 00:06:29,259
预设体现在已经在这个位置了

170
00:06:29,259 --> 00:06:33,670
那么这时候如果我把预设体放到这个位置

171
00:06:33,670 --> 00:06:36,439
并且比如说让它稍微旋转一点

172
00:06:36,660 --> 00:06:39,000
然后我按一下保存以后

173
00:06:39,120 --> 00:06:43,139
这时候这个预设体啊就是已经被改变了

174
00:06:43,139 --> 00:06:45,149
我们把它再拖出来一个新的

175
00:06:45,149 --> 00:06:48,120
大家发现拖出来一个新的

176
00:06:48,120 --> 00:06:50,850
是不是已经变成这种旋转的一个模式了

177
00:06:50,850 --> 00:06:56,300
那么这个就是它的一个嗯保存啊

178
00:06:56,300 --> 00:06:59,459
就是说你对预设体如果要进行修改

179
00:06:59,459 --> 00:07:00,779
你在这没法修改

180
00:07:00,779 --> 00:07:01,259
对不对

181
00:07:01,259 --> 00:07:02,459
你要把它拖出来

182
00:07:02,459 --> 00:07:03,629
你能看见了以后

183
00:07:03,629 --> 00:07:08,040
现在比如说我觉得这个保存的这个东西我不太喜欢啊

184
00:07:08,040 --> 00:07:09,300
我对它进行修改

185
00:07:09,300 --> 00:07:11,660
修改完了以后哎

186
00:07:11,660 --> 00:07:12,379
修改完了以后

187
00:07:12,379 --> 00:07:14,509
我按下保存就行了啊

188
00:07:14,509 --> 00:07:18,980
然后如果你看和刚才一样拉伸

189
00:07:18,980 --> 00:07:22,279
我觉得这个行行这个情况不太好啊

190
00:07:22,279 --> 00:07:24,680
我本来想按这个这个状态去保存的

191
00:07:24,680 --> 00:07:30,319
但是我觉得这个诶我现在不想用这个

192
00:07:30,319 --> 00:07:32,360
我想恢复最开始的形态怎么办

193
00:07:32,360 --> 00:07:34,100
有点回退啊

194
00:07:34,100 --> 00:07:36,019
其实你ctrl加z也行啊

195
00:07:36,019 --> 00:07:37,220
这个东西倒无所谓

196
00:07:37,220 --> 00:07:38,959
一般很少点

197
00:07:38,959 --> 00:07:40,639
偶尔我们拿到预设题

198
00:07:40,639 --> 00:07:43,160
一般点的最多的就是这个保存啊

199
00:07:43,160 --> 00:07:45,759
就是你拖上来一个物体对它修改修改

200
00:07:45,759 --> 00:07:46,779
然后点一下保存

201
00:07:46,779 --> 00:07:49,509
保存一下当前的形态就可以了啊

202
00:07:49,509 --> 00:07:51,730
常常去做这些事儿啊

203
00:07:51,730 --> 00:07:56,490
有旋转旋转成负的30保存

204
00:07:56,490 --> 00:07:58,439
保存完了拖上来

205
00:07:58,439 --> 00:08:01,860
你看又变成新的这个角度了

206
00:08:01,860 --> 00:08:02,879
对不对啊

207
00:08:02,879 --> 00:08:04,829
所以这些注意一下啊

208
00:08:04,829 --> 00:08:08,079
它的保存就是王预设体啊

209
00:08:08,079 --> 00:08:09,610
保存新的数据

210
00:08:09,610 --> 00:08:11,920
相当于咱们那会儿说存档

211
00:08:11,920 --> 00:08:14,720
就是说比如说你把一个物体存了

212
00:08:14,720 --> 00:08:15,899
当成了预设体

213
00:08:15,899 --> 00:08:16,560
读了档

214
00:08:16,560 --> 00:08:17,459
拿出来读档

215
00:08:17,459 --> 00:08:18,899
拿出来的东西修改完了

216
00:08:18,899 --> 00:08:20,120
你想再存档

217
00:08:20,120 --> 00:08:22,220
并且覆盖这个存档是吧

218
00:08:22,220 --> 00:08:23,300
你想覆盖这个存档

219
00:08:23,300 --> 00:08:24,800
你就点这个保存啊

220
00:08:24,800 --> 00:08:27,100
就是这样一个意思啊

221
00:08:27,100 --> 00:08:27,699
ok啊

222
00:08:27,699 --> 00:08:31,899
那么这些东西啊用着用着就会了啊

223
00:08:31,899 --> 00:08:32,980
用着用着就会了

224
00:08:32,980 --> 00:08:34,659
那我们现在再说一个东西啊

225
00:08:34,659 --> 00:08:36,940
首先比如说我们有一个物体

226
00:08:36,940 --> 00:08:40,299
物体上面有一个脚本啊

227
00:08:40,299 --> 00:08:43,570
我现在主要就是在这个脚本里面里面要写一些代码的

228
00:08:43,570 --> 00:08:45,100
首先我要通过脚本

229
00:08:45,100 --> 00:08:47,779
我们知道在这里可以创建一个空的节点

230
00:08:47,779 --> 00:08:50,149
能不能在脚本里面去创建

231
00:08:50,149 --> 00:08:51,379
肯定是可以的

232
00:08:51,379 --> 00:08:52,919
我们打开这个脚本

233
00:08:53,360 --> 00:08:56,149
我们把之前的内容给它删掉

234
00:08:56,149 --> 00:08:58,279
我们来创建一个

235
00:09:00,059 --> 00:09:01,440
创建一个节点

236
00:09:01,440 --> 00:09:03,179
通过代码的方式

237
00:09:03,179 --> 00:09:05,700
代码的方式怎么创建啊

238
00:09:05,700 --> 00:09:06,899
代码的方式怎么创建

239
00:09:06,899 --> 00:09:07,980
非常简单

240
00:09:09,200 --> 00:09:13,600
我们直接嗯let node啊

241
00:09:13,600 --> 00:09:15,340
这个node就是我们的新的一个节点

242
00:09:15,340 --> 00:09:20,200
然后就等一个new cc.sport啊

243
00:09:20,200 --> 00:09:21,639
或者说cc.note啊

244
00:09:21,639 --> 00:09:22,779
我直接创建这个节点

245
00:09:22,779 --> 00:09:24,330
好创建节点

246
00:09:24,330 --> 00:09:25,860
这个节点是有名称的

247
00:09:25,860 --> 00:09:26,220
对不对

248
00:09:26,220 --> 00:09:28,580
我们可以叫做new哎

249
00:09:28,580 --> 00:09:31,460
这样的话我们就创建了一个节点啊

250
00:09:31,460 --> 00:09:32,779
叫做new的一个空节点

251
00:09:32,779 --> 00:09:33,620
我们来运行一下

252
00:09:33,620 --> 00:09:35,600
看一下有没有啊

253
00:09:35,600 --> 00:09:37,460
这边应该还是看不出来啊

254
00:09:37,460 --> 00:09:38,720
这边还是看不出来

255
00:09:38,720 --> 00:09:40,879
但实际上它已经有了啊

256
00:09:40,879 --> 00:09:44,059
实际上我们这个场景里面已经有一个空节点了啊

257
00:09:44,059 --> 00:09:46,340
因为我们这个编辑器啊

258
00:09:46,340 --> 00:09:47,960
它实时刷新不出来

259
00:09:47,960 --> 00:09:49,509
是这样的啊

260
00:09:49,509 --> 00:09:55,600
那么我们现在这个方法是可以创建一个节点的

261
00:09:55,600 --> 00:10:00,039
那么如果我要对一个节点通过代码的方式添加组件

262
00:10:00,679 --> 00:10:03,440
我们知道平时添加组件是怎样添加的

263
00:10:03,440 --> 00:10:04,379
我们是这样的

264
00:10:04,399 --> 00:10:06,230
在这里添加一个组件

265
00:10:06,230 --> 00:10:08,840
或者直接把我们脚本脚本就是我们的组件

266
00:10:08,840 --> 00:10:09,139
对不对

267
00:10:09,139 --> 00:10:10,580
我们拖上来就行了

268
00:10:11,240 --> 00:10:15,440
那比如说我们要加一个通过代码的方式加组件

269
00:10:15,440 --> 00:10:15,980
怎么加

270
00:10:15,980 --> 00:10:18,440
我们直接note.i的confident

271
00:10:18,440 --> 00:10:19,519
上节课我们还说一下

272
00:10:19,519 --> 00:10:21,279
get confident得到组件

273
00:10:21,279 --> 00:10:22,899
还有一个i的confident

274
00:10:22,899 --> 00:10:25,419
就是添加一个组件啊

275
00:10:25,419 --> 00:10:26,779
添加一个组件

276
00:10:26,799 --> 00:10:28,360
添加一个组件的话

277
00:10:28,360 --> 00:10:30,399
我们在这里cc点

278
00:10:30,399 --> 00:10:32,440
比如说添加一个精灵渲染的组件

279
00:10:32,440 --> 00:10:33,860
就是cc disp

280
00:10:33,879 --> 00:10:38,460
那这样的话这个new呢本身也是属于一个精灵组件的啊

281
00:10:38,460 --> 00:10:40,740
它就会加了一个精灵组件

282
00:10:40,740 --> 00:10:42,029
ok啊

283
00:10:42,029 --> 00:10:43,899
那么这个就是我们

284
00:10:45,440 --> 00:10:48,590
呃这个创建组件

285
00:10:48,590 --> 00:10:50,240
创建这个节点和组件的方法

286
00:10:50,240 --> 00:10:51,919
当然我们主要不是说他啊

287
00:10:51,919 --> 00:10:53,379
我们只是顺便说一下

288
00:10:53,399 --> 00:10:54,779
那我们现在来说一下

289
00:10:54,779 --> 00:10:55,860
如果有预设题

290
00:10:55,860 --> 00:10:57,559
我们怎样通过代码创建

291
00:10:58,059 --> 00:11:01,179
呃其实很简单啊

292
00:11:01,179 --> 00:11:02,169
其实很简单

293
00:11:02,169 --> 00:11:03,759
我们来看一下

294
00:11:05,779 --> 00:11:11,120
那么为了我们呃一会儿看起来就是写代码的话

295
00:11:11,120 --> 00:11:12,259
看起来更加方便

296
00:11:12,259 --> 00:11:14,919
所以我这边这样嗯

297
00:11:17,059 --> 00:11:19,700
我们把这个地面删了

298
00:11:19,700 --> 00:11:21,049
只留一个空节点

299
00:11:21,049 --> 00:11:23,519
然后在空节点上面啊

300
00:11:23,519 --> 00:11:25,500
空间点就在零零位置上

301
00:11:25,500 --> 00:11:27,419
或者我们往中间稍微拖一拖

302
00:11:27,419 --> 00:11:30,460
然后我们给他这样的一个我们的脚本

303
00:11:30,480 --> 00:11:33,750
也就是说这个空节点目前的作用是干嘛的

304
00:11:33,750 --> 00:11:35,740
它不是为了显示内容的

305
00:11:35,740 --> 00:11:38,860
他就是为了去运行这个组件

306
00:11:38,860 --> 00:11:40,299
也就是说运行我们脚本的

307
00:11:40,299 --> 00:11:46,299
因为呃上节课咱们说这个脚本的时候说了脚本这个东西

308
00:11:46,299 --> 00:11:48,940
你它它里面的方法是会自动运行的

309
00:11:48,940 --> 00:11:50,980
但是它必须作为组件才行

310
00:11:50,980 --> 00:11:53,250
所以有时候为了运行脚本

311
00:11:53,250 --> 00:11:54,960
你也得创建一个空的组件

312
00:11:54,960 --> 00:11:57,259
这个空组件就是为了运行脚本

313
00:11:57,620 --> 00:12:01,220
那么在这里这个脚本这里我们打开它

314
00:12:02,100 --> 00:12:03,059
我们在这里

315
00:12:03,059 --> 00:12:08,360
比如说想通过代码实例化一个预设体啊

316
00:12:08,360 --> 00:12:10,820
想把这个地面给它实例化出来

317
00:12:10,820 --> 00:12:13,220
因为我们知道放上来就是实例化上来了

318
00:12:13,220 --> 00:12:13,919
对不对

319
00:12:14,740 --> 00:12:15,250
嗯

320
00:12:15,250 --> 00:12:16,840
不要删错删他

321
00:12:18,480 --> 00:12:22,080
那么在这里我们首先要实例哪个预设题

322
00:12:22,080 --> 00:12:26,299
我们肯定要把预设题放置写成一个属性

323
00:12:26,299 --> 00:12:29,299
然后从外界进行预设体的关联

324
00:12:34,899 --> 00:12:37,600
c c点预设题叫py five啊

325
00:12:37,600 --> 00:12:39,159
它不是一个普通的类型

326
00:12:39,159 --> 00:12:40,480
所以要让它的面板显示

327
00:12:40,480 --> 00:12:41,710
必须这样去写

328
00:12:41,710 --> 00:12:45,840
然后就比如说我们叫p r e啊

329
00:12:45,840 --> 00:12:49,759
然后cc.prefe啊

330
00:12:49,759 --> 00:12:52,220
默认是个空啊

331
00:12:52,220 --> 00:12:52,970
默认是个空

332
00:12:52,970 --> 00:12:57,620
那么这个就是我们的一个预设的预设体属性

333
00:12:57,620 --> 00:12:58,820
回到面板上

334
00:12:58,820 --> 00:13:03,289
我们来看一下有没有我们看见有这样一个呃

335
00:13:03,289 --> 00:13:05,029
有这样一个属性出来

336
00:13:05,029 --> 00:13:06,860
我们把它拖过去

337
00:13:06,860 --> 00:13:07,759
关联一下

338
00:13:09,240 --> 00:13:10,440
拖过来关联一下

339
00:13:10,440 --> 00:13:11,340
如果你关联错了

340
00:13:11,340 --> 00:13:12,960
你可以点叉取消了啊

341
00:13:12,960 --> 00:13:15,509
当然我们这关联一下就好了

342
00:13:15,509 --> 00:13:17,100
关联完了以后

343
00:13:17,460 --> 00:13:19,139
这时候他就有内容了

344
00:13:19,139 --> 00:13:20,759
它指向的就是那个预测题

345
00:13:20,759 --> 00:13:21,330
对不对

346
00:13:21,330 --> 00:13:22,320
我们通过代码

347
00:13:22,320 --> 00:13:23,879
比如在初始化的时候

348
00:13:23,879 --> 00:13:25,519
我们就让它实例化一个

349
00:13:27,980 --> 00:13:29,120
非常简单

350
00:13:29,120 --> 00:13:34,799
实例化预设体直接cc有一个方法叫inst啊

351
00:13:34,799 --> 00:13:38,620
就是实例化物体的直接把我们的预设体放到这里

352
00:13:38,700 --> 00:13:41,519
那么这里注意的是什么啊

353
00:13:41,519 --> 00:13:44,440
我们看一下啊

354
00:13:44,440 --> 00:13:48,879
this this this点

355
00:13:49,580 --> 00:13:51,620
那么当实力完成以后

356
00:13:51,620 --> 00:13:58,519
它能返回值就是一个我们我们的这个实例化以后的一个节点啊

357
00:13:58,519 --> 00:14:03,190
也就是说最后返回的结果就是一个节点

358
00:14:03,190 --> 00:14:05,679
那么这里一定要注意的是什么啊

359
00:14:05,679 --> 00:14:08,519
我们先看一下运行完有没有啊

360
00:14:10,340 --> 00:14:12,139
哎我们发现好像没有看到

361
00:14:12,139 --> 00:14:13,940
转一转屏幕也看不到对吧

362
00:14:13,940 --> 00:14:16,139
那么这个东西到底在哪里

363
00:14:16,299 --> 00:14:17,919
我们一定不要忘了啊

364
00:14:17,919 --> 00:14:19,659
我们那会儿也没有做这一步啊

365
00:14:19,659 --> 00:14:22,720
那会儿可能就是忘了把我们最后要做一步

366
00:14:22,720 --> 00:14:24,220
就是和刚才一样

367
00:14:24,220 --> 00:14:25,659
刚才我们也创建了节点了

368
00:14:25,659 --> 00:14:26,740
对不对啊

369
00:14:26,740 --> 00:14:28,279
刚才创建的节点

370
00:14:28,279 --> 00:14:31,460
那么这个节点只要是你先产生出来的

371
00:14:31,460 --> 00:14:36,080
你一定要给它设置一个负节点啊

372
00:14:36,639 --> 00:14:38,440
你要设置一个父节点

373
00:14:38,440 --> 00:14:41,759
那么这个副节点我们设置为谁

374
00:14:43,179 --> 00:14:46,899
我们设置为set parent啊

375
00:14:46,899 --> 00:14:51,220
就是我们新产出来的这个节点的父节点就是我自己这个节点啊

376
00:14:51,220 --> 00:14:53,620
就我这个空物体的这个节点啊

377
00:14:53,620 --> 00:14:56,129
这样的话当运行以后

378
00:14:56,129 --> 00:15:01,059
这个test下面实际上就会产生出咱们的这个节点了啊

379
00:15:01,059 --> 00:15:05,379
产生出咱们的这个用代码创建的这个物体了

380
00:15:05,379 --> 00:15:06,940
我们运行一下看一下

381
00:15:06,940 --> 00:15:09,279
你看有了吧

382
00:15:09,940 --> 00:15:11,080
有了啊

383
00:15:11,080 --> 00:15:13,750
那么这个就是我们要的要的东西

384
00:15:13,750 --> 00:15:15,700
我们也知道用代码创建出来了

385
00:15:15,700 --> 00:15:17,649
可是实际上你看盗墓运行以后

386
00:15:17,649 --> 00:15:20,779
这边这边他是不刷新的

387
00:15:20,779 --> 00:15:23,240
但是你知道它下面有一个就好啊

388
00:15:23,240 --> 00:15:24,919
你知道它下面有一个就好了

389
00:15:25,460 --> 00:15:27,320
那么这里有个刷新按钮

390
00:15:27,320 --> 00:15:28,580
它也不是刷新这儿的

391
00:15:28,580 --> 00:15:32,360
他是刷新你运行运行的那个网页的啊

392
00:15:32,360 --> 00:15:34,539
就是这个这个预览的它是刷新这

393
00:15:35,980 --> 00:15:37,899
所以这个注意一下啊

394
00:15:37,899 --> 00:15:40,990
这个注意一下嗯

395
00:15:40,990 --> 00:15:43,759
如果你以前学习别的有心情

396
00:15:43,799 --> 00:15:45,720
因为别的游戏引擎很多

397
00:15:45,720 --> 00:15:49,179
就是他从头到尾全是他自己去写的啊

398
00:15:49,179 --> 00:15:50,259
比如说这个有心情

399
00:15:50,259 --> 00:15:51,250
从脚本啊

400
00:15:51,250 --> 00:15:55,120
脚本控制到id到很多东西都是都是人家自己写的

401
00:15:55,120 --> 00:16:00,919
那他们的那个有行情可能会呃更新更新的比较实时一点啊

402
00:16:00,919 --> 00:16:04,360
就是你游戏跟这个界面永远同步啊

403
00:16:04,360 --> 00:16:05,980
像这个咱们这个cos create

404
00:16:05,980 --> 00:16:08,559
它毕竟是封装了一下cocs啊

405
00:16:08,559 --> 00:16:13,419
所以说他的很多东西就没办法做到非常好啊

406
00:16:13,419 --> 00:16:15,740
他们很多东西没办法做到非常好

407
00:16:15,740 --> 00:16:18,259
那比如说这个同步就是啊

408
00:16:18,259 --> 00:16:19,850
但是大家一定要知道

409
00:16:19,850 --> 00:16:21,019
就是运行完以后

410
00:16:21,019 --> 00:16:23,500
实际上他现在应该是个什么样的效果

411
00:16:23,519 --> 00:16:25,860
这个你应该知道啊

412
00:16:25,860 --> 00:16:32,870
ok那这个我们就说完了呃

413
00:16:32,870 --> 00:16:34,460
预设体这个东西啊

414
00:16:34,460 --> 00:16:35,480
后面很有用啊

415
00:16:35,480 --> 00:16:38,809
当然这节课嗯知道怎么样创建一个预设题

416
00:16:38,809 --> 00:16:40,759
然后怎样更新预设题

417
00:16:40,759 --> 00:16:42,740
怎样用代码创建就可以了

418
00:16:42,740 --> 00:16:46,899
我们下节课继续别的内容嗯


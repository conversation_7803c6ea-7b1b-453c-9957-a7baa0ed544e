1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,380 --> 00:00:14,179
那么我们这节课来说一下资源的一个动态加载啊

3
00:00:14,179 --> 00:00:14,960
什么意思

4
00:00:14,960 --> 00:00:17,780
什么是这个资源的动态加载啊

5
00:00:18,140 --> 00:00:21,980
那比如说我要把一张图片啊

6
00:00:21,980 --> 00:00:26,460
我要把这个图片啊加载到我们这个场景当中啊

7
00:00:26,460 --> 00:00:29,339
然后把它这个图片能赋值给一个精灵

8
00:00:29,339 --> 00:00:31,920
让一个精灵去显示这个图片啊

9
00:00:31,920 --> 00:00:34,020
那我们知道一个精灵显示谁

10
00:00:34,020 --> 00:00:36,520
实际上有一个属性叫spirit frame

11
00:00:36,539 --> 00:00:39,030
那么我们这边所有的图片

12
00:00:39,030 --> 00:00:43,420
每一个图片加载到我们场景里面都是spirit frame类型

13
00:00:43,420 --> 00:00:45,100
那它本来是个地面

14
00:00:45,100 --> 00:00:47,409
比如说我想通过代码的方式

15
00:00:47,409 --> 00:00:50,710
我加载其他的图片加载进来

16
00:00:50,710 --> 00:00:51,880
加载进来以后

17
00:00:51,880 --> 00:00:54,520
然后可以可以代替它啊

18
00:00:54,520 --> 00:00:56,759
或者说我们这有一个精灵

19
00:00:56,759 --> 00:00:59,159
但是这个精灵本来是没有

20
00:00:59,159 --> 00:01:00,420
是不会显示的

21
00:01:00,439 --> 00:01:04,829
但是我想通过代码把这个图片加载上来

22
00:01:04,829 --> 00:01:05,640
加载上来

23
00:01:05,640 --> 00:01:06,959
成了spring frame以后

24
00:01:06,959 --> 00:01:09,540
再把它赋值给这个精灵

25
00:01:09,540 --> 00:01:11,700
那像这种动态的这个资源加载

26
00:01:11,700 --> 00:01:13,040
我们怎么去做

27
00:01:13,060 --> 00:01:16,239
那么如果如果一个资源需要动态加载

28
00:01:16,239 --> 00:01:18,280
我们就要把它放到一个文件夹里

29
00:01:18,280 --> 00:01:21,239
这个文件夹叫做resource

30
00:01:21,439 --> 00:01:23,359
大小写都不能错啊

31
00:01:23,359 --> 00:01:25,640
这个resource是固定的一个文件夹

32
00:01:25,640 --> 00:01:26,959
如果你把它创建对了

33
00:01:26,959 --> 00:01:30,189
你可以看在右边就可以看到哎

34
00:01:30,189 --> 00:01:31,959
它有一个解释啊

35
00:01:31,959 --> 00:01:34,180
resource里面的资源可以怎样怎样怎样

36
00:01:34,180 --> 00:01:37,359
这个就证明你创建创建对了

37
00:01:37,359 --> 00:01:41,260
那么在它里面的资源都可以被动态加载呃

38
00:01:41,260 --> 00:01:43,599
你可以把资源直接放到它下面

39
00:01:43,599 --> 00:01:45,519
也可以创建多层文件夹

40
00:01:45,519 --> 00:01:46,540
这个已经不影响了

41
00:01:46,540 --> 00:01:49,700
比如说你里面可以再创建一层test啊

42
00:01:49,700 --> 00:01:50,659
这都无所谓

43
00:01:50,659 --> 00:01:51,739
创建几层都行

44
00:01:51,739 --> 00:01:54,900
但是最外层一定是resource

45
00:01:54,920 --> 00:01:57,469
它里面的东西才能被动态加载

46
00:01:57,469 --> 00:02:00,260
比如说我们就要把这个地面给它加载上来

47
00:02:00,260 --> 00:02:00,920
显示出来

48
00:02:00,920 --> 00:02:03,379
那我把这个地面放到test里面

49
00:02:03,379 --> 00:02:05,879
比如啊我放到这个test里面

50
00:02:06,500 --> 00:02:11,360
然后右边就可以看到这个图片相关的一些信息啊

51
00:02:11,379 --> 00:02:12,939
那么放到这个里面以后

52
00:02:12,939 --> 00:02:14,439
我们现在就要动态加载它

53
00:02:14,439 --> 00:02:14,979
怎么加载

54
00:02:14,979 --> 00:02:17,879
首先我需要写脚本了啊

55
00:02:17,879 --> 00:02:21,719
比如说我们把这个test脚本给了他啊

56
00:02:21,719 --> 00:02:26,219
那其实这些脚本大家可以创建新的脚本啊

57
00:02:26,219 --> 00:02:28,139
我是一直在用这一个脚本

58
00:02:28,139 --> 00:02:31,120
因为这个代码都比较简单

59
00:02:31,120 --> 00:02:32,560
没几行啊

60
00:02:32,560 --> 00:02:35,199
那么在这里啊

61
00:02:36,719 --> 00:02:38,360
之前的内容删了

62
00:02:38,620 --> 00:02:41,860
那么在这里这个脚本是用来做什么的

63
00:02:41,860 --> 00:02:43,840
我是要动态去加载这个资源了

64
00:02:43,840 --> 00:02:45,000
怎么加载

65
00:02:45,719 --> 00:02:47,819
他给我们提供了一个方法

66
00:02:47,819 --> 00:02:49,349
这个方法叫什么

67
00:02:49,349 --> 00:02:54,659
叫做cc.low的啊

68
00:02:54,659 --> 00:02:56,159
这个类是干嘛的

69
00:02:56,159 --> 00:03:02,050
这个类就是给我们去动态加载资源时候用的一个类啊

70
00:03:02,050 --> 00:03:05,379
它就是动态加载资源时候用的一个类

71
00:03:05,379 --> 00:03:09,139
那么在这里load我们直接点

72
00:03:11,400 --> 00:03:14,099
load rs就可以了

73
00:03:14,099 --> 00:03:16,689
它就是加载资源的啊

74
00:03:16,689 --> 00:03:20,379
这个和这个这个方法一定用这个方法以后

75
00:03:20,379 --> 00:03:27,270
那么在里面首先第一个参数你要填你加载的这个资源名称叫什么啊

76
00:03:27,270 --> 00:03:29,550
那么这个资源名称叫什么呢

77
00:03:29,550 --> 00:03:31,740
呃我们叫做烂

78
00:03:31,740 --> 00:03:32,659
对不对

79
00:03:34,460 --> 00:03:36,740
但是这里直接写烂的不行

80
00:03:36,740 --> 00:03:38,240
因为lp在test下面

81
00:03:38,240 --> 00:03:40,430
所以如果有这种层级关系的话

82
00:03:40,430 --> 00:03:44,939
我们在这里要这样test斜杠而来的

83
00:03:45,199 --> 00:03:48,680
这样的话他就先找test再找烂啊

84
00:03:48,680 --> 00:03:50,580
如果有层级关系就是这样的

85
00:03:50,599 --> 00:03:52,520
当加载完以后啊

86
00:03:52,520 --> 00:03:55,460
它这个并不是通过返回值给你这个资源的

87
00:03:55,460 --> 00:04:00,280
是逗号function啊

88
00:04:00,280 --> 00:04:04,879
它是通过这个这个回调给你的啊

89
00:04:04,879 --> 00:04:06,080
它通过这个回调给你的

90
00:04:06,080 --> 00:04:06,860
为什么

91
00:04:06,860 --> 00:04:10,159
因为我不知道这个返回值是立刻会拿到的

92
00:04:10,159 --> 00:04:11,419
但是资源加载

93
00:04:11,419 --> 00:04:12,500
因为是读内存

94
00:04:12,500 --> 00:04:15,560
或者这个是可以填写网络地址的啊

95
00:04:15,560 --> 00:04:16,220
是可以填写

96
00:04:16,220 --> 00:04:22,980
比如http 3 w点什么什么什么什么什么点com斜杠1.png

97
00:04:22,980 --> 00:04:26,579
啊它是可以直接放这个东西去下载这个图片的

98
00:04:26,600 --> 00:04:29,600
如果你通过在线的方式加载了一个图片

99
00:04:29,600 --> 00:04:32,779
那么大家想这个图片从网上下载下来还是需要时间的

100
00:04:32,779 --> 00:04:33,410
对不对

101
00:04:33,410 --> 00:04:39,860
那么他呢如果我们通过返回值要拿到这个图片

102
00:04:39,860 --> 00:04:41,620
那么大家去想

103
00:04:41,839 --> 00:04:44,060
也就意味着一执行这个方法以后

104
00:04:44,060 --> 00:04:45,800
首先我们程序会卡死

105
00:04:45,800 --> 00:04:46,759
为什么卡斯

106
00:04:46,759 --> 00:04:48,350
因为他得不到返回值

107
00:04:48,350 --> 00:04:50,660
你下载这个图片是需要时间的

108
00:04:50,660 --> 00:04:51,339
对不对

109
00:04:51,339 --> 00:04:53,259
那么这时候就会产生问题

110
00:04:53,259 --> 00:04:54,939
那么所以为了解决这个问题

111
00:04:54,939 --> 00:04:58,800
这里就用了这个呃函数啊

112
00:04:58,800 --> 00:05:00,389
用了这个回调啊

113
00:05:00,389 --> 00:05:03,139
它那么这里注意一点啊

114
00:05:03,560 --> 00:05:06,660
比如说这里有代码回调

115
00:05:06,660 --> 00:05:07,800
里面也有代码

116
00:05:07,800 --> 00:05:09,839
到底是哪个代码先执行

117
00:05:09,839 --> 00:05:11,160
这个是不确定的了啊

118
00:05:11,160 --> 00:05:13,740
这两块哪块代码先执行是不确定的啊

119
00:05:13,740 --> 00:05:15,079
就是这个代码顺序

120
00:05:15,079 --> 00:05:16,420
因为我说了

121
00:05:16,420 --> 00:05:18,040
他这个从网上

122
00:05:18,040 --> 00:05:19,660
比如说现在下载啊

123
00:05:19,660 --> 00:05:20,560
下载一个图片

124
00:05:20,560 --> 00:05:22,240
或者是从内存加载图片

125
00:05:22,240 --> 00:05:23,620
可能是耗时的

126
00:05:23,620 --> 00:05:25,660
在耗时加载的时候

127
00:05:25,660 --> 00:05:30,519
他这个交给就相当于呃这个这个轴我们后面后面说了

128
00:05:30,519 --> 00:05:32,139
这个同步异步的案就清楚了

129
00:05:32,139 --> 00:05:33,600
我现在就简单说下

130
00:05:33,600 --> 00:05:37,720
就好比我主要去加载这个图片的人啊

131
00:05:37,720 --> 00:05:40,060
然后一发现要加载资源了

132
00:05:40,060 --> 00:05:42,480
我就找一个人帮我去加载啊

133
00:05:42,480 --> 00:05:43,860
我找一个人帮我去加载

134
00:05:43,860 --> 00:05:45,060
然后我紧接着干嘛

135
00:05:45,060 --> 00:05:46,579
我要下去执行代码了

136
00:05:46,600 --> 00:05:48,699
当那个人把图片加载完以后

137
00:05:48,699 --> 00:05:51,129
我再回来执行这里面的代码

138
00:05:51,129 --> 00:05:54,279
也就是说这里面的代码就是什么时候这个图片加载完了

139
00:05:54,279 --> 00:05:55,779
我什么时候执行啊

140
00:05:55,779 --> 00:05:59,860
所以这两个部分的代码顺序啊先后是不一定的

141
00:05:59,860 --> 00:06:03,519
那么这个东西大家现在有点印象行了啊

142
00:06:03,519 --> 00:06:05,860
因为咱们还没有说到同步异步啊

143
00:06:05,860 --> 00:06:07,449
说到同步异步的时候

144
00:06:07,449 --> 00:06:10,439
对这个东西你就明白了啊

145
00:06:12,139 --> 00:06:16,459
ok那么总之就是我们现在要在这个里面写代码

146
00:06:16,459 --> 00:06:19,100
因为在里面我们能确保这个图片已经下载完了

147
00:06:19,100 --> 00:06:20,620
那这个图片在哪呢

148
00:06:20,620 --> 00:06:21,939
在这里面有两个参数

149
00:06:21,939 --> 00:06:23,620
第一个是输出的

150
00:06:23,620 --> 00:06:24,779
就是是否有错误

151
00:06:24,779 --> 00:06:26,579
如果下载完有错误啊

152
00:06:26,579 --> 00:06:28,949
你可以把这个错误错误输出出来

153
00:06:28,949 --> 00:06:30,420
如果没错误

154
00:06:30,740 --> 00:06:34,399
他呢就会把这个资源下载下来了啊

155
00:06:34,399 --> 00:06:36,709
那么比如说我们这个资源名字

156
00:06:36,709 --> 00:06:40,449
我们就就叫sp版啊

157
00:06:40,449 --> 00:06:44,939
sp呃它是什么类型啊

158
00:06:44,939 --> 00:06:46,779
就是你下载的是什么类型

159
00:06:46,779 --> 00:06:49,839
我们可以不指定也可以指定啊

160
00:06:49,839 --> 00:06:51,100
指定的话就要在这里

161
00:06:51,100 --> 00:06:55,019
比如说我们这个下载或者加载的资源是个图片

162
00:06:55,019 --> 00:06:58,459
所以它的结果就是cc加cret frame

163
00:06:58,480 --> 00:07:00,639
它就是一个精灵的呃

164
00:07:00,639 --> 00:07:02,560
一帧的这样的一个图片啊

165
00:07:02,560 --> 00:07:07,779
哈那么所有的图片加载上来都是都是这个类型啊

166
00:07:07,779 --> 00:07:09,399
它就类似于精灵里面的一帧

167
00:07:09,399 --> 00:07:10,139
对不对

168
00:07:11,379 --> 00:07:14,980
呃也就是说这里指定了类型

169
00:07:14,980 --> 00:07:16,480
实际上这个类型就是谁

170
00:07:16,480 --> 00:07:17,860
就是这个sp啊

171
00:07:17,860 --> 00:07:19,000
就是这个s

172
00:07:20,339 --> 00:07:21,959
那么你也可以不加载啊

173
00:07:21,959 --> 00:07:22,860
你也可以不写类型

174
00:07:22,860 --> 00:07:24,300
当然一般推荐写上

175
00:07:24,300 --> 00:07:27,420
因为有可能比如说有两个文件同名啊

176
00:07:27,420 --> 00:07:28,860
但是它的后缀名不一样

177
00:07:28,860 --> 00:07:30,000
这是允许存在的

178
00:07:30,000 --> 00:07:32,399
这时候如果你不加这个类型

179
00:07:32,399 --> 00:07:35,350
有可能文件名就会产生冲突了

180
00:07:35,350 --> 00:07:35,980
对不对

181
00:07:35,980 --> 00:07:38,079
就他不知道你加载的是哪一个

182
00:07:38,660 --> 00:07:40,160
那么这个拿到以后

183
00:07:40,160 --> 00:07:42,439
我们现在我们想给它赋值

184
00:07:42,439 --> 00:07:43,639
我们看一下怎么赋值啊

185
00:07:43,639 --> 00:07:49,980
首先我test现在是加载到我们的精灵身上的

186
00:07:49,980 --> 00:07:50,800
对不对

187
00:07:51,160 --> 00:07:53,259
唉我先要获取这个精灵组件

188
00:07:53,259 --> 00:07:54,360
再给它复制

189
00:07:55,259 --> 00:07:57,899
那么在这里我们就应该怎么去写了

190
00:07:57,899 --> 00:08:00,629
是不是this.get confident

191
00:08:00,629 --> 00:08:02,420
get get诶

192
00:08:02,420 --> 00:08:04,850
但是我们发现get confident找不见了

193
00:08:04,850 --> 00:08:08,779
这是为什么this.node node也找不见啊

194
00:08:08,779 --> 00:08:09,560
这是注意啊

195
00:08:09,560 --> 00:08:10,879
在这种匿名方法里面

196
00:08:10,879 --> 00:08:12,740
有时候我们是调不到this的

197
00:08:12,740 --> 00:08:14,420
调不到z怎么办

198
00:08:14,420 --> 00:08:18,199
有一种方法就是let随便在这儿写个变量啊

199
00:08:18,199 --> 00:08:22,329
比如说你可以叫变量a b c或者叫cf啊都行

200
00:08:22,329 --> 00:08:23,019
这取个名

201
00:08:23,019 --> 00:08:26,040
比如说self变量等于this啊

202
00:08:26,040 --> 00:08:27,660
让cf等于this了

203
00:08:27,660 --> 00:08:31,139
那这时候在这里虽然虽然不能直接叫this

204
00:08:31,139 --> 00:08:36,190
但是啊我们可以通过这个self来调用啊

205
00:08:36,190 --> 00:08:38,470
这是它里面的一个方法

206
00:08:38,470 --> 00:08:40,639
get competent在里面

207
00:08:41,220 --> 00:08:44,220
spirit我们要得到精灵组件

208
00:08:44,220 --> 00:08:45,629
得到精灵组件以后

209
00:08:45,629 --> 00:08:48,419
精灵有一个属性叫做spirit frame

210
00:08:48,419 --> 00:08:49,220
对不对

211
00:08:49,720 --> 00:08:53,139
spirit frame我们直接给他等于就行了

212
00:08:53,139 --> 00:08:56,440
就等于我们下载好的这个sp就搞定了

213
00:08:56,440 --> 00:08:57,720
我们运行一下

214
00:09:01,460 --> 00:09:03,659
诶是不是已经出来了

215
00:09:03,679 --> 00:09:05,360
已经显示出来了啊

216
00:09:05,360 --> 00:09:07,549
就是说它本来你看它是没图片的

217
00:09:07,549 --> 00:09:10,860
我们通过代码动态把这个图片加载了进来

218
00:09:10,879 --> 00:09:12,440
然后把它显示了出来

219
00:09:12,440 --> 00:09:14,779
那么这个就是图片的一个动态加载

220
00:09:14,799 --> 00:09:17,299
那么对于图集而言啊

221
00:09:17,299 --> 00:09:20,000
我们想也是应该可以加载的

222
00:09:20,000 --> 00:09:22,960
就是加载不光是可以加载单张图片

223
00:09:22,960 --> 00:09:24,850
图集也是可以加载的

224
00:09:24,850 --> 00:09:27,399
那在这里咱们有一对图集

225
00:09:27,399 --> 00:09:28,120
是不是

226
00:09:28,120 --> 00:09:30,159
而我们把它放到里面来

227
00:09:30,159 --> 00:09:32,590
那么这个东西怎样去加载

228
00:09:32,590 --> 00:09:34,519
首先我们打开我们的脚本

229
00:09:34,740 --> 00:09:36,299
那么这是我们刚才写的

230
00:09:36,299 --> 00:09:37,960
我们给它注释掉

231
00:09:40,440 --> 00:09:42,779
我们在这里cc.load

232
00:09:42,779 --> 00:09:44,899
点load i

233
00:09:45,080 --> 00:09:50,000
那我们现在就sdr就是加载很多资源了啊

234
00:09:50,000 --> 00:09:51,500
就不是一张资源了

235
00:09:51,500 --> 00:09:53,659
第二就是就是一个目录的意思

236
00:09:53,659 --> 00:09:55,700
就相当于加载了一个文件目录

237
00:09:55,700 --> 00:10:00,000
那么在这个里面第一个参数是test 1

238
00:10:00,000 --> 00:10:02,460
因为我们图集就是一类型

239
00:10:02,460 --> 00:10:03,120
叫什么

240
00:10:03,120 --> 00:10:03,659
注意啊

241
00:10:03,659 --> 00:10:06,940
这里我们加载的类型就叫图集

242
00:10:06,940 --> 00:10:09,159
而不是精灵真了啊

243
00:10:09,159 --> 00:10:11,289
是这个图集不是图片

244
00:10:11,289 --> 00:10:13,899
是图集加载的上来就是一个集合

245
00:10:13,899 --> 00:10:19,480
那所以后面这个函数第一个参数是什么error

246
00:10:19,480 --> 00:10:22,879
第二个参数其实就是我们的图集

247
00:10:24,980 --> 00:10:26,809
类型c c加

248
00:10:26,809 --> 00:10:28,820
我们最好把类型写上啊

249
00:10:28,820 --> 00:10:30,279
尽量把类型写上

250
00:10:32,320 --> 00:10:37,779
那么哦他这里报错了类型

251
00:10:37,779 --> 00:10:40,360
与它提供的不匹配

252
00:10:40,360 --> 00:10:41,559
怎么不匹配呢

253
00:10:44,399 --> 00:10:46,350
类型

254
00:10:46,350 --> 00:10:48,679
蓝色的

255
00:10:50,440 --> 00:10:51,940
啊在这里啊

256
00:10:51,940 --> 00:10:52,960
这里是不用的啊

257
00:10:52,960 --> 00:10:54,850
这是老的方法啊

258
00:10:54,850 --> 00:10:58,200
这个是之前写是用这种方法啊

259
00:10:58,200 --> 00:11:00,419
现在我们加载已经不需要那个啊

260
00:11:00,419 --> 00:11:06,120
就是以前在这个应该是一一版本1.1.11.2版本之前

261
00:11:06,120 --> 00:11:08,940
他是用那个dr的方法去加载的啊

262
00:11:08,940 --> 00:11:09,899
现在不需要了啊

263
00:11:09,899 --> 00:11:12,320
现在直接用这个漏的r e s就可以了啊

264
00:11:12,320 --> 00:11:14,779
它就能给你加载出来这样一个图集了啊

265
00:11:14,779 --> 00:11:15,620
就不需要用别的了

266
00:11:15,620 --> 00:11:18,240
我们所以我们就记住lol s就可以了

267
00:11:18,240 --> 00:11:19,779
然后在这里面

268
00:11:21,600 --> 00:11:28,220
a self.get confid c c点

269
00:11:28,220 --> 00:11:32,220
这里注意还是先获取那个精灵啊

270
00:11:32,220 --> 00:11:34,259
然后精灵的一张图片

271
00:11:34,259 --> 00:11:36,330
我们还是给它赋值赋值

272
00:11:36,330 --> 00:11:39,210
我们就要从这个图集里面这次去取了

273
00:11:39,210 --> 00:11:40,539
怎么去取

274
00:11:40,620 --> 00:11:42,240
把图纸写出来

275
00:11:42,240 --> 00:11:43,769
直接点get

276
00:11:43,769 --> 00:11:46,740
你看从图集里面得到一个图片

277
00:11:46,740 --> 00:11:48,210
得到哪个图片

278
00:11:48,210 --> 00:11:51,419
你只需要把图片的名称填上就行了

279
00:11:51,419 --> 00:11:54,480
比如说这个bg几个g

280
00:11:54,480 --> 00:11:57,000
我得这个背景这个大好看一点

281
00:11:57,340 --> 00:12:00,779
那我们在这里就是bg d g d

282
00:12:01,299 --> 00:12:03,039
然后我们来运行一下

283
00:12:03,039 --> 00:12:08,500
看一下能不能把它加载出来运行我们看一下是不是加载出来了

284
00:12:08,500 --> 00:12:10,240
加载出来了没有问题啊

285
00:12:10,240 --> 00:12:13,059
所以说这个用动态的方式去加载

286
00:12:13,059 --> 00:12:15,250
不管加载单张图片也可以

287
00:12:15,250 --> 00:12:17,850
图集也依然可以啊

288
00:12:17,850 --> 00:12:22,559
ok啊现在这个突击加载也是非常的简单了

289
00:12:22,840 --> 00:12:25,299
跟这个普通加载基本上没区别

290
00:12:25,299 --> 00:12:27,519
唯一区别就是类型不一样

291
00:12:27,519 --> 00:12:28,720
得到的图集图集

292
00:12:28,720 --> 00:12:30,460
然后再得到图片是吧

293
00:12:30,840 --> 00:12:34,240
嗯几乎代码没什么太大区别啊

294
00:12:34,720 --> 00:12:38,379
o k那么这个就是动态加载啊

295
00:12:38,379 --> 00:12:40,539
这个就是动态加载呃

296
00:12:40,539 --> 00:12:43,679
对于这个资源的加载啊

297
00:12:43,679 --> 00:12:45,720
这个动态加载是一定要会的啊

298
00:12:45,720 --> 00:12:46,980
虽然也没几行代码

299
00:12:46,980 --> 00:12:49,200
但是还是一定要记着的啊

300
00:12:49,200 --> 00:12:52,019
我们常常需要动态加载啊

301
00:12:52,019 --> 00:12:55,720
在做这个大型项目的时候嗯那行

302
00:12:55,720 --> 00:12:57,679
那我们这节课就这么多内容


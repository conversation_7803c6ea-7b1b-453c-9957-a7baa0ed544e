1
00:00:09,900 --> 00:00:14,208
ok这节课咱们来说一下物理引擎啊

2
00:00:14,208 --> 00:00:15,198
物理引擎

3
00:00:15,198 --> 00:00:18,320
那么这个物理引擎的话呃

4
00:00:18,320 --> 00:00:21,710
我们这节课主要来说一下怎样去启用它啊

5
00:00:21,710 --> 00:00:24,780
以及启用它以后的一些基本属性

6
00:00:25,278 --> 00:00:27,649
那么我们来看一下啊

7
00:00:27,649 --> 00:00:28,998
我们做游戏

8
00:00:28,998 --> 00:00:31,818
实际上很多游戏都会受到物理的影响

9
00:00:31,818 --> 00:00:35,030
比如说我们的这个flappy bird啊

10
00:00:35,030 --> 00:00:36,079
像这种游戏

11
00:00:36,079 --> 00:00:38,240
如果我这里有一只鸟的话啊

12
00:00:38,240 --> 00:00:39,469
有一只小鸟的话

13
00:00:39,469 --> 00:00:41,850
那么它正常是向下去掉的

14
00:00:41,850 --> 00:00:44,490
正常是向下去掉落的啊

15
00:00:44,490 --> 00:00:45,750
那么越掉越快

16
00:00:45,750 --> 00:00:46,450
对不对

17
00:00:46,450 --> 00:00:49,210
那么这就是物理系统啊

18
00:00:49,210 --> 00:00:50,170
这就是物理系统

19
00:00:50,170 --> 00:00:52,570
如果我们做一个超级马里奥的话

20
00:00:52,570 --> 00:00:54,899
马里奥可以在地面上左右移动

21
00:00:54,899 --> 00:00:57,450
当它移动到一个位置

22
00:00:57,450 --> 00:01:00,058
我们可以按空格让他起跳

23
00:01:00,060 --> 00:01:02,909
跳起来以后并不是无限往天上跳了

24
00:01:02,909 --> 00:01:04,260
诶跳起来以后诶

25
00:01:04,260 --> 00:01:07,680
它会逐渐再就是跳到最高点

26
00:01:07,680 --> 00:01:09,939
然后逐渐下落啊

27
00:01:09,939 --> 00:01:12,189
就形成这样一个抛物线的状态是吧

28
00:01:12,189 --> 00:01:13,719
之所以能做到这样的事

29
00:01:13,719 --> 00:01:15,950
也都是物理效果啊

30
00:01:15,950 --> 00:01:16,849
也都是物理效果

31
00:01:16,849 --> 00:01:20,859
因为它是模仿一个真实世界里面的一些物理特性的

32
00:01:21,599 --> 00:01:25,700
那么我们之前做过一个检测是吧

33
00:01:25,700 --> 00:01:26,900
做过一个碰撞检测

34
00:01:26,900 --> 00:01:27,859
我们学习过啊

35
00:01:27,859 --> 00:01:31,579
那个碰撞检测就是指没有物理系统的情况下

36
00:01:31,579 --> 00:01:35,030
那么当有了物理系统的情况下以后

37
00:01:35,030 --> 00:01:38,060
我们后面会再学习一套碰撞检测

38
00:01:38,280 --> 00:01:41,519
但这一套和之前那一套是完全不一样的啊

39
00:01:41,519 --> 00:01:42,989
大家注意一下啊

40
00:01:42,989 --> 00:01:45,329
所以以后大家在这个写游戏的时候

41
00:01:45,329 --> 00:01:46,709
需要用到物理系统

42
00:01:46,709 --> 00:01:48,150
咱们就用这套碰撞检测

43
00:01:48,150 --> 00:01:48,750
不需要的话

44
00:01:48,750 --> 00:01:50,120
就用之前那一套

45
00:01:50,120 --> 00:01:53,040
那么首先物理系统呃

46
00:01:53,040 --> 00:01:55,439
怎样去开启这个物理系统啊

47
00:01:55,439 --> 00:01:57,280
怎样开启这个物理系统

48
00:01:57,480 --> 00:02:01,000
那比如说啊我们做这样一个操作啊

49
00:02:04,159 --> 00:02:06,099
我们再拖上来一个物体吧

50
00:02:06,099 --> 00:02:08,199
比如说拖上来一个小鸟啊

51
00:02:08,199 --> 00:02:09,580
比如拖上来一个小鸟

52
00:02:09,580 --> 00:02:16,259
那么如果我希望那个小鸟它是受到物理系统影响的啊

53
00:02:16,259 --> 00:02:17,819
也就是说我希望让它上来

54
00:02:17,819 --> 00:02:19,800
它就会向下掉落啊

55
00:02:19,800 --> 00:02:21,300
它是受到这个物理影响的

56
00:02:21,300 --> 00:02:22,099
怎么办

57
00:02:22,118 --> 00:02:23,258
那么注意啊

58
00:02:23,258 --> 00:02:27,439
在我们游戏里面所有需要呃

59
00:02:27,439 --> 00:02:30,289
这个受到物理影响的这个这个节点

60
00:02:30,289 --> 00:02:32,939
我们都要给它添加一个组件啊

61
00:02:32,939 --> 00:02:34,560
这个组件就叫钢铁

62
00:02:34,560 --> 00:02:37,110
实际上整个我们这个cos creator

63
00:02:37,110 --> 00:02:41,550
它是把一个叫做box 2 d的一个物理引擎封装到里面了啊

64
00:02:41,550 --> 00:02:46,840
那大家有兴趣的话可以去查一下这个box 2 d这个物理引擎啊

65
00:02:47,039 --> 00:02:51,060
那么我们在这里有一个添加碰撞组件

66
00:02:51,060 --> 00:02:51,419
对不对

67
00:02:51,419 --> 00:02:52,620
也也有一个物理

68
00:02:52,620 --> 00:02:56,740
我们选择物理最后一个叫做钢铁

69
00:02:56,740 --> 00:03:00,158
所有的这个节点啊

70
00:03:00,158 --> 00:03:02,558
任何一个节点只要加了刚体

71
00:03:02,558 --> 00:03:05,748
那么他们呢就参与这个物理计算了啊

72
00:03:05,748 --> 00:03:07,429
他们就受到这个物理影响了

73
00:03:07,429 --> 00:03:11,139
也就相当于我们让它把它放在一个物理世界中了

74
00:03:11,319 --> 00:03:13,539
所以看似比如说一个游戏很复杂

75
00:03:13,539 --> 00:03:14,590
上面很多东西

76
00:03:14,590 --> 00:03:19,938
但是如果我们看哪一些组件受到物理的影响啊

77
00:03:19,938 --> 00:03:22,340
可能就没有那么多了啊

78
00:03:22,340 --> 00:03:26,360
比如说如果现在看似有一个小鸟一个背景对吧

79
00:03:26,360 --> 00:03:28,889
但实际上在物理的世界里面

80
00:03:28,889 --> 00:03:30,149
这个背景是不存在的

81
00:03:30,149 --> 00:03:31,949
因为我们没有给它加钢铁

82
00:03:31,949 --> 00:03:33,929
所以实际上在物理的世界里面

83
00:03:33,929 --> 00:03:36,740
它就有一只小鸟在这个位置啊

84
00:03:36,939 --> 00:03:38,439
那么就是这样的

85
00:03:38,679 --> 00:03:40,899
那么这个小鸟啊

86
00:03:40,899 --> 00:03:42,968
它有了这个缸体了

87
00:03:42,968 --> 00:03:45,930
那么它呢现在就会受到重力的影响了

88
00:03:45,930 --> 00:03:47,069
受到重力影响

89
00:03:47,069 --> 00:03:49,289
也就是说它会从这里往下掉落了

90
00:03:49,289 --> 00:03:50,250
那我们先试一下

91
00:03:50,250 --> 00:03:55,300
是不是我运行完以后发现诶他怎么还在这里

92
00:03:55,300 --> 00:03:56,680
它并没有向下掉落

93
00:03:56,680 --> 00:03:57,370
对不对

94
00:03:57,370 --> 00:03:58,870
哎这是怎么回事

95
00:03:58,870 --> 00:04:02,180
这个不是加了它就会受到这个物理的影响了吗

96
00:04:02,299 --> 00:04:04,399
那么我们这里注意啊

97
00:04:04,399 --> 00:04:06,819
我们在这里创建一个新的脚本

98
00:04:09,718 --> 00:04:14,219
啊创建一个新的脚本小鸟的一个脚本

99
00:04:14,699 --> 00:04:15,750
物理啊

100
00:04:15,750 --> 00:04:20,319
物理系统和咱们之前的那个呃碰撞啊

101
00:04:20,319 --> 00:04:23,600
其实有一点类似之前的那个碰撞系统

102
00:04:23,600 --> 00:04:24,680
大家大家记得啊

103
00:04:24,680 --> 00:04:26,240
就是你在碰撞之前

104
00:04:26,240 --> 00:04:29,300
你要开启一个碰撞检测系统

105
00:04:29,439 --> 00:04:30,759
而物理也一样

106
00:04:30,759 --> 00:04:34,829
因为物理这个本身啊这个东西它是好性能的啊

107
00:04:34,829 --> 00:04:36,569
就是物理特性是好性能的

108
00:04:36,569 --> 00:04:39,420
越多的精灵参与这个物理计算

109
00:04:39,420 --> 00:04:41,370
那么性能也就消耗的越厉害

110
00:04:41,370 --> 00:04:44,930
所以默认这个物理功能它是关闭的

111
00:04:44,930 --> 00:04:47,420
我们在这里把脚本拖上来

112
00:04:47,420 --> 00:04:53,788
在脚本里面我们要把它的物理这个系统开启啊

113
00:04:53,788 --> 00:04:55,500
那么这里还注意啊

114
00:04:56,399 --> 00:04:57,319
物理系统

115
00:04:57,319 --> 00:04:59,600
它这个要求必须在onload里面开启

116
00:04:59,600 --> 00:05:01,668
在start里面开启是不管用的

117
00:05:01,668 --> 00:05:03,649
在onload里面开启啊

118
00:05:03,649 --> 00:05:07,519
所以在这里unload里面我们就可以cc.director

119
00:05:07,519 --> 00:05:09,920
点get physics manager

120
00:05:09,920 --> 00:05:11,060
物理管理器

121
00:05:11,060 --> 00:05:15,560
点in a b等于tru开启物理的一个管理系统

122
00:05:15,959 --> 00:05:16,980
只要开启以后

123
00:05:16,980 --> 00:05:18,899
我们再次运行诶

124
00:05:18,899 --> 00:05:21,040
你看这个小鸟掉下去了

125
00:05:21,040 --> 00:05:23,259
有没有这个小鸟就掉下来了

126
00:05:23,259 --> 00:05:25,300
他就受到这个物理的影响啊

127
00:05:25,300 --> 00:05:26,839
他就受到物理的影响

128
00:05:27,420 --> 00:05:30,420
那么在这里我们

129
00:05:32,300 --> 00:05:34,120
我们到这边回到这边

130
00:05:34,120 --> 00:05:35,470
那么在这里啊

131
00:05:35,470 --> 00:05:37,730
对于钢铁来说啊

132
00:05:37,730 --> 00:05:39,740
加上钢体它就受到物理影响了

133
00:05:39,740 --> 00:05:42,589
所以每一个缸体上面有很多属性

134
00:05:42,589 --> 00:05:45,490
规定了当前这个精灵啊

135
00:05:45,490 --> 00:05:48,329
规定了这个精灵或者说这个节点啊

136
00:05:48,329 --> 00:05:54,129
规定了它是以怎样的物理形式存在于我们的物理世界当中的啊

137
00:05:54,129 --> 00:05:56,350
那么在在这里我们可以看一下啊

138
00:05:56,350 --> 00:05:57,850
右边的这些内容

139
00:05:57,850 --> 00:06:00,319
我们一个一个去讲解一下啊

140
00:06:01,019 --> 00:06:08,069
那么首先啊第一个第一个就是是否开启碰撞检测啊

141
00:06:08,069 --> 00:06:10,230
这个跟咱们之前那个碰撞检测一样的

142
00:06:10,230 --> 00:06:12,629
如果你希望它和别的物体碰到以后

143
00:06:12,629 --> 00:06:14,040
碰撞检测是开启的

144
00:06:14,040 --> 00:06:15,879
你就把它勾上啊

145
00:06:15,879 --> 00:06:18,550
那么只是他这个直接给我们变成一个属性

146
00:06:18,550 --> 00:06:19,209
比较方便

147
00:06:19,209 --> 00:06:20,050
不用写代码

148
00:06:20,050 --> 00:06:21,079
勾上就行

149
00:06:21,098 --> 00:06:22,389
第二个是什么

150
00:06:22,389 --> 00:06:25,298
它的这个解释其实已经很好了啊

151
00:06:25,298 --> 00:06:27,959
缸体是否是一个快速移动的钢铁

152
00:06:28,158 --> 00:06:29,778
什么意思意思啊

153
00:06:29,778 --> 00:06:34,759
比如说大家大家玩那种打枪的游戏是吧啊

154
00:06:34,759 --> 00:06:36,259
我们拿三个游戏举例子啊

155
00:06:36,259 --> 00:06:38,598
就比如说那种射击游戏现在很火的吃鸡

156
00:06:38,598 --> 00:06:39,319
对不对

157
00:06:39,319 --> 00:06:40,579
你打一发子弹

158
00:06:40,579 --> 00:06:42,850
这个子弹的移动速度太快了

159
00:06:42,850 --> 00:06:44,470
如果你子弹不过去

160
00:06:44,470 --> 00:06:47,279
这边有一堵特别薄的墙啊

161
00:06:47,279 --> 00:06:49,560
比如说这里有一个特别薄的墙

162
00:06:49,560 --> 00:06:51,480
然后你子弹也特别快的速度

163
00:06:51,480 --> 00:06:52,920
比如说从这儿飞过去

164
00:06:52,920 --> 00:06:55,069
那么有可能发生的是什么事

165
00:06:55,069 --> 00:06:57,680
这个子弹快速的穿过了这堵墙

166
00:06:57,680 --> 00:07:00,860
但是这个墙却和它没有产生计算

167
00:07:01,860 --> 00:07:04,879
那么这个情况是有可能存在的啊

168
00:07:04,879 --> 00:07:06,100
有可能存在的

169
00:07:06,360 --> 00:07:07,800
这是一个墙啊

170
00:07:07,800 --> 00:07:09,689
这是一个高速飞行的子弹

171
00:07:09,689 --> 00:07:13,470
当我飞的时候很有可能怎样了

172
00:07:13,470 --> 00:07:15,449
没有和这个墙发生碰撞

173
00:07:15,449 --> 00:07:16,529
直接冲过去了

174
00:07:16,529 --> 00:07:18,389
这个在物理世界当中啊

175
00:07:18,389 --> 00:07:21,329
这个在我们这个模拟的物理世界世界当中啊

176
00:07:21,329 --> 00:07:23,819
是可能存在的嗯

177
00:07:25,300 --> 00:07:27,480
这为什么会可能存在

178
00:07:27,480 --> 00:07:29,980
我们这样大家去看一下就明白了

179
00:07:30,779 --> 00:07:38,069
比如说这个这个这个这个我们把它的这个比如说这是一个墙

180
00:07:38,069 --> 00:07:38,759
很窄

181
00:07:38,759 --> 00:07:39,509
对不对

182
00:07:39,509 --> 00:07:45,060
比如说这个小鸟一针每一帧移动50像素

183
00:07:45,060 --> 00:07:46,680
那么也就是说第一针在这里

184
00:07:46,680 --> 00:07:47,699
第二针在这里

185
00:07:47,699 --> 00:07:48,779
第三针在这里

186
00:07:48,779 --> 00:07:49,980
第四针在这里

187
00:07:49,980 --> 00:07:51,000
第五针在这里

188
00:07:51,000 --> 00:07:52,149
第六针在这里

189
00:07:52,149 --> 00:07:54,189
第七章可能在这个墙的前面

190
00:07:54,189 --> 00:07:54,829
对不对

191
00:07:54,829 --> 00:07:56,569
在第八针的时候怎样

192
00:07:56,569 --> 00:07:59,029
他可能刚好已经自己就穿过墙了

193
00:07:59,029 --> 00:08:01,670
那么这样的话这个墙压根和他就怎样

194
00:08:01,670 --> 00:08:03,839
就没有产生碰撞啊

195
00:08:03,839 --> 00:08:04,800
没有产生碰撞

196
00:08:04,800 --> 00:08:06,420
所以这时候就会产生问题了

197
00:08:06,420 --> 00:08:07,560
就是你做游戏的时候

198
00:08:07,560 --> 00:08:09,600
明明一个子弹打到一个墙上了

199
00:08:09,600 --> 00:08:12,639
你发现这个墙没检测到这个子弹啊

200
00:08:12,639 --> 00:08:14,319
而且这个子弹也穿过这堵墙

201
00:08:14,319 --> 00:08:16,028
往后面继续打出去了

202
00:08:16,028 --> 00:08:18,000
这个就产生问题了啊

203
00:08:18,180 --> 00:08:20,370
如果遇到这样的情况

204
00:08:20,370 --> 00:08:24,620
你就要把像子弹这种高速的物体给它勾上这个勾

205
00:08:25,879 --> 00:08:28,839
那么这个效应其实就叫子弹效应

206
00:08:28,839 --> 00:08:32,818
所以在这里它这个属性就是一个子弹子弹的意思

207
00:08:32,899 --> 00:08:34,879
那么在这里我把它勾上以后

208
00:08:34,879 --> 00:08:41,659
这时候它的检测就会更加的这个这个这个这个紧密一些啊

209
00:08:41,659 --> 00:08:42,590
就是如果有个墙

210
00:08:42,590 --> 00:08:47,149
它是百分之百能够保证你这个高速飞行的物体和这个墙碰到的

211
00:08:47,149 --> 00:08:49,759
但是他也是比较费性能的

212
00:08:49,759 --> 00:08:51,679
所以除非是高速的物体

213
00:08:51,679 --> 00:08:53,360
要不然我们也不勾他啊

214
00:08:53,360 --> 00:08:54,559
明白这个啊

215
00:08:54,700 --> 00:08:59,019
嗯那么接下来类型钢体有四种类型啊

216
00:08:59,019 --> 00:09:01,840
准确来说正常应该是有三种啊

217
00:09:01,840 --> 00:09:04,259
就coos里面有三种嗯

218
00:09:04,259 --> 00:09:06,450
我们知道这个creator是对他的封装

219
00:09:06,450 --> 00:09:07,169
封装完了以后

220
00:09:07,169 --> 00:09:08,429
他自己给加了一种

221
00:09:08,429 --> 00:09:09,639
是第四种

222
00:09:09,639 --> 00:09:11,529
我们一个一个来说一下啊

223
00:09:11,529 --> 00:09:13,720
首先我们默认选择的是第三种

224
00:09:13,720 --> 00:09:14,539
对不对

225
00:09:15,139 --> 00:09:19,839
那么因为第三种它呢是什么意思啊

226
00:09:19,960 --> 00:09:23,320
作为物理就是受到物理的影响啊

227
00:09:23,320 --> 00:09:26,379
第三个它是有受到重力影响的

228
00:09:26,379 --> 00:09:28,578
它是可以受到重力影响的

229
00:09:28,960 --> 00:09:31,659
也就是说我们选了这个类型

230
00:09:31,659 --> 00:09:35,779
我们运行完以后它才会掉下来

231
00:09:39,200 --> 00:09:41,500
啊那么这是第三项啊

232
00:09:41,500 --> 00:09:43,659
它是可以受到这个重力影响的

233
00:09:43,659 --> 00:09:46,059
同时我们在物理系统中

234
00:09:46,059 --> 00:09:48,129
比如说给他一个向右上的速度

235
00:09:48,129 --> 00:09:51,370
它也是可以哎往右上走的啊

236
00:09:51,370 --> 00:09:54,168
它也是可以这个被速度影响的

237
00:09:54,168 --> 00:09:56,789
就是说它既既受到重力的影响

238
00:09:56,789 --> 00:09:59,909
而且在物理系统我们通过代码给它一个速度

239
00:09:59,909 --> 00:10:02,679
它也是可以受到这个速度影响的

240
00:10:02,720 --> 00:10:07,980
那么第一个静态的一个钢体静态类型

241
00:10:07,980 --> 00:10:10,620
静态类型就是不会受到重力

242
00:10:10,620 --> 00:10:13,019
也不会受到速度的影响啊

243
00:10:13,019 --> 00:10:19,320
说白了就是如果有一个比如说有一个这个箱子

244
00:10:19,320 --> 00:10:20,820
这个有一个房子

245
00:10:20,820 --> 00:10:21,960
房子在地上

246
00:10:21,960 --> 00:10:24,240
那你希望这个房子是个钢铁

247
00:10:24,240 --> 00:10:28,000
你这个房子一般它不会移动啊

248
00:10:28,000 --> 00:10:30,159
它任何情况下它都是只站在那

249
00:10:30,159 --> 00:10:31,419
那你如果是房子的话

250
00:10:31,419 --> 00:10:33,099
你就可以给他一个静态

251
00:10:33,200 --> 00:10:34,039
对不对

252
00:10:34,039 --> 00:10:35,720
不会受到重力的影响

253
00:10:35,720 --> 00:10:36,440
不会下落

254
00:10:36,440 --> 00:10:38,480
也不会有受到速度的影响

255
00:10:38,480 --> 00:10:39,679
你给这个房子一个速度

256
00:10:39,679 --> 00:10:40,740
房子也不动

257
00:10:41,740 --> 00:10:44,110
那就是纯静态的一个钢铁

258
00:10:44,110 --> 00:10:46,960
第二个第二个怎么说啊

259
00:10:46,960 --> 00:10:48,219
它这个东西

260
00:10:50,120 --> 00:10:52,389
介于第一种和第三种之间

261
00:10:52,389 --> 00:10:54,879
那首先如果选择这个钢铁

262
00:10:54,879 --> 00:10:55,779
我们先运行一下

263
00:10:55,779 --> 00:10:56,639
看一下

264
00:10:56,659 --> 00:10:58,940
它默认也是不会动的啊

265
00:10:58,940 --> 00:11:02,360
就是这个类型它也是不会动的啊

266
00:11:02,360 --> 00:11:04,198
区别是什么啊

267
00:11:04,580 --> 00:11:06,740
跟那个静态或者它的区别是什么

268
00:11:06,740 --> 00:11:07,700
首先它不会动

269
00:11:07,700 --> 00:11:09,320
这个跟静态的是一样的

270
00:11:09,320 --> 00:11:11,649
但是他可以受到速度的影响

271
00:11:11,649 --> 00:11:15,279
也就是说如果我们给了这个小鸟一个右上的速度

272
00:11:15,279 --> 00:11:18,279
那么它呢还是会往右上飞出去的啊

273
00:11:18,279 --> 00:11:20,980
所以说它会受到速度的影响

274
00:11:20,980 --> 00:11:23,039
但是不会受到重力的影响

275
00:11:23,039 --> 00:11:24,990
而第一个是都不会受到影响

276
00:11:24,990 --> 00:11:26,879
这个是都会受到影响啊

277
00:11:26,879 --> 00:11:29,460
所以实际上正常而言就这三种啊

278
00:11:29,460 --> 00:11:31,379
三种组合方式对不对

279
00:11:31,379 --> 00:11:33,620
那么最后一种是什么意思啊

280
00:11:34,320 --> 00:11:36,679
因为咱们这个里面多了一个动画系统

281
00:11:36,679 --> 00:11:37,250
对不对

282
00:11:37,250 --> 00:11:39,649
咱们cos creator里面多了个动画系统

283
00:11:39,649 --> 00:11:43,870
如果比如说一个动画系统的动画精灵

284
00:11:43,870 --> 00:11:46,120
你要给它去添加刚体啊

285
00:11:46,120 --> 00:11:51,269
也就是说如果一个精灵它本身它要参与这个动画的一个计算

286
00:11:51,269 --> 00:11:53,250
比如说通过动画它要进行移动

287
00:11:53,250 --> 00:11:55,318
或者通过动画它要进行旋转

288
00:11:55,318 --> 00:11:59,548
那么它本身已经在参与这个动画的一个计算了

289
00:11:59,548 --> 00:12:04,500
那么为了让他这个这个不会产生一些问题啊

290
00:12:04,500 --> 00:12:06,539
因为他有时候如果你让他参与动画

291
00:12:06,539 --> 00:12:07,980
再让他使用前三种

292
00:12:07,980 --> 00:12:09,559
它会产生一些问题

293
00:12:09,559 --> 00:12:10,639
所以那种情况下

294
00:12:10,639 --> 00:12:12,250
如果他参与了动画

295
00:12:12,250 --> 00:12:13,870
我们就可以选择第四种

296
00:12:13,870 --> 00:12:16,509
所以第四种就叫动画的一个类型啊

297
00:12:16,509 --> 00:12:18,759
就是和动画去搭配啊

298
00:12:18,759 --> 00:12:22,299
所以一般情况下我们目前而言就看前三种啊

299
00:12:22,299 --> 00:12:26,529
所以我们在这里选择这种这种只有这种都会受到影响

300
00:12:26,529 --> 00:12:29,279
所以它受到重力影响才会掉下来

301
00:12:30,299 --> 00:12:32,240
那么它也是默认的选项啊

302
00:12:32,240 --> 00:12:33,259
他也是默认的选项

303
00:12:34,580 --> 00:12:39,210
然后这里是否进入这个睡眠啊

304
00:12:39,210 --> 00:12:42,539
那么这里他换上这个钩a就允许他进入睡眠

305
00:12:42,539 --> 00:12:44,909
就这个缸体在不用的时候啊

306
00:12:44,909 --> 00:12:47,950
那么它是允许进行睡眠的啊

307
00:12:47,950 --> 00:12:49,090
这个东西我们不用管

308
00:12:49,090 --> 00:12:50,470
我们就给它勾上就行了啊

309
00:12:50,470 --> 00:12:51,789
这个是节省性能的

310
00:12:51,789 --> 00:12:52,990
你要是不给它勾上

311
00:12:52,990 --> 00:12:56,679
就是他哪怕就站在原地不动啊

312
00:12:56,679 --> 00:12:59,690
他哪怕比如说桌子上有一个杯子

313
00:12:59,690 --> 00:13:01,370
杯子是是一个钢铁

314
00:13:01,370 --> 00:13:02,929
那么它本来就在这个桌子上的

315
00:13:02,929 --> 00:13:04,309
他本来就不动啊

316
00:13:04,309 --> 00:13:05,389
他这会儿也没事儿

317
00:13:05,389 --> 00:13:07,789
他还在屏幕外面啊

318
00:13:07,789 --> 00:13:09,438
那么这种情况下

319
00:13:09,799 --> 00:13:11,480
他按道理休眠就行了

320
00:13:11,480 --> 00:13:13,070
你如果把这个勾取消了

321
00:13:13,070 --> 00:13:15,698
他在外面他还是计算这个物理效果的

322
00:13:16,139 --> 00:13:18,360
你想在屏幕外面一个桌子

323
00:13:18,360 --> 00:13:19,259
桌子上面一个杯子

324
00:13:19,259 --> 00:13:22,289
这个杯子跟我们屏幕里面的压根没什么关联

325
00:13:22,289 --> 00:13:23,580
你要把它取消了

326
00:13:23,580 --> 00:13:24,929
在外面它都计算

327
00:13:24,929 --> 00:13:26,730
所以这个计算量就会增大啊

328
00:13:26,730 --> 00:13:28,200
所以这个一般不用管的

329
00:13:28,200 --> 00:13:30,960
这个是受到我们那个物理影响

330
00:13:30,960 --> 00:13:32,370
受到多大的物理影响

331
00:13:32,370 --> 00:13:34,200
比如说你看我们掉落的速度很快

332
00:13:34,200 --> 00:13:34,710
对不对

333
00:13:34,710 --> 00:13:36,120
我们要想让它慢一点

334
00:13:36,120 --> 00:13:39,059
比如说受到一半的物理物理影响

335
00:13:39,059 --> 00:13:40,409
重力影响啊

336
00:13:40,409 --> 00:13:42,899
受到一半的重力影响运行

337
00:13:42,899 --> 00:13:45,419
你看是不是就会慢一些了啊

338
00:13:45,419 --> 00:13:46,139
慢一些了

339
00:13:46,139 --> 00:13:49,879
那这个我们就可以去调整啊

340
00:13:49,879 --> 00:13:52,078
你要调到0.1

341
00:13:53,240 --> 00:13:54,820
0.1的话

342
00:13:54,820 --> 00:13:58,899
那我们运行你看是不是就非常慢了

343
00:13:58,899 --> 00:13:59,919
这就是重力啊

344
00:13:59,919 --> 00:14:01,120
这就是重力

345
00:14:02,940 --> 00:14:05,539
一的话就是受到正常的一个重力影响

346
00:14:05,539 --> 00:14:08,809
当然重力你可以在代码里面去调啊

347
00:14:08,809 --> 00:14:11,629
那在这里这个是一个线性阻尼

348
00:14:11,629 --> 00:14:12,110
什么意思

349
00:14:12,110 --> 00:14:14,698
就是你比如说

350
00:14:17,139 --> 00:14:18,340
一个人啊

351
00:14:18,340 --> 00:14:20,080
如果比如说啊一个平面

352
00:14:20,080 --> 00:14:20,950
一个一个地面

353
00:14:20,950 --> 00:14:22,629
地面是完全平滑的

354
00:14:22,629 --> 00:14:23,919
没有摩擦力的

355
00:14:23,919 --> 00:14:25,179
上面有个箱子

356
00:14:25,179 --> 00:14:27,460
箱子和它之间是没有摩擦力的

357
00:14:27,460 --> 00:14:30,099
那么当那个箱子往右边移动的时候

358
00:14:30,120 --> 00:14:33,480
我们给它右边给了一个很小的推一个力啊

359
00:14:33,480 --> 00:14:34,710
就推了箱子吧

360
00:14:34,710 --> 00:14:38,379
实际上这个箱子是不是应该会一直移动

361
00:14:38,379 --> 00:14:40,240
因为它跟地面是没有摩擦力的

362
00:14:40,240 --> 00:14:42,078
零摩擦力它会一直移动

363
00:14:42,078 --> 00:14:44,178
线性阻尼就是你要给到它数值

364
00:14:44,178 --> 00:14:45,979
它就会逐渐减速啊

365
00:14:45,979 --> 00:14:48,798
就是即即使没有摩擦力

366
00:14:48,798 --> 00:14:50,239
它也会减速啊

367
00:14:50,239 --> 00:14:54,500
说白了这个线性这就是一个减速的一个速度啊

368
00:14:54,500 --> 00:14:56,940
你要给他零的话

369
00:14:56,940 --> 00:14:58,320
就是它的减速

370
00:14:58,320 --> 00:15:00,450
一切只靠摩擦力来进行减速

371
00:15:00,450 --> 00:15:01,919
而你要给它数值的话

372
00:15:01,919 --> 00:15:03,340
就是没有摩擦力

373
00:15:03,340 --> 00:15:06,850
我这个物体比如说在运动过程中也会减速啊

374
00:15:06,850 --> 00:15:09,129
那这是线性的

375
00:15:09,129 --> 00:15:10,509
这个是角速度的

376
00:15:10,509 --> 00:15:11,470
也是一个阻尼

377
00:15:11,470 --> 00:15:14,659
角速度的阻尼其实就是一个旋转的阻尼啊

378
00:15:14,659 --> 00:15:15,259
就是旋转

379
00:15:15,259 --> 00:15:18,870
如果这个我们给他很大的阻尼的话

380
00:15:18,870 --> 00:15:20,970
那么给他一个力转

381
00:15:20,970 --> 00:15:22,889
比如说转起来还没转几下呢

382
00:15:22,889 --> 00:15:24,039
可能就不转了啊

383
00:15:24,679 --> 00:15:27,120
一个是线性的一个角速度的

384
00:15:27,600 --> 00:15:32,200
然后这个是当前的一个给他一个速度啊

385
00:15:32,200 --> 00:15:36,100
比如说给他x方向和y方y方向一个速度啊

386
00:15:36,100 --> 00:15:37,120
或者给他一个角速度

387
00:15:37,120 --> 00:15:39,580
比如说我们给它x一个呃

388
00:15:39,580 --> 00:15:44,779
比如说你看啊零点我们用另外一个吧

389
00:15:46,580 --> 00:15:47,419
我们用它吧

390
00:15:47,419 --> 00:15:48,830
我们不受到重力的影响

391
00:15:48,830 --> 00:15:49,820
不受到重力影响

392
00:15:49,820 --> 00:15:53,639
现在是不是按道理而言是不会动的

393
00:15:53,639 --> 00:15:54,419
对不对

394
00:15:54,419 --> 00:15:57,389
那么我们可以给它x发现一个速度

395
00:15:57,389 --> 00:16:00,379
比如说十运行

396
00:16:02,340 --> 00:16:06,179
大家可以看是不是他就在x方向上走起来了啊

397
00:16:06,179 --> 00:16:08,519
所以这个其实就是给了他一个速度啊

398
00:16:08,519 --> 00:16:09,859
给了他一个速度

399
00:16:12,899 --> 00:16:14,698
很有意思啊

400
00:16:14,840 --> 00:16:16,039
那么有x速度

401
00:16:16,039 --> 00:16:17,059
也有y的速度

402
00:16:17,059 --> 00:16:19,159
还有角速度啊

403
00:16:19,159 --> 00:16:21,320
比如说这个是零角速度

404
00:16:21,320 --> 00:16:23,438
给个50

405
00:16:25,820 --> 00:16:27,019
角速度给个50

406
00:16:27,019 --> 00:16:29,299
你看就和风扇一样转起来了

407
00:16:29,299 --> 00:16:30,379
对不对啊

408
00:16:30,379 --> 00:16:31,679
就转起来了

409
00:16:32,539 --> 00:16:35,539
那当然这个我们可以通过这个rotation改变

410
00:16:35,539 --> 00:16:38,330
但是如果它本身有物理性质的话

411
00:16:38,330 --> 00:16:41,099
你也可以通过这个角速度来改变

412
00:16:42,419 --> 00:16:44,159
那么这里有一个问题啊

413
00:16:44,159 --> 00:16:45,450
有一个什么问题

414
00:16:45,450 --> 00:16:48,559
如果我在这里啊

415
00:16:48,559 --> 00:16:51,259
我选择这个正常的这个重力情况啊

416
00:16:52,399 --> 00:16:53,909
正常的重力情况

417
00:16:53,909 --> 00:16:55,529
那么这个鸟儿啊

418
00:16:55,529 --> 00:16:56,370
它是正常

419
00:16:56,370 --> 00:16:57,389
现在是掉下去了

420
00:16:57,389 --> 00:16:58,220
对不对

421
00:16:58,220 --> 00:17:00,500
正常他现在是掉落下去了

422
00:17:00,500 --> 00:17:02,870
但是实际上比如说这有一个斜坡

423
00:17:02,870 --> 00:17:04,970
那么这个鸟碰到斜坡以后

424
00:17:04,970 --> 00:17:06,598
这个鸟就会怎样

425
00:17:06,598 --> 00:17:08,459
由于这个碰到了斜坡

426
00:17:08,459 --> 00:17:11,219
那肯定我们这个鸟首先它会旋转啊

427
00:17:11,219 --> 00:17:14,118
它就会一直转着滚到这个斜坡下面

428
00:17:14,118 --> 00:17:16,269
这个是个正常的物理现象

429
00:17:16,269 --> 00:17:17,170
没有问题吧

430
00:17:17,170 --> 00:17:18,970
啊我们现在由于没有斜坡

431
00:17:18,970 --> 00:17:20,710
所以你看着它是正常掉下来

432
00:17:20,710 --> 00:17:21,849
如果这有个斜坡

433
00:17:21,849 --> 00:17:22,569
他在这个坡上

434
00:17:22,569 --> 00:17:24,569
它就会一直滚滚滚滚下来

435
00:17:24,569 --> 00:17:25,349
对不对

436
00:17:25,349 --> 00:17:29,920
那么如果我们希望哪怕到斜坡上

437
00:17:29,920 --> 00:17:31,789
我是滑下去的啊

438
00:17:31,789 --> 00:17:35,339
就是我还是永远是面朝右边啊

439
00:17:35,339 --> 00:17:36,390
我在斜坡上

440
00:17:36,390 --> 00:17:39,119
我也是哎从斜坡上哎这样滑下来

441
00:17:39,119 --> 00:17:40,500
而不是滚下来的

442
00:17:40,500 --> 00:17:41,599
怎么做

443
00:17:41,599 --> 00:17:44,660
在这里你就可以把它的旋转固定了啊

444
00:17:44,660 --> 00:17:46,160
把它的旋转固定了

445
00:17:46,160 --> 00:17:47,720
只要把它的旋转固定了

446
00:17:47,720 --> 00:17:48,680
那么从现在开始

447
00:17:48,680 --> 00:17:52,059
它本身就不会旋转了啊

448
00:17:52,059 --> 00:17:53,319
就是哪怕在一个斜坡上

449
00:17:53,319 --> 00:17:55,690
它直接会蹭着这个斜坡往下滑

450
00:17:55,690 --> 00:17:58,019
而不会滚下去了啊

451
00:17:58,019 --> 00:17:59,009
那就是这个东西

452
00:17:59,009 --> 00:18:00,599
所以如果你有一个

453
00:18:00,599 --> 00:18:02,099
比如说一个马里奥

454
00:18:02,099 --> 00:18:02,579
马里奥

455
00:18:02,579 --> 00:18:03,539
如果你不勾上

456
00:18:03,539 --> 00:18:04,559
这个就有个问题

457
00:18:04,559 --> 00:18:05,279
走着走着走着

458
00:18:05,279 --> 00:18:06,539
你可能摔倒了

459
00:18:06,539 --> 00:18:07,529
人就倒了

460
00:18:07,529 --> 00:18:08,339
那就很坑

461
00:18:08,339 --> 00:18:10,440
是不是马里奥永远是脚踩着地的

462
00:18:10,440 --> 00:18:12,119
你没有见马里奥倒下过

463
00:18:12,119 --> 00:18:14,099
所以对于这种人物来说

464
00:18:14,099 --> 00:18:15,720
你肯定要把它勾上啊

465
00:18:15,720 --> 00:18:18,319
要确保它不会旋转啊

466
00:18:18,740 --> 00:18:22,940
啊最后一个我们不用管它是否立刻唤醒此钢铁啊

467
00:18:22,940 --> 00:18:25,160
那这个就是如果休眠的时候

468
00:18:25,160 --> 00:18:26,329
你你把它

469
00:18:26,329 --> 00:18:27,799
如果你把它勾上了

470
00:18:27,799 --> 00:18:31,259
那么它永远会参与这个物理计算啊

471
00:18:31,259 --> 00:18:34,589
就是他直接现在这个钢琴就变成有效的了啊

472
00:18:34,589 --> 00:18:36,390
那实际上这个钢铁有效没效

473
00:18:36,390 --> 00:18:39,470
这个系统给我们做一些嗯操作啊

474
00:18:39,470 --> 00:18:42,230
就是他该生效的时候自己就生效了啊

475
00:18:42,230 --> 00:18:44,089
那么所以这个东西不用管它了

476
00:18:44,089 --> 00:18:45,589
说白了这是一个优化啊

477
00:18:45,589 --> 00:18:47,900
你不要去管它嗯

478
00:18:50,059 --> 00:18:50,690
ok啊

479
00:18:50,690 --> 00:18:53,160
那么这边东西啊

480
00:18:53,160 --> 00:18:54,900
这边的东西就这么多啊

481
00:18:54,900 --> 00:18:57,299
这边刚体就这么多啊

482
00:18:57,299 --> 00:19:02,519
我们这节课大家就是要知道这个缸体怎首先怎样生效

483
00:19:02,519 --> 00:19:03,779
我们一定要开启

484
00:19:03,779 --> 00:19:06,220
那那一句话一句话的代码啊

485
00:19:06,220 --> 00:19:07,839
一句话的代码一定要开启

486
00:19:07,839 --> 00:19:09,460
而且一定要在unload里面

487
00:19:09,460 --> 00:19:13,730
然后就是如果一个物体它要受到物理影响

488
00:19:13,730 --> 00:19:14,900
你就要加缸体

489
00:19:14,900 --> 00:19:17,420
缸体上面这么多属性分别是什么意思啊

490
00:19:17,420 --> 00:19:18,529
你要了解一下

491
00:19:18,529 --> 00:19:19,849
那么这节课就这么多

492
00:19:19,849 --> 00:19:24,659
下节课我们看一下它的一些常用的代码

493
00:19:25,539 --> 00:19:26,440
略略略


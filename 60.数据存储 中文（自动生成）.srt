1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,900 --> 00:00:14,910
ok咱们这节课来说一下这个数据的一个保存

3
00:00:14,910 --> 00:00:16,780
数据的一个储存

4
00:00:16,879 --> 00:00:19,579
那么我们在写这个项目的时候啊

5
00:00:19,579 --> 00:00:23,379
往往会遇到这个项目储存这样的一个需求啊

6
00:00:23,379 --> 00:00:25,960
比如说我们创建一个项目啊

7
00:00:25,960 --> 00:00:29,429
然后在里面可能我们写了一个游戏啊

8
00:00:29,429 --> 00:00:30,809
这个游戏很简单

9
00:00:30,809 --> 00:00:35,100
可能是比如说是一个跑酷类型的啊

10
00:00:35,100 --> 00:00:37,259
或者咱们前面那个flappy bird

11
00:00:37,259 --> 00:00:39,460
那么它是有积分的啊

12
00:00:39,460 --> 00:00:41,380
就是你每一次你玩完以后

13
00:00:41,380 --> 00:00:42,460
我们需要做什么

14
00:00:42,460 --> 00:00:44,439
需要把这个积分存到本地

15
00:00:44,460 --> 00:00:46,799
然后第二次我们再打开这个游戏的时候

16
00:00:46,799 --> 00:00:48,719
我们可以看到我们之前的积分

17
00:00:48,719 --> 00:00:49,700
对不对

18
00:00:49,759 --> 00:00:52,100
那么如果你做的游戏再大一点

19
00:00:52,100 --> 00:00:55,719
比如说做到这种rpg游戏了啊

20
00:00:55,719 --> 00:00:56,979
做的rpg游戏了

21
00:00:56,979 --> 00:01:00,340
那么他呢里面可能是需要保存这个进度啊

22
00:01:00,340 --> 00:01:02,299
你这次比如说玩到哪了诶

23
00:01:02,299 --> 00:01:05,060
你把你这个需要保存的数据保存下来

24
00:01:05,060 --> 00:01:07,640
然后下一次我们进入游戏唉

25
00:01:07,640 --> 00:01:09,590
可以从这个进度继续玩

26
00:01:09,590 --> 00:01:10,439
对不对

27
00:01:10,799 --> 00:01:13,739
那么这时候我们就要涉及到一个问题来

28
00:01:13,739 --> 00:01:16,500
这个数据我们怎样存到本地

29
00:01:16,560 --> 00:01:23,000
那么如果我们现在啊我们用的是其他的游戏引擎

30
00:01:23,260 --> 00:01:27,840
当然这种游戏前我们主要是指这种做端游的啊

31
00:01:27,840 --> 00:01:29,219
做端游的游戏引擎

32
00:01:29,219 --> 00:01:33,060
那么像做端游的游戏引擎遇到文件储存啊

33
00:01:33,060 --> 00:01:33,840
那很简单

34
00:01:33,840 --> 00:01:38,439
基本上我们直接把要保存的数据啊给他写到本地

35
00:01:38,439 --> 00:01:40,540
给他写到本地就ok了啊

36
00:01:40,540 --> 00:01:42,670
就写文件存到本地啊

37
00:01:42,670 --> 00:01:47,680
那么或者是我们来一个小型的一个轻量级的数据库啊

38
00:01:47,680 --> 00:01:50,340
把我们需要保存的数据存到数据库里面

39
00:01:50,359 --> 00:01:52,099
然后第二次我们打开游戏

40
00:01:52,099 --> 00:01:53,920
再从数据里面去读取

41
00:01:54,040 --> 00:01:58,480
但是我们这个cos creator属于一个主打

42
00:01:58,480 --> 00:02:02,060
其实是做这个h5 游戏的这样的一个引擎

43
00:02:02,060 --> 00:02:03,799
那么不光是cos creator

44
00:02:03,799 --> 00:02:08,930
基本上所有主要去做这个网页游戏的这种引擎

45
00:02:08,930 --> 00:02:10,680
那么它有一个特点

46
00:02:10,680 --> 00:02:12,479
因为我们是在网页上面啊

47
00:02:12,479 --> 00:02:15,300
我们做的这个游戏最后要在网页上面跑

48
00:02:15,419 --> 00:02:17,759
那么网页上面它是不支持

49
00:02:17,759 --> 00:02:22,099
就是说你你去写文件或者是用这个数据库的

50
00:02:22,419 --> 00:02:23,500
对不对啊

51
00:02:23,500 --> 00:02:25,000
那怎么办啊

52
00:02:25,000 --> 00:02:26,300
那怎么办

53
00:02:26,319 --> 00:02:29,319
所以在这里它给我们提供了一种方式啊

54
00:02:29,319 --> 00:02:31,300
去进行数据的保存啊

55
00:02:31,300 --> 00:02:33,819
那实际上用起来是非常简单的啊

56
00:02:33,819 --> 00:02:34,879
非常简单的

57
00:02:34,879 --> 00:02:36,740
我们在这里看一下啊

58
00:02:36,740 --> 00:02:39,539
首先我们创建一个空的节点啊

59
00:02:39,539 --> 00:02:42,120
我们叫做比如说数据的一个测试

60
00:02:42,120 --> 00:02:43,319
data test

61
00:02:43,319 --> 00:02:44,979
我们再来一个脚本

62
00:02:48,139 --> 00:02:50,840
啊脚本挂载到他身上啊

63
00:02:50,840 --> 00:02:52,460
只是为了执行一下这个脚本

64
00:02:52,460 --> 00:02:54,060
然后我们打开这个脚本

65
00:02:54,560 --> 00:03:00,020
那么在这里我们怎样去做的这个储存我们的数据呢

66
00:03:00,659 --> 00:03:02,879
啊怎样做到储存数据呢

67
00:03:02,879 --> 00:03:04,289
非常非常简单

68
00:03:04,289 --> 00:03:05,639
一行代码

69
00:03:05,639 --> 00:03:10,819
那么它呢是使用了这种键值对的方式去进行储存的啊

70
00:03:10,819 --> 00:03:13,159
就类似于字典这样的方式啊

71
00:03:13,159 --> 00:03:14,400
有见有直

72
00:03:15,379 --> 00:03:19,900
那么它里面给我们提供了这样的一个对象啊

73
00:03:19,900 --> 00:03:23,080
这个对象也是一个单例的一个对象啊

74
00:03:23,080 --> 00:03:25,180
就是我们直接拿来就可以去使用啊

75
00:03:25,180 --> 00:03:27,719
是系统里面给我们提供的一个对象

76
00:03:27,719 --> 00:03:29,879
这个对象有一个方法

77
00:03:29,879 --> 00:03:33,490
这个方法叫做sat sitem

78
00:03:33,490 --> 00:03:36,039
那么它里面有两个参数

79
00:03:36,039 --> 00:03:39,240
前面是k啊

80
00:03:39,240 --> 00:03:41,159
后面呢是个value

81
00:03:41,159 --> 00:03:43,139
但是它都是字符串类型

82
00:03:43,139 --> 00:03:45,780
比如说你前面可以给他一个k

83
00:03:45,780 --> 00:03:48,780
我们用name给他的name诶

84
00:03:48,780 --> 00:03:50,699
然后这边呢我们可以给它一个

85
00:03:50,699 --> 00:03:52,120
比如说蝙蝠侠

86
00:03:54,759 --> 00:03:58,180
那么这个啊就是用来保存数据了

87
00:03:58,180 --> 00:04:01,689
我们就可以把当前的这个数据保存起来

88
00:04:01,689 --> 00:04:05,120
然后等我们下一次嗯

89
00:04:05,699 --> 00:04:07,439
在这个打开这个游戏的时候

90
00:04:07,439 --> 00:04:09,719
我们还可以通过这个键啊

91
00:04:09,719 --> 00:04:11,340
通过这个k拿到这个数据

92
00:04:11,340 --> 00:04:14,280
实际上他这个就是封装的h5 啊

93
00:04:14,280 --> 00:04:18,240
就是h5 这个网页编程的这个数据保存啊

94
00:04:18,240 --> 00:04:20,160
那么在这里我们不管那么多啊

95
00:04:20,160 --> 00:04:23,279
我们只需要知道它给我们提供这种方式去保存数据

96
00:04:23,279 --> 00:04:24,180
我们就用

97
00:04:24,180 --> 00:04:26,459
那么在这里我们测试一下

98
00:04:26,459 --> 00:04:27,720
首先我们先运行一下

99
00:04:27,720 --> 00:04:28,620
先保存一下

100
00:04:28,620 --> 00:04:30,089
就是执行一下这行代码

101
00:04:30,089 --> 00:04:32,420
看看能不能给它保存起来

102
00:04:32,759 --> 00:04:34,540
我们现在运行一下

103
00:04:34,779 --> 00:04:36,399
什么也没有发生啊

104
00:04:36,399 --> 00:04:37,899
当然我们什么也没有去写

105
00:04:37,899 --> 00:04:40,000
但是这行代码肯定已经被执行了

106
00:04:40,000 --> 00:04:40,509
对不对

107
00:04:40,509 --> 00:04:41,529
储存代码

108
00:04:41,529 --> 00:04:43,699
储存数据的代码已经被执行了

109
00:04:43,720 --> 00:04:45,639
那接下来我们就要干嘛了

110
00:04:45,639 --> 00:04:46,959
获取数据

111
00:04:49,279 --> 00:04:51,439
而我们把上面这个也注释了啊

112
00:04:51,439 --> 00:04:55,579
就是这一次我们运行就是为了获取一下我们刚才保存到本地的数据

113
00:04:55,579 --> 00:04:58,639
看看能不能正常获取啊

114
00:04:58,639 --> 00:05:03,139
我们来这个这个这个这个还有什么local light啊

115
00:05:03,139 --> 00:05:08,269
然后这个name cc点

116
00:05:08,269 --> 00:05:10,660
get item

117
00:05:12,220 --> 00:05:13,660
刚才是赛德统

118
00:05:13,660 --> 00:05:14,740
这次是盖疼

119
00:05:14,740 --> 00:05:17,939
然后把我们要获取数据的这个k啊

120
00:05:17,939 --> 00:05:20,069
也就是这个键放到这里

121
00:05:20,069 --> 00:05:22,949
那这时候他就会把对应的值返回回来

122
00:05:22,949 --> 00:05:25,660
然后我们把这个值输出一下

123
00:05:26,060 --> 00:05:27,500
输出输出啊

124
00:05:27,500 --> 00:05:30,860
我们是康熙路点debug

125
00:05:31,459 --> 00:05:33,680
那我们来运行一下

126
00:05:33,680 --> 00:05:34,040
试一下

127
00:05:34,040 --> 00:05:35,420
看看行不行啊

128
00:05:40,500 --> 00:05:44,980
诶大家看一下这边蝙蝠侠是不是已经输出出来了

129
00:05:45,459 --> 00:05:46,959
我们这边注意啊

130
00:05:46,959 --> 00:05:50,019
储存的这一行代码已经被我们注释掉了

131
00:05:50,019 --> 00:05:51,279
所以它现在不影响了

132
00:05:51,279 --> 00:05:54,829
也就是说实际上我们上来我们就做了这样一件事

133
00:05:54,829 --> 00:05:56,480
去拿name对应的这个值

134
00:05:56,480 --> 00:05:59,180
拿到值以后把它输出出来

135
00:05:59,180 --> 00:06:01,160
但是我们发现蝙蝠侠是有的

136
00:06:01,160 --> 00:06:01,980
对不对

137
00:06:02,139 --> 00:06:05,899
那么这就是我们的键值储存方式啊

138
00:06:05,899 --> 00:06:08,180
电池储存方式非常非常简单啊

139
00:06:08,180 --> 00:06:09,279
要储存什么

140
00:06:09,279 --> 00:06:10,839
你就在这儿去存

141
00:06:10,839 --> 00:06:12,730
然后给他一个对应的键

142
00:06:12,730 --> 00:06:14,259
然后你把剑记录起来

143
00:06:14,259 --> 00:06:16,459
什么时候需要拿到这个值了

144
00:06:16,459 --> 00:06:18,139
你直接get item

145
00:06:18,139 --> 00:06:20,699
把剑往这一放就ok了啊

146
00:06:20,699 --> 00:06:21,420
这是兼职

147
00:06:21,420 --> 00:06:22,319
的方式啊

148
00:06:22,319 --> 00:06:23,420
兼职方式

149
00:06:28,680 --> 00:06:32,160
诶建筑方式啊

150
00:06:32,459 --> 00:06:35,819
那么有时候我们会对这个数据做其他操作

151
00:06:35,819 --> 00:06:39,060
比如说我们要移除一个数据啊

152
00:06:39,060 --> 00:06:40,199
我们要移除一个数据

153
00:06:40,199 --> 00:06:41,519
怎样去移除呢

154
00:06:42,560 --> 00:06:44,600
我们放到最最下面算

155
00:06:44,600 --> 00:06:46,639
比如说我们要移除一个数据

156
00:06:46,639 --> 00:06:47,879
很简单

157
00:06:51,639 --> 00:06:55,300
叫remove item啊

158
00:06:55,300 --> 00:06:56,139
remove item

159
00:06:56,139 --> 00:06:57,579
然后把对应的k放在这儿

160
00:06:57,579 --> 00:06:58,519
比如说name

161
00:06:59,120 --> 00:07:01,920
那这样的话只要我们移除完了

162
00:07:02,000 --> 00:07:04,250
那么我们再去读这个

163
00:07:04,250 --> 00:07:05,660
再去获取这个数据

164
00:07:05,660 --> 00:07:07,339
就获取不到了啊

165
00:07:07,339 --> 00:07:08,240
就获取不到了

166
00:07:08,240 --> 00:07:09,899
那么这个就是移除

167
00:07:09,920 --> 00:07:11,990
但是这是移除单个数据

168
00:07:11,990 --> 00:07:13,040
我们在这里啊

169
00:07:13,040 --> 00:07:14,560
我们可以先试一下啊

170
00:07:16,100 --> 00:07:18,259
我们现在肯定移除啊

171
00:07:18,259 --> 00:07:19,040
移除了

172
00:07:19,040 --> 00:07:20,480
那接下来

173
00:07:21,980 --> 00:07:23,540
我们在这里移除完了

174
00:07:23,540 --> 00:07:25,339
我们再获取啊

175
00:07:25,339 --> 00:07:26,000
注意啊

176
00:07:26,000 --> 00:07:26,959
这个数据已经没了

177
00:07:26,959 --> 00:07:28,100
然后我们现在想获取

178
00:07:28,100 --> 00:07:29,519
看看能不能获取到

179
00:07:30,620 --> 00:07:32,089
看一下输出

180
00:07:32,089 --> 00:07:34,480
我们发现成了空值了

181
00:07:34,500 --> 00:07:35,519
成了空值了

182
00:07:35,519 --> 00:07:36,209
对不对

183
00:07:36,209 --> 00:07:39,420
那么这就证明我们这个数这个数值啊

184
00:07:39,420 --> 00:07:42,660
这对简直对被我们删除掉了啊

185
00:07:42,660 --> 00:07:46,470
那么这这个呢只能移除一个数据

186
00:07:46,470 --> 00:07:49,079
还有一个啊是进行一个清空

187
00:07:51,279 --> 00:07:52,680
清除数据

188
00:07:52,899 --> 00:07:55,209
那么只要调了这个方法

189
00:07:55,209 --> 00:07:58,000
就是你目前存到本地的所有的数据啊

190
00:07:58,000 --> 00:07:59,500
都会被删除掉

191
00:08:04,480 --> 00:08:05,860
啊这个都没提示

192
00:08:05,860 --> 00:08:06,339
没关系

193
00:08:06,339 --> 00:08:07,720
你敲上就行啊

194
00:08:07,720 --> 00:08:08,800
清除

195
00:08:08,800 --> 00:08:10,600
那么只要调用这个方法

196
00:08:10,600 --> 00:08:14,689
所有的数据啊都会被我们清除掉哈

197
00:08:14,689 --> 00:08:17,060
所以这个就算是一清一干二净了

198
00:08:17,060 --> 00:08:17,420
是不是

199
00:08:17,420 --> 00:08:19,939
比如一个人他有好多存档

200
00:08:19,939 --> 00:08:20,480
对不对

201
00:08:20,480 --> 00:08:21,860
好多存档啊

202
00:08:21,860 --> 00:08:24,180
然后现在你调一下这个方法

203
00:08:24,180 --> 00:08:26,100
直接所有的存档全没了啊

204
00:08:26,100 --> 00:08:26,879
全没了

205
00:08:27,279 --> 00:08:28,389
那么ok啊

206
00:08:28,389 --> 00:08:30,579
这个使用起来是不是非常简单啊

207
00:08:30,579 --> 00:08:31,779
然后咱同学可能说啊

208
00:08:31,779 --> 00:08:33,340
那这么简单啊

209
00:08:33,340 --> 00:08:34,059
非常好用

210
00:08:34,059 --> 00:08:34,419
对不对

211
00:08:34,419 --> 00:08:36,178
我已经会了数据储存了

212
00:08:36,200 --> 00:08:38,090
但是这里注意一个问题啊

213
00:08:38,090 --> 00:08:40,320
我们在这里大家要思考一个问题

214
00:08:41,600 --> 00:08:44,340
我们在这里存取的全是字符串类型的

215
00:08:44,360 --> 00:08:47,899
但是我们有时候希望存到本地的并不是这么简单

216
00:08:47,899 --> 00:08:49,220
就存个字符串什么的

217
00:08:49,220 --> 00:08:52,080
大家想比如说你做一个rpg游戏的进度

218
00:08:52,080 --> 00:08:55,019
你要存到本地有多少多少内容呢

219
00:08:55,019 --> 00:08:59,639
是不是存当前这个主角剧情做到哪了

220
00:08:59,639 --> 00:09:00,840
位置坐标在哪

221
00:09:00,840 --> 00:09:01,559
在哪个地图

222
00:09:01,559 --> 00:09:02,129
对不对

223
00:09:02,129 --> 00:09:04,750
然后当前主角有多少级

224
00:09:04,750 --> 00:09:05,740
有什么装备

225
00:09:05,740 --> 00:09:06,279
什么物品

226
00:09:06,279 --> 00:09:06,700
对不对

227
00:09:06,700 --> 00:09:08,379
这个内容太多了

228
00:09:08,379 --> 00:09:14,409
怎么可能就说很简单的存一个字符串就能给它存存下来了啊

229
00:09:14,409 --> 00:09:16,029
那这个问题怎么解决

230
00:09:16,029 --> 00:09:18,940
这时候就需要用到数据格式问题了啊

231
00:09:18,940 --> 00:09:22,019
就说我们这个数据就不能用这么简单的字符串啊

232
00:09:22,019 --> 00:09:23,879
我们就要用别的啊

233
00:09:23,879 --> 00:09:26,220
方式来去进行一个保存了啊

234
00:09:26,220 --> 00:09:30,360
我们先把这个我们让我们可以先把这个很复杂的数据给它

235
00:09:30,360 --> 00:09:31,500
做成一个字符串

236
00:09:31,500 --> 00:09:35,159
做成字符串以后再用这种方式去进行保存啊

237
00:09:35,399 --> 00:09:39,059
ok这个这个详细的内容我们就下节课再说啊

238
00:09:39,059 --> 00:09:41,940
这节课大家先把这个东西试一试


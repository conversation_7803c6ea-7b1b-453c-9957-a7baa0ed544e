@echo off
chcp 65001 >nul

echo ========================================
echo    一键删除"中文（自动生成）"
echo ========================================
echo.

powershell -Command "& { $files = Get-ChildItem -File | Where-Object { $_.Name -like '*中文（自动生成）*' }; if ($files.Count -eq 0) { Write-Host '当前目录下没有找到需要重命名的文件' -ForegroundColor Yellow } else { Write-Host \"找到 $($files.Count) 个文件，开始重命名...\" -ForegroundColor Green; Write-Host ''; $success = 0; foreach ($file in $files) { try { $newName = $file.Name -replace ' 中文（自动生成）', ''; if (Test-Path $newName) { Write-Host \"[跳过] $($file.Name) - 目标文件已存在\" -ForegroundColor Yellow } else { Rename-Item -Path $file.FullName -NewName $newName; Write-Host \"[完成] $($file.Name)\" -ForegroundColor Green; $success++ } } catch { Write-Host \"[失败] $($file.Name)\" -ForegroundColor Red } }; Write-Host ''; Write-Host \"重命名完成！成功处理 $success 个文件\" -ForegroundColor Cyan } }"

echo.
pause

@echo off
echo ========================================
echo    One-Click Rename Tool
echo    Remove " 中文（自动生成）" from filenames
echo ========================================
echo.

powershell -Command "Get-ChildItem -File | Where-Object { $_.Name -like '*中文*自动生成*' } | ForEach-Object { $newName = $_.Name -replace ' 中文（自动生成）', ''; if (Test-Path $newName) { Write-Host 'Skip: ' $_.Name ' - Target exists' } else { Rename-Item $_.FullName $newName; Write-Host 'Renamed: ' $_.Name } }"

echo.
echo Operation completed!
pause

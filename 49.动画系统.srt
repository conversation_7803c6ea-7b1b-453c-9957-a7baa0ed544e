1
00:00:09,800 --> 00:00:13,720
ok啊我们这节课来说一下动画系统

2
00:00:14,460 --> 00:00:16,879
那么我们创建一个空的工程

3
00:00:16,879 --> 00:00:18,559
然后我们打开它

4
00:00:35,159 --> 00:00:36,558
打开它

5
00:00:43,740 --> 00:00:46,340
ok我们在左边仍然是

6
00:00:46,340 --> 00:00:49,429
首先我们需要一个单色的精灵

7
00:00:49,429 --> 00:00:51,020
我们用它来做动画啊

8
00:00:51,020 --> 00:00:52,619
我们用它来做动画

9
00:00:55,119 --> 00:00:57,179
那么怎么去做一个动画啊

10
00:00:57,179 --> 00:01:01,299
我们之前比如说我们想用这个精灵动起来啊

11
00:01:01,299 --> 00:01:03,909
我们目前可能会想到两种方法

12
00:01:03,909 --> 00:01:07,198
第一种方法给它添加一个脚本

13
00:01:07,198 --> 00:01:10,549
在脚本里面怎样哎修改它的位置

14
00:01:10,549 --> 00:01:13,040
直接修改它的位置来让它动起来啊

15
00:01:13,040 --> 00:01:13,819
想让他怎么动

16
00:01:13,819 --> 00:01:15,980
你就怎样去修改它的位置呃

17
00:01:15,980 --> 00:01:16,730
就ok了

18
00:01:16,730 --> 00:01:18,340
那么第二种方法

19
00:01:18,700 --> 00:01:20,500
第二种方法是什么

20
00:01:20,500 --> 00:01:25,420
那么就是用我们上节课学的这个动作啊

21
00:01:25,420 --> 00:01:29,858
给他去让它执行一个比如说移动的动作或者旋转的动作

22
00:01:29,858 --> 00:01:31,659
那么它本身就有动画了

23
00:01:31,659 --> 00:01:34,328
那么这节课我们再说一个方式啊

24
00:01:34,328 --> 00:01:37,409
就是使用我们动画系统

25
00:01:37,409 --> 00:01:40,230
动画系统是coco coco creator啊

26
00:01:40,230 --> 00:01:42,750
他这边自己给我们去加上的

27
00:01:42,750 --> 00:01:46,819
那么这个动画系统的话嗯

28
00:01:46,819 --> 00:01:50,299
除了可以去做基本的这些操作以外

29
00:01:50,299 --> 00:01:52,040
它还有一个很重要的功能

30
00:01:52,040 --> 00:01:56,099
就是它可以去做一个真动画啊

31
00:01:56,099 --> 00:01:57,599
去做一系列的帧动画

32
00:01:57,599 --> 00:02:00,120
我们知道比如说一个人物在屏幕上动起来

33
00:02:00,120 --> 00:02:03,180
其实就是很多张图片在这快速切换了

34
00:02:03,180 --> 00:02:03,659
对不对

35
00:02:03,659 --> 00:02:05,819
我们就可以看到这个人物动起来了

36
00:02:05,819 --> 00:02:11,188
也就是说我们要快速的去对这个精灵它的这个显示图片

37
00:02:11,188 --> 00:02:13,050
图片进行一个切换

38
00:02:13,050 --> 00:02:17,610
那么我们如果通过代码的方式是可以做到这个效果的啊

39
00:02:17,610 --> 00:02:18,509
可以做到这个效果

40
00:02:18,509 --> 00:02:20,819
通过代码让这个一直进行切换

41
00:02:20,819 --> 00:02:22,560
然后我们可以看到人物动起来了

42
00:02:22,560 --> 00:02:23,709
这个是没问题的

43
00:02:23,709 --> 00:02:25,628
但是还有一种更简单的方式

44
00:02:25,628 --> 00:02:30,039
就是使用我们cos creator提供的这个动画编辑器了啊

45
00:02:30,039 --> 00:02:31,539
那我们一点点来说啊

46
00:02:31,539 --> 00:02:37,300
首先我们先来说一下通过动画编辑器来进行移动旋转这些操作啊

47
00:02:38,438 --> 00:02:41,998
那么首先啊如果我们要选中一个物体

48
00:02:41,998 --> 00:02:43,618
要对它进行动画编辑器

49
00:02:43,618 --> 00:02:44,818
你先要找到这个面板

50
00:02:44,818 --> 00:02:45,479
对不对

51
00:02:45,479 --> 00:02:47,939
那么如果找不到面板在这里啊

52
00:02:47,939 --> 00:02:49,729
你就可以找到动画编辑器

53
00:02:49,729 --> 00:02:56,639
然后在这里有一点就是你要想让他去这个执行我们的动画啊

54
00:02:56,639 --> 00:02:59,248
要让这个精灵有播放动画的功能

55
00:02:59,248 --> 00:03:01,739
你就必须让它有一个组件

56
00:03:01,739 --> 00:03:04,118
就是这个animation组件啊

57
00:03:04,118 --> 00:03:05,258
animation组件

58
00:03:05,258 --> 00:03:08,378
那么我们如果没animation组件也没关系

59
00:03:08,378 --> 00:03:10,740
我们在这里大家可以看动画编辑器

60
00:03:10,740 --> 00:03:12,180
人家就不让你去编辑

61
00:03:12,180 --> 00:03:12,900
人家就说了

62
00:03:12,900 --> 00:03:13,919
要制作动画

63
00:03:13,919 --> 00:03:15,719
必须添加animation组件

64
00:03:15,719 --> 00:03:18,318
你在这儿点一下添加也是一样的

65
00:03:18,318 --> 00:03:22,459
它这边就会出现一个组件啊

66
00:03:22,459 --> 00:03:25,128
这个组件就是来帮我们去播放动画的

67
00:03:25,128 --> 00:03:27,699
那么在这里我们可以看一下啊

68
00:03:27,719 --> 00:03:30,419
首先它它这里有两项

69
00:03:30,419 --> 00:03:32,159
第一个是动画剪辑

70
00:03:32,159 --> 00:03:33,399
第二个是剪辑

71
00:03:33,508 --> 00:03:37,658
那么animation clip就是一个动画片段啊

72
00:03:37,658 --> 00:03:39,129
就是一个动画片段

73
00:03:39,129 --> 00:03:44,050
也就是说如果我希望当前这个方块执行一个动画啊

74
00:03:44,050 --> 00:03:47,210
我需要把一个动画片段放到这里

75
00:03:47,210 --> 00:03:49,819
那么它呢就会执行这个动画片段嗯

76
00:03:49,819 --> 00:03:52,310
内部执行内部的这个动画了啊

77
00:03:52,310 --> 00:03:55,000
比如说我有个move的动画啊

78
00:03:55,000 --> 00:03:56,259
我一个move的动画片段

79
00:03:56,259 --> 00:03:57,400
我把它拖到这里

80
00:03:57,400 --> 00:04:00,699
那么他就会执行这个move的这个这个这个动作

81
00:04:00,699 --> 00:04:03,580
为一个旋转的animation clip

82
00:04:03,580 --> 00:04:05,439
我也可以把旋转的拖进来

83
00:04:05,439 --> 00:04:06,580
那么它就会执行

84
00:04:06,580 --> 00:04:08,680
所以实际上对于动画系统而言

85
00:04:08,680 --> 00:04:11,069
animation这个是一个组件

86
00:04:11,069 --> 00:04:12,719
这个是用来播放动画的

87
00:04:12,719 --> 00:04:16,019
那每一个动画呢它都叫一个animation clip

88
00:04:16,019 --> 00:04:17,350
都是一个动画片段

89
00:04:17,350 --> 00:04:20,269
那么动画片段同时也是一个文件啊

90
00:04:20,269 --> 00:04:23,629
也就是说每一个动画我们都会给它生成一个文件

91
00:04:23,629 --> 00:04:25,550
最后把文件拖到这里就行了

92
00:04:25,550 --> 00:04:29,819
那么这里这里是个动画片段的一个数组

93
00:04:29,978 --> 00:04:32,139
我们可以有很多个诶

94
00:04:32,139 --> 00:04:33,968
你看比如说我我打个二

95
00:04:33,968 --> 00:04:35,559
这里就有两个片段了

96
00:04:35,559 --> 00:04:41,240
也就意味着哎我们这个动画啊默认是播放这个动画片段的

97
00:04:41,240 --> 00:04:45,410
但是它除了默认的它还可以支持播放哪些动画

98
00:04:45,410 --> 00:04:47,310
那么就是这个数组里的了啊

99
00:04:47,310 --> 00:04:50,279
这些数组里的就是它能播放的动画

100
00:04:50,279 --> 00:04:55,589
而这个默认的就是它嗯默认情况下播放的那个动画啊

101
00:04:55,589 --> 00:04:57,149
我们先给它恢复成零

102
00:04:57,149 --> 00:05:00,180
最后一个按钮就是如果把它勾上以后

103
00:05:00,180 --> 00:05:02,250
这里默认的动画如果有东西

104
00:05:02,250 --> 00:05:04,939
它就会默认去执行这个动画啊

105
00:05:04,939 --> 00:05:06,139
如果你不给他勾上

106
00:05:06,139 --> 00:05:07,819
你就算给他一个默认动画

107
00:05:07,819 --> 00:05:10,040
他上来也不会自己去播放

108
00:05:10,740 --> 00:05:14,310
那么我们先把这三项知道了啊

109
00:05:14,310 --> 00:05:16,139
然后呢接下来我们知道了

110
00:05:16,139 --> 00:05:17,579
有了动画播放器了

111
00:05:17,579 --> 00:05:19,949
这个animation组件就是用来播放动画的

112
00:05:19,949 --> 00:05:24,620
那么接下来我们就要去创建这个animation clip了啊

113
00:05:24,620 --> 00:05:25,759
就是动画片段

114
00:05:25,759 --> 00:05:27,779
那么在这里我们可以看一下

115
00:05:27,839 --> 00:05:30,899
他告诉我们我们当前一个动画片段都没有啊

116
00:05:30,899 --> 00:05:32,939
我们是不是要给这个方块创建一个

117
00:05:32,939 --> 00:05:34,459
我们就创建一个吧

118
00:05:34,459 --> 00:05:38,480
那么保存位置我们这里可以创建文件夹啊

119
00:05:38,480 --> 00:05:40,759
那这里这个位置就和这里是对应的

120
00:05:40,759 --> 00:05:41,660
assets文件

121
00:05:41,660 --> 00:05:42,699
大家可以看一下

122
00:05:42,699 --> 00:05:46,899
那么在这里比如说我们学个名字就叫木板

123
00:05:46,899 --> 00:05:48,620
直接放到这里根目录

124
00:05:48,699 --> 00:05:50,230
那么大家可以看一下

125
00:05:50,230 --> 00:05:54,139
这里就会出现这样的一个图标啊

126
00:05:54,139 --> 00:05:55,310
这样的一个图标

127
00:05:55,310 --> 00:05:56,720
一个n型的一个图标

128
00:05:56,720 --> 00:05:57,319
是不是

129
00:05:57,319 --> 00:05:58,339
然后叫木

130
00:05:58,339 --> 00:06:00,480
那么这个就代表一个动画片段

131
00:06:00,560 --> 00:06:02,720
然后这时候我们选中我们的精灵

132
00:06:02,720 --> 00:06:05,420
在右边我们可以看一下它的这个动画

133
00:06:05,420 --> 00:06:06,980
数组里面已经有一个动画了

134
00:06:06,980 --> 00:06:08,060
就叫木啊

135
00:06:08,060 --> 00:06:11,949
也就是说它是呃有一个木偶动画啊

136
00:06:11,949 --> 00:06:13,360
是可以执行的

137
00:06:13,360 --> 00:06:16,329
如果你希望他上来就执行这个木偶动画

138
00:06:16,329 --> 00:06:18,889
你就可以把木拖到这里

139
00:06:18,889 --> 00:06:20,119
然后把勾勾画上

140
00:06:20,119 --> 00:06:23,298
那这样的话只要我们这个程序一运行

141
00:06:23,298 --> 00:06:25,839
它就会自己来播放的目录动画了

142
00:06:26,180 --> 00:06:27,620
但是现在木有动画

143
00:06:27,620 --> 00:06:29,420
我们知道我们虽然创建了这个动画

144
00:06:29,420 --> 00:06:30,379
但是它是空的

145
00:06:30,379 --> 00:06:31,220
对不对

146
00:06:31,579 --> 00:06:35,689
怎样去呃编辑这个动画呢

147
00:06:35,689 --> 00:06:38,089
首先我要选中这个物体啊

148
00:06:38,089 --> 00:06:39,649
就是播放动画的这个物体

149
00:06:39,649 --> 00:06:45,209
在动画编辑器里面有第一个按钮是打开编辑模式

150
00:06:45,209 --> 00:06:46,860
你必须打开编辑模式

151
00:06:46,860 --> 00:06:48,060
你看它变亮了

152
00:06:48,060 --> 00:06:49,480
这时候才可以编辑

153
00:06:49,480 --> 00:06:51,879
然后这里就会出现这样一个按钮

154
00:06:51,879 --> 00:06:56,009
这样一组按钮就是你比如说你在这里做完动画

155
00:06:56,009 --> 00:06:59,399
你要点保存或者ctrl加s才能保存起来啊

156
00:06:59,399 --> 00:07:01,290
不想编辑了就点关闭

157
00:07:01,290 --> 00:07:03,939
你看编辑模式就结束了

158
00:07:03,939 --> 00:07:04,839
对不对

159
00:07:04,839 --> 00:07:06,519
那么ok啊

160
00:07:07,218 --> 00:07:12,528
我们来看一下开始编辑以后的动编辑的这个这个面板啊

161
00:07:12,528 --> 00:07:15,420
它它的每一项功能是什么啊

162
00:07:15,478 --> 00:07:20,480
呃首先我们知道大家可以看这个动画是一帧一帧组成的啊

163
00:07:20,480 --> 00:07:23,060
这里面每一格都是一针啊

164
00:07:23,060 --> 00:07:24,410
从零开始

165
00:07:24,410 --> 00:07:26,860
那么上面这个是不是代表时间

166
00:07:26,899 --> 00:07:28,459
首先前面冒号

167
00:07:28,459 --> 00:07:30,158
前面的是代表几秒

168
00:07:30,158 --> 00:07:32,108
比如说零就是零秒

169
00:07:32,108 --> 00:07:34,538
或者到到后面一就是一秒两秒

170
00:07:34,538 --> 00:07:38,079
我们滑动滚轮可以给我们缩放这个时间轴

171
00:07:38,158 --> 00:07:41,278
我们可以看到到了这里就一秒了啊

172
00:07:41,278 --> 00:07:42,959
所以如果你要做个一秒的动画

173
00:07:42,959 --> 00:07:44,788
你就可以从这里做到这里

174
00:07:44,788 --> 00:07:47,379
那么后面这个代表的是什么意思

175
00:07:47,899 --> 00:07:49,189
你的同学一看啊

176
00:07:49,189 --> 00:07:51,360
50啊一秒

177
00:07:51,360 --> 00:07:53,759
那这55到这刚好零零

178
00:07:53,759 --> 00:07:55,560
这是不是又是个60啊

179
00:07:55,560 --> 00:07:57,000
那这60是什么意思

180
00:07:57,000 --> 00:08:00,319
那么这60是真它不是时间了

181
00:08:00,319 --> 00:08:01,579
它是针啊

182
00:08:01,579 --> 00:08:03,589
也就是说这里是第五针

183
00:08:03,589 --> 00:08:04,459
第十帧

184
00:08:04,459 --> 00:08:05,720
第15针到这里

185
00:08:05,720 --> 00:08:08,220
55帧到这里也就是几十帧

186
00:08:08,220 --> 00:08:09,339
60帧

187
00:08:12,759 --> 00:08:14,019
这个是默认的设定

188
00:08:14,019 --> 00:08:15,220
这个设定在哪里呢

189
00:08:15,220 --> 00:08:18,670
在这里大家看一下

190
00:08:18,670 --> 00:08:20,639
默认是一秒60帧

191
00:08:20,939 --> 00:08:23,699
如果你对它进行修改啊

192
00:08:23,699 --> 00:08:24,629
30

193
00:08:24,629 --> 00:08:29,920
那么再仔细看这里50 15 20 25~30的时候就变成一秒了

194
00:08:29,920 --> 00:08:31,819
也就是一秒播放30帧

195
00:08:31,819 --> 00:08:35,629
这个动画到底是以多少帧来去做这个动画

196
00:08:35,629 --> 00:08:37,889
这个就看你自己的一个操作了

197
00:08:37,889 --> 00:08:39,690
那么后面还有一个播放的速度啊

198
00:08:39,690 --> 00:08:41,549
就是你整个比如说动画做完了

199
00:08:41,549 --> 00:08:45,360
你觉得这个速度比较慢或者比较快

200
00:08:45,360 --> 00:08:45,750
怎么办

201
00:08:45,750 --> 00:08:48,029
在这里去可以自己调一下啊

202
00:08:48,029 --> 00:08:50,450
这里可以调这个动画播放的速度

203
00:08:50,450 --> 00:08:52,879
这里是动画播放的一个模式啊

204
00:08:52,879 --> 00:08:54,169
是怎样播放的

205
00:08:54,169 --> 00:08:57,980
那么一会儿我们把这个我们先稍微做一个动画

206
00:08:57,980 --> 00:08:59,779
才能去试出这个模式啊

207
00:08:59,779 --> 00:09:01,740
不同的模式有什么区别

208
00:09:01,860 --> 00:09:04,479
那么在这里一排按钮对不对

209
00:09:05,539 --> 00:09:08,320
那么第一个这个打开关闭编辑模式

210
00:09:08,320 --> 00:09:08,980
我们知道了

211
00:09:08,980 --> 00:09:11,559
然后这这几个按钮就是对真做操作

212
00:09:11,559 --> 00:09:13,580
比如说和下一帧

213
00:09:13,580 --> 00:09:14,509
上一帧

214
00:09:14,509 --> 00:09:15,500
这个是播放

215
00:09:15,500 --> 00:09:16,549
如果当时有动画

216
00:09:16,549 --> 00:09:18,519
我们点这个按钮就可以播放动画了

217
00:09:18,519 --> 00:09:20,799
这个是到最前面第一针啊

218
00:09:20,799 --> 00:09:22,240
比如说你真到很后面了

219
00:09:22,240 --> 00:09:23,440
点这个就到第一针了啊

220
00:09:23,440 --> 00:09:24,399
其实这些无所谓

221
00:09:24,399 --> 00:09:26,458
因为我们直接可以拖在上面啊

222
00:09:26,659 --> 00:09:28,370
啊我们直接是吧

223
00:09:28,370 --> 00:09:32,210
选中点中这个红线就可以左右拖啊

224
00:09:32,210 --> 00:09:37,580
然后这里显示的是当前它的一个多少帧啊

225
00:09:37,580 --> 00:09:39,299
你看现在就是14帧

226
00:09:39,679 --> 00:09:40,820
到这里啊

227
00:09:40,820 --> 00:09:44,110
这里就是11帧嗯

228
00:09:44,110 --> 00:09:46,779
然后后右边还有两个啊

229
00:09:46,779 --> 00:09:48,879
右边还有两个呃

230
00:09:48,879 --> 00:09:50,200
这两个是做什么的

231
00:09:50,200 --> 00:09:53,799
首先这个我们先不管他啊

232
00:09:53,799 --> 00:09:56,860
我们到最后面再说这个啊

233
00:09:56,860 --> 00:09:59,320
到这个动画一会儿说完所有的

234
00:09:59,320 --> 00:10:01,159
最后再说这个嗯

235
00:10:01,159 --> 00:10:04,179
那么再玩这个这个加号啊

236
00:10:04,179 --> 00:10:05,980
这个加号点一下加号

237
00:10:05,980 --> 00:10:08,379
我们可以创建一个新的动画片段啊

238
00:10:08,379 --> 00:10:12,899
也就是说这个加号是创建新的动画片段的啊

239
00:10:12,899 --> 00:10:14,639
你可以创建很多动画片段啊

240
00:10:14,639 --> 00:10:16,049
所以其实非常简单

241
00:10:16,049 --> 00:10:21,120
只有这个真事件我们放到呃这个动画的后面

242
00:10:21,120 --> 00:10:22,279
再说啊

243
00:10:22,519 --> 00:10:26,120
那么在这里往下面这里大家可以看一下

244
00:10:26,120 --> 00:10:29,840
这里就是我们做的动画的这个呃精灵类

245
00:10:29,840 --> 00:10:31,399
你看是不是和它对应的

246
00:10:31,399 --> 00:10:33,679
就是你现在给谁去做动画呢

247
00:10:33,759 --> 00:10:35,620
啊那么它们是对应的

248
00:10:37,600 --> 00:10:43,549
那么呃除了他除了他我们下面这里还有一组

249
00:10:43,549 --> 00:10:44,990
这一组是干嘛的

250
00:10:44,990 --> 00:10:46,129
这一组很重要啊

251
00:10:46,129 --> 00:10:48,740
我们主要就是在这一组做操作的啊

252
00:10:48,740 --> 00:10:50,419
因为实际上大家可以看啊

253
00:10:50,419 --> 00:10:51,940
比如说我在这里

254
00:10:51,940 --> 00:10:53,080
我在这个位置

255
00:10:53,080 --> 00:10:55,000
比如说我要播放一秒的动画啊

256
00:10:55,000 --> 00:10:57,419
我要有一秒的动画怎么去做

257
00:10:57,620 --> 00:10:59,600
在第一帧的位置啊

258
00:10:59,600 --> 00:11:02,029
在第一帧的位置在这里注意啊

259
00:11:02,029 --> 00:11:07,159
我们可以给它去添加一个属性轨道

260
00:11:07,539 --> 00:11:08,799
因为你要做动画

261
00:11:08,799 --> 00:11:09,519
你要规定

262
00:11:09,519 --> 00:11:11,950
比如说我要对他的什么东西做动画

263
00:11:11,950 --> 00:11:14,059
比如说我们这个是木

264
00:11:14,059 --> 00:11:16,179
我要对它的位置做动画

265
00:11:16,179 --> 00:11:16,600
对不对

266
00:11:16,600 --> 00:11:18,669
还是我要对它的旋转做动画

267
00:11:18,669 --> 00:11:20,919
还是我要做帧动画还是做什么

268
00:11:20,919 --> 00:11:23,289
就是这个动画可以分很多种类的

269
00:11:23,289 --> 00:11:25,220
你要对它什么做动画

270
00:11:25,220 --> 00:11:27,500
你就把什么属性加到这里

271
00:11:27,500 --> 00:11:29,480
那我们在这里要做一个move

272
00:11:29,480 --> 00:11:31,309
111个移动的动画

273
00:11:31,309 --> 00:11:35,620
所以在这里我要点下加号去加上一个位置

274
00:11:35,620 --> 00:11:37,779
那么我们可以加个position

275
00:11:37,779 --> 00:11:39,009
就是x y都有

276
00:11:39,009 --> 00:11:41,720
也可以只加一个啊

277
00:11:41,720 --> 00:11:44,090
比如说你可以加个position啊

278
00:11:44,090 --> 00:11:46,279
这样的话就是x y都影响

279
00:11:46,279 --> 00:11:49,279
你也可以选选中右边这个给它删除

280
00:11:49,279 --> 00:11:53,059
也可以怎样加x或者加y啊

281
00:11:53,059 --> 00:11:53,960
也是可以的

282
00:11:53,960 --> 00:11:55,850
这个就是单向的一个位置

283
00:11:55,850 --> 00:11:58,120
那比如说我现在就加一个y

284
00:11:58,559 --> 00:12:01,320
我就加一个y加x吧

285
00:12:01,320 --> 00:12:02,580
我们让它左右移动吧

286
00:12:02,580 --> 00:12:04,429
这个场景适合左右移动

287
00:12:04,429 --> 00:12:06,379
比如说我加一个x属性

288
00:12:06,379 --> 00:12:08,870
我希望给他的x属性做动画

289
00:12:08,870 --> 00:12:10,839
就是从左向右移动

290
00:12:10,839 --> 00:12:12,039
那么怎么办

291
00:12:12,039 --> 00:12:13,120
那么在这里

292
00:12:13,120 --> 00:12:16,779
首先我们在这里插入一个关键帧啊

293
00:12:16,779 --> 00:12:18,250
我这里已经插入了

294
00:12:18,250 --> 00:12:19,979
那么大家可以看一下啊

295
00:12:20,840 --> 00:12:26,059
嗯关键帧就是在这一条线上出现了这样的一个蓝色的点啊

296
00:12:26,059 --> 00:12:27,559
你哪怕线移动了

297
00:12:27,559 --> 00:12:29,240
它都会有这个蓝色的点

298
00:12:29,240 --> 00:12:31,500
比如说我们要到一秒啊

299
00:12:31,500 --> 00:12:34,500
我们到一秒的时候要给他再做一个关键帧

300
00:12:34,500 --> 00:12:39,460
那么它的第一个关键帧和最后一个关键帧就是我们整个的动画时间

301
00:12:39,460 --> 00:12:44,220
那么加关键帧是不是我们再要在这儿点一下插入关键帧

302
00:12:44,220 --> 00:12:45,840
是不是这样是可以的

303
00:12:45,840 --> 00:12:47,220
我们点一下插入关键帧

304
00:12:47,220 --> 00:12:48,600
它就又插入一个

305
00:12:48,600 --> 00:12:50,490
当然也可以删除啊

306
00:12:50,490 --> 00:12:52,679
那么在这里还有一种方法

307
00:12:52,679 --> 00:12:56,779
只要它的x因为我们是给x做的动画

308
00:12:56,779 --> 00:12:58,549
这一帧的x是在这里

309
00:12:58,549 --> 00:13:02,000
如果你希望在一秒的时候再加入一个关键帧

310
00:13:02,000 --> 00:13:04,339
你只需要让它的x发生变化

311
00:13:04,339 --> 00:13:06,019
你把它移动

312
00:13:06,179 --> 00:13:08,399
你看当你产生移动以后

313
00:13:08,399 --> 00:13:11,370
那么他知道这个位置的x和这个是不一样的

314
00:13:11,370 --> 00:13:14,740
所以这里它就会自动加入一个关键帧

315
00:13:14,960 --> 00:13:19,919
那这样的话你再拖你就发现这个动画就生成了非常简单

316
00:13:19,919 --> 00:13:22,289
在中间我们可以给他很多关键帧

317
00:13:22,289 --> 00:13:24,480
比如说我们可以让他做这样的一个动画

318
00:13:24,480 --> 00:13:27,529
那么在这里你还能给他一个关键帧啊

319
00:13:27,529 --> 00:13:28,850
你还能给他一个关键帧啊

320
00:13:28,850 --> 00:13:30,590
当然我们现在不能做这个动画

321
00:13:30,590 --> 00:13:34,049
因为我们只有x方向的动画啊

322
00:13:34,049 --> 00:13:35,879
我没有加y的啊

323
00:13:35,879 --> 00:13:38,740
当然我们可以单独的把y加上也行啊

324
00:13:40,759 --> 00:13:41,840
加入关键帧

325
00:13:43,399 --> 00:13:44,919
加入一个关键帧

326
00:13:47,639 --> 00:13:54,789
那么呃y的话中间我们让它向上移

327
00:13:54,789 --> 00:14:00,049
那这样的话大家可以看是不是就这样一个效果了啊

328
00:14:00,049 --> 00:14:02,720
那么这里就是对两个属性做操作了

329
00:14:02,720 --> 00:14:04,519
实际上你可以只给一个position啊

330
00:14:04,519 --> 00:14:05,679
它就都有了

331
00:14:08,799 --> 00:14:10,440
那么插入关键帧

332
00:14:10,440 --> 00:14:12,960
大家看我在这里都是点这个插入关键帧

333
00:14:12,960 --> 00:14:15,360
是不是实际上你稍微对它动一下

334
00:14:15,360 --> 00:14:17,549
自动就可以生成这个关键帧

335
00:14:17,549 --> 00:14:21,500
比如说我在这里再加个旋转rotation

336
00:14:21,500 --> 00:14:22,700
你看啊

337
00:14:22,700 --> 00:14:25,839
只要对旋转稍微随便动一下

338
00:14:26,860 --> 00:14:28,179
然后你再改回来

339
00:14:28,179 --> 00:14:30,789
那么这里就自动会生成一个一个关键帧

340
00:14:30,789 --> 00:14:34,190
就是你你要对什么属性做动画

341
00:14:34,190 --> 00:14:37,100
你在这里只要对这个属性稍微调一下

342
00:14:37,100 --> 00:14:40,068
那么这个位置就会产生一个关键帧

343
00:14:40,068 --> 00:14:41,448
比如说在最后位置

344
00:14:41,448 --> 00:14:44,279
我让他转290度

345
00:14:45,019 --> 00:14:50,559
那么这里大家可以看我翻是不是就变成这样了

346
00:14:51,039 --> 00:14:53,620
按这个这个预览按钮

347
00:14:53,620 --> 00:14:56,219
你看这个动画就开始播放了

348
00:14:57,100 --> 00:14:59,139
这是不是就是一个混合的一个动画了

349
00:14:59,139 --> 00:15:02,089
我们对三个属性做了这样一个动画啊

350
00:15:02,089 --> 00:15:03,859
三个属性做了这样一个动画

351
00:15:03,859 --> 00:15:07,958
那么这时候我们就可以来看一下动画的一个循环模式了

352
00:15:09,340 --> 00:15:11,559
首先第一个是普通模式

353
00:15:13,059 --> 00:15:14,200
默认的不用管啊

354
00:15:14,200 --> 00:15:15,679
从这个normal开始看

355
00:15:16,360 --> 00:15:18,730
那么它是一个正常的模式

356
00:15:18,730 --> 00:15:23,139
那么再往下这个reverse reverse啊

357
00:15:23,139 --> 00:15:25,240
我们看这这是这三个是一组

358
00:15:25,240 --> 00:15:26,320
这三个是一组

359
00:15:26,320 --> 00:15:28,899
和上面三个和下面三个完全一样

360
00:15:28,899 --> 00:15:31,340
区别就是改了个名字叫reverse

361
00:15:31,340 --> 00:15:32,929
或者后面加了个reverse

362
00:15:32,929 --> 00:15:33,740
reverse

363
00:15:33,740 --> 00:15:38,450
其实就是把这个动画逆向播放啊

364
00:15:38,450 --> 00:15:40,610
就是从后面往前面播放啊

365
00:15:40,610 --> 00:15:42,019
注意这一组啊

366
00:15:42,019 --> 00:15:43,610
所以你只要明白这一个

367
00:15:43,610 --> 00:15:44,809
后面这两个是一样的

368
00:15:44,809 --> 00:15:47,490
后面三个全是逆向播放的啊

369
00:15:47,490 --> 00:15:50,139
上面这三个是正向播放的

370
00:15:50,259 --> 00:15:53,740
那么选normal就是播放一次loop

371
00:15:54,320 --> 00:15:55,730
只要一开始播放

372
00:15:55,730 --> 00:15:58,578
他这个动画就会一直循环播放

373
00:15:59,700 --> 00:16:02,820
你看啊就会变成这样是吧啊

374
00:16:02,820 --> 00:16:05,399
一直在这里进行一个动画的播放

375
00:16:06,179 --> 00:16:07,958
还有一个叫乒乓

376
00:16:08,159 --> 00:16:09,480
乒乓这个东西

377
00:16:09,480 --> 00:16:11,399
大家大家想就跟乒乓球一样

378
00:16:11,399 --> 00:16:12,419
乒乓球的特点

379
00:16:12,419 --> 00:16:14,580
你打过来他打回来

380
00:16:14,580 --> 00:16:17,509
你打过去他打回来是不是这样的一个情况

381
00:16:17,509 --> 00:16:20,029
那么在这里如果设置成平方

382
00:16:20,029 --> 00:16:21,860
你看正向一变

383
00:16:21,860 --> 00:16:22,700
逆向一遍

384
00:16:22,700 --> 00:16:23,960
是不是就和乒乓球一样

385
00:16:23,960 --> 00:16:25,159
打过来打过去啊

386
00:16:25,159 --> 00:16:25,879
这个样子

387
00:16:25,879 --> 00:16:27,740
那么这个就是平方啊

388
00:16:27,740 --> 00:16:29,099
这个就是平方

389
00:16:31,120 --> 00:16:32,179
ok啊

390
00:16:40,639 --> 00:16:42,080
那么这个东西啊

391
00:16:42,080 --> 00:16:47,110
这个东西就是我们给他拿普通的这个属性来做了一个动画

392
00:16:47,110 --> 00:16:49,340
拿普通的属性来做了一个动画

393
00:16:49,340 --> 00:16:51,320
那么这节课我们先说这么多

394
00:16:51,320 --> 00:16:53,600
大家先把这个动画这一套熟悉一下

395
00:16:53,600 --> 00:16:57,419
那么下节课我们来讲这个动画剩余的部分

396
00:17:08,700 --> 00:17:10,059
略略略略略


1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,759 --> 00:00:11,699
ok这节课咱们来稍微做一个小练习

3
00:00:11,699 --> 00:00:14,279
然后来练习一下咱们上节课的射线啊

4
00:00:14,279 --> 00:00:18,239
看一下他是不是和咱们之前想的这个一样

5
00:00:18,260 --> 00:00:21,920
那么在这里咱们说了这个敌人的巡逻的这样的一个情况

6
00:00:21,920 --> 00:00:25,789
那么它无非就是到了一个前面检测到要碰到物体了

7
00:00:25,789 --> 00:00:30,460
然后他就他就往其他部分去旋转啊

8
00:00:30,460 --> 00:00:32,380
他就要不然转到这边

9
00:00:32,780 --> 00:00:34,219
要不然转到这边

10
00:00:34,219 --> 00:00:35,880
要不然这边要不然这边

11
00:00:35,880 --> 00:00:38,100
那么其实就这样一个逻辑啊

12
00:00:38,100 --> 00:00:40,740
那么我们这里做一个类似的小练习

13
00:00:40,740 --> 00:00:41,460
为了简单

14
00:00:41,460 --> 00:00:42,719
我们只做y轴

15
00:00:42,719 --> 00:00:45,520
我们就让它往上走啊

16
00:00:45,520 --> 00:00:48,009
到了上面往下走啊

17
00:00:48,009 --> 00:00:48,820
其实这个一样

18
00:00:48,820 --> 00:00:52,810
无非就是我们不让它去检测x的方向啊

19
00:00:52,810 --> 00:00:54,219
那么写完以后

20
00:00:54,219 --> 00:00:57,460
如果大家把这个搞搞定了啊

21
00:00:57,460 --> 00:01:00,700
大家可以自己去加一个带x方向检测的啊

22
00:01:00,700 --> 00:01:03,039
我们就做这样的一个运动啊

23
00:01:03,039 --> 00:01:04,959
依然是用这个射线来检测啊

24
00:01:04,959 --> 00:01:06,340
主要就是来练习这个射线

25
00:01:06,340 --> 00:01:09,519
那我们创建一个空的工程嗯

26
00:01:09,519 --> 00:01:11,700
然后在这里创建两堵墙

27
00:01:12,239 --> 00:01:16,859
我们用直接用单色的这样的一个精灵去创建

28
00:01:18,500 --> 00:01:19,849
就是这个墙

29
00:01:19,849 --> 00:01:21,620
这就是这个墙

30
00:01:24,180 --> 00:01:26,760
防水加第一份上面一个

31
00:01:26,760 --> 00:01:27,629
下面一个

32
00:01:27,629 --> 00:01:30,980
然后每一个墙我们都给它加上一个

33
00:01:32,379 --> 00:01:34,719
物理的一个碰撞

34
00:01:34,719 --> 00:01:36,459
然后给它设置为静态的

35
00:01:36,459 --> 00:01:37,659
一定要设置为静态的

36
00:01:37,659 --> 00:01:38,980
要不然他就掉下去

37
00:01:42,480 --> 00:01:47,040
然后我们再来一个我们的自己的一个玩家啊

38
00:01:47,040 --> 00:01:49,260
这个准确的不能叫玩家了

39
00:01:49,260 --> 00:01:51,840
就一个自动巡逻的这样的一个东西

40
00:01:51,840 --> 00:01:52,560
对不对

41
00:01:55,239 --> 00:01:57,219
ok就是它啊

42
00:01:57,219 --> 00:01:58,719
就这样一个小方块

43
00:01:58,719 --> 00:02:00,040
那么

44
00:02:01,420 --> 00:02:04,150
我们在他身上去编写一个脚本

45
00:02:04,150 --> 00:02:07,159
新建一个脚本放到他的身上

46
00:02:09,460 --> 00:02:11,979
打开这个脚本去编写

47
00:02:13,939 --> 00:02:18,889
首先我们需要保存一下它的一个变量啊

48
00:02:18,889 --> 00:02:20,419
这个变量是什么

49
00:02:20,419 --> 00:02:26,000
就是方向当前的方向是向上还是向下还是向左向右

50
00:02:26,000 --> 00:02:27,379
那么在这里啊

51
00:02:27,379 --> 00:02:29,240
我写就是按四个方向写了

52
00:02:29,240 --> 00:02:32,360
所以我们这里用的结构体v2 

53
00:02:32,780 --> 00:02:34,819
我们用零逗号一啊

54
00:02:34,819 --> 00:02:35,900
如果要四个方向

55
00:02:35,900 --> 00:02:39,340
其实主要就是判断的时候还能判断一个x的

56
00:02:39,360 --> 00:02:41,879
我们这里只只关心y不管x啊

57
00:02:41,879 --> 00:02:44,669
我们就上下向上就是正一

58
00:02:44,669 --> 00:02:45,930
向下就是-1

59
00:02:45,930 --> 00:02:46,500
对不对

60
00:02:46,500 --> 00:02:47,460
向上就是-1

61
00:02:47,460 --> 00:02:50,620
向下就是向上正一向下-1啊

62
00:02:50,620 --> 00:02:52,569
那么这个就代表我们一个方向

63
00:02:52,569 --> 00:02:54,219
你要是想把它类型写出来

64
00:02:54,219 --> 00:02:56,080
就是cc.vor two

65
00:02:58,219 --> 00:03:01,389
然后在这里update

66
00:03:01,389 --> 00:03:02,379
还有unload

67
00:03:02,379 --> 00:03:03,400
我们都要写内容

68
00:03:03,400 --> 00:03:04,120
unload里面

69
00:03:04,120 --> 00:03:07,180
首先我们要开启物理物理引擎

70
00:03:12,259 --> 00:03:14,699
开启物理引擎等于tru

71
00:03:14,800 --> 00:03:18,219
然后在update里面我们先要把移动做出来

72
00:03:18,219 --> 00:03:19,419
先让它动起来

73
00:03:19,419 --> 00:03:22,740
沿着我们这个方向动起来其实非常简单

74
00:03:23,580 --> 00:03:29,039
首先x的移动就是我当前的x的位置

75
00:03:29,039 --> 00:03:31,379
再加上一个z点

76
00:03:31,379 --> 00:03:33,389
dir.x就是我的方向

77
00:03:33,389 --> 00:03:39,050
是不是这个方向其实就是零方向乘以一个速度啊

78
00:03:39,050 --> 00:03:41,240
100乘以一个dt啊

79
00:03:41,240 --> 00:03:44,039
就是把这个100乘dt啊

80
00:03:44,039 --> 00:03:47,159
就是每帧100变成每秒100了啊

81
00:03:47,159 --> 00:03:50,370
前面一直说这个一定不要忘了啊

82
00:03:50,370 --> 00:03:55,780
乘一个dt y轴一模一样

83
00:03:55,780 --> 00:03:57,939
然后我们现在运行一下

84
00:03:57,939 --> 00:03:59,300
看一下效果

85
00:04:03,219 --> 00:04:04,990
哎往上看一下

86
00:04:04,990 --> 00:04:06,400
那么他现在就上去了

87
00:04:06,400 --> 00:04:08,740
但是现在它是没有射线检测的

88
00:04:08,740 --> 00:04:11,060
我们把射线检测给加上

89
00:04:14,280 --> 00:04:22,149
lir s等于一个cc.director.get physics manager

90
00:04:22,149 --> 00:04:25,759
点recast射线检测

91
00:04:25,759 --> 00:04:28,500
首先第一个点就是打出射线的点

92
00:04:28,959 --> 00:04:32,379
是用我们自身去打打出来啊

93
00:04:32,379 --> 00:04:34,420
我这个射线是放在这个update里了

94
00:04:34,420 --> 00:04:37,019
因为我每一帧都要重新打一条射线

95
00:04:37,439 --> 00:04:42,339
然后第二个点我们就应该用cc.v2 

96
00:04:42,560 --> 00:04:47,540
然后在这里x还是z.note.x啊

97
00:04:47,540 --> 00:04:50,100
所以说实际上它的嗯

98
00:04:50,100 --> 00:04:54,540
第二个点的x方向x的位置和第一个点的x位置啊

99
00:04:54,540 --> 00:04:56,160
它是在一个纵轴上

100
00:04:56,160 --> 00:04:57,540
所以它是垂直的

101
00:04:57,540 --> 00:04:58,279
对不对

102
00:04:58,639 --> 00:05:03,529
然后第二个就是this.note.y

103
00:05:03,529 --> 00:05:11,889
加上一个加上一个this.dr.y啊

104
00:05:11,889 --> 00:05:14,740
这个就代表我当前我的中心点

105
00:05:14,740 --> 00:05:15,100
对不对

106
00:05:15,100 --> 00:05:16,360
我那个小方块的中心点

107
00:05:16,360 --> 00:05:19,560
然后小方块的中心点加上一个方向

108
00:05:19,600 --> 00:05:22,180
这个代表一个方向是向上还是向下

109
00:05:22,180 --> 00:05:23,920
是正一还是-1

110
00:05:23,939 --> 00:05:25,949
然后检测的距离是多少

111
00:05:25,949 --> 00:05:27,029
乘以个50

112
00:05:27,029 --> 00:05:29,199
那就是正50还是-50

113
00:05:29,220 --> 00:05:35,860
所以结果就是嗯结果就是这个小方块向上一个箭头

114
00:05:35,860 --> 00:05:37,480
这个箭头检测多远啊

115
00:05:37,480 --> 00:05:41,399
向上这个向量就是五长度为50的向量去检测

116
00:05:41,420 --> 00:05:42,620
打到墙以后

117
00:05:42,620 --> 00:05:46,819
然后再从这个中心点向下打一条50的50向量的

118
00:05:46,819 --> 00:05:51,240
这样的一个50长度的向量去检测啊

119
00:05:51,240 --> 00:05:51,720
非常简

120
00:05:51,720 --> 00:05:52,829
单啊这个

121
00:05:52,829 --> 00:05:56,300
然后呃

122
00:05:58,279 --> 00:06:00,279
逗号第三个

123
00:06:02,139 --> 00:06:05,980
类型我们就是只检测单点就行了

124
00:06:05,980 --> 00:06:07,810
那么检测完了以后

125
00:06:07,810 --> 00:06:10,120
我们现在先输出一下

126
00:06:10,120 --> 00:06:11,319
看一下行不行

127
00:06:16,500 --> 00:06:18,000
如果这个less变成一

128
00:06:18,000 --> 00:06:21,000
就证明检测到这个碰碰撞啊

129
00:06:21,000 --> 00:06:21,600
如果他是零

130
00:06:21,600 --> 00:06:24,139
就证明目前没有检测到这个碰撞

131
00:06:24,160 --> 00:06:25,029
对不对

132
00:06:25,029 --> 00:06:26,939
那我们现在运行看一下效果

133
00:06:28,680 --> 00:06:30,660
啊一直是0000000

134
00:06:30,660 --> 00:06:31,860
快到墙上了

135
00:06:31,860 --> 00:06:36,500
一你看快到墙的时候就变成一了啊

136
00:06:36,800 --> 00:06:38,779
那么这个距离可能有点短

137
00:06:38,779 --> 00:06:40,100
我们让它再长点吧

138
00:06:40,100 --> 00:06:42,160
检测100像素吧

139
00:06:42,180 --> 00:06:45,240
就是我的中心点离墙有100像素的时候

140
00:06:45,240 --> 00:06:47,240
我就让它掉头啊

141
00:06:47,240 --> 00:06:48,529
那这个就很简单了

142
00:06:48,529 --> 00:06:52,879
如果ios.lens大于零

143
00:06:52,879 --> 00:06:54,980
就证明我碰到墙了

144
00:06:54,980 --> 00:06:56,480
碰到墙以后怎么办

145
00:06:56,500 --> 00:06:57,939
转弯对不对

146
00:06:57,939 --> 00:06:59,079
转到哪个方向

147
00:06:59,079 --> 00:07:02,019
我这里就不用关心x了啊

148
00:07:02,060 --> 00:07:04,279
我就只关心外外呢

149
00:07:04,279 --> 00:07:06,660
就让他乘以等于-1

150
00:07:06,660 --> 00:07:08,399
就让它朝相反的方向啊

151
00:07:08,399 --> 00:07:09,000
如果是一

152
00:07:09,000 --> 00:07:09,839
我就变成-1

153
00:07:09,839 --> 00:07:11,250
如果是-1就变成一

154
00:07:11,250 --> 00:07:12,480
非常简单啊

155
00:07:12,480 --> 00:07:14,720
然后我们现在来运行看一下效果

156
00:07:18,019 --> 00:07:20,029
往上走

157
00:07:20,029 --> 00:07:22,000
是不是

158
00:07:23,060 --> 00:07:28,399
然后到下面它就变成一个来回摇摆的这样的一个呃

159
00:07:28,399 --> 00:07:30,060
这样的一个运动路线了

160
00:07:33,920 --> 00:07:36,980
啊你自己就要去想象他现在就向下

161
00:07:36,980 --> 00:07:39,319
一直有一个向下的箭头是吧

162
00:07:39,319 --> 00:07:42,439
这会一直向上有一个向上的箭头啊

163
00:07:42,439 --> 00:07:44,149
只不过这个我们看不见啊

164
00:07:44,149 --> 00:07:46,250
当这个箭头碰到一个物体了

165
00:07:46,250 --> 00:07:48,980
马上就返回回来啊

166
00:07:49,259 --> 00:07:51,000
一定要按这个感觉啊

167
00:07:51,180 --> 00:07:55,800
那么这个东西啊就是射线的一个简单练习

168
00:07:55,800 --> 00:07:57,180
如果你会的单方向

169
00:07:57,180 --> 00:07:59,040
你可以尝试去做呃

170
00:07:59,040 --> 00:08:00,180
把x加上啊

171
00:08:00,180 --> 00:08:01,439
这里把x加上

172
00:08:01,439 --> 00:08:03,660
那无非就变成四个四种判断了

173
00:08:03,660 --> 00:08:04,980
就是010 -1

174
00:08:04,980 --> 00:08:08,800
然后一零-10就是上下左右四个方向

175
00:08:08,800 --> 00:08:10,000
对不对啊

176
00:08:10,000 --> 00:08:11,920
然后每一次检测到了以后

177
00:08:11,920 --> 00:08:16,160
你这儿给它下一个方向就ok了啊

178
00:08:16,160 --> 00:08:17,000
那么行

179
00:08:17,000 --> 00:08:18,740
那么我们这节课很简单

180
00:08:18,740 --> 00:08:20,579
就这么多啊


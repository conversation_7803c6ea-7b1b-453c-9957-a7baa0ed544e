1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,640 --> 00:00:11,880
那么这节课我们来讲一个数据格式

3
00:00:11,880 --> 00:00:17,300
那么这个数据格式应该是我们目前最常用的一个数据格式了啊

4
00:00:17,300 --> 00:00:19,460
不管你是做端游

5
00:00:19,460 --> 00:00:20,570
手游啊

6
00:00:20,570 --> 00:00:22,460
或者是你做应用啊

7
00:00:22,460 --> 00:00:24,620
软件以及手机上的软件

8
00:00:24,620 --> 00:00:25,219
苹果软件

9
00:00:25,219 --> 00:00:26,030
安卓软件

10
00:00:26,030 --> 00:00:28,719
那么它都是最常用的这个数据格式

11
00:00:28,739 --> 00:00:30,500
那么就是我们的jas

12
00:00:31,140 --> 00:00:32,520
什么是js

13
00:00:32,520 --> 00:00:36,500
那我们在这里先创建一个空的节点啊

14
00:00:38,619 --> 00:00:39,880
杰森test

15
00:00:39,880 --> 00:00:41,619
然后我们再创建一个脚本

16
00:00:45,460 --> 00:00:47,880
我们把脚本挂载上来

17
00:00:49,359 --> 00:00:52,420
让那个脚本这样就可以执行了啊

18
00:00:52,420 --> 00:00:53,170
对不对

19
00:00:53,170 --> 00:00:54,700
打开这个脚本

20
00:00:54,700 --> 00:00:57,460
那在这里我们就可以编写代码了

21
00:00:57,799 --> 00:01:00,439
那么首先我们先来说一下什么是jason啊

22
00:01:00,439 --> 00:01:01,460
什么是jason

23
00:01:09,540 --> 00:01:11,400
那么jason很简单

24
00:01:11,400 --> 00:01:13,920
首先它是属于一种数据格式的啊

25
00:01:13,920 --> 00:01:15,019
数据格式

26
00:01:15,739 --> 00:01:18,140
那数据格式不光有jason啊

27
00:01:18,140 --> 00:01:20,180
它还有其他的一些格式啊

28
00:01:20,180 --> 00:01:22,560
比如说xl啊

29
00:01:22,560 --> 00:01:25,379
比如说csv啊

30
00:01:25,379 --> 00:01:26,760
甚至你最简单的啊

31
00:01:26,760 --> 00:01:30,200
你自己组建的格式去干嘛写文本啊

32
00:01:30,200 --> 00:01:31,400
那都是可以的

33
00:01:31,400 --> 00:01:34,879
二一就是你自己去想一些格式去写文本啊

34
00:01:34,879 --> 00:01:35,680
都可以

35
00:01:35,700 --> 00:01:38,489
那么数据格式的种类是非常多的啊

36
00:01:38,489 --> 00:01:42,540
那在这里我们学的是最常用的啊

37
00:01:42,540 --> 00:01:44,620
这样的一个数据格式jason

38
00:01:44,680 --> 00:01:48,099
那么现在我们先来说下数据格式有什么用啊

39
00:01:48,099 --> 00:01:50,019
为什么我们要用到数据格式

40
00:01:50,019 --> 00:01:52,140
那知道了以后之后

41
00:01:52,140 --> 00:01:54,959
我们可能会用到json xml啊

42
00:01:54,959 --> 00:01:57,359
那么有一些端游的话啊

43
00:01:57,359 --> 00:01:59,340
他会用这个csv啊

44
00:01:59,340 --> 00:02:00,700
他会用csv

45
00:02:01,019 --> 00:02:04,920
但实际上这个是应该是格式里面最简单的啊

46
00:02:04,920 --> 00:02:07,260
我们也会提一下呃

47
00:02:07,260 --> 00:02:09,120
首先我们先来说什么是数据格式啊

48
00:02:09,120 --> 00:02:11,129
它是用来干嘛的啊

49
00:02:11,129 --> 00:02:14,199
那么我们先不要想他是干嘛的

50
00:02:14,199 --> 00:02:15,909
我们先想这样一个问题啊

51
00:02:15,909 --> 00:02:18,800
比如说我们有两个这个客户端

52
00:02:20,020 --> 00:02:21,460
有两个客户端

53
00:02:21,460 --> 00:02:25,900
客户端之间呢我们通过这个网络连接或者蓝牙连接啊

54
00:02:25,900 --> 00:02:28,259
就各种方式连接都可以

55
00:02:28,300 --> 00:02:29,439
连接上以后

56
00:02:29,439 --> 00:02:32,259
也就是说这两个客户端是作为联网应用了

57
00:02:32,259 --> 00:02:32,500
对不对

58
00:02:32,500 --> 00:02:33,400
是联网应用了

59
00:02:33,400 --> 00:02:34,750
不是单机的啊

60
00:02:34,750 --> 00:02:39,129
那这时候比如说我第一个客户端诶

61
00:02:39,129 --> 00:02:42,340
我有一些数据我要发给第二个客户端

62
00:02:43,580 --> 00:02:45,919
那么我们怎么发啊

63
00:02:45,919 --> 00:02:47,159
我们怎么发送

64
00:02:47,219 --> 00:02:49,319
那么通过网络发送

65
00:02:49,319 --> 00:02:50,400
大家去想啊

66
00:02:50,400 --> 00:02:53,039
那么我们实际上就是发送

67
00:02:53,039 --> 00:02:56,360
比如说大家现在认为它就是发送一串文本

68
00:02:56,479 --> 00:03:00,259
比如说我客户端要给第二个客户端发送一串文本

69
00:03:00,259 --> 00:03:03,560
这个文本里面就包含我要告诉他的一些信息啊

70
00:03:03,560 --> 00:03:05,219
我要告诉他的一些信息

71
00:03:05,240 --> 00:03:07,840
但是这时候大家就会想到一个问题啊

72
00:03:07,840 --> 00:03:12,340
比如说网络游戏一个客户端告诉另外一个客户端啊

73
00:03:12,340 --> 00:03:13,330
给他的信息

74
00:03:13,330 --> 00:03:16,460
这个数据量可能是非常庞大的啊

75
00:03:16,460 --> 00:03:20,180
并不是说告诉他哎很简单的一段文本就完事了

76
00:03:20,180 --> 00:03:22,099
可能有非常大的这个信息量

77
00:03:22,099 --> 00:03:26,780
比如说我这个客户端里面所在的角色目前位置是在哪

78
00:03:26,780 --> 00:03:27,800
血量是在哪

79
00:03:27,800 --> 00:03:29,500
什么防御啊什么的

80
00:03:29,520 --> 00:03:31,020
诶是怎么回事儿

81
00:03:31,020 --> 00:03:34,240
那可能这些数据都要同步给第二个客户端

82
00:03:34,439 --> 00:03:37,680
那这时候大家想数据量这么复杂

83
00:03:37,680 --> 00:03:40,710
我怎样把它组建成一段文本

84
00:03:40,710 --> 00:03:43,280
告诉第二个客户端啊

85
00:03:43,280 --> 00:03:44,150
怎么样去做

86
00:03:44,150 --> 00:03:45,319
这是第一个啊

87
00:03:45,319 --> 00:03:50,509
第一个问题就是客户端之间它的一个沟通怎么怎么沟通比较好

88
00:03:50,509 --> 00:03:53,439
第二个问题就是咱们说了

89
00:03:53,439 --> 00:03:56,439
比如说咱们做这个游戏啊

90
00:03:56,439 --> 00:03:57,639
他有这个存档功能

91
00:03:57,639 --> 00:03:58,419
对不对比

92
00:03:58,419 --> 00:03:59,979
如说你做了个rpg游戏

93
00:03:59,979 --> 00:04:01,990
你要把它存档啊

94
00:04:01,990 --> 00:04:04,330
那么你要存档的话就有问题了

95
00:04:04,330 --> 00:04:06,580
你要保存下来的有什么内容

96
00:04:07,219 --> 00:04:09,919
你比如说我玩了一段时间啊

97
00:04:09,919 --> 00:04:12,259
我然后我现在做了一个存档

98
00:04:12,259 --> 00:04:15,050
那存档下来首先肯定有我目前的什么呀

99
00:04:15,050 --> 00:04:22,290
我目前的地图坐标最起码什么人物等级是不是

100
00:04:22,290 --> 00:04:25,439
然后等级那还有一大串的这个属性

101
00:04:25,439 --> 00:04:26,370
什么攻击啊

102
00:04:26,370 --> 00:04:29,589
防御啊啊对不对等等

103
00:04:29,589 --> 00:04:30,670
还有什么呀

104
00:04:30,670 --> 00:04:33,569
当前有什么物品啊

105
00:04:33,569 --> 00:04:36,579
比如说你现在很多游戏很复杂

106
00:04:36,579 --> 00:04:38,860
然后武功都是可以自己学的啊

107
00:04:38,860 --> 00:04:40,779
他不是他不是定死的

108
00:04:40,779 --> 00:04:43,060
所以就是目前有什么武功啊

109
00:04:43,060 --> 00:04:44,680
目前和每个人的好友

110
00:04:44,680 --> 00:04:46,589
度对吧

111
00:04:46,589 --> 00:04:50,439
和每个nbc有好友度等等等等等等啊

112
00:04:50,439 --> 00:04:51,490
可能有宠物的

113
00:04:51,490 --> 00:04:53,019
还有目前有什么宠物

114
00:04:53,019 --> 00:04:53,699
对不对

115
00:04:53,699 --> 00:04:58,740
就他这个东西啊需要保存到本地的内容太多了

116
00:04:58,740 --> 00:05:01,680
那么大家想上节课我们去学了一下存本地

117
00:05:01,680 --> 00:05:02,279
对不对

118
00:05:02,300 --> 00:05:06,199
其实在我们这个里面存本地存的就是一个什么东西

119
00:05:06,199 --> 00:05:07,319
一串字符串

120
00:05:07,339 --> 00:05:08,420
一个键值对

121
00:05:08,420 --> 00:05:10,220
然后这个值就是一串字符串

122
00:05:10,220 --> 00:05:11,209
对不对

123
00:05:11,209 --> 00:05:17,720
那么这时候大家想我们怎样才能诶保存一个字符串

124
00:05:17,720 --> 00:05:20,839
就把这么多内容都给存到本地呢

125
00:05:21,319 --> 00:05:23,000
那么这时候我们就想啊

126
00:05:23,000 --> 00:05:26,730
这时候我们就需要一种通用的数据格式去做了啊

127
00:05:26,730 --> 00:05:28,170
需要一种数据格式

128
00:05:28,170 --> 00:05:32,860
把这些内容能给他a组织起来啊

129
00:05:32,860 --> 00:05:36,250
那么这就能达到了我们保存的一个效果啊

130
00:05:36,250 --> 00:05:38,000
达到我们一个保存的效果

131
00:05:39,199 --> 00:05:40,639
那么我们在这里啊

132
00:05:40,639 --> 00:05:41,860
比如举个例子

133
00:05:43,120 --> 00:05:50,490
那比如说我们在这里有一个类class class person

134
00:05:50,490 --> 00:05:52,529
在这个person里面呢

135
00:05:52,529 --> 00:05:54,870
所有的属性啊

136
00:05:54,870 --> 00:05:57,839
所有的属性我们都是要保存在本地的啊

137
00:05:57,839 --> 00:05:59,399
所有的属性都是要保存在本地的

138
00:05:59,399 --> 00:06:00,839
比如说这个人里面有什么呀

139
00:06:00,839 --> 00:06:06,680
有这个当前的人物的i d有这个名字啊

140
00:06:06,680 --> 00:06:08,420
有人物的姓名啊

141
00:06:08,420 --> 00:06:10,879
还有很多很多属性啊

142
00:06:10,879 --> 00:06:13,160
多的咱们就不说了啊

143
00:06:13,160 --> 00:06:14,240
咱目前就举例子

144
00:06:14,240 --> 00:06:17,220
比如说这样一个类类里面很多属性

145
00:06:17,220 --> 00:06:20,160
然后如果我们现在就想把这个类

146
00:06:20,160 --> 00:06:22,980
把这个类存在本地的话

147
00:06:22,980 --> 00:06:25,660
怎样去存啊

148
00:06:25,660 --> 00:06:29,500
那么这个这种方法在rpg游戏里面很常见的啊

149
00:06:29,500 --> 00:06:30,720
我们就是拿一个类

150
00:06:30,720 --> 00:06:35,100
这个类里面每一个内容都是我们要存到本地的数据啊

151
00:06:35,100 --> 00:06:37,480
就是你做存档要存到本地的数据

152
00:06:37,620 --> 00:06:40,560
那这样的话大家去想

153
00:06:40,560 --> 00:06:42,959
我这里先创建一个person啊

154
00:06:42,959 --> 00:06:44,399
我这里创建一个person

155
00:06:45,079 --> 00:06:54,230
比如说person就等于一个我这里又一个person创建了这样一个对象

156
00:06:54,230 --> 00:06:59,759
然后这个person他的这个比如说i d等于一个十

157
00:07:02,300 --> 00:07:03,500
name啊

158
00:07:03,500 --> 00:07:04,339
就等于一个

159
00:07:04,339 --> 00:07:06,639
比如说你逍遥

160
00:07:08,420 --> 00:07:10,699
那这里有一个person对象了

161
00:07:10,699 --> 00:07:11,180
对不对

162
00:07:11,180 --> 00:07:12,480
这里有个person对象了

163
00:07:12,500 --> 00:07:15,620
我现在要把这个person对象里面的内容存到本地

164
00:07:15,620 --> 00:07:18,800
而且以一个单字符串的方式存到了本地

165
00:07:18,800 --> 00:07:19,540
怎么做

166
00:07:19,540 --> 00:07:22,300
那现在我们做的其实就是一个操作

167
00:07:22,300 --> 00:07:23,540
就是对象

168
00:07:23,980 --> 00:07:26,709
我们要把一个对象转成什么呀

169
00:07:26,709 --> 00:07:29,079
转成一个字符串

170
00:07:29,079 --> 00:07:29,839
对不对

171
00:07:30,079 --> 00:07:33,579
那么转成字符串怎么转啊

172
00:07:33,579 --> 00:07:36,519
这个字符串其实就是我们要的一个格式

173
00:07:36,519 --> 00:07:37,899
那这个格式我们在这里

174
00:07:37,899 --> 00:07:39,160
比如说用jason啊

175
00:07:39,160 --> 00:07:41,680
我们来这里说一下jason的这个格式啊

176
00:07:41,680 --> 00:07:42,480
长什么样

177
00:07:42,500 --> 00:07:45,800
比如说我们要把这个接这个这个这个对象啊

178
00:07:45,800 --> 00:07:48,379
我们要给它组建成json格式

179
00:07:48,459 --> 00:07:49,810
那么它就是这样的

180
00:07:49,810 --> 00:07:53,939
在jason里面大括号代表一个对象

181
00:07:54,980 --> 00:07:58,540
中括号代表一个数组啊

182
00:07:58,540 --> 00:08:00,699
那么就这两种啊

183
00:08:00,699 --> 00:08:02,379
一个是代表一个对象

184
00:08:02,379 --> 00:08:03,639
一个代表一个数组啊

185
00:08:03,639 --> 00:08:04,959
jason是非常简单的

186
00:08:04,959 --> 00:08:07,079
jason是非常简单的这个数据格式

187
00:08:07,079 --> 00:08:11,339
那么在这里如果我要把这个对象把它转成字符串的话

188
00:08:11,339 --> 00:08:12,959
而且转成json格式的话

189
00:08:12,959 --> 00:08:16,180
用jason这种方式去描述这个对象的话

190
00:08:16,180 --> 00:08:17,529
怎样去描述呢

191
00:08:17,529 --> 00:08:18,639
那就应该这样

192
00:08:18,639 --> 00:08:22,120
首先那个大括号代表我们这个person这个对象

193
00:08:22,120 --> 00:08:22,759
对不对

194
00:08:22,779 --> 00:08:26,110
在对象里面有几个内容

195
00:08:26,110 --> 00:08:28,819
首先有个id对不对

196
00:08:29,000 --> 00:08:32,659
简直对的方式把它的属性写出来啊

197
00:08:32,659 --> 00:08:34,399
比如说d是个十

198
00:08:34,399 --> 00:08:35,419
对不对

199
00:08:35,419 --> 00:08:38,440
然后第二个有个name啊逗号

200
00:08:38,440 --> 00:08:39,220
因为它有两个

201
00:08:39,220 --> 00:08:39,820
对不对

202
00:08:39,820 --> 00:08:44,210
然后比如说它叫做李逍遥

203
00:08:44,210 --> 00:08:48,259
那比如说我们再加一个数组的吧啊武功

204
00:08:49,940 --> 00:08:53,779
武功我们就是字符串类型的这样的一个数组啊

205
00:08:53,779 --> 00:08:54,440
那在这里

206
00:08:54,440 --> 00:08:58,960
比如说我们再给一些person点武功

207
00:08:58,960 --> 00:09:04,460
就等于个什么什么什么随便啊

208
00:09:04,460 --> 00:09:05,759
降龙18掌

209
00:09:12,700 --> 00:09:14,320
独孤九剑啊

210
00:09:14,320 --> 00:09:16,179
我们就是随便写两个

211
00:09:16,179 --> 00:09:18,259
那么在这边怎么写

212
00:09:18,779 --> 00:09:20,279
第三个属性是武功

213
00:09:20,279 --> 00:09:20,759
对不对

214
00:09:20,759 --> 00:09:22,659
所以我们就要武功

215
00:09:25,879 --> 00:09:27,980
那么武功对应的注意啊

216
00:09:27,980 --> 00:09:29,320
在这里是个数组

217
00:09:29,340 --> 00:09:31,679
所以我们在这里冒号后面

218
00:09:31,679 --> 00:09:33,360
你就应该直接写一个数组

219
00:09:33,379 --> 00:09:39,080
在这个数组里面直接就去把这个内容再次填上就可以了啊

220
00:09:39,080 --> 00:09:41,299
数组里面就是非限制对的啊

221
00:09:41,299 --> 00:09:42,620
所以这样的话就ok了

222
00:09:42,639 --> 00:09:46,690
那么这就描述了我们上面的这样的一个对象啊

223
00:09:46,690 --> 00:09:51,519
所以实际上我们要做的就是通过程序能把这样的一个对象

224
00:09:51,519 --> 00:09:53,259
转成这样的一个字符串

225
00:09:53,259 --> 00:09:56,200
这样的话我们是不是就能拿到这个字符串了

226
00:09:56,200 --> 00:09:58,059
因为它本身是一个字符串

227
00:09:58,080 --> 00:09:59,639
所以我们就可以怎样了

228
00:09:59,639 --> 00:10:02,120
把这个字符串给它存到本地了

229
00:10:02,159 --> 00:10:04,200
是不是就可以做存档了啊

230
00:10:04,200 --> 00:10:06,240
我们就可以把这个对象存到本地了

231
00:10:06,259 --> 00:10:08,659
那么如果第二次进来

232
00:10:08,659 --> 00:10:09,889
我再读档的话

233
00:10:09,889 --> 00:10:17,259
我们还要通过程序可以把这一串文本怎样再返回成这样的一个对象啊

234
00:10:17,259 --> 00:10:18,919
这就是我们要的一个效果

235
00:10:19,820 --> 00:10:21,860
那么在这里啊

236
00:10:21,860 --> 00:10:24,460
在这里我们这个对象已经有了啊

237
00:10:24,460 --> 00:10:25,360
对象已经有了

238
00:10:25,360 --> 00:10:27,340
我现在要把它转成这种形式

239
00:10:27,340 --> 00:10:28,240
怎么去转啊

240
00:10:28,240 --> 00:10:29,759
我们现在是知道的

241
00:10:29,779 --> 00:10:31,639
它这个格式应该是这个样子

242
00:10:31,639 --> 00:10:32,000
对不对

243
00:10:32,000 --> 00:10:33,080
应该是这个样子

244
00:10:33,080 --> 00:10:34,700
但是我们现在还没有去转

245
00:10:34,700 --> 00:10:35,299
怎样去转

246
00:10:35,299 --> 00:10:36,519
非常简单啊

247
00:10:37,519 --> 00:10:40,669
我们这里先说把对象转成这个字符串

248
00:10:40,669 --> 00:10:41,860
转成jason

249
00:10:44,019 --> 00:10:46,539
那么在这里非常非常简单

250
00:10:46,539 --> 00:10:49,600
我们直接let jason

251
00:10:49,600 --> 00:10:51,360
就等于一个

252
00:10:55,860 --> 00:10:57,120
那么大家注意啊

253
00:10:57,120 --> 00:11:02,019
在这里有一个接口叫做大写的js啊

254
00:11:02,019 --> 00:11:03,549
叫做大写的这个jason

255
00:11:03,549 --> 00:11:06,460
我们直接把它写出来啊

256
00:11:06,460 --> 00:11:08,529
把它写出来点

257
00:11:08,529 --> 00:11:09,940
它有一个方法

258
00:11:09,940 --> 00:11:15,360
这个方法呢里面填写我们的对象啊

259
00:11:15,360 --> 00:11:16,820
填写我们的对象

260
00:11:18,700 --> 00:11:23,259
这样的话就可以把这个对象转成json字符串啊

261
00:11:23,259 --> 00:11:26,820
返回的这个js大家可以看他自己就推断为一个字符串

262
00:11:26,820 --> 00:11:30,179
那我们在这里直接把它打印出来

263
00:11:30,299 --> 00:11:34,019
看一下是不是和我们想象中的这个字符串是一样的

264
00:11:42,639 --> 00:11:45,019
我们在这里运行一下

265
00:11:49,019 --> 00:11:51,620
那么大家可以看打印出来的内容

266
00:11:51,740 --> 00:11:55,370
这一串内容是不是和我们刚才写的完全一样

267
00:11:55,370 --> 00:11:56,629
是不是完全一样

268
00:11:56,629 --> 00:11:58,159
没有任何问题啊

269
00:11:58,159 --> 00:12:01,279
那这一串就是我们的杰森啊

270
00:12:01,279 --> 00:12:02,840
这船就是我们的杰森

271
00:12:02,840 --> 00:12:04,789
这就是通过这一行

272
00:12:04,789 --> 00:12:07,399
你看填写了一个对象啊

273
00:12:07,399 --> 00:12:12,629
把对象就转成了它对应的这种json格式的字符串啊

274
00:12:12,629 --> 00:12:14,879
那么这时候我们就拿到字符串了

275
00:12:14,879 --> 00:12:17,360
那比如说我现在就可以干嘛了

276
00:12:17,460 --> 00:12:19,200
把它存本地了

277
00:12:19,200 --> 00:12:19,500
对不对

278
00:12:19,500 --> 00:12:21,100
你就可以做什么操作了

279
00:12:21,399 --> 00:12:26,389
你可以cc.system点存本地

280
00:12:26,389 --> 00:12:27,049
对不对

281
00:12:27,049 --> 00:12:29,149
点上sitem

282
00:12:29,149 --> 00:12:34,500
你就可以这里比如说你k就叫啊存档一

283
00:12:34,500 --> 00:12:34,980
对不对

284
00:12:34,980 --> 00:12:35,700
你就可以叫

285
00:12:35,700 --> 00:12:37,440
比如说c51 存档一

286
00:12:37,440 --> 00:12:38,820
在存档一的地方

287
00:12:38,820 --> 00:12:40,720
我们去把这个jason存下来

288
00:12:40,720 --> 00:12:42,669
这样的话我们就做了一个存档

289
00:12:42,669 --> 00:12:43,389
存档一

290
00:12:43,389 --> 00:12:46,120
如果比如说用户放到存档的第二个位置

291
00:12:46,120 --> 00:12:47,649
你又是存档二

292
00:12:47,649 --> 00:12:48,320
对不对

293
00:12:48,340 --> 00:12:50,259
这其实就是做了存档了

294
00:12:50,259 --> 00:12:52,000
那么存档做完

295
00:12:52,000 --> 00:12:56,240
比如说如果如果啊我们第二次进来了

296
00:12:56,240 --> 00:12:59,570
想从这个存档继续继续去玩儿啊

297
00:12:59,570 --> 00:13:01,100
那我们就要读取存档

298
00:13:01,100 --> 00:13:01,779
对不对

299
00:13:01,779 --> 00:13:03,700
那么当我们读取存档以后

300
00:13:03,700 --> 00:13:08,110
实际上最后得到的结果就是读出来的就是这样的一个字符串

301
00:13:08,110 --> 00:13:10,480
那接下来我们要求的就是干嘛了

302
00:13:10,500 --> 00:13:13,169
把字符串转回成我们的对象

303
00:13:13,169 --> 00:13:17,039
这样的话我们就可以方便地拿到它里面的内容了

304
00:13:17,039 --> 00:13:17,779
对不对

305
00:13:17,940 --> 00:13:25,379
那怎样把一个字符串给它返回成一个对象呢啊那这个东西注意啊

306
00:13:25,379 --> 00:13:26,279
就要这样去做了

307
00:13:26,279 --> 00:13:27,480
比如说that person

308
00:13:27,480 --> 00:13:30,080
二person

309
00:13:30,080 --> 00:13:32,720
我专门这个名字还跟他取的不一样啊

310
00:13:33,059 --> 00:13:35,759
那么这个对象我们就让它等于一个

311
00:13:35,759 --> 00:13:41,399
我们要这样这样去写object点啊

312
00:13:41,399 --> 00:13:43,379
sl就是给一个对象去赋值

313
00:13:43,379 --> 00:13:45,029
给哪个对象去赋值

314
00:13:45,029 --> 00:13:47,279
创建一个新的对象

315
00:13:48,299 --> 00:13:49,259
这里注意啊

316
00:13:49,259 --> 00:13:50,399
有两个参数

317
00:13:50,399 --> 00:13:53,789
第一个参数我们要给他创建一个新的对象

318
00:13:53,789 --> 00:13:55,769
创建哪个新的对象

319
00:13:55,769 --> 00:13:59,700
那么就是你去序列化的哪个对象啊

320
00:13:59,700 --> 00:14:02,759
序列化就是把对象转成jason啊

321
00:14:02,759 --> 00:14:04,639
这个过程叫它序列化

322
00:14:06,000 --> 00:14:08,340
那么当然相反就要反序列化了

323
00:14:08,340 --> 00:14:09,720
就是在你序列化的时候

324
00:14:09,720 --> 00:14:11,639
你序列化的那个对象是什么类型

325
00:14:11,639 --> 00:14:13,080
在这里是person类型

326
00:14:13,080 --> 00:14:13,720
对不对

327
00:14:13,740 --> 00:14:14,460
是person

328
00:14:14,460 --> 00:14:17,970
所以在这里我们第一个就要给他一个person对象啊

329
00:14:17,970 --> 00:14:21,480
意思就是我们最终得到的就是这样的一个person对象

330
00:14:22,360 --> 00:14:27,120
那么第二个就要去放上什么呀

331
00:14:27,740 --> 00:14:29,840
我们要放上这样的一个东西

332
00:14:29,840 --> 00:14:32,840
首先还是杰森jason里面我们用到第二个方法

333
00:14:32,840 --> 00:14:34,460
你看jason一共就三个东西

334
00:14:34,460 --> 00:14:35,419
对不对啊

335
00:14:35,419 --> 00:14:37,019
除了这个我们就用它

336
00:14:37,840 --> 00:14:42,259
在这里我们就去填上我们的jason字符串啊

337
00:14:42,259 --> 00:14:44,299
这里字符串我直接就用这个啊

338
00:14:44,299 --> 00:14:45,259
它就是这个字符串

339
00:14:45,259 --> 00:14:45,830
对不对

340
00:14:45,830 --> 00:14:47,559
把字符串放到这里

341
00:14:47,580 --> 00:14:49,799
然后通过这个方法啊

342
00:14:49,799 --> 00:14:52,230
准确是通过这个大方法

343
00:14:52,230 --> 00:14:57,960
就把这个jason里面的所有的内容赋值给了这个新的对象了啊

344
00:14:57,960 --> 00:15:02,850
就把这个jason文本里面所对应的东西赋值给了新创建的对象

345
00:15:02,850 --> 00:15:05,299
这样的话拿到的最终的这个对象

346
00:15:05,299 --> 00:15:08,929
其实里面就包含了jason里面所有的这个数据了

347
00:15:08,929 --> 00:15:10,580
我们在这里可以再输出一下

348
00:15:10,580 --> 00:15:11,279
看一下

349
00:15:14,299 --> 00:15:15,379
点给bug

350
00:15:15,379 --> 00:15:15,799
在这里

351
00:15:15,799 --> 00:15:20,759
比如说我们可以输出一下person 2点

352
00:15:20,759 --> 00:15:23,039
你看我们比如说输出内部

353
00:15:23,039 --> 00:15:24,120
我们试一个就知道了

354
00:15:24,120 --> 00:15:27,559
看看能不能把这个名字拿出来啊

355
00:15:27,559 --> 00:15:30,200
能把这个李逍遥这个名字拿出来吗

356
00:15:30,700 --> 00:15:32,740
我们在这里运行一下

357
00:15:36,080 --> 00:15:40,159
哎大家可以看一下是不是拿到了李逍遥啊

358
00:15:40,159 --> 00:15:43,279
拿到了那就证明这个是没有问题了啊

359
00:15:43,279 --> 00:15:44,480
这个是没有问题了

360
00:15:44,480 --> 00:15:46,220
所以这里注意啊

361
00:15:46,440 --> 00:15:49,860
嗯首先啊我们现在可以大概回顾一下

362
00:15:49,860 --> 00:15:55,120
就是说我们常常啊要把这个数据啊

363
00:15:55,120 --> 00:15:58,000
很多很复杂的数据存到本地

364
00:15:58,000 --> 00:16:02,080
或者说把很多很复杂的数据发送给别的客户端

365
00:16:02,080 --> 00:16:03,940
做客户端之间的网络交流

366
00:16:03,940 --> 00:16:04,679
对不对

367
00:16:04,679 --> 00:16:09,299
那么这时候我们就要把这些数据给它存到一个文本里

368
00:16:09,320 --> 00:16:12,059
那么存到一个文本里的时候

369
00:16:12,179 --> 00:16:13,860
因为数据量太多

370
00:16:13,860 --> 00:16:15,360
那就会导致这个很乱

371
00:16:15,360 --> 00:16:15,720
对不对

372
00:16:15,720 --> 00:16:17,730
你也不知道怎样才能存到一个文本里

373
00:16:17,730 --> 00:16:20,899
这时候我们就需要一种统一的格式啊

374
00:16:20,899 --> 00:16:23,210
用统一的格式去保存

375
00:16:23,210 --> 00:16:25,580
那么统一的格式有哪些啊

376
00:16:25,580 --> 00:16:28,039
那jason是其中一种常用的

377
00:16:28,039 --> 00:16:29,980
比如说还有xl啊

378
00:16:29,980 --> 00:16:31,960
其实这是最常用的杰森啊

379
00:16:31,960 --> 00:16:33,419
最常的就是这个杰森

380
00:16:33,440 --> 00:16:36,440
那么我们如果选择了这种格式

381
00:16:36,440 --> 00:16:39,440
其实我们现在做的就是比如说拿到一个对象

382
00:16:39,440 --> 00:16:43,580
我们就可以通过这个对象啊

383
00:16:43,580 --> 00:16:48,590
通过这个对象把它转成使用json格式的字符串啊

384
00:16:48,590 --> 00:16:53,919
方法就是这啊对象放到这里就会把这个对象转成了json格式的字符串啊

385
00:16:53,919 --> 00:16:55,379
你就可以拿到字符串了

386
00:16:55,399 --> 00:16:56,720
那么拿到字符串以后

387
00:16:56,720 --> 00:16:57,679
你就可以干嘛了

388
00:16:57,679 --> 00:17:00,500
存本地或者说发送网络都可以

389
00:17:00,500 --> 00:17:02,179
这里就可以做你的操作了

390
00:17:02,200 --> 00:17:05,380
那么当然如果比如说我现在要读档

391
00:17:05,380 --> 00:17:08,079
或者是我或者说我是另外一个客户端

392
00:17:08,079 --> 00:17:09,598
我接收到的数据

393
00:17:09,618 --> 00:17:11,419
那这时候我要干嘛

394
00:17:11,419 --> 00:17:12,679
我拿到这一串文本了

395
00:17:12,679 --> 00:17:14,959
我肯定要把它转回成我要的对象

396
00:17:14,959 --> 00:17:15,598
对不对

397
00:17:15,598 --> 00:17:17,219
那我这时候就要干嘛了

398
00:17:17,219 --> 00:17:22,739
我这时候就要去做这样的一个把jason字符串转回成这个对象的

399
00:17:22,739 --> 00:17:25,539
这样的一个反序列化的这样的一个操作

400
00:17:25,539 --> 00:17:28,779
那么操作啊就是这一行啊

401
00:17:28,779 --> 00:17:29,920
那么这一行结束以后

402
00:17:29,920 --> 00:17:31,450
你就会拿到个新的对象

403
00:17:31,450 --> 00:17:36,700
但是这个对象里面所有的数值都和我们js里面的相对应了

404
00:17:37,039 --> 00:17:41,359
那么其实呃大概就是这样的一个情况啊

405
00:17:41,359 --> 00:17:42,740
当然这里我们说一下的

406
00:17:42,740 --> 00:17:47,819
就是比如说我们现在嗯通过读文本

407
00:17:47,819 --> 00:17:51,029
或者是或者说是从网络获取了一个jason

408
00:17:51,029 --> 00:17:52,960
那么这时候大家注意啊

409
00:17:53,279 --> 00:17:56,099
我们知道这个jason要解析解析的话

410
00:17:56,099 --> 00:17:58,079
是不是必须解析成一个对象

411
00:17:58,099 --> 00:18:00,980
那所以这时候如果我们从网络获取到了一个jason

412
00:18:00,980 --> 00:18:02,960
比如说你看我们获取了这个jason

413
00:18:02,960 --> 00:18:04,480
想解析它怎么办

414
00:18:04,500 --> 00:18:08,220
你就必须在这里诶自己写一个类

415
00:18:08,220 --> 00:18:10,940
这个类要干嘛和它对应啊

416
00:18:10,940 --> 00:18:11,900
比如说你看啊

417
00:18:11,900 --> 00:18:14,660
比如说我这诶我一看他

418
00:18:14,660 --> 00:18:17,920
我说我要解析这个jason怎么样解析啊

419
00:18:17,920 --> 00:18:20,650
因为这个jason啊不是我去保存的

420
00:18:20,650 --> 00:18:22,119
是我从网上获取的

421
00:18:22,119 --> 00:18:24,619
或者说我从别的文文本里面读取的

422
00:18:24,619 --> 00:18:25,819
然后我想解析它

423
00:18:25,819 --> 00:18:30,140
你就必须在这里一一对应的去把你要的内容写上啊

424
00:18:30,140 --> 00:18:33,279
比如说这个i d i d啊

425
00:18:33,279 --> 00:18:34,809
我知道了这个i d

426
00:18:34,809 --> 00:18:36,819
然后还有个name啊

427
00:18:36,819 --> 00:18:39,799
我知道了这有个name是不是啊

428
00:18:39,799 --> 00:18:41,599
然后还有个什么武功啊

429
00:18:41,599 --> 00:18:43,559
所以你就要把武功写上

430
00:18:43,579 --> 00:18:46,069
这时候你发现a对应完了

431
00:18:46,069 --> 00:18:48,799
你就可以按照我们解析方法

432
00:18:48,799 --> 00:18:52,559
在这里就用就用person 2这样的一个对象啊

433
00:18:52,559 --> 00:18:54,059
去解析也是没有问题的

434
00:18:54,059 --> 00:18:57,829
这时候他依然是能解能正常解析的啊

435
00:18:57,829 --> 00:19:02,980
他要求的只是你这个类的格式和你这个杰森相对应就行了啊

436
00:19:02,980 --> 00:19:03,759
相对应就行

437
00:19:03,759 --> 00:19:07,420
只要对应你就可以把这个jason进行一个解析

438
00:19:07,420 --> 00:19:10,099
把它解析回对象

439
00:19:10,859 --> 00:19:14,450
那么大概就是这么一回事啊

440
00:19:14,450 --> 00:19:16,039
大概就是这么一回事啊

441
00:19:16,039 --> 00:19:22,140
这个东西嗯等大家去用上那么两次以后啊

442
00:19:22,140 --> 00:19:25,849
用上两次以后其实就搞清楚了啊

443
00:19:25,849 --> 00:19:27,440
这个是最常用的

444
00:19:27,440 --> 00:19:29,180
就算你不想熟悉它

445
00:19:29,200 --> 00:19:31,660
如果你以后去做了这个工作啊

446
00:19:31,660 --> 00:19:33,220
比如说cos creator啊

447
00:19:33,220 --> 00:19:34,660
你的工作就是来做他的

448
00:19:34,660 --> 00:19:36,279
或甚至你的工作

449
00:19:36,279 --> 00:19:38,480
只要是做这个编码的啊

450
00:19:38,480 --> 00:19:39,200
用不了多久

451
00:19:39,200 --> 00:19:42,589
你对这个jason就就变得非常非常熟了哈

452
00:19:42,589 --> 00:19:43,670
一个是简单

453
00:19:43,670 --> 00:19:46,039
第二个就是啊太常用了啊

454
00:19:46,039 --> 00:19:48,740
所以这个东西嗯

455
00:19:48,740 --> 00:19:55,519
早晚你应该是最熟的一个东西了哈哈也是最熟的一个数据格式啊

456
00:19:55,660 --> 00:19:56,319
行啊

457
00:19:56,319 --> 00:19:58,900
那我们这节课就先说这么多啊

458
00:19:58,900 --> 00:20:00,500
我们下节课继续

459
00:20:00,660 --> 00:20:03,359
下节课我们来说一下嗯

460
00:20:03,359 --> 00:20:05,920
其他的这种数据格式啊

461
00:20:09,660 --> 00:20:10,200
哦


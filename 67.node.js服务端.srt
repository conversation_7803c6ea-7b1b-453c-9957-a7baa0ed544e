1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,160 --> 00:00:12,019
ok这节课我们继续来说一下

3
00:00:12,019 --> 00:00:15,380
首先我们回顾一下之前讲的这个内容

4
00:00:15,380 --> 00:00:17,480
之前我们讲过两种网络连接

5
00:00:17,500 --> 00:00:19,750
一种叫套接字啊

6
00:00:19,750 --> 00:00:22,140
还有一种就是我们的网络请求

7
00:00:22,160 --> 00:00:23,120
那大家去想

8
00:00:23,120 --> 00:00:24,679
如果我们做一个网游的话

9
00:00:24,679 --> 00:00:28,460
那基本上用的用的应该是这个套接字比较多

10
00:00:28,460 --> 00:00:29,179
对不对

11
00:00:29,440 --> 00:00:32,079
那么网络请求一般用在哪里啊

12
00:00:32,079 --> 00:00:33,640
你要分清楚网络请求

13
00:00:33,640 --> 00:00:37,179
比如说我们可以用在比如说游戏进去有公告

14
00:00:37,539 --> 00:00:39,520
公告就是一大段的文本

15
00:00:39,520 --> 00:00:40,210
对不对

16
00:00:40,210 --> 00:00:41,979
那所以像这种公告的获取

17
00:00:41,979 --> 00:00:45,140
我们就可以用普通的网络请求的方式发个请求

18
00:00:45,140 --> 00:00:47,399
然后服务器给我们返回一个公告

19
00:00:47,640 --> 00:00:49,740
那或者是一些游戏

20
00:00:49,740 --> 00:00:52,619
它根本没有这种联网功能

21
00:00:52,619 --> 00:00:56,380
但是它有类似于排行榜啊这种东西

22
00:00:56,619 --> 00:00:59,859
那么这种东西你看似是联网的

23
00:00:59,859 --> 00:01:02,259
只能说这种游戏是有网络的

24
00:01:02,259 --> 00:01:03,899
但它并不是网络游戏

25
00:01:03,899 --> 00:01:07,469
那这时候它的排行榜这些数据从哪儿啊

26
00:01:07,469 --> 00:01:08,780
从哪更新

27
00:01:08,799 --> 00:01:10,719
也是从服务器

28
00:01:10,719 --> 00:01:14,019
那这时候我们用的肯定就是普通的网络请求

29
00:01:14,019 --> 00:01:14,780
对不对

30
00:01:15,000 --> 00:01:18,719
那么套接字一般就是用来干嘛的

31
00:01:18,719 --> 00:01:21,719
用来在客户端之间传消息的啊

32
00:01:21,719 --> 00:01:24,760
比如说我给其他客户端发个消息

33
00:01:24,780 --> 00:01:26,099
然后告诉他们

34
00:01:26,099 --> 00:01:27,689
我现在用了一个绝招

35
00:01:27,689 --> 00:01:31,859
然后所有的客户端同时让这个这个人啊

36
00:01:31,859 --> 00:01:34,159
这个客户端操作的那个人哎

37
00:01:34,159 --> 00:01:36,980
播放一个放放这个绝招的动作

38
00:01:36,980 --> 00:01:37,670
对不对

39
00:01:37,670 --> 00:01:41,709
就比如说啊这里哎我说我要放一个绝招

40
00:01:41,709 --> 00:01:42,920
放一个大招

41
00:01:42,920 --> 00:01:44,239
我告诉服务端

42
00:01:44,239 --> 00:01:45,620
服务端通知所有的客户端

43
00:01:45,620 --> 00:01:46,640
它要放一个大招

44
00:01:46,640 --> 00:01:49,659
那么所有的客户端上看到的画面

45
00:01:49,939 --> 00:01:53,959
这个人控制的这个客户端控制的那个人都会放一个大招

46
00:01:53,959 --> 00:01:55,040
对不对啊

47
00:01:55,040 --> 00:01:58,140
所以实际上像这种实时的呃信息

48
00:01:58,159 --> 00:02:01,519
那么我们一般都是用这种套接字来做的啊

49
00:02:01,519 --> 00:02:06,230
只有那种就是我们要获取一些呃

50
00:02:06,230 --> 00:02:08,180
获取一些数据信息

51
00:02:08,180 --> 00:02:12,340
而且这种数据信息又不是说经常获取的

52
00:02:12,340 --> 00:02:14,319
比如说像这种游戏公告

53
00:02:14,319 --> 00:02:16,780
就我们打开游戏的时候看见一次

54
00:02:16,780 --> 00:02:17,349
对不对

55
00:02:17,349 --> 00:02:18,099
这个公告

56
00:02:18,099 --> 00:02:22,610
所以它完全可以用这种网络请求来来获取

57
00:02:22,610 --> 00:02:24,539
然后比如说我们那个排行榜

58
00:02:24,560 --> 00:02:26,000
排行榜的话

59
00:02:26,000 --> 00:02:28,460
排行榜数据也是我们这个排行榜

60
00:02:28,460 --> 00:02:29,879
不是说一直看

61
00:02:29,879 --> 00:02:32,099
是不是我们偶尔点开一次排行

62
00:02:32,099 --> 00:02:33,030
看到排行榜

63
00:02:33,030 --> 00:02:35,479
那么所以看排行榜的时候

64
00:02:35,879 --> 00:02:40,900
这个排行榜的数据我们也可以给他发个网络请求去获得啊

65
00:02:41,360 --> 00:02:42,500
嗯

66
00:02:44,080 --> 00:02:46,780
其实还有一个方法更好区分啊

67
00:02:46,780 --> 00:02:47,439
我也说一下

68
00:02:47,439 --> 00:02:52,520
就是有些数据我是要给其他客户端去共享的啊

69
00:02:52,520 --> 00:02:54,800
我是要给其他客户端共享的

70
00:02:54,800 --> 00:02:57,620
那么我们就用这种套接字啊

71
00:02:57,620 --> 00:02:58,919
这种方式去做

72
00:02:59,000 --> 00:03:01,759
有些数据我们是要从服务器获取的

73
00:03:01,759 --> 00:03:03,719
比如说大家想像排行榜

74
00:03:03,900 --> 00:03:07,650
然后像公告这些数据其实是放在服务端的

75
00:03:07,650 --> 00:03:09,120
和其他客户端没关系

76
00:03:09,120 --> 00:03:13,139
也就是说这仅仅是我这个服务端和我这个客户端的一个交流

77
00:03:13,159 --> 00:03:18,889
那么这种情况下我们就可以用http去获取啊

78
00:03:18,889 --> 00:03:22,819
所以说是这个嗯怎么说啊

79
00:03:22,819 --> 00:03:24,680
这两种各有各的用处啊

80
00:03:24,680 --> 00:03:25,759
各有各的用处

81
00:03:27,379 --> 00:03:28,520
那么ok啊

82
00:03:28,520 --> 00:03:30,740
我们就先继续往后去说啊

83
00:03:30,740 --> 00:03:33,439
实际上等我们真正的这个做了以后啊

84
00:03:33,439 --> 00:03:35,870
就是做出这个东西以后嗯

85
00:03:35,870 --> 00:03:38,460
或者说等大家以后上班啊

86
00:03:38,460 --> 00:03:40,139
只要你接触到这个网络游戏

87
00:03:40,139 --> 00:03:42,539
等你做上这样一两个项目以后啊

88
00:03:42,539 --> 00:03:45,439
对这些问题就简直就不在话下了啊

89
00:03:45,439 --> 00:03:48,080
就会分得非常清楚了啊

90
00:03:48,560 --> 00:03:49,909
那在这里啊

91
00:03:49,909 --> 00:03:51,169
咱们之前说了

92
00:03:51,169 --> 00:03:54,840
h5 这边的套接字叫做web socket

93
00:03:57,139 --> 00:04:01,789
但是我们知道h t m l5 这个东西是近几年才有的

94
00:04:01,789 --> 00:04:03,169
之前是没有的

95
00:04:03,169 --> 00:04:05,800
所以它支持web socket

96
00:04:05,800 --> 00:04:10,400
并不代表所有的浏览器都能正常的去支持web socket

97
00:04:10,979 --> 00:04:12,479
这样的话就有问题了

98
00:04:12,479 --> 00:04:17,189
如果我们是基于web socket这个技术去编写的游戏啊

99
00:04:17,189 --> 00:04:20,399
那么我们想通过wifi通信

100
00:04:20,399 --> 00:04:22,199
有的浏览器打开可以通行

101
00:04:22,199 --> 00:04:25,459
有的浏览器我们发现打开怎样了就通信不了

102
00:04:25,579 --> 00:04:29,269
也就是说不同的浏览器对于h5 的支持程度不一样啊

103
00:04:29,269 --> 00:04:31,819
对这个web socket支持的程度也不一样

104
00:04:31,819 --> 00:04:32,959
那怎么办

105
00:04:33,240 --> 00:04:37,259
那么有一个框架叫做socket.i

106
00:04:38,060 --> 00:04:42,339
他呢就是对web soft又算是进行了一次封装

107
00:04:42,360 --> 00:04:47,100
也就是说它会判断当前的浏览器支持什么样的方式

108
00:04:47,100 --> 00:04:48,750
它就使用什么样的方式

109
00:04:48,750 --> 00:04:51,439
那这样的话对于我们而言就是非常好的

110
00:04:51,459 --> 00:04:53,740
而且它用起来也更加的简单了

111
00:04:53,740 --> 00:04:57,709
所以在这里我们就使用这个socket点啊

112
00:04:57,709 --> 00:05:02,209
这个框架来进行套接字的一个通信啊

113
00:05:02,209 --> 00:05:07,100
那么他的话也是基于我们的这个

114
00:05:09,699 --> 00:05:11,589
诶我这边没了

115
00:05:11,589 --> 00:05:17,800
也是基于我们的node g s啊这样的一个环境的啊

116
00:05:17,800 --> 00:05:21,819
也就是说它实际上也是javascript的javascript的这个东西

117
00:05:21,819 --> 00:05:25,019
soft点服务端也是gs的

118
00:05:25,019 --> 00:05:28,620
那所以在这边我们上节课已经把环境布置好了

119
00:05:28,620 --> 00:05:29,220
对不对

120
00:05:29,220 --> 00:05:30,569
环境布置好了

121
00:05:30,569 --> 00:05:34,300
我们在这边node js啊

122
00:05:34,300 --> 00:05:35,620
杠v已经能出版本了

123
00:05:35,620 --> 00:05:39,519
就证明我们的电脑现在是可以运行这个javascript了啊

124
00:05:39,519 --> 00:05:41,959
我们有这个环境了

125
00:05:41,959 --> 00:05:44,990
那么但是啊还没完

126
00:05:44,990 --> 00:05:47,750
我们要给它去下载插件

127
00:05:47,750 --> 00:05:49,220
下载什么插件

128
00:05:49,220 --> 00:05:51,379
就是这个soft.io插件

129
00:05:51,379 --> 00:05:52,790
下载完这个插件

130
00:05:52,790 --> 00:05:58,060
我们的这个服务器才支持这个soft的dio通信啊

131
00:05:58,060 --> 00:05:59,879
才支持这个框架通信

132
00:05:59,920 --> 00:06:01,540
怎样去下载嗯

133
00:06:01,540 --> 00:06:02,709
怎样去下载呢

134
00:06:02,709 --> 00:06:04,420
首先我们要下载两个内容

135
00:06:04,420 --> 00:06:05,800
我们一个一个去下载

136
00:06:05,800 --> 00:06:08,040
第一个约束n pm

137
00:06:08,060 --> 00:06:13,600
n pm是一个java script的一个插件的一个管理工具啊

138
00:06:13,600 --> 00:06:16,480
我们的像node js就是我们装的这个东西

139
00:06:16,480 --> 00:06:19,860
它默认就是用这个np m去管理的啊

140
00:06:19,860 --> 00:06:21,269
所以你只要np m

141
00:06:21,269 --> 00:06:22,439
你后面的命令

142
00:06:22,439 --> 00:06:27,480
也就是说实际上全是对这个note gs里面的插件去进行操作的

143
00:06:27,600 --> 00:06:34,680
我们想安装直接就是n pm空格安装杠杠save命令

144
00:06:34,680 --> 00:06:38,699
你照着书第一个e x p r e s s

145
00:06:38,699 --> 00:06:41,189
这是第一个我们要安装的内容

146
00:06:41,189 --> 00:06:43,019
回车以后大家注意啊

147
00:06:43,100 --> 00:06:44,660
他就会有这些东西啊

148
00:06:44,660 --> 00:06:46,699
我这边我这边他说有错误

149
00:06:46,699 --> 00:06:48,740
是因为我已经装完了啊

150
00:06:48,740 --> 00:06:50,459
第二个要装的

151
00:06:50,920 --> 00:06:52,420
只把名字改下就行

152
00:06:52,420 --> 00:06:56,199
就是我们的socket.lo主要用的是它

153
00:06:57,019 --> 00:06:59,000
但是它是基于上面这个东西的

154
00:06:59,000 --> 00:07:00,259
所以我们也要装一下

155
00:07:03,800 --> 00:07:05,360
哎我这边也是装过了

156
00:07:05,360 --> 00:07:06,019
所以也报错了

157
00:07:06,019 --> 00:07:08,959
但是实际上大家可以看到刚才走进度啊

158
00:07:08,959 --> 00:07:10,339
这就是去安装了

159
00:07:10,360 --> 00:07:13,120
那么当把这两个命令都执行完以后

160
00:07:13,120 --> 00:07:15,000
我们的这个环境啊

161
00:07:15,000 --> 00:07:22,120
就是node js这个环境就包含了就支持了这个soft点这个编程了啊

162
00:07:22,120 --> 00:07:24,300
支持这个soft.lo的通信

163
00:07:24,319 --> 00:07:26,180
接下来我们就可以去写代码了

164
00:07:26,180 --> 00:07:27,699
我们可以把它缩小

165
00:07:29,319 --> 00:07:33,519
那么我们现在就开始编写服务端的这个程序了

166
00:07:34,439 --> 00:07:39,180
呃也就是说我们现在既要在我们既要把我们的电脑当做服务端啊

167
00:07:39,180 --> 00:07:41,839
也要在上面去编写客户端啊

168
00:07:41,860 --> 00:07:47,139
首先我们打开我们的这个vs code啊

169
00:07:47,139 --> 00:07:48,189
打开vs code

170
00:07:48,189 --> 00:07:50,620
然后在这里新建一个文件啊

171
00:07:50,620 --> 00:07:52,360
我这个已经是新建一个文件

172
00:07:52,360 --> 00:07:55,139
把这个文件呢放到桌面上

173
00:07:55,139 --> 00:07:57,120
给它取个名字啊

174
00:07:57,120 --> 00:08:00,470
比如说叫my server啊

175
00:08:00,470 --> 00:08:04,639
保存类型就是javascript

176
00:08:04,639 --> 00:08:06,379
服务端运行的是js脚本

177
00:08:06,379 --> 00:08:09,160
所以我们要找这个javascript在这里

178
00:08:10,399 --> 00:08:12,699
然后保存点一下保存

179
00:08:15,540 --> 00:08:16,379
保存完以后

180
00:08:16,379 --> 00:08:19,560
我们就可以在这儿去编写js的这个脚本了

181
00:08:19,560 --> 00:08:20,279
对不对

182
00:08:20,279 --> 00:08:23,399
那么首先啊这里有三行代码啊

183
00:08:23,399 --> 00:08:24,120
是必须写的

184
00:08:24,120 --> 00:08:25,500
是固定的写死的

185
00:08:25,500 --> 00:08:28,339
大家跟着我写一下就可以了啊

186
00:08:34,798 --> 00:08:38,639
实际上java script它的整个语法就相当于

187
00:08:38,639 --> 00:08:44,389
你把它想成没有这个面向对象的t s就行了哈哈

188
00:08:44,389 --> 00:08:47,919
因为咱们之前说过t s就是这个嗯

189
00:08:47,919 --> 00:08:50,980
type script就是这个java script的一个扩展

190
00:08:50,980 --> 00:08:53,019
它就是支持了面向对象的最主要的

191
00:08:53,019 --> 00:08:53,779
对不对

192
00:08:55,620 --> 00:08:59,200
点server app

193
00:09:03,399 --> 00:09:06,129
这块

194
00:09:06,129 --> 00:09:11,120
还c d p

195
00:09:11,399 --> 00:09:12,840
那么这是固定写法

196
00:09:12,840 --> 00:09:14,610
固定写法会得到三个变量

197
00:09:14,610 --> 00:09:16,919
这三个变量其中有两个我们会用

198
00:09:16,919 --> 00:09:18,820
一个是l一个是http

199
00:09:18,840 --> 00:09:21,059
他们两个变量是有什么用啊

200
00:09:21,059 --> 00:09:22,559
得到的这两个变量有什么用

201
00:09:22,559 --> 00:09:23,759
我们这里来说一下啊

202
00:09:23,759 --> 00:09:24,840
这三行固定写法

203
00:09:24,840 --> 00:09:26,279
大家跟着我写上就行

204
00:09:26,440 --> 00:09:28,539
那么首先我在这里再说一下

205
00:09:28,539 --> 00:09:29,419
比如说

206
00:09:31,960 --> 00:09:34,120
还是回到我们之前的这个话题上

207
00:09:34,120 --> 00:09:35,679
比如说这个是服务端

208
00:09:37,480 --> 00:09:39,399
我有很多客户端

209
00:09:39,399 --> 00:09:40,960
我我想去连到服务端

210
00:09:40,960 --> 00:09:41,379
对不对

211
00:09:41,379 --> 00:09:43,419
我有很多客户端想连服务端

212
00:09:44,039 --> 00:09:45,539
那么首先我连它

213
00:09:45,539 --> 00:09:47,399
我就一定要知道服务端的ip地址

214
00:09:47,399 --> 00:09:48,539
这是我第一个要知道的

215
00:09:48,539 --> 00:09:49,799
我要知道他的ip地址

216
00:09:49,799 --> 00:09:52,120
第二个我要知道连接的端口

217
00:09:52,379 --> 00:09:53,799
比如说3000

218
00:09:56,200 --> 00:09:58,120
如果我要连接呃

219
00:09:58,120 --> 00:10:00,549
我知道了我要连的ip服务端的ip

220
00:10:00,549 --> 00:10:01,720
知道了端口

221
00:10:01,720 --> 00:10:02,440
比如说3000

222
00:10:02,440 --> 00:10:02,980
我也知道了

223
00:10:02,980 --> 00:10:05,000
我就能和他去相连

224
00:10:05,000 --> 00:10:06,259
那么连连完以后

225
00:10:06,259 --> 00:10:08,360
也就是说我上面有个端口

226
00:10:08,360 --> 00:10:09,620
它上面有个端口

227
00:10:09,620 --> 00:10:11,399
那它的端口就是3000

228
00:10:11,980 --> 00:10:13,539
这样的话我们就连起来了

229
00:10:13,539 --> 00:10:14,139
对不对

230
00:10:14,139 --> 00:10:16,059
但是如果真是这样的逻辑的话

231
00:10:16,059 --> 00:10:17,799
我们再来第二个客户端

232
00:10:17,840 --> 00:10:19,879
因为客户端的逻辑是一样的

233
00:10:19,879 --> 00:10:20,539
代码是一样的

234
00:10:20,539 --> 00:10:23,279
那么他也要和这个ip冒号3000去连

235
00:10:23,299 --> 00:10:25,399
他也要连这个3000这个端口

236
00:10:25,399 --> 00:10:29,360
那么他一连就发现这个3000端口是怎样的被占用了

237
00:10:29,360 --> 00:10:31,539
这样他就连不上了啊

238
00:10:31,539 --> 00:10:33,850
所以说这时候就发现这样不行

239
00:10:33,850 --> 00:10:37,990
我们不能说我我去和3100连

240
00:10:37,990 --> 00:10:39,850
这时候别的客户端都连进来了

241
00:10:39,850 --> 00:10:44,960
那我怎样才能说我我们我们好多客户端能一直和服务端联呢

242
00:10:44,960 --> 00:10:49,139
诶后来想想这样是不是比较合适

243
00:10:49,320 --> 00:10:53,220
我这个3000当做监听端口啊

244
00:10:53,220 --> 00:10:53,820
什么意思

245
00:10:53,820 --> 00:10:57,200
就是客户端每次你过来和我连都连3000

246
00:10:57,200 --> 00:10:58,100
只要一连

247
00:10:58,100 --> 00:11:00,379
真正和和你连起来的

248
00:11:00,379 --> 00:11:03,629
我新创建一个套接字给你啊

249
00:11:03,629 --> 00:11:06,120
比如说这个可能是3001

250
00:11:06,120 --> 00:11:08,919
那么第二个客户端来和我去连

251
00:11:09,580 --> 00:11:11,799
这时候再连3000能不能连上

252
00:11:11,799 --> 00:11:11,980
诶

253
00:11:11,980 --> 00:11:12,970
发现可以连

254
00:11:12,970 --> 00:11:14,320
如果真的连上了

255
00:11:14,320 --> 00:11:16,419
他也会给他创建一个新的接口

256
00:11:16,419 --> 00:11:17,700
叫3002

257
00:11:19,460 --> 00:11:21,179
明白我这个意思没有

258
00:11:21,200 --> 00:11:25,639
也就是说实际上这个服务端啊它会有一个端口

259
00:11:25,639 --> 00:11:30,200
这个端口呢所有的客户端连接都是要过来去连它的

260
00:11:30,200 --> 00:11:31,879
但是真正连上以后

261
00:11:31,879 --> 00:11:34,309
那这个端口就会发生改变了

262
00:11:34,309 --> 00:11:36,620
也就是说这个端口我们可以给它取个名字

263
00:11:36,620 --> 00:11:38,299
只是作为一个监听的

264
00:11:38,299 --> 00:11:40,340
这个叫做监听端口

265
00:11:42,840 --> 00:11:45,059
监听端口啊

266
00:11:45,059 --> 00:11:49,870
监听端口就相当于我们比如说不管去去哪里啊

267
00:11:49,870 --> 00:11:52,570
比如说去很多地儿去银行吧

268
00:11:52,570 --> 00:11:53,320
有个钱财

269
00:11:53,320 --> 00:11:54,120
对不对

270
00:11:54,379 --> 00:11:56,120
这个就相当于前台啊

271
00:11:56,120 --> 00:11:59,519
我们可能办很多事的时候都是先找前台

272
00:11:59,519 --> 00:12:02,100
然后前台说ok我给你引荐哪个人

273
00:12:02,100 --> 00:12:04,240
然后你和那个人去对接了

274
00:12:04,659 --> 00:12:07,480
所以前台只是一个中转点

275
00:12:07,480 --> 00:12:09,399
这个监听端口就是一个中转点

276
00:12:09,399 --> 00:12:10,779
它只负责监听啊

277
00:12:10,779 --> 00:12:11,320
监听完了

278
00:12:11,320 --> 00:12:13,460
真正连起来的就不是这个端口了

279
00:12:13,460 --> 00:12:15,320
所以才能做到什么呀

280
00:12:15,320 --> 00:12:16,940
很多客户端都连3000

281
00:12:16,940 --> 00:12:18,740
但是都能连上啊

282
00:12:18,740 --> 00:12:20,899
这才能做到咱们这样的一个状况啊

283
00:12:20,899 --> 00:12:24,440
无数客户端来连一个服务器啊

284
00:12:24,659 --> 00:12:25,679
啊ok啊

285
00:12:25,679 --> 00:12:27,960
那我们现在又知道了这个监听端口了啊

286
00:12:27,960 --> 00:12:29,720
我们又知道这个监听端口了

287
00:12:30,379 --> 00:12:31,519
那么再回来

288
00:12:31,519 --> 00:12:32,360
那么这里说一下

289
00:12:32,360 --> 00:12:34,580
这个端口尽量用大一点

290
00:12:34,580 --> 00:12:36,779
不要太小啊

291
00:12:36,779 --> 00:12:41,519
大一点尽量比如说用到434 千以后吧

292
00:12:41,519 --> 00:12:43,600
三四千以后不要用的太小

293
00:12:43,600 --> 00:12:48,860
因为很多端口其实小的这个端口很多已经被系统占用了

294
00:12:49,240 --> 00:12:53,559
比如说咱们的这个电脑在电脑上面有很多端口

295
00:12:53,559 --> 00:12:57,730
有些同学以前玩过那个电脑可以远程连接啊

296
00:12:57,730 --> 00:13:00,820
就是你的电脑可以连他的电脑是3389

297
00:13:00,840 --> 00:13:01,710
对不对

298
00:13:01,710 --> 00:13:04,620
比如说数据库的远程端口是吧啊

299
00:13:04,620 --> 00:13:09,360
比如说有33061433这些端口都是固定的啊

300
00:13:09,360 --> 00:13:10,769
就是很常用的端口

301
00:13:10,769 --> 00:13:11,639
我们都知道了

302
00:13:11,639 --> 00:13:15,299
这个是cl server数据库的这个网络连连接端口

303
00:13:15,299 --> 00:13:16,320
就是my circle的啊

304
00:13:16,320 --> 00:13:20,720
这是呃这个这个远程控制的等等等等

305
00:13:20,720 --> 00:13:25,580
比如说还有135139等等啊

306
00:13:25,580 --> 00:13:29,720
太多这个端口它是有这个固定的这个用法了

307
00:13:29,720 --> 00:13:31,519
所以实际上就是能往大的用

308
00:13:31,519 --> 00:13:32,320
就往大的用

309
00:13:32,340 --> 00:13:35,620
大家也可以看到这里3000多都有很多用的啊

310
00:13:35,620 --> 00:13:37,720
所以尽量往四四千以后啊

311
00:13:37,720 --> 00:13:38,980
四五千以后去用

312
00:13:39,000 --> 00:13:41,940
因为你看这个端口咱们之前说的数量有很多对吧

313
00:13:41,940 --> 00:13:43,320
不要用太小

314
00:13:44,980 --> 00:13:46,179
ok啊

315
00:13:46,179 --> 00:13:51,379
那么我们来这里继续编写代码

316
00:13:51,379 --> 00:13:53,690
首先我们作为服务端

317
00:13:53,690 --> 00:13:55,139
我们要干嘛了

318
00:13:55,860 --> 00:14:03,899
在某个端口开始监听客户端连接

319
00:14:03,899 --> 00:14:04,860
对不对

320
00:14:05,840 --> 00:14:07,279
也就是要做这个事了

321
00:14:07,279 --> 00:14:09,200
我们要创建一个端口

322
00:14:09,200 --> 00:14:09,860
3000端口

323
00:14:09,860 --> 00:14:14,100
在这个端口上面一直监听有没有人来连接我啊

324
00:14:14,259 --> 00:14:16,139
那么过来

325
00:14:18,000 --> 00:14:20,669
我们这里谁是负责监听的

326
00:14:20,669 --> 00:14:23,639
http就是这个对象

327
00:14:23,639 --> 00:14:25,200
它就是用来去监听的

328
00:14:25,200 --> 00:14:27,120
来怎样监听它一个方法

329
00:14:27,299 --> 00:14:30,179
leon就是监听的里面有两个参数

330
00:14:30,179 --> 00:14:33,299
第一个就是端口在哪个上面去监听

331
00:14:33,299 --> 00:14:35,200
第二个就是一个回调

332
00:14:35,700 --> 00:14:39,509
那么这个回调的话什么意思

333
00:14:39,509 --> 00:14:43,639
只要有人呃啊不是说只要人啊

334
00:14:43,639 --> 00:14:46,700
这个不是连接这个就是开始监听啊

335
00:14:46,700 --> 00:14:47,240
开始监听

336
00:14:47,240 --> 00:14:48,679
就是说只要调这个回调

337
00:14:48,679 --> 00:14:50,419
就证明我们已经开始监听了

338
00:14:50,419 --> 00:14:52,639
所以我们这里可以打印一个消息

339
00:14:52,659 --> 00:14:54,100
只要能进到这个里面

340
00:14:54,100 --> 00:14:56,860
证明我们这个服务器启动完成了

341
00:14:56,860 --> 00:15:08,019
就证明sarah是吧嗯server比如说lesson on 3000啊

342
00:15:08,019 --> 00:15:10,029
在这个3000端口上面开始监听了

343
00:15:10,029 --> 00:15:11,320
我们可以打印一个信息

344
00:15:11,320 --> 00:15:12,120
是不是

345
00:15:13,220 --> 00:15:20,519
那这时候如果有客户端过来连接就可以连得上啊

346
00:15:20,519 --> 00:15:21,809
就已经可以连得上了

347
00:15:21,809 --> 00:15:24,600
目前只要有客户端已经能连得上了

348
00:15:24,600 --> 00:15:25,409
这个注意啊

349
00:15:25,409 --> 00:15:27,279
就是我已经开始监控连接了

350
00:15:27,299 --> 00:15:29,129
作为一个服务器而言

351
00:15:29,129 --> 00:15:31,220
我已经做完了

352
00:15:31,240 --> 00:15:32,080
哈哈哈

353
00:15:32,080 --> 00:15:32,980
代码很简单

354
00:15:32,980 --> 00:15:33,610
是不是

355
00:15:33,610 --> 00:15:35,860
但是我光得到这些信息不行

356
00:15:35,860 --> 00:15:36,970
信息量有点少

357
00:15:36,970 --> 00:15:40,240
所以接下来的内容就是得到信息量的一个过程了

358
00:15:41,000 --> 00:15:43,519
首先我要得到什么信息

359
00:15:43,600 --> 00:15:46,059
只要有客户端连接啊

360
00:15:46,059 --> 00:15:48,549
我就要得到这个客户端的一个信息

361
00:15:48,549 --> 00:15:55,539
那么这里就是开始监听有客户端连接啊

362
00:15:55,539 --> 00:16:00,149
也就是说我这个服务端一启动就会执行这句话啊

363
00:16:00,149 --> 00:16:02,820
但是一旦有客户端真正来连你了

364
00:16:02,820 --> 00:16:07,440
连接成功一个就会掉下面的这个回调的这个就是io了

365
00:16:07,519 --> 00:16:11,059
也就是说lo是过来监听客户端有没有连接的

366
00:16:11,059 --> 00:16:15,750
那io.二on就是用来监听有没有连接

367
00:16:15,750 --> 00:16:19,620
第一个一定是connection啊

368
00:16:19,620 --> 00:16:20,700
一定是connection

369
00:16:20,700 --> 00:16:23,720
他就是来监听这个连接的啊

370
00:16:23,720 --> 00:16:25,250
如果连接成功

371
00:16:25,250 --> 00:16:27,259
有一个方法

372
00:16:27,259 --> 00:16:28,940
它一个参数叫socket

373
00:16:30,259 --> 00:16:32,840
那么这时候只要有客端来连接

374
00:16:32,840 --> 00:16:35,330
就会就会掉这个回调

375
00:16:35,330 --> 00:16:37,100
在这个回调里面有一个参数

376
00:16:37,100 --> 00:16:38,360
这个socket

377
00:16:38,440 --> 00:16:40,779
这个socket就是什么

378
00:16:40,779 --> 00:16:45,200
就是真正和客户端连接的这个套接字啊

379
00:16:45,200 --> 00:16:48,620
也就是说这个三天在监听监听监听完了以后

380
00:16:48,639 --> 00:16:50,740
发现有个客户端真正连接了

381
00:16:50,740 --> 00:16:52,000
那么他就会调那个方法

382
00:16:52,000 --> 00:16:55,870
并且把这个套接字就是这个3001或者3002

383
00:16:55,870 --> 00:16:59,679
这个真正连接的套接字怎样了

384
00:17:00,179 --> 00:17:00,960
诶

385
00:17:00,960 --> 00:17:01,860
给你了

386
00:17:01,860 --> 00:17:03,690
那么你拿到这个套接字

387
00:17:03,690 --> 00:17:06,650
就相当于这个就是真正的呃

388
00:17:06,650 --> 00:17:07,819
就相当于一个电话了

389
00:17:07,819 --> 00:17:08,180
对不对

390
00:17:08,180 --> 00:17:09,440
你就可以对他干嘛了

391
00:17:09,440 --> 00:17:10,759
发消息了啊

392
00:17:10,759 --> 00:17:11,920
对他发消息了

393
00:17:13,578 --> 00:17:16,338
那么这时候比如说经听到有人连接了

394
00:17:16,338 --> 00:17:19,578
你就可以嗯给他发消息啊

395
00:17:19,578 --> 00:17:20,989
怎样发消息

396
00:17:20,989 --> 00:17:27,799
比如说你就可以告诉他mit这个就是发消息了

397
00:17:27,799 --> 00:17:32,299
就是给和你连接的连接成功的那个人发送消息了

398
00:17:34,900 --> 00:17:36,960
发送消息

399
00:17:39,079 --> 00:17:40,700
那么发送什么消息

400
00:17:40,700 --> 00:17:42,019
它里面两个参数

401
00:17:42,019 --> 00:17:45,890
第一个是个自定义的参数啊

402
00:17:45,890 --> 00:17:47,839
就这从这里开始啊

403
00:17:47,839 --> 00:17:48,619
这个是固定的

404
00:17:48,619 --> 00:17:49,819
但是这里就是自定义的了

405
00:17:49,819 --> 00:17:51,299
比如说我们用message

406
00:17:51,299 --> 00:17:53,160
比如说我们发个消息

407
00:17:53,160 --> 00:17:58,319
消息就是连接成功了

408
00:17:58,319 --> 00:18:00,680
连接成功了啊

409
00:18:00,680 --> 00:18:03,559
这样的话我们现在做的逻辑就是什么呀

410
00:18:03,559 --> 00:18:05,000
如果有客端来连接

411
00:18:05,000 --> 00:18:06,619
我只要连接成了

412
00:18:06,619 --> 00:18:07,700
我就给他发个消息

413
00:18:07,700 --> 00:18:09,839
告诉他你连接成功了啊

414
00:18:09,839 --> 00:18:12,059
但实际上我们现在可以先不发这个消息

415
00:18:12,059 --> 00:18:13,599
因为对于客户端而言

416
00:18:13,619 --> 00:18:15,150
它本身也有回调

417
00:18:15,150 --> 00:18:16,410
就是你不给它回

418
00:18:16,410 --> 00:18:17,640
它连接成功以后

419
00:18:17,640 --> 00:18:19,380
他也他自己也知道诶

420
00:18:19,380 --> 00:18:20,500
他连成功了

421
00:18:20,579 --> 00:18:24,299
那么我们先先知道可以这样去用就行了啊

422
00:18:24,299 --> 00:18:26,339
一会儿等具体内容啊

423
00:18:26,339 --> 00:18:27,720
怎样写啊

424
00:18:27,720 --> 00:18:28,900
我们一会儿再说

425
00:18:29,920 --> 00:18:31,420
那么现在我们先知道啊

426
00:18:31,420 --> 00:18:32,680
我们就可以给他发消息了

427
00:18:32,680 --> 00:18:34,799
但是我们也可以监听消息

428
00:18:35,500 --> 00:18:37,480
不但可以给客户端发消息

429
00:18:37,480 --> 00:18:41,700
还可以监听客户端发来的消息

430
00:18:41,839 --> 00:18:44,180
也是使用socket啊

431
00:18:44,180 --> 00:18:47,319
因为这个就是就这个口是吧

432
00:18:47,319 --> 00:18:50,319
你不管发消息还是听消息都是通过他的

433
00:18:50,680 --> 00:18:53,200
所以在这里监听也是用

434
00:18:53,200 --> 00:19:00,109
它也是二客户端发来的这个这个这个这个消息的这个k算是一个件

435
00:19:00,109 --> 00:19:02,299
我们比如说这也叫message

436
00:19:02,960 --> 00:19:05,059
然后function

437
00:19:08,420 --> 00:19:09,799
一个数据

438
00:19:10,180 --> 00:19:12,460
那这样的话就是只要客户端发来的消息

439
00:19:12,460 --> 00:19:13,539
就会调这个方法

440
00:19:13,539 --> 00:19:17,339
并且这个data就是客户端发来的消息啊

441
00:19:17,339 --> 00:19:20,519
我们在这里可以给它打印出来

442
00:19:20,519 --> 00:19:23,539
在终端上打印就是console log就行了

443
00:19:23,960 --> 00:19:27,079
客户端发来消息

444
00:19:27,079 --> 00:19:28,400
什么消息呢

445
00:19:28,400 --> 00:19:33,420
你可以把这个data放这儿就行了啊

446
00:19:33,420 --> 00:19:36,930
也就是说我们在这里只要有客户端连接成功了

447
00:19:36,930 --> 00:19:38,279
我们就可以干嘛了

448
00:19:38,279 --> 00:19:40,380
可以给这个客户端发消息

449
00:19:40,380 --> 00:19:42,240
同时也可以监听消息

450
00:19:42,240 --> 00:19:43,619
我们现在先不让发消息

451
00:19:43,619 --> 00:19:44,779
先一直监听

452
00:19:44,779 --> 00:19:47,000
然后我们下节课来写客户端

453
00:19:47,000 --> 00:19:49,579
我们先从客户端发消息啊

454
00:19:49,579 --> 00:19:51,259
先让客户端给服务器发消息

455
00:19:51,259 --> 00:19:54,740
然后再试用服务器给客户端回消息啊

456
00:19:54,859 --> 00:19:57,799
ok那么服务端咱们就这么多内容

457
00:19:57,799 --> 00:19:59,599
就写这么多内容非常简单

458
00:19:59,599 --> 00:20:00,079
是不是

459
00:20:00,079 --> 00:20:02,359
其实这对于服务端而言

460
00:20:02,359 --> 00:20:04,180
还有一些发送消息的方法

461
00:20:04,200 --> 00:20:08,509
但是最常用的啊就是这个啊

462
00:20:08,509 --> 00:20:10,160
ok啊我们就先这么多

463
00:20:10,160 --> 00:20:11,119
我们先这么多

464
00:20:11,119 --> 00:20:15,819
我们下节课来说客户端怎么去写嗯


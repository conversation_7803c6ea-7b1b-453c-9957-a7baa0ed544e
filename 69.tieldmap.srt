1
00:00:09,279 --> 00:00:12,820
ok那么这节课咱们来说一下这个瓦片地图啊

2
00:00:12,820 --> 00:00:18,118
那这个瓦片地图其实也是比较简单的啊

3
00:00:18,118 --> 00:00:19,318
也是比较简单的

4
00:00:19,318 --> 00:00:20,518
那什么是瓦片地图啊

5
00:00:20,518 --> 00:00:23,099
我们知道比如说一个2d游戏来做地图啊

6
00:00:23,099 --> 00:00:25,109
我们在这里大家去想啊

7
00:00:25,109 --> 00:00:26,940
2d游戏做地图啊

8
00:00:26,940 --> 00:00:28,230
一个一个背景

9
00:00:28,230 --> 00:00:30,519
比如说可能是草原啊

10
00:00:30,519 --> 00:00:31,719
全是绿色的

11
00:00:31,719 --> 00:00:34,000
背景上面呢有几个房子

12
00:00:34,000 --> 00:00:35,320
那么大家会发现

13
00:00:35,320 --> 00:00:38,280
其实在2d游戏里面不可能每一个房子是一个样

14
00:00:38,280 --> 00:00:40,020
那么其实这几个房子啊

15
00:00:40,020 --> 00:00:42,500
比如说一个地图上面的两个房子

16
00:00:42,500 --> 00:00:44,509
这两个房子可能长得是一样的

17
00:00:44,509 --> 00:00:46,579
然后这个地图有很多树

18
00:00:46,579 --> 00:00:50,409
可能这些树长得也是一样的啊

19
00:00:50,409 --> 00:00:52,719
你不可能说我这边一个2d游戏

20
00:00:52,719 --> 00:00:55,000
2d游戏里面没有一个东西是重复的

21
00:00:55,000 --> 00:00:55,600
对不对

22
00:00:55,600 --> 00:00:58,140
它很多东西都是重复画下来的

23
00:00:58,140 --> 00:01:02,079
那么所谓的瓦片地图就是有一张大图

24
00:01:02,079 --> 00:01:04,540
这张大图上面有很多小的资源

25
00:01:04,540 --> 00:01:06,700
之后我们要来做一个地图的话

26
00:01:06,700 --> 00:01:09,159
我们就把这个瓦片地图啊

27
00:01:09,159 --> 00:01:12,159
就把这个资源当做我们的原素材

28
00:01:12,159 --> 00:01:14,689
然后来进行绘制啊

29
00:01:14,689 --> 00:01:15,739
来进行绘制

30
00:01:15,739 --> 00:01:20,760
那绘制出来的结果就是瓦片地图这个东西啊说不太好说呃

31
00:01:20,760 --> 00:01:22,560
我们大家一起来做一下

32
00:01:22,560 --> 00:01:24,689
一块儿看一下就知道了啊

33
00:01:24,689 --> 00:01:28,859
那首先瓦片地图的话

34
00:01:29,819 --> 00:01:32,219
那么我们先要去下载一个软件

35
00:01:32,219 --> 00:01:34,319
这个软件是必须下载的

36
00:01:34,319 --> 00:01:38,060
必须用的叫做tell the map

37
00:01:40,599 --> 00:01:44,099
那么大家搜一下就会出现他的这个官网啊

38
00:01:44,099 --> 00:01:45,180
就会出现它的官网

39
00:01:45,180 --> 00:01:47,079
然后我们直接点击它

40
00:01:48,799 --> 00:01:50,170
点一下下载

41
00:01:50,170 --> 00:01:50,980
大家可以看

42
00:01:50,980 --> 00:01:52,180
这就是瓦片地图

43
00:01:52,180 --> 00:01:53,859
是不是一格一格的

44
00:01:56,700 --> 00:01:58,819
那么到了这里以后啊

45
00:01:58,819 --> 00:01:59,719
到了这里以后

46
00:01:59,719 --> 00:02:02,359
我们直接在这里选现在下载

47
00:02:03,180 --> 00:02:07,579
然后这里他是问你要问你要一些赞助费啊

48
00:02:07,579 --> 00:02:09,360
那你可以不给是吧

49
00:02:09,979 --> 00:02:13,379
然后在这里你就可以去选择进行下载了

50
00:02:14,118 --> 00:02:15,758
那么有32位的

51
00:02:15,758 --> 00:02:16,538
64位的

52
00:02:16,538 --> 00:02:17,318
有苹果系统

53
00:02:17,318 --> 00:02:18,878
linux系统的啊

54
00:02:18,878 --> 00:02:23,020
那么按照你自己对应的去选择啊

55
00:02:23,020 --> 00:02:24,580
按照你对应的去选择

56
00:02:24,580 --> 00:02:27,039
选择完以后安装好以后

57
00:02:27,039 --> 00:02:31,020
我们打开它能打开大

58
00:02:37,558 --> 00:02:41,149
打开它以后我们就可以看到这样的一个界面了啊

59
00:02:41,149 --> 00:02:43,479
这个软件啊就是这样的界面

60
00:02:43,579 --> 00:02:46,098
那么在这里他说没有打开的文件

61
00:02:46,098 --> 00:02:48,498
你在这里可以新建一个地图啊

62
00:02:48,498 --> 00:02:49,860
新建一个地图

63
00:02:50,019 --> 00:02:52,299
新建地图的时候注意啊

64
00:02:52,299 --> 00:02:55,218
新建地图的时候在这里啊

65
00:02:55,218 --> 00:02:56,838
就是地图方向什么的

66
00:02:56,838 --> 00:02:57,919
大家现在不用管

67
00:02:57,919 --> 00:02:59,419
那我们看一下这个方向吧

68
00:02:59,419 --> 00:03:02,899
啊方向的话有个正常的啊

69
00:03:02,899 --> 00:03:06,079
正常的就是2d游戏里面大部分都是这种俯视地图

70
00:03:06,079 --> 00:03:11,280
比如说最开始那那个最开始很火的一些系列啊

71
00:03:11,280 --> 00:03:13,199
比如说日本的国民游戏是吧

72
00:03:13,199 --> 00:03:15,180
就是这个勇者斗恶龙啊

73
00:03:15,180 --> 00:03:16,409
还有这个最终幻想

74
00:03:16,409 --> 00:03:19,050
那么他们都是完全的一个俯视图

75
00:03:19,050 --> 00:03:22,110
那么我们这个正常就是俯视图的这个视角

76
00:03:22,110 --> 00:03:23,310
我们在这里就用正常

77
00:03:23,310 --> 00:03:24,520
大家一会儿就可以看到

78
00:03:24,520 --> 00:03:25,300
除此之外

79
00:03:25,300 --> 00:03:27,969
2d游戏大家知道还有45度角的

80
00:03:27,969 --> 00:03:32,990
还有有一些现在呃比较火的一些唉

81
00:03:32,990 --> 00:03:36,650
一些这个游戏他用的是这种六角的网格啊

82
00:03:36,650 --> 00:03:38,750
就是他这个格子不是不是正方形的

83
00:03:38,750 --> 00:03:41,090
是这种六边形的啊

84
00:03:41,090 --> 00:03:42,340
也都是有的

85
00:03:44,919 --> 00:03:47,460
这些其实大家完了可以自己去创建

86
00:03:47,460 --> 00:03:48,180
创建完

87
00:03:48,180 --> 00:03:51,349
然后大家可以看一下他给你分的这个格子是什么形状

88
00:03:51,349 --> 00:03:53,090
我们在这里就用正常的啊

89
00:03:53,090 --> 00:03:54,620
就用这个方格就可以了

90
00:03:54,620 --> 00:03:56,639
然后对于这个方格而言

91
00:03:56,739 --> 00:04:00,278
首先啊在这里我们看这里啊

92
00:04:00,278 --> 00:04:02,838
看这个位置有一个块大小

93
00:04:02,838 --> 00:04:03,859
一个地图大小

94
00:04:03,859 --> 00:04:05,479
地图大小是固定的啊

95
00:04:05,479 --> 00:04:06,979
我们肯定就选固定的啊

96
00:04:06,979 --> 00:04:08,088
一般都是固定的

97
00:04:08,088 --> 00:04:09,889
那么固定的这里他说了

98
00:04:09,889 --> 00:04:11,329
你横向要铺多少块

99
00:04:11,329 --> 00:04:12,819
纵向要铺多少块

100
00:04:12,919 --> 00:04:15,620
那么这里我们铺多少块合适呢

101
00:04:15,620 --> 00:04:17,660
首先我们要看一块有多大

102
00:04:17,838 --> 00:04:20,478
在这里我们看一下我们创建的这个地图啊

103
00:04:21,559 --> 00:04:23,319
一块儿有多大

104
00:04:23,319 --> 00:04:25,779
它是32像素乘以32像素

105
00:04:25,779 --> 00:04:29,800
那么这个像素我们怎么给这个要看你的这个图片的

106
00:04:29,800 --> 00:04:31,560
比如说我们看一下这个图片啊

107
00:04:31,939 --> 00:04:33,920
这个图片我们仔细看一下

108
00:04:34,939 --> 00:04:36,620
比如说一行有几格

109
00:04:36,658 --> 00:04:38,129
一格两格

110
00:04:38,129 --> 00:04:39,069
三格

111
00:04:39,069 --> 00:04:39,910
四格

112
00:04:39,910 --> 00:04:40,329
五格

113
00:04:40,329 --> 00:04:40,810
六格

114
00:04:40,810 --> 00:04:41,410
七格八格

115
00:04:41,410 --> 00:04:44,769
这个很明显能看出来一行它分了八格啊

116
00:04:44,769 --> 00:04:47,709
因为这八个就是完全不同的这个东西

117
00:04:47,709 --> 00:04:48,360
对不对

118
00:04:48,360 --> 00:04:49,800
一行有八个的话

119
00:04:49,800 --> 00:04:52,949
这个图片像素我我看一下啊

120
00:04:52,949 --> 00:04:55,949
图片像素是256

121
00:04:55,949 --> 00:05:00,110
所以256÷838 24刚好就是32

122
00:05:00,110 --> 00:05:01,850
所以对于这个图片而言

123
00:05:01,850 --> 00:05:03,699
它的每个格子都是32

124
00:05:03,699 --> 00:05:06,069
那在这里就是32x32

125
00:05:06,069 --> 00:05:07,899
这个是要跟图片对应的啊

126
00:05:07,899 --> 00:05:09,519
你自己拿到一个图片以后

127
00:05:09,519 --> 00:05:10,569
拿到素材以后

128
00:05:10,569 --> 00:05:12,769
你要去计算的啊

129
00:05:12,769 --> 00:05:14,839
这里一格是32x32

130
00:05:14,839 --> 00:05:16,220
所以我们创建地图的时候

131
00:05:16,220 --> 00:05:18,069
一块就是32x32

132
00:05:18,069 --> 00:05:22,209
那他的他这张图片里面的每一个小格子啊

133
00:05:22,209 --> 00:05:26,459
都可以当做我们这里的一个呃一个瓦片或者叫一个砖块

134
00:05:28,139 --> 00:05:32,060
那么在这里地图大小我们就可以来30x30

135
00:05:32,060 --> 00:05:33,680
就是横向可以铺30块

136
00:05:33,680 --> 00:05:35,250
纵向也可以铺30块

137
00:05:35,250 --> 00:05:40,468
最后得到的结果就是一个960x960的一个呃游戏地图啊

138
00:05:40,468 --> 00:05:42,569
就这么大像素的一个地图

139
00:05:42,569 --> 00:05:45,399
然后我们可以选择另存为一下

140
00:05:46,600 --> 00:05:49,839
选择一下保存的一个路径啊

141
00:05:49,839 --> 00:05:52,930
比如说保存到一个文件夹里啊

142
00:05:52,930 --> 00:05:56,160
类型我们就选这种默认的tmx

143
00:05:59,379 --> 00:06:01,500
我们选择了保存以后

144
00:06:01,500 --> 00:06:05,059
大家可以看它就会出现这样的一个界面啊

145
00:06:05,059 --> 00:06:07,158
那这个界面就是有很多小格子了

146
00:06:07,158 --> 00:06:08,059
大家可以看出来

147
00:06:08,059 --> 00:06:09,319
这就是正常的格子

148
00:06:09,319 --> 00:06:10,939
每个都是一个小方格啊

149
00:06:10,939 --> 00:06:12,560
每一个都是一个小方格

150
00:06:18,819 --> 00:06:20,160
那么在这里啊

151
00:06:20,160 --> 00:06:23,040
在这里我们把重要的东西来说一下

152
00:06:23,040 --> 00:06:25,918
首先创建好一个新地图

153
00:06:25,918 --> 00:06:27,178
它现在是空的啊

154
00:06:27,178 --> 00:06:28,348
它现在是空的

155
00:06:28,348 --> 00:06:32,980
那么在这里右下角有一个很重要的东西叫做图块

156
00:06:33,819 --> 00:06:35,098
没有图块的话

157
00:06:35,098 --> 00:06:36,358
我们当前什么也做不了

158
00:06:36,358 --> 00:06:38,579
大家可以看这什么也没有图块

159
00:06:38,579 --> 00:06:40,918
就是我们刚才看到的那个图片啊

160
00:06:40,918 --> 00:06:43,139
我们新建一个图块

161
00:06:44,680 --> 00:06:46,319
在这里大家可以看啊

162
00:06:46,319 --> 00:06:48,240
他就要很多内容了

163
00:06:48,240 --> 00:06:50,879
我们在这里看到这儿有一个图像

164
00:06:50,879 --> 00:06:51,240
对不对

165
00:06:51,240 --> 00:06:52,620
我们选择浏览

166
00:06:53,000 --> 00:06:54,540
把我们的

167
00:06:56,120 --> 00:06:59,019
把我们的刚才看的这个图片选中

168
00:06:59,019 --> 00:06:59,680
它

169
00:07:00,180 --> 00:07:01,339
选中它以后

170
00:07:01,339 --> 00:07:02,660
这时候大家可以看

171
00:07:02,660 --> 00:07:05,060
他要问你一下这个图片

172
00:07:05,060 --> 00:07:09,019
每个图块的大小我们就是32x32啊

173
00:07:09,019 --> 00:07:10,939
如果有编剧或者间距的话

174
00:07:10,939 --> 00:07:12,019
你在这里填上

175
00:07:12,019 --> 00:07:13,459
这里没有就是零

176
00:07:13,459 --> 00:07:15,858
然后另存为啊

177
00:07:15,858 --> 00:07:17,939
我们就还放到这个里面就可以了

178
00:07:18,939 --> 00:07:23,319
这时候大家可以看它就打开了另外的一张呃

179
00:07:23,319 --> 00:07:24,639
另外的一个设置界面

180
00:07:24,639 --> 00:07:26,379
在这个设置界面啊

181
00:07:26,379 --> 00:07:31,259
你就可以看到这个我们的这个图片被划分开了

182
00:07:31,259 --> 00:07:34,079
划分成32x32的这样的一个小格子

183
00:07:34,079 --> 00:07:36,000
你看它有这个网格状的

184
00:07:36,000 --> 00:07:37,709
把我们这个图片切分开了

185
00:07:37,709 --> 00:07:40,199
我们可以选择其中的每个格子

186
00:07:40,199 --> 00:07:43,319
你可以看到每个格子它都是宽高都有

187
00:07:43,319 --> 00:07:45,310
然后有不同的id啊

188
00:07:45,310 --> 00:07:47,529
每一个格子是有不同的id的啊

189
00:07:47,529 --> 00:07:50,600
那么在这边你是可以看到的呃

190
00:07:50,600 --> 00:07:57,649
那么在这里你可以对它进行嗯这个很多的这个操作啊

191
00:07:57,649 --> 00:08:00,699
比如说选择这个数啊

192
00:08:00,699 --> 00:08:02,019
选择这个数啊

193
00:08:02,019 --> 00:08:03,439
或者说选择这个吧

194
00:08:03,439 --> 00:08:05,420
这个比如说是一堆木头

195
00:08:05,420 --> 00:08:06,019
对不对

196
00:08:06,019 --> 00:08:08,180
如果它在地图上显示啊

197
00:08:08,180 --> 00:08:11,569
我是希望他不能被主角走过的啊

198
00:08:11,569 --> 00:08:13,550
就是它是一个碰撞体啊

199
00:08:13,550 --> 00:08:18,209
我们甚至可以选择上面在这里编辑一下它的碰撞体啊

200
00:08:18,209 --> 00:08:21,300
这个是你可以去进行这个编辑的

201
00:08:26,379 --> 00:08:29,129
这样的话我们就给它编辑了一个碰撞体

202
00:08:29,129 --> 00:08:30,269
那从现在开始

203
00:08:30,269 --> 00:08:31,860
只要我们用这个素材

204
00:08:31,860 --> 00:08:35,899
那么它呢就会变成一个带碰撞体的这样的一个素材

205
00:08:35,899 --> 00:08:40,190
当然你也可以不用它的这个自带的这个碰撞效果啊

206
00:08:40,190 --> 00:08:43,610
然后我们导到这个游戏里面生成地图以后

207
00:08:43,610 --> 00:08:46,070
在游戏里面我们去给它加碰撞

208
00:08:46,070 --> 00:08:47,330
也是可以的啊

209
00:08:47,330 --> 00:08:47,990
也是可以的

210
00:08:47,990 --> 00:08:50,599
那我这里就不用他这个

211
00:08:52,740 --> 00:08:55,639
总之这个图图块啊我们导进来以后

212
00:08:55,639 --> 00:08:57,590
我们就可以可以给它关掉了

213
00:08:57,590 --> 00:08:58,938
关掉以后

214
00:09:04,720 --> 00:09:07,899
等一下啊保存

215
00:09:15,019 --> 00:09:16,578
我们这个图块

216
00:09:25,379 --> 00:09:27,379
我在这里重新导一下啊

217
00:09:27,379 --> 00:09:28,519
重新导一下

218
00:09:35,559 --> 00:09:36,750
重新导一下

219
00:09:36,750 --> 00:09:38,669
然后这时候大家可以看一下啊

220
00:09:38,669 --> 00:09:42,940
在右下角这边就会显示我们这个图块了啊

221
00:09:42,940 --> 00:09:44,529
就会显示我们这个图块了

222
00:09:44,529 --> 00:09:48,850
这时候我们选择其中的任何一个图块

223
00:09:48,850 --> 00:09:51,919
我们就能进行地图的绘制了啊

224
00:09:51,919 --> 00:09:54,200
比如说大家可以看诶

225
00:09:54,200 --> 00:09:55,429
绘制草地

226
00:09:55,429 --> 00:09:56,599
对不对

227
00:09:57,320 --> 00:10:00,840
绘制木木头是吧

228
00:10:00,840 --> 00:10:04,470
那么这时候我们已经可以进行这个绘制了

229
00:10:04,470 --> 00:10:05,639
当然这时候大家发现

230
00:10:05,639 --> 00:10:10,519
比如说我想把这个这这这这堆木头啊

231
00:10:10,519 --> 00:10:12,200
我想放在这个草地上面

232
00:10:12,200 --> 00:10:13,820
你会发现放不上去啊

233
00:10:13,820 --> 00:10:14,779
他是被遮盖的

234
00:10:14,779 --> 00:10:15,139
对不对

235
00:10:15,139 --> 00:10:16,318
他是被遮盖的

236
00:10:16,318 --> 00:10:19,078
所以这时候我们要说很重要的一个东西了啊

237
00:10:19,078 --> 00:10:20,639
我们选择橡皮啊

238
00:10:20,639 --> 00:10:22,379
可以给它擦掉啊

239
00:10:22,379 --> 00:10:26,220
那么在右上角这个内容啊

240
00:10:26,220 --> 00:10:29,870
这个区域右上角这个区域大家可以看它有这几块

241
00:10:29,870 --> 00:10:30,590
对不对不对

242
00:10:30,590 --> 00:10:32,629
三块第一块迷你地图啊

243
00:10:32,629 --> 00:10:37,899
它就是会显示我们当前绘制的这个地图的一个呃整体的一个区域

244
00:10:37,899 --> 00:10:39,129
还有个对象层

245
00:10:39,129 --> 00:10:40,149
我们现在先不用管

246
00:10:40,149 --> 00:10:41,379
还有一个图层

247
00:10:41,500 --> 00:10:45,120
这里这个图层中图层概念很重要啊

248
00:10:45,320 --> 00:10:48,279
图层的话它就代表当前有几层

249
00:10:48,659 --> 00:10:52,559
那么这些每一层之间它是一个覆盖关系啊

250
00:10:52,559 --> 00:10:55,070
比如说我们创建新的右键

251
00:10:55,070 --> 00:10:57,590
在这里新建一个图块层

252
00:10:57,590 --> 00:10:59,509
再新建一个图块层啊

253
00:10:59,509 --> 00:11:02,269
就是基本上我们最少也有三块啊

254
00:11:02,269 --> 00:11:03,759
也有这样的三层

255
00:11:03,759 --> 00:11:05,120
应该是三层

256
00:11:05,120 --> 00:11:09,139
这三层往往我们会在这个最下面的一层

257
00:11:09,139 --> 00:11:10,340
也就是第一层

258
00:11:10,340 --> 00:11:13,100
因为它的覆盖关系是从上到下覆盖的

259
00:11:13,100 --> 00:11:17,269
比如说最下面一层往往放这个草地

260
00:11:17,269 --> 00:11:18,799
往往放这种草地

261
00:11:18,799 --> 00:11:21,169
比如说图块一往往放草地

262
00:11:21,169 --> 00:11:23,440
图块二呢就放这些

263
00:11:23,440 --> 00:11:24,460
比如说木块呀

264
00:11:24,460 --> 00:11:25,120
木堆呀

265
00:11:25,120 --> 00:11:25,720
石块呀

266
00:11:25,720 --> 00:11:26,379
或者花呀

267
00:11:26,379 --> 00:11:26,919
草呀

268
00:11:26,919 --> 00:11:28,659
可以覆盖这个草地的

269
00:11:28,659 --> 00:11:30,860
那么它呢是可以覆盖的啊

270
00:11:30,860 --> 00:11:33,139
图块三呢可能就放这个树啊

271
00:11:33,139 --> 00:11:34,639
什么东西的啊

272
00:11:34,860 --> 00:11:37,919
那一般情况下最少我们都会有这三层

273
00:11:37,919 --> 00:11:39,629
那我们选中图块一

274
00:11:39,629 --> 00:11:40,980
比如说在图块一上面

275
00:11:40,980 --> 00:11:42,879
我们要绘制这个草地

276
00:11:43,980 --> 00:11:49,549
那么我们现在默认选中呢叫做这样的一个图章啊

277
00:11:49,549 --> 00:11:51,740
那这个图章的话大家也看到了

278
00:11:51,740 --> 00:11:53,299
我们绘制的时候很麻烦

279
00:11:53,299 --> 00:11:54,149
对不对

280
00:11:54,149 --> 00:11:57,120
这么多图块我们这样一点点绘制太麻烦了

281
00:11:57,120 --> 00:11:58,649
那在这里往右边

282
00:11:58,649 --> 00:12:00,090
我们大家自己去找啊

283
00:12:00,090 --> 00:12:02,100
其实这个自己去找就能找出来

284
00:12:02,100 --> 00:12:04,438
我们大家就可以找到一个叫填充

285
00:12:04,639 --> 00:12:07,429
找到填充以后选择某一个图块

286
00:12:07,429 --> 00:12:08,539
你在这里按一下

287
00:12:08,539 --> 00:12:16,639
你就会发现这时候我们图块一已经被绘制成全部绘制成这个草地了啊

288
00:12:16,639 --> 00:12:19,440
每一块都是这个草地了啊

289
00:12:19,440 --> 00:12:20,759
每一块都是藏镜

290
00:12:20,759 --> 00:12:25,698
然后我们再回来再选成这个默认的这个图块

291
00:12:26,399 --> 00:12:28,019
这个就是一块一块绘制了

292
00:12:28,019 --> 00:12:28,500
对不对

293
00:12:28,500 --> 00:12:31,769
然后我们选到这个第二层上面

294
00:12:31,769 --> 00:12:32,970
第二层上面

295
00:12:32,970 --> 00:12:34,919
比如说现在我们在绘制

296
00:12:34,919 --> 00:12:37,019
比如说绘制树根

297
00:12:37,019 --> 00:12:38,339
大家看一下

298
00:12:39,480 --> 00:12:43,980
你看这时候树根额外的部分啊

299
00:12:43,980 --> 00:12:46,500
额外的部分它还是会显示草地

300
00:12:46,500 --> 00:12:48,279
而不是会和刚才一样

301
00:12:48,279 --> 00:12:50,379
就就感觉少了这么这样一块

302
00:12:50,379 --> 00:12:53,620
但是你要是在这个图这个这个第一层里面

303
00:12:53,620 --> 00:12:55,179
你比如说你会输根

304
00:12:55,179 --> 00:13:00,120
你看这个图片明显就和这个草地重重合了啊

305
00:13:00,120 --> 00:13:02,700
所以他们就把这个草地这个图片删了

306
00:13:02,700 --> 00:13:04,019
然后直接显示树根了

307
00:13:04,019 --> 00:13:05,399
那这样的话就不行了

308
00:13:05,399 --> 00:13:06,519
对不对

309
00:13:09,139 --> 00:13:12,320
那所以覆盖的话大家就可以看到这个好处了啊

310
00:13:12,320 --> 00:13:13,679
可以看到好处了

311
00:13:15,120 --> 00:13:19,399
那么这时候啊这时候比如说还有这个图块三啊

312
00:13:19,399 --> 00:13:20,299
三层

313
00:13:20,559 --> 00:13:22,419
那么三层的话

314
00:13:22,419 --> 00:13:24,940
比如说我们可以来一些树啊

315
00:13:24,940 --> 00:13:25,659
来些树

316
00:13:25,659 --> 00:13:26,799
那大家去想

317
00:13:26,799 --> 00:13:29,940
既然第二层和第三层呃

318
00:13:29,940 --> 00:13:31,769
都是这个覆盖的

319
00:13:31,769 --> 00:13:33,840
那么覆盖层来一层不就行了

320
00:13:33,840 --> 00:13:36,139
为什么要来两层啊

321
00:13:36,139 --> 00:13:38,600
其实因为这涉及到一个主角问题啊

322
00:13:38,600 --> 00:13:39,559
大家去想一下

323
00:13:39,559 --> 00:13:43,740
比如说现在我这个花就代表一个主角啊

324
00:13:43,740 --> 00:13:45,299
花就代表一个主角

325
00:13:45,299 --> 00:13:47,599
那么主角过来以后

326
00:13:48,539 --> 00:13:53,500
比如说我这里来这个草地吧

327
00:13:54,639 --> 00:13:56,679
那比如说花儿代表主角

328
00:13:56,679 --> 00:13:58,840
主角可以在我们这个地面上走

329
00:13:58,840 --> 00:13:59,549
对不对

330
00:13:59,549 --> 00:14:00,990
主角走走走走

331
00:14:00,990 --> 00:14:02,129
走到这个位置的时候

332
00:14:02,129 --> 00:14:02,669
注意啊

333
00:14:02,669 --> 00:14:04,649
主角是可以往草地里面走的

334
00:14:04,649 --> 00:14:07,320
当主角走到草地里面的时候

335
00:14:07,320 --> 00:14:10,219
主角是可以覆盖这个草地的

336
00:14:10,519 --> 00:14:11,419
对不对

337
00:14:11,419 --> 00:14:13,580
主角是可以覆盖这个草地的

338
00:14:13,580 --> 00:14:14,480
这个是没问题的

339
00:14:14,480 --> 00:14:15,620
它是可以覆盖草地的

340
00:14:15,620 --> 00:14:20,059
比如说我们有一些这个这个这个石头地啊

341
00:14:20,059 --> 00:14:23,029
这个这个比如说我们是允许主角在上面走的

342
00:14:23,029 --> 00:14:24,679
那我们主角是可以走上去的

343
00:14:24,679 --> 00:14:27,818
也就是说这个主角肯定是要比第二层高的

344
00:14:27,840 --> 00:14:30,839
但是有些东西大家想比如说这个数

345
00:14:33,120 --> 00:14:34,860
那这里我们要画一棵树

346
00:14:34,860 --> 00:14:35,340
怎么画

347
00:14:35,340 --> 00:14:38,159
我们不用一啊一格一格去画

348
00:14:38,159 --> 00:14:41,589
我们直接在这里选中第一格拖拽下来

349
00:14:41,589 --> 00:14:44,889
这时候整个一个哎你看一个数就出来了

350
00:14:44,889 --> 00:14:48,730
然后在这里我们去画一棵树

351
00:14:48,730 --> 00:14:52,600
那这时候我我这棵树是在图块三啊

352
00:14:52,600 --> 00:14:54,698
就是在这个第三层绘制的

353
00:14:54,840 --> 00:14:56,879
那么这时候为什么要在第三层呢

354
00:14:56,879 --> 00:14:58,019
大家去想一想啊

355
00:14:58,019 --> 00:14:59,580
你作为一个主角而言

356
00:14:59,580 --> 00:15:01,529
比如说这个花是个主角啊

357
00:15:01,529 --> 00:15:04,679
它是可以占到这个草地和这个石块上的

358
00:15:04,679 --> 00:15:07,000
但是它能不能站到树上面

359
00:15:08,039 --> 00:15:10,980
你想想肯定不能

360
00:15:10,980 --> 00:15:13,359
所以这个对于玩家而言

361
00:15:13,799 --> 00:15:15,000
玩家走走走

362
00:15:15,000 --> 00:15:16,980
玩家应该是被树遮住的

363
00:15:18,000 --> 00:15:20,139
玩家是被树遮住的

364
00:15:20,299 --> 00:15:21,919
这个一定要有感觉啊

365
00:15:21,919 --> 00:15:25,220
所以说像树这种东西往往都在第三层

366
00:15:25,220 --> 00:15:27,110
因为它是可以遮盖玩家的

367
00:15:27,110 --> 00:15:29,818
而玩家是可以遮盖第二层的

368
00:15:30,000 --> 00:15:31,629
你看对不对

369
00:15:31,629 --> 00:15:32,529
有这个感觉啊

370
00:15:32,529 --> 00:15:33,669
一定要有这个感觉

371
00:15:33,669 --> 00:15:35,559
玩家可以遮盖第二层

372
00:15:35,559 --> 00:15:38,349
但是第三层是来遮盖玩家的

373
00:15:38,349 --> 00:15:40,028
这样玩家从树后面走过

374
00:15:40,028 --> 00:15:41,109
我们就看不到玩家了

375
00:15:41,109 --> 00:15:42,489
因为他被树遮住了

376
00:15:42,489 --> 00:15:44,859
但是如果玩家从这里走

377
00:15:44,879 --> 00:15:46,559
玩家从这里走

378
00:15:46,559 --> 00:15:49,799
他应该是能遮住树根的啊

379
00:15:49,799 --> 00:15:52,980
所以有时候我们为了做的更细一点

380
00:15:52,980 --> 00:15:54,360
我们做一棵树的时候

381
00:15:54,360 --> 00:15:57,059
我们还要到图块到第二层

382
00:15:57,100 --> 00:16:00,789
把树下面的部分重新绘制一遍

383
00:16:00,789 --> 00:16:02,649
那这样的话结果就是什么了

384
00:16:02,649 --> 00:16:05,850
一个数由第二层和第三层组成

385
00:16:05,850 --> 00:16:07,940
这个树根那块是第二层

386
00:16:07,940 --> 00:16:09,559
上面部分是第三层

387
00:16:09,559 --> 00:16:13,019
也就是说玩家如果比如说走到这个位置啊

388
00:16:13,019 --> 00:16:13,950
走到这个位置

389
00:16:13,950 --> 00:16:16,679
玩家是可以遮盖住这个树根的啊

390
00:16:16,679 --> 00:16:19,950
让我们感觉a玩家现在走到这个树根前面了

391
00:16:19,950 --> 00:16:22,440
但是玩家如果从树后面走的话

392
00:16:22,440 --> 00:16:25,909
玩家是会被树给怎样给遮住的啊

393
00:16:25,909 --> 00:16:27,289
明白这个意思吧

394
00:16:27,289 --> 00:16:31,299
ok啊那么这里面很多东西啊

395
00:16:31,299 --> 00:16:32,080
比如说这个草

396
00:16:32,080 --> 00:16:34,759
你看这个草地是这几个格子

397
00:16:34,759 --> 00:16:35,299
对不对

398
00:16:35,299 --> 00:16:38,360
但是你不要认为只能绘制这样一点草啊

399
00:16:38,360 --> 00:16:45,129
你可以选择比如说把右边的框住啊

400
00:16:45,129 --> 00:16:48,120
不要动横向的哎

401
00:16:48,120 --> 00:16:51,330
大家可以看是不是直接绘制出来很多草地了

402
00:16:51,330 --> 00:16:52,559
那么这里注意啊

403
00:16:52,559 --> 00:16:53,580
教大家一个操作

404
00:16:53,580 --> 00:16:56,940
你按着右键从一个格子去框选

405
00:16:56,940 --> 00:16:59,159
那这时候也相当于选择啊

406
00:16:59,159 --> 00:17:02,250
比如说我右右键框选框选了一堆草地

407
00:17:02,250 --> 00:17:05,568
大家可以看现在我们的选择就是这一堆草地

408
00:17:05,568 --> 00:17:06,740
然后你往上

409
00:17:09,559 --> 00:17:11,118
鼠标老飘

410
00:17:11,240 --> 00:17:15,680
那这时候大家可以看看似是这九个格的这样的一个草地

411
00:17:15,680 --> 00:17:19,759
实际上我在这里很方便的就绘制了这么大片的这样的一个草地

412
00:17:19,759 --> 00:17:20,839
对不对啊

413
00:17:20,839 --> 00:17:24,299
那我们玩家就可以在这里移动了啊

414
00:17:27,160 --> 00:17:27,869
ok啊

415
00:17:27,869 --> 00:17:31,138
那这里大家自己去进行绘制就ok了

416
00:17:31,220 --> 00:17:33,680
呃基本的绘制我们就说完了啊

417
00:17:33,680 --> 00:17:35,089
基本的绘制就说完了

418
00:17:35,089 --> 00:17:38,429
那在这里我们再往下面再说一层

419
00:17:38,429 --> 00:17:40,348
这里面现在有三层啊

420
00:17:40,348 --> 00:17:41,489
都是普通的图层

421
00:17:41,489 --> 00:17:41,969
对不对

422
00:17:41,969 --> 00:17:46,700
普通图层我们在这里右键创建新的一层

423
00:17:48,359 --> 00:17:49,799
叫做对象层

424
00:17:49,799 --> 00:17:52,859
对象层呢我们给它拖拽到2~3之间

425
00:17:52,859 --> 00:17:55,779
我们就可以给它取名叫做玩家层

426
00:17:55,880 --> 00:17:56,960
这就是我说的

427
00:17:56,960 --> 00:17:58,910
实际上玩家应该在这一层

428
00:17:58,910 --> 00:18:01,910
它是被这个第三层遮住的

429
00:18:01,910 --> 00:18:05,750
那么它是能遮住下面这两层的一二层的啊

430
00:18:05,750 --> 00:18:07,388
那这个就是玩家层

431
00:18:07,388 --> 00:18:10,388
那么为什么玩家层要用这个对象呢

432
00:18:10,388 --> 00:18:11,648
对象层是干嘛的

433
00:18:11,648 --> 00:18:17,269
对象层其实就是我们在对象层里面绘制的内容啊

434
00:18:17,269 --> 00:18:19,819
它是可以去保存一些信息的

435
00:18:19,819 --> 00:18:23,029
这些信息我们可以通过代码获取的啊

436
00:18:23,029 --> 00:18:25,019
在这儿举一个很简单的例子啊

437
00:18:25,019 --> 00:18:26,579
选择了这个对象层以后

438
00:18:26,579 --> 00:18:28,720
在这里大家可以看对象层

439
00:18:28,720 --> 00:18:30,339
你可以绘制很多

440
00:18:30,339 --> 00:18:31,660
比如说一个句型啊

441
00:18:31,660 --> 00:18:34,259
或者说一个圆形啊

442
00:18:34,259 --> 00:18:36,960
对象层里面可以绘制很多形状啊

443
00:18:36,960 --> 00:18:41,720
但是在这里我们选择这一项插入图块啊

444
00:18:41,720 --> 00:18:44,660
我们不用形状来描述我们对呃

445
00:18:44,660 --> 00:18:46,378
来描述我们的对象啊

446
00:18:46,599 --> 00:18:49,180
我们用这个图块来描述我们的对象

447
00:18:49,180 --> 00:18:52,599
比如说在这里花就代表我们的一个对象啊

448
00:18:52,599 --> 00:18:54,138
我们在这里点一下

449
00:18:54,960 --> 00:18:57,119
那么这时候大家注意啊

450
00:18:57,119 --> 00:19:02,829
这个花这个花现在就变成了一个对象了

451
00:19:02,829 --> 00:19:04,898
这个对象注意啊

452
00:19:05,200 --> 00:19:08,980
当这个我们游戏运行的时候啊

453
00:19:08,980 --> 00:19:10,950
它是看不见的啊

454
00:19:10,950 --> 00:19:13,230
这个对象层里面的内容啊

455
00:19:13,230 --> 00:19:14,490
他都是看不见的

456
00:19:14,490 --> 00:19:16,589
它就是为了保存一些信息的

457
00:19:16,589 --> 00:19:18,509
那这里之所以显示一朵花

458
00:19:18,509 --> 00:19:21,069
只是为了我们在这里看起来方便啊

459
00:19:21,069 --> 00:19:22,929
他最终是不会被看见的

460
00:19:22,929 --> 00:19:25,209
那么我们这时候大家就会想了

461
00:19:25,209 --> 00:19:26,739
那我们要它有什么用

462
00:19:26,739 --> 00:19:28,319
我们选择这个按钮

463
00:19:28,319 --> 00:19:30,119
这个按钮是选择对象的啊

464
00:19:30,119 --> 00:19:32,440
我们就可以把这个对象来回拖拽了

465
00:19:33,440 --> 00:19:39,200
这个花对于在我们看来就是我们这个主角的这个起始位置啊

466
00:19:39,200 --> 00:19:43,029
所以我们在这里可以给他写个名称叫做

467
00:19:43,029 --> 00:19:46,500
比如说start position

468
00:19:47,839 --> 00:19:49,400
就叫起始位置

469
00:19:49,400 --> 00:19:51,380
大家可以看它就有个名称了

470
00:19:51,380 --> 00:19:52,069
起始点

471
00:19:52,069 --> 00:19:56,779
那其实这个东西啊真正运行游戏以后是看不见的啊

472
00:19:57,099 --> 00:20:00,039
那么在左边可以看到一些他的属性

473
00:20:00,039 --> 00:20:02,799
比如说它的xy和它的宽度高度

474
00:20:02,839 --> 00:20:05,059
那么对于他而言啊

475
00:20:05,059 --> 00:20:06,619
对于这种对象层而言

476
00:20:06,619 --> 00:20:10,138
最重要的就是下面有一栏叫做自定义属性

477
00:20:10,220 --> 00:20:11,420
自定义属性

478
00:20:11,420 --> 00:20:15,500
自定义属性我们是可以点一下加号去加这个自定义属性的

479
00:20:15,500 --> 00:20:17,720
在这里有一些属性我们可以添加

480
00:20:17,720 --> 00:20:20,170
比如说可以添加颜色属性啊

481
00:20:20,170 --> 00:20:22,690
浮点属性以及这个其他属性

482
00:20:22,690 --> 00:20:25,269
我们在这里一般用的也就是这个

483
00:20:25,269 --> 00:20:27,940
比如说布尔整形和字符串

484
00:20:27,940 --> 00:20:31,089
那在这里我们选择布尔类型

485
00:20:31,089 --> 00:20:35,900
我们叫做is plear点

486
00:20:35,900 --> 00:20:38,119
ok把它画上勾

487
00:20:38,119 --> 00:20:40,940
意思是我们现在给它加了一个布尔类型属性啊

488
00:20:40,940 --> 00:20:43,279
这个对象加了一个自定义的布尔类型属性

489
00:20:43,279 --> 00:20:44,809
叫做是否是玩家

490
00:20:44,809 --> 00:20:47,799
并且给他选择上了是啊

491
00:20:47,799 --> 00:20:51,309
那这样的话当前我们就创建了一个自定义的对象

492
00:20:51,309 --> 00:20:53,740
给他加了一个布尔类型的属性

493
00:20:53,740 --> 00:20:55,240
然后用它来标记了一下

494
00:20:55,240 --> 00:21:00,240
我们当前这个对象代表的是一个玩家

495
00:21:01,200 --> 00:21:03,599
那这时候我们整个地图就做完了

496
00:21:03,599 --> 00:21:04,859
当前地图的信息

497
00:21:04,859 --> 00:21:09,500
包括123层的纯的这个图片信息啊

498
00:21:09,500 --> 00:21:11,119
还有一层对象信息

499
00:21:11,119 --> 00:21:13,529
对象信息里面只有一个对象啊

500
00:21:13,529 --> 00:21:14,339
只有一个对象

501
00:21:14,339 --> 00:21:16,710
这个对象就是玩家的一个起始点

502
00:21:16,710 --> 00:21:18,750
如果我们有很多npc的话

503
00:21:18,750 --> 00:21:21,930
你可以把每个npc都给它做成一个对象啊

504
00:21:21,930 --> 00:21:25,819
也就是说实际上在游戏里面大家去想玩家位置

505
00:21:25,819 --> 00:21:27,469
每个npc的位置

506
00:21:27,469 --> 00:21:28,608
比如说有一个宝箱

507
00:21:28,608 --> 00:21:30,078
这个宝箱我们可以去操作

508
00:21:30,078 --> 00:21:31,680
那宝箱的话

509
00:21:31,680 --> 00:21:34,079
这些其实可以去交互的东西

510
00:21:34,079 --> 00:21:37,240
往往我们都会把它放到对象层里面

511
00:21:38,559 --> 00:21:43,720
呃其实这个大家真正如果真正去做这种类型游戏的话啊

512
00:21:43,720 --> 00:21:44,799
做一做就明白了

513
00:21:44,799 --> 00:21:48,839
但实际上这种类型游戏现在很少人去

514
00:21:48,839 --> 00:21:50,339
一个是很少人去做啊

515
00:21:50,339 --> 00:21:51,240
这种类型的游戏

516
00:21:51,240 --> 00:21:55,619
第二个这种游戏现在有有一些专用的这种游戏引擎啊

517
00:21:55,619 --> 00:21:57,750
他就是专门去做这种游戏的

518
00:21:57,750 --> 00:21:58,049
呃

519
00:21:58,049 --> 00:22:01,410
做起来比我们这个coos省事多了啊

520
00:22:01,410 --> 00:22:03,450
你不用写代码都能做出这样一个游戏

521
00:22:03,450 --> 00:22:04,279
所以

522
00:22:06,039 --> 00:22:10,660
现在用我们这个游戏引擎做这种游戏的啊

523
00:22:10,660 --> 00:22:12,619
其实少很多了啊

524
00:22:13,160 --> 00:22:14,519
嗯ok啊

525
00:22:14,640 --> 00:22:19,289
那么我们现在把这个当前的游戏地图保存一下

526
00:22:19,289 --> 00:22:20,819
保存一下

527
00:22:20,940 --> 00:22:25,319
现在我们一共有这么三个文件了啊

528
00:22:25,319 --> 00:22:27,339
一个是图块信息啊

529
00:22:27,339 --> 00:22:29,079
一个是默认的图片信息

530
00:22:29,079 --> 00:22:31,210
还有一个生成的地图文件

531
00:22:31,210 --> 00:22:33,039
还有一个生成的地图文件

532
00:22:33,039 --> 00:22:35,160
那现在我们把它关掉

533
00:22:35,160 --> 00:22:39,289
创建一个新的这样的一个场景

534
00:22:39,289 --> 00:22:41,329
那我们接下来下节课

535
00:22:41,329 --> 00:22:46,539
我们就开始在场景里面去导入我们的这个地图进行使用了啊

536
00:22:46,539 --> 00:22:48,160
ok那我们这节课先这么多

537
00:22:48,160 --> 00:22:52,039
大家先把这个地图画好


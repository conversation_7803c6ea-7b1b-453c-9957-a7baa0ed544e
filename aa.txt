1. 一键重命名.bat ⭐ (推荐)
最简单易用，双击即可运行
自动查找并重命名所有包含"中文（自动生成）"的文件
无需确认，直接执行重命名
2. 快速重命名.bat
使用PowerShell引擎，功能强大
会显示重命名预览，需要确认后执行
有详细的成功/失败统计
3. 批量重命名-删除中文自动生成.bat
纯批处理脚本，兼容性最好
详细显示每个文件的重命名过程
有完整的错误处理
4. 通用批量重命名工具.bat
可以自定义要删除的文本内容
不仅限于"中文（自动生成）"
适合处理其他类似的批量重命名需求
使用方法：
将任意一个.bat文件复制到包含需要重命名文件的目录
双击运行.bat文件
按照提示操作即可
建议：
日常使用推荐"一键重命名.bat" - 最快速简单
需要预览确认推荐"快速重命名.bat" - 更安全
处理其他文本推荐"通用批量重命名工具.bat" - 最灵活
这样你以后遇到类似的文件重命名需求时，只需要双击对应的.bat文件就可以了！
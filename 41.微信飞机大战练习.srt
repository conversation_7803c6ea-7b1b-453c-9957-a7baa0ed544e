1
00:00:19,179 --> 00:00:21,250
我们用的素材就是这个微信

2
00:00:21,250 --> 00:00:24,719
以前有一个特别火的一个爆款小游戏啊

3
00:00:24,719 --> 00:00:26,039
微信啊

4
00:00:26,039 --> 00:00:26,910
是不是

5
00:00:26,910 --> 00:00:29,070
那么我们用这个素材来做一下

6
00:00:29,070 --> 00:00:31,920
那么大家创建好一个空的一个工程

7
00:00:31,920 --> 00:00:34,179
然后咱们开始做

8
00:00:36,100 --> 00:00:37,890
首先资源啊

9
00:00:42,259 --> 00:00:45,579
咱们放到咱们的这个群共享里面

10
00:00:45,579 --> 00:00:48,259
然后大家从群共享里面去找就可以了

11
00:00:48,880 --> 00:00:51,399
那么我们看一下啊

12
00:00:53,299 --> 00:00:56,590
嗯素材首先看一下里面有几个素材

13
00:00:56,590 --> 00:00:59,259
然后有背景是不是有子弹

14
00:00:59,259 --> 00:01:00,820
有这个敌机爆炸的

15
00:01:00,820 --> 00:01:02,179
还有这个敌机

16
00:01:02,240 --> 00:01:05,659
还有这个自己爆炸的啊

17
00:01:05,659 --> 00:01:08,259
然后有这个玩家自己的飞机

18
00:01:08,640 --> 00:01:09,840
那在这儿啊

19
00:01:09,840 --> 00:01:13,519
我们就是尽量都给他用上啊

20
00:01:13,519 --> 00:01:15,319
那首先我们一点点来做啊

21
00:01:15,319 --> 00:01:16,760
那么这个微信飞机啊

22
00:01:16,760 --> 00:01:18,590
首先它是一个竖屏的

23
00:01:18,590 --> 00:01:20,500
我们先给它改成竖屏的

24
00:01:20,500 --> 00:01:25,730
那么它的这个背景是480x852的一个大小

25
00:01:25,730 --> 00:01:28,140
那我们在这里找到canvas

26
00:01:29,640 --> 00:01:31,730
canvas是我们的屏幕

27
00:01:31,730 --> 00:01:36,599
那我们把屏幕修改成480乘以一个

28
00:01:36,599 --> 00:01:38,899
比如说我们修改乘以个800

29
00:01:43,819 --> 00:01:46,450
那么修改好了以后啊

30
00:01:46,450 --> 00:01:47,650
修改好了以后

31
00:01:47,650 --> 00:01:52,159
那么在这里我们就可以把背景拖上来看一下效果了啊

32
00:01:52,159 --> 00:01:54,759
我就可以拖上来看一下这个背景的效果了

33
00:01:54,759 --> 00:01:55,899
我们把背景拖上来

34
00:01:57,278 --> 00:02:00,019
然后把背景放到中间来

35
00:02:07,219 --> 00:02:09,379
唉这样的话对得比较齐

36
00:02:09,399 --> 00:02:10,899
那么大家可以看一下啊

37
00:02:10,899 --> 00:02:14,199
我一个背景现在是这个稍微超出来一截

38
00:02:14,199 --> 00:02:15,349
当然这个无所谓

39
00:02:15,349 --> 00:02:16,310
那么注意啊

40
00:02:16,310 --> 00:02:18,199
像这个微信啊

41
00:02:18,199 --> 00:02:20,990
比如说这种飞机大战这种类型的项目

42
00:02:20,990 --> 00:02:23,900
就是我这个玩家在这里

43
00:02:25,219 --> 00:02:28,460
但是怎样才能让玩家感觉我们在飞呢

44
00:02:28,460 --> 00:02:32,289
就是因为你飞机肯定是不能真正的往天上飞

45
00:02:32,289 --> 00:02:33,310
一会儿飞出屏幕了

46
00:02:33,310 --> 00:02:33,879
对不对

47
00:02:33,879 --> 00:02:35,830
所以为了给人感觉飞机在飞

48
00:02:35,830 --> 00:02:38,710
我们就是让这个背景向下移动

49
00:02:38,710 --> 00:02:39,969
向反方向移动

50
00:02:39,969 --> 00:02:42,400
这样的话给人的感觉就是飞机在飞了

51
00:02:42,400 --> 00:02:45,530
那所以我们要做的事就是让背景往下移动

52
00:02:45,530 --> 00:02:47,960
但是背景一旦向下移动

53
00:02:48,558 --> 00:02:50,438
那么这个大家可以看这个背景

54
00:02:50,438 --> 00:02:51,218
这边就空了

55
00:02:51,218 --> 00:02:51,848
对不对

56
00:02:51,848 --> 00:02:52,748
怎么办

57
00:02:52,748 --> 00:02:56,689
我们最好的方式是再来一个背景

58
00:02:59,419 --> 00:03:01,870
两个背景那么拼接起来

59
00:03:01,870 --> 00:03:03,490
然后往下走

60
00:03:03,490 --> 00:03:06,250
这样的话当我们显示第二个背景的时候

61
00:03:06,250 --> 00:03:08,319
我们再把第一个背景给踩死

62
00:03:08,459 --> 00:03:10,979
放放到上面来啊

63
00:03:10,979 --> 00:03:12,530
那这样的话大家去想

64
00:03:12,530 --> 00:03:16,370
他是不是就变成一个一直循环的这样的一个背景了啊

65
00:03:16,370 --> 00:03:18,860
两个背景一直是这样挨着往下走

66
00:03:18,860 --> 00:03:21,620
然后只要哪一个背景出去诶

67
00:03:21,620 --> 00:03:23,629
我就让他立刻跳到上面了

68
00:03:23,629 --> 00:03:24,680
对不对

69
00:03:24,879 --> 00:03:27,860
那么这样的话我们给它取个名字好了

70
00:03:27,860 --> 00:03:29,240
一个是背景1号

71
00:03:29,240 --> 00:03:30,919
一个是背景2号

72
00:03:30,919 --> 00:03:34,680
那么1号我们放到零的位置上

73
00:03:36,218 --> 00:03:37,679
啊不能在零的位置

74
00:03:37,679 --> 00:03:41,800
大概在比如说240x410的位置上

75
00:03:41,800 --> 00:03:44,590
那么背景二在哪个位置上

76
00:03:44,590 --> 00:03:47,769
我们背景二先不要管他们先放出去

77
00:03:47,769 --> 00:03:50,400
首先啊如果没有两个背景的话

78
00:03:51,338 --> 00:03:51,929
呃

79
00:03:51,929 --> 00:03:54,899
我们单独直接把它们都放最外面啊

80
00:03:54,899 --> 00:03:55,618
并不太好

81
00:03:55,618 --> 00:03:58,860
我们最好创建一个空的节点啊

82
00:03:58,860 --> 00:04:00,090
创建一个空的节点

83
00:04:00,090 --> 00:04:04,229
这个空的节点给它取个名字叫做背景

84
00:04:04,229 --> 00:04:05,699
那我为什么刚才创建

85
00:04:05,699 --> 00:04:07,419
现在它里面创建了

86
00:04:07,479 --> 00:04:09,979
而不是直接在外面创建的

87
00:04:10,000 --> 00:04:12,699
我直接在背景一上面创建空间点了

88
00:04:12,699 --> 00:04:13,210
对不对

89
00:04:13,210 --> 00:04:16,329
那是因为如果我在背景音上面创建完空间点

90
00:04:16,329 --> 00:04:21,660
它默认的这个这个点的中心就在我们背景一的这个中心上

91
00:04:21,959 --> 00:04:24,778
而如果我在最外层创建空间点

92
00:04:24,778 --> 00:04:29,220
大家可以看它的这个点默认是在左下角啊

93
00:04:29,220 --> 00:04:30,300
所以就是这样的区别

94
00:04:30,300 --> 00:04:32,939
所以我在背景一下面创建一个新的节点

95
00:04:32,939 --> 00:04:34,019
再把它拖出来

96
00:04:34,019 --> 00:04:39,279
那么这个点默认就在我们这个背景音的这个中心了啊

97
00:04:39,279 --> 00:04:43,139
那么这样的话我们创建完以后

98
00:04:43,139 --> 00:04:45,769
把背景音和背景二都拖都拖上来

99
00:04:45,769 --> 00:04:48,230
也就是说背景一二都归他去管

100
00:04:48,230 --> 00:04:49,670
那这样的话比如说背景做完

101
00:04:49,670 --> 00:04:50,629
我把它缩起来

102
00:04:50,629 --> 00:04:54,829
这样的话整个这个界面是不是都非常非常好看啊

103
00:04:54,829 --> 00:04:56,120
那我们看一下背景一

104
00:04:56,120 --> 00:04:58,040
当前的坐标是零零

105
00:04:58,040 --> 00:04:58,639
对不对

106
00:04:58,639 --> 00:05:03,050
那么背景二的坐标我们我们看一下每个这个图

107
00:05:03,050 --> 00:05:05,240
它的这个高是852

108
00:05:05,240 --> 00:05:08,829
所以背景二我们就给他一个852

109
00:05:08,829 --> 00:05:12,189
这样的话它就应该是刚好和背景音

110
00:05:12,189 --> 00:05:14,680
你看拼接起来对不对

111
00:05:14,680 --> 00:05:16,029
因为每一个高度

112
00:05:16,029 --> 00:05:18,550
每一个图片高度是852

113
00:05:18,550 --> 00:05:20,139
那我背景一如果是零

114
00:05:20,139 --> 00:05:23,149
那背景二给他刚好852

115
00:05:23,149 --> 00:05:27,680
那么就是呃一个图片的一个高度啊

116
00:05:27,680 --> 00:05:28,759
一个图片的高度差

117
00:05:28,759 --> 00:05:30,740
那这样的话他们刚好拼接起来

118
00:05:30,740 --> 00:05:33,019
那么接下来我们就要做一件事了

119
00:05:33,019 --> 00:05:34,459
让他们往下开始运动

120
00:05:34,459 --> 00:05:35,089
对不对

121
00:05:35,089 --> 00:05:39,319
那这个运动我们就要开始去编写我们的脚本了

122
00:05:39,360 --> 00:05:44,319
那么我们尽量先按ctrl s保存一下当前的场景

123
00:05:44,959 --> 00:05:50,680
然后我们创建一个新的文件夹叫脚本

124
00:05:50,680 --> 00:05:53,660
在脚本里面我们去编写我们的脚本

125
00:05:53,718 --> 00:05:54,619
脚本的话

126
00:05:54,619 --> 00:05:56,899
首先我们有个这个bg ctrl啊

127
00:05:56,899 --> 00:05:58,908
就是去负责这个背景的

128
00:05:58,908 --> 00:06:00,738
然后我们把它挂载到哪里

129
00:06:00,738 --> 00:06:04,430
挂载到我们的这个空节点上就可以了啊

130
00:06:04,430 --> 00:06:07,129
通过它我们来管理里面的两个子节点

131
00:06:07,129 --> 00:06:09,410
因为两个子节点的逻辑都是一样的

132
00:06:09,410 --> 00:06:11,350
都是向下运动啊

133
00:06:11,350 --> 00:06:15,910
然后出去以后就就自己自己跑到这个最上面来啊

134
00:06:15,910 --> 00:06:16,990
所以他逻辑一样

135
00:06:16,990 --> 00:06:19,279
那我们就直接放到它附体上

136
00:06:20,060 --> 00:06:21,680
如果逻辑不一样啊

137
00:06:21,680 --> 00:06:22,939
两个子物体逻辑不一样

138
00:06:22,939 --> 00:06:25,819
你就要给两个这个子物体单独去写脚本了

139
00:06:25,819 --> 00:06:26,660
对不对

140
00:06:27,500 --> 00:06:29,199
我们把这个属性去掉

141
00:06:29,199 --> 00:06:30,639
这些没必要用得上

142
00:06:30,639 --> 00:06:33,459
然后在这里我们把这个名字改一下

143
00:06:33,459 --> 00:06:35,220
叫做bg control

144
00:06:37,480 --> 00:06:38,379
ok

145
00:06:43,180 --> 00:06:45,089
那我们就这个样子啊

146
00:06:45,089 --> 00:06:47,879
那么这个样子比较好看一点

147
00:06:47,879 --> 00:06:50,600
那首先啊我们这个要做背景移动

148
00:06:50,860 --> 00:06:52,720
移动的话肯定要一直移动

149
00:06:52,720 --> 00:06:54,399
所以我们要放在update里面

150
00:06:54,399 --> 00:06:55,540
每一帧都要移动

151
00:06:55,540 --> 00:06:56,180
对不对

152
00:06:56,180 --> 00:06:57,379
那么移动的是谁

153
00:06:57,379 --> 00:06:58,339
是我的子物体

154
00:06:58,339 --> 00:06:59,600
所以我要在这里干嘛

155
00:06:59,600 --> 00:07:04,139
便利子物体其实就是子物体

156
00:07:04,139 --> 00:07:04,620
就是什么呀

157
00:07:04,620 --> 00:07:07,980
就是我们的背景背景

158
00:07:08,420 --> 00:07:09,379
对不对

159
00:07:09,379 --> 00:07:12,019
那我们在这里循环便利一下

160
00:07:12,079 --> 00:07:13,819
let bg node

161
00:07:13,819 --> 00:07:15,139
两个子物体

162
00:07:15,139 --> 00:07:18,860
两个背景的node怎样便利用of去便利啊

163
00:07:18,860 --> 00:07:20,160
这个不要忘了啊

164
00:07:23,259 --> 00:07:25,499
然后我们在这个里面啊

165
00:07:25,499 --> 00:07:27,000
然后我们在这个里面

166
00:07:29,019 --> 00:07:29,939
去写内容

167
00:07:29,939 --> 00:07:32,038
去移动移动的话很简单

168
00:07:32,038 --> 00:07:36,329
无非就是更改这个节点的什么坐标y坐标啊

169
00:07:36,329 --> 00:07:39,180
更改坐标我们可以直接set position

170
00:07:39,180 --> 00:07:42,019
也可以直接修改y

171
00:07:43,040 --> 00:07:45,759
但是你这里用position.one

172
00:07:45,759 --> 00:07:48,339
有时候修改是不在呃

173
00:07:48,339 --> 00:07:49,300
是没什么效果的

174
00:07:49,300 --> 00:07:56,839
所以我们在这里就是用node.one减等于因为我们是向下移动

175
00:07:56,839 --> 00:07:59,629
所以减等于我们如果给个50

176
00:07:59,629 --> 00:08:04,139
那么意思现在就是每一帧移动50像素

177
00:08:04,139 --> 00:08:06,000
那么大家想每帧移动50像素

178
00:08:06,000 --> 00:08:07,459
这个速度是不是太快了

179
00:08:07,459 --> 00:08:09,620
而且每个设备性能不一样

180
00:08:09,620 --> 00:08:11,389
大家跑的帧率是不一样的

181
00:08:11,389 --> 00:08:13,879
那么结果就是跑到不同设备上

182
00:08:13,879 --> 00:08:16,439
它的背景移动速度速度也不一样

183
00:08:16,439 --> 00:08:17,579
为了统一

184
00:08:17,579 --> 00:08:25,300
所以我们要把这个真单位就是从这个每帧移动50啊

185
00:08:25,300 --> 00:08:27,899
移动50像素改成什么样

186
00:08:28,279 --> 00:08:31,819
改成每秒移动50啊

187
00:08:31,819 --> 00:08:34,908
所以在这里实际上怎么样去改单位

188
00:08:34,908 --> 00:08:36,828
就是乘以下我们的这个dt

189
00:08:36,828 --> 00:08:39,409
只要把我们的这个计算的真啊

190
00:08:39,409 --> 00:08:41,929
以真单位乘一下我们传进来的dt

191
00:08:41,929 --> 00:08:46,360
因为dt实际上就是两针之间的一个间隔啊

192
00:08:46,360 --> 00:08:50,019
两针之间的间隔它是很小的一个浮点数啊

193
00:08:50,019 --> 00:08:51,700
所以我们乘以一下这个间隔

194
00:08:51,700 --> 00:08:54,490
就乘以下浮点数得到的结果

195
00:08:54,490 --> 00:08:58,450
那么其实就是把真单位改成了这个秒的单位啊

196
00:08:58,450 --> 00:09:00,830
就本来是每帧移动50像素

197
00:09:00,830 --> 00:09:02,690
现在就是每秒移动50像素

198
00:09:02,690 --> 00:09:06,460
这样的话就是不同的设备之间它也同步了啊

199
00:09:06,460 --> 00:09:08,320
他也同步了嗯

200
00:09:09,580 --> 00:09:11,080
其实这个道理很简单啊

201
00:09:11,080 --> 00:09:14,350
但是这边也没什么太多去说的啊

202
00:09:14,350 --> 00:09:19,419
就是最简单最简单的以后你只要去写这个速度之类的啊

203
00:09:19,419 --> 00:09:20,799
你要用秒做单位啊

204
00:09:20,799 --> 00:09:23,419
你就要把你的速度乘以一下什么呀

205
00:09:23,419 --> 00:09:25,820
乘以一下这个dt啊

206
00:09:25,820 --> 00:09:26,879
就ok了

207
00:09:27,080 --> 00:09:28,580
然后我们先这样

208
00:09:28,580 --> 00:09:29,480
我们先看一下

209
00:09:29,480 --> 00:09:29,960
运行一下

210
00:09:29,960 --> 00:09:31,120
看一下效果

211
00:09:33,519 --> 00:09:35,259
我们在这里运行

212
00:09:35,320 --> 00:09:36,399
那我们看一下

213
00:09:36,399 --> 00:09:37,899
现在就已经向下运行了

214
00:09:37,899 --> 00:09:38,320
对不对

215
00:09:38,320 --> 00:09:41,769
如果有飞机就会感觉这个飞机在往上走了啊

216
00:09:41,769 --> 00:09:43,000
这是一个视觉差

217
00:09:43,000 --> 00:09:44,379
是不是视觉差

218
00:09:44,379 --> 00:09:45,759
但是现在有个问题啊

219
00:09:45,759 --> 00:09:47,409
就是我们飞一会儿以后

220
00:09:47,409 --> 00:09:50,750
这个背景如果移动到背景二

221
00:09:50,750 --> 00:09:52,100
然后还出来了以后

222
00:09:52,100 --> 00:09:54,230
你就会看到这个背景诶

223
00:09:54,230 --> 00:09:55,070
完全出去了

224
00:09:55,070 --> 00:09:55,549
对不对

225
00:09:55,549 --> 00:09:57,649
你再移动一会就会发现变成这样了

226
00:09:57,649 --> 00:09:58,490
上面是空的了

227
00:09:58,490 --> 00:09:59,769
就变成黑屏了

228
00:09:59,769 --> 00:10:00,490
怎么办

229
00:10:00,490 --> 00:10:02,169
我们这儿就要去写代码

230
00:10:02,169 --> 00:10:05,769
如果第一个背景移动出了屏幕啊

231
00:10:05,769 --> 00:10:07,570
就当第一个背景移动出了屏幕

232
00:10:07,570 --> 00:10:08,839
我把它放上去

233
00:10:09,019 --> 00:10:11,090
那么也就是大家看一下

234
00:10:11,090 --> 00:10:15,980
大概就是在-5的800 850左右啊

235
00:10:15,980 --> 00:10:17,419
我们就给他写代码

236
00:10:17,419 --> 00:10:21,769
如果它数值就是它的y轴数值小于负的850

237
00:10:21,769 --> 00:10:23,870
我们就让它移动到上面就可以了

238
00:10:25,250 --> 00:10:30,980
那么这样的话我们在这里去写代码嗯

239
00:10:32,659 --> 00:10:41,919
如果bg.bg note.y小于了负的850啊

240
00:10:41,919 --> 00:10:42,859
850

241
00:10:43,240 --> 00:10:44,559
那么注意啊

242
00:10:44,559 --> 00:10:46,120
小于负的850

243
00:10:46,120 --> 00:10:49,240
我们就让他到最上层

244
00:10:49,240 --> 00:10:50,500
到最最上层

245
00:10:50,500 --> 00:10:52,099
其实非常简单

246
00:10:52,179 --> 00:10:56,019
你就让他加等于他自己的两个高

247
00:10:56,019 --> 00:10:58,919
一个高是852

248
00:10:58,919 --> 00:11:03,230
那么两个高就是852x2啊

249
00:11:03,230 --> 00:11:06,200
就是让他离移动他两个高的这样的一个位置

250
00:11:06,200 --> 00:11:07,220
那大家想一想啊

251
00:11:07,220 --> 00:11:08,269
你仔细看这个

252
00:11:08,269 --> 00:11:10,480
比如说他在这个位置

253
00:11:10,480 --> 00:11:13,240
如果我们加上他两个高的位置是多少

254
00:11:13,240 --> 00:11:14,860
首先加上一个高的位置

255
00:11:14,860 --> 00:11:16,960
是在这里和第二个图重复了

256
00:11:16,960 --> 00:11:18,820
那么加上它本身两个高的位置

257
00:11:18,820 --> 00:11:20,679
是不是就刚好到了这里了啊

258
00:11:20,679 --> 00:11:22,019
那就拼接起来了

259
00:11:23,000 --> 00:11:24,220
那么我们来看啊

260
00:11:24,220 --> 00:11:25,259
我们来看

261
00:11:30,100 --> 00:11:31,720
这边应该已经写完了啊

262
00:11:31,720 --> 00:11:32,440
背景写完了

263
00:11:32,440 --> 00:11:34,179
我们来保存一下

264
00:11:34,879 --> 00:11:37,639
这时候我们在这边去运行啊

265
00:11:37,639 --> 00:11:38,269
运行

266
00:11:38,269 --> 00:11:40,039
那么大家仔细看

267
00:11:40,360 --> 00:11:45,490
这个东西已经变成一个无限循环的这样的一个背景了啊

268
00:11:45,490 --> 00:11:48,339
所以这个无线我们就是用两张图去做的

269
00:11:58,340 --> 00:11:59,480
啊他怎么样走

270
00:11:59,480 --> 00:12:00,320
他也玩不了啊

271
00:12:00,320 --> 00:12:01,279
他一直在循环

272
00:12:01,279 --> 00:12:02,600
第一个图一旦出了

273
00:12:02,600 --> 00:12:03,440
完全出了屏幕

274
00:12:03,440 --> 00:12:06,370
它会立刻填到这个最上面啊

275
00:12:06,370 --> 00:12:09,220
那么变成一个无限滚动的了啊

276
00:12:09,220 --> 00:12:13,840
那太多太多这种2d游戏都是用这种方式去做的

277
00:12:13,840 --> 00:12:16,899
这样的一个无限循环的这样的一个背景啊

278
00:12:20,559 --> 00:12:21,419
ok搞定

279
00:12:22,799 --> 00:12:23,940
那么我们继续来

280
00:12:23,940 --> 00:12:26,409
我们继续来嗯

281
00:12:26,409 --> 00:12:28,259
回到我们代码这边

282
00:12:28,259 --> 00:12:31,440
那么代码背景这边就算暂时完成了啊

283
00:12:31,440 --> 00:12:33,059
那么我们在这边啊

284
00:12:33,059 --> 00:12:36,500
我们在这边就去创建我们的玩家啊

285
00:12:36,500 --> 00:12:38,419
背景暂时就先不管它了啊

286
00:12:38,419 --> 00:12:40,730
创建我们的这个玩家hero

287
00:12:40,730 --> 00:12:42,639
改个名字叫y好了

288
00:12:44,639 --> 00:12:49,539
玩家玩家我们目前先放到大概这样一个位置

289
00:12:49,659 --> 00:12:55,250
那因为我们这个引擎很多都主要是做这种呃

290
00:12:55,250 --> 00:12:57,470
比如说做做出来的游戏

291
00:12:57,470 --> 00:12:58,909
可能是在这个手机上跑

292
00:12:58,909 --> 00:13:01,340
或者是说做这个微信小游戏

293
00:13:01,340 --> 00:13:04,659
但是它的运行环境大多都是在手机上面

294
00:13:04,659 --> 00:13:05,379
对不对

295
00:13:05,379 --> 00:13:07,899
那所以我们在这里做的这个操作

296
00:13:07,899 --> 00:13:11,360
就是唉我们手指按住这个飞机

297
00:13:11,360 --> 00:13:13,100
然后我们左右拖拽这个飞机

298
00:13:13,100 --> 00:13:16,259
就能跟随我们手指的位置进行移动啊

299
00:13:16,259 --> 00:13:17,580
其他的限制先不给

300
00:13:17,580 --> 00:13:20,480
现在就做一个最简单的这样的一个操作啊

301
00:13:20,480 --> 00:13:22,340
这个飞机正常在这里一直飞

302
00:13:22,340 --> 00:13:23,779
我们现在手指按住它

303
00:13:23,779 --> 00:13:26,620
左右拖拽它就会跟着我们手指移动啊

304
00:13:26,620 --> 00:13:27,789
那怎样去做

305
00:13:27,789 --> 00:13:29,049
首先有玩家了

306
00:13:29,049 --> 00:13:31,578
我们也要创建一个玩家的脚本

307
00:13:32,720 --> 00:13:36,318
flayer control给这个玩家

308
00:13:38,559 --> 00:13:40,318
打开这个脚本

309
00:13:42,700 --> 00:13:44,320
那么这个脚本的话

310
00:13:44,320 --> 00:13:47,299
我们在这里作为玩家

311
00:13:47,299 --> 00:13:49,379
我们看一下需要什么操作

312
00:13:52,559 --> 00:13:55,809
那么首先它有它会往下啊

313
00:13:55,809 --> 00:13:57,309
它会有这样的一个移动

314
00:13:57,309 --> 00:13:59,529
那我们先先不管其他的功能

315
00:13:59,529 --> 00:14:00,970
我们先写移动好了啊

316
00:14:00,970 --> 00:14:02,120
先写移动好

317
00:14:02,559 --> 00:14:06,318
移动的话就要在update里面我们去做了

318
00:14:06,759 --> 00:14:07,240
呃

319
00:14:07,240 --> 00:14:10,580
我们想想update啊

320
00:14:10,580 --> 00:14:13,279
其实我们在这个start里面做就ok啊

321
00:14:13,279 --> 00:14:17,019
我们在start里面做就ok移动

322
00:14:17,019 --> 00:14:19,210
因为我们在这里监听事件的啊

323
00:14:19,210 --> 00:14:21,909
我们通过这个事件监听的方法来做

324
00:14:21,909 --> 00:14:23,230
那在这里移动

325
00:14:23,230 --> 00:14:27,360
let我们就要声明这个了

326
00:14:27,360 --> 00:14:29,299
let's see等于this

327
00:14:31,039 --> 00:14:33,620
啊因为我们马上就要写什么了

328
00:14:33,620 --> 00:14:38,919
写昨天我们上一节课讲的这个事件内容了

329
00:14:38,919 --> 00:14:40,120
note点啊

330
00:14:40,120 --> 00:14:42,339
我们要监听一个事件

331
00:14:42,620 --> 00:14:44,120
事件叫什么

332
00:14:44,120 --> 00:14:44,720
cc点

333
00:14:44,720 --> 00:14:46,190
node点

334
00:14:46,190 --> 00:14:47,659
invent type点儿

335
00:14:47,659 --> 00:14:48,620
touch mood

336
00:14:48,620 --> 00:14:53,240
我们用这个手指在屏幕上移动这样的一个事件

337
00:14:54,059 --> 00:14:54,899
function

338
00:15:05,029 --> 00:15:06,200
为什么要这样去写

339
00:15:06,200 --> 00:15:06,980
我再说一遍啊

340
00:15:06,980 --> 00:15:09,399
因为你在这里直接调出this啊

341
00:15:09,399 --> 00:15:11,349
你想调用必须调用self

342
00:15:11,349 --> 00:15:12,698
对不对啊

343
00:15:12,698 --> 00:15:15,169
这个一定要注意啊

344
00:15:17,340 --> 00:15:20,759
那么我们这节课就可以给大家升级了啊

345
00:15:20,759 --> 00:15:22,950
那我们教大家另外一种方式啊

346
00:15:22,950 --> 00:15:25,789
我们当时说匿名函数的时候啊

347
00:15:25,789 --> 00:15:26,690
其实说了两种

348
00:15:26,690 --> 00:15:27,710
这是其中一种

349
00:15:27,710 --> 00:15:29,210
还有另外一种更简单的

350
00:15:29,210 --> 00:15:30,019
对不对

351
00:15:30,019 --> 00:15:31,460
那我们现在用另外一种

352
00:15:31,460 --> 00:15:32,818
大家试一试啊

353
00:15:33,059 --> 00:15:34,470
如果我现在用

354
00:15:34,470 --> 00:15:35,940
我先把它里面的东西删了

355
00:15:35,940 --> 00:15:37,139
我用另外一种方式

356
00:15:37,139 --> 00:15:40,879
就是用括号等于箭头

357
00:15:43,120 --> 00:15:44,240
inv

358
00:15:45,519 --> 00:15:47,409
那当然借着这种方式吧

359
00:15:47,409 --> 00:15:50,139
这种方式啊也是个匿名函数啊

360
00:15:50,139 --> 00:15:51,610
也是个匿名函数

361
00:15:51,610 --> 00:15:53,320
那么这时候从里面掉

362
00:15:53,320 --> 00:15:56,919
大家发现是可以调到node的啊

363
00:15:56,919 --> 00:16:01,529
所以说如果大家知道了那个function的问题

364
00:16:01,529 --> 00:16:05,399
然后必须啊function内部是调不到这个this的啊

365
00:16:05,399 --> 00:16:06,759
是不能使用this的

366
00:16:06,759 --> 00:16:09,179
我们必须给它传递一下啊

367
00:16:09,179 --> 00:16:10,500
声明一个我们自己的变量

368
00:16:10,500 --> 00:16:13,019
然后通过我们自己的变量访问this

369
00:16:13,019 --> 00:16:15,700
那么知道了这个特性以后

370
00:16:15,700 --> 00:16:17,379
然后我们现在从现在开始

371
00:16:17,379 --> 00:16:20,068
如果我们去使用另外一种匿名函数

372
00:16:20,068 --> 00:16:22,649
那么这种匿名函数就不需要这么麻烦了

373
00:16:22,649 --> 00:16:25,350
我们可以直接this.node啊

374
00:16:25,350 --> 00:16:26,669
就可以去使用了啊

375
00:16:26,669 --> 00:16:28,470
你看this.node啊

376
00:16:28,470 --> 00:16:32,230
比如说点set position

377
00:16:32,230 --> 00:16:36,438
你看就可以去编写我们的这个正常的代码了啊

378
00:16:36,799 --> 00:16:39,919
那么他这里报错了这个位置啊

379
00:16:39,919 --> 00:16:42,080
就是这个坐标我们应该给他一个坐标

380
00:16:42,080 --> 00:16:43,519
坐标是什么

381
00:16:43,980 --> 00:16:47,879
我们就让飞机的坐标和我们手指的坐标重合

382
00:16:47,879 --> 00:16:50,089
这就是一个最简单的跟随啊

383
00:16:50,089 --> 00:16:51,619
invent点

384
00:16:51,619 --> 00:16:53,479
怎样得到手指的坐标

385
00:16:53,479 --> 00:16:54,349
不要忘了啊

386
00:16:54,349 --> 00:16:56,649
get location

387
00:16:56,649 --> 00:16:58,019
括号

388
00:16:59,740 --> 00:17:01,480
如果你最后发现没跟随

389
00:17:01,480 --> 00:17:03,700
你可以看一下是不是哪里敲错了啊

390
00:17:06,828 --> 00:17:07,519
跟我说

391
00:17:07,519 --> 00:17:09,588
我的代码跟老师的一样是吧

392
00:17:09,588 --> 00:17:12,640
为什么为什么我的就出错了

393
00:17:12,640 --> 00:17:16,409
是不是那太多同学每次都问我这个问题了

394
00:17:16,409 --> 00:17:19,299
我就说你你自己去找肯定不一样啊

395
00:17:19,299 --> 00:17:20,380
你自己去调试

396
00:17:20,380 --> 00:17:22,210
最后肯定能找出你的错误

397
00:17:22,210 --> 00:17:25,460
然后每一次都跟他们说很多遍

398
00:17:25,460 --> 00:17:26,240
他们就不信

399
00:17:26,240 --> 00:17:29,359
然后最后总之还是能找出是自己的错误啊

400
00:17:29,359 --> 00:17:29,839
不是这样

401
00:17:29,839 --> 00:17:31,220
错了就是那错了

402
00:17:32,160 --> 00:17:37,079
所以说这个东西你要相信咱们这个程序是不是你跟着我去敲

403
00:17:37,079 --> 00:17:39,329
只要你的代码是对的啊

404
00:17:39,329 --> 00:17:42,900
这个编译器啊一定不可能给你搞错了

405
00:17:42,900 --> 00:17:43,839
对不对

406
00:17:44,140 --> 00:17:46,180
不要对自己太过自信啊

407
00:17:46,180 --> 00:17:47,799
出了错自己一定要去检查

408
00:17:50,900 --> 00:17:52,880
那ok我们现在就写这么一点

409
00:17:52,880 --> 00:17:54,019
我们现在来试一下

410
00:17:54,019 --> 00:17:55,309
看看行不行

411
00:17:55,309 --> 00:17:59,420
运行起来哎我们点中它左右拖拽

412
00:17:59,420 --> 00:18:01,759
我们发现已经可以了

413
00:18:01,759 --> 00:18:03,259
是不是已经可以了

414
00:18:03,259 --> 00:18:06,140
那么这里啊如果大家跟我的画面不太一样

415
00:18:06,140 --> 00:18:07,759
可能是我们这边选择的不一样

416
00:18:07,759 --> 00:18:09,859
我这边就用的是默认的啊

417
00:18:10,079 --> 00:18:13,559
那么在这里我们选中这个飞机就可以左右拖拽了啊

418
00:18:13,559 --> 00:18:15,138
就可以左右拖拽了

419
00:18:17,700 --> 00:18:20,279
那么首先这是第一步我们就做完了

420
00:18:20,279 --> 00:18:21,759
第一步我们就做完了

421
00:18:21,759 --> 00:18:23,740
我们的飞机已经可以移动了

422
00:18:23,740 --> 00:18:26,019
那么第二步就是我们要做什么了

423
00:18:26,019 --> 00:18:27,429
要做攻击了

424
00:18:27,429 --> 00:18:30,429
这个微信飞机这个攻击是怎样的啊

425
00:18:30,429 --> 00:18:33,329
是他他从头到尾他就自己默认攻击的

426
00:18:33,329 --> 00:18:35,910
就不需要你去按什么键攻击他自己

427
00:18:35,910 --> 00:18:37,349
就比如说每隔半秒钟

428
00:18:37,349 --> 00:18:40,398
比如说我们就让他每隔半秒钟打出一发子弹好

429
00:18:40,440 --> 00:18:42,059
那么他是这样的效果

430
00:18:42,059 --> 00:18:43,859
那这样的话我们要做攻击

431
00:18:43,859 --> 00:18:45,559
那大家去想子弹

432
00:18:45,619 --> 00:18:47,299
我们就要去创建子弹了

433
00:18:47,299 --> 00:18:49,569
那么子弹的话呃

434
00:18:49,569 --> 00:18:52,119
就必须先给他创建出来啊

435
00:18:52,119 --> 00:18:54,369
我们先要把子弹的预设体创建好

436
00:18:54,369 --> 00:18:58,259
然后在飞机脚本里面一直实例化这个子弹就可以了

437
00:18:58,259 --> 00:18:59,819
那么我们把子弹写出来

438
00:19:01,619 --> 00:19:03,319
把子弹放出来

439
00:19:05,420 --> 00:19:07,579
那么子弹拿出来以后啊

440
00:19:07,579 --> 00:19:09,529
子弹拿出来以后呃

441
00:19:09,529 --> 00:19:10,700
子弹有什么功能

442
00:19:10,700 --> 00:19:13,990
首先子弹会和敌机产生碰撞啊

443
00:19:13,990 --> 00:19:15,940
子弹会和敌机产生碰撞

444
00:19:15,940 --> 00:19:17,440
那么我们自己的碰撞

445
00:19:17,440 --> 00:19:19,420
我在这就先不写了啊

446
00:19:19,420 --> 00:19:21,980
我们主要去写这个子弹去打敌机的

447
00:19:21,980 --> 00:19:23,779
那么这个只要搞清楚了

448
00:19:23,779 --> 00:19:27,299
那么敌机比如说碰到玩家让玩家死亡

449
00:19:27,420 --> 00:19:29,160
那么这个逻辑是完全一样的

450
00:19:29,160 --> 00:19:31,680
那么这一点啊可以给大家留下来

451
00:19:31,680 --> 00:19:33,220
让大家自己去实现

452
00:19:33,220 --> 00:19:35,619
那咱们来看这个子弹打敌机啊

453
00:19:35,619 --> 00:19:38,500
那么子弹的话既然要和敌机产生碰撞

454
00:19:38,500 --> 00:19:40,359
所以它必须加一个碰撞组件

455
00:19:40,359 --> 00:19:41,740
是box clid

456
00:19:41,740 --> 00:19:43,960
是不是就是这样的一个碰撞

457
00:19:45,819 --> 00:19:48,240
那么这个子弹啊

458
00:19:48,240 --> 00:19:50,250
子弹有了这个碰撞了

459
00:19:50,250 --> 00:19:52,769
接下来我们就可以去编写子弹的脚本了

460
00:19:52,769 --> 00:19:54,089
因为子弹是会飞的

461
00:19:54,089 --> 00:19:55,829
子弹是会从这个位置

462
00:19:55,829 --> 00:19:57,029
就他不管在哪个位置

463
00:19:57,029 --> 00:19:58,369
他都是向上飞的

464
00:19:58,369 --> 00:20:01,009
而且子弹一个特点就是碰到敌人

465
00:20:01,009 --> 00:20:02,210
让敌人死亡

466
00:20:02,210 --> 00:20:02,990
自己也死亡

467
00:20:02,990 --> 00:20:04,670
如果没碰到敌人出了屏幕

468
00:20:04,670 --> 00:20:05,930
就要把子弹销毁

469
00:20:05,930 --> 00:20:06,980
自动销毁

470
00:20:06,980 --> 00:20:08,000
如果你出了屏幕

471
00:20:08,000 --> 00:20:08,779
你没有销毁

472
00:20:08,779 --> 00:20:10,859
那你这个子弹越创建越多

473
00:20:10,859 --> 00:20:12,420
虽然你更多的看不见了

474
00:20:12,420 --> 00:20:14,279
但实际上你创建出了一堆子弹

475
00:20:14,279 --> 00:20:14,849
对不对

476
00:20:14,849 --> 00:20:16,200
所以这样也是不行的

477
00:20:16,200 --> 00:20:20,799
那我们创建一个脚本blete control

478
00:20:21,500 --> 00:20:23,839
打开这个子弹的这个脚本

479
00:20:23,839 --> 00:20:27,359
那我们来编写一下这个子弹啊

480
00:20:27,359 --> 00:20:28,289
编写一下子弹

481
00:20:28,289 --> 00:20:29,799
那在这里

482
00:20:33,319 --> 00:20:35,450
在这里呃

483
00:20:35,450 --> 00:20:37,659
这个子弹的话

484
00:20:39,519 --> 00:20:43,420
我看啊我们先把脚本创建出来啊

485
00:20:43,420 --> 00:20:44,500
创建出来以后

486
00:20:44,500 --> 00:20:47,099
然后把这个首先类名先改好

487
00:20:49,920 --> 00:20:52,138
玩家这边也是类名

488
00:20:59,339 --> 00:21:01,380
那么我们子弹的逻辑啊

489
00:21:01,380 --> 00:21:03,299
我们子弹的逻辑一会儿再写

490
00:21:03,299 --> 00:21:05,339
我们先打开这边啊

491
00:21:05,339 --> 00:21:07,700
我们先把这个脚本挂载上来

492
00:21:08,500 --> 00:21:09,519
脚本挂在上来

493
00:21:09,519 --> 00:21:14,680
那么这样的话最起码子子弹的这个右边的这个组件面板就算了

494
00:21:14,759 --> 00:21:16,559
就算这个完整了啊

495
00:21:16,559 --> 00:21:22,500
我们无非最后最后最后差的就是去修改它的这个子弹的这个控制器啊

496
00:21:22,500 --> 00:21:26,339
这个这个控制脚本啊啊那么这个我们放一会儿再写

497
00:21:26,339 --> 00:21:30,279
我们先把这个玩家的这个逻辑实现完好啊

498
00:21:30,279 --> 00:21:34,878
我们完整的一块功能一块一块功能的实现啊

499
00:21:35,200 --> 00:21:36,910
这样就不用来回倒了

500
00:21:36,910 --> 00:21:41,398
那首先我们再回到玩家这边

501
00:21:42,759 --> 00:21:44,170
然后回到玩家这边

502
00:21:44,170 --> 00:21:46,599
我要在这里射击子弹

503
00:21:46,599 --> 00:21:48,878
也就是要写这个攻击了

504
00:21:49,980 --> 00:21:52,380
而且这个攻击了攻击的话

505
00:21:52,380 --> 00:21:57,920
比如说我们希望每隔多长时间就产生一次攻击

506
00:21:57,920 --> 00:22:01,279
那么这个攻击我们依然是可以写在这里的

507
00:22:03,039 --> 00:22:05,740
比如说我希望每隔0.5秒产生一次攻击

508
00:22:05,740 --> 00:22:07,930
那么我们在这里教大家一个东西啊

509
00:22:07,930 --> 00:22:09,400
叫计时器啊

510
00:22:09,400 --> 00:22:11,119
这种东西呃

511
00:22:11,119 --> 00:22:12,920
咱们就放到这个项目直接讲了

512
00:22:12,920 --> 00:22:14,180
因为它太简单了啊

513
00:22:14,180 --> 00:22:17,059
咱没必要一节课花一节课去讲它

514
00:22:17,339 --> 00:22:18,900
那么有个计时器

515
00:22:18,900 --> 00:22:20,099
计时器是什么

516
00:22:20,099 --> 00:22:25,519
每个节点都可以调一个计时器呃

517
00:22:25,519 --> 00:22:29,279
我们找一下schedule

518
00:22:32,099 --> 00:22:34,500
啊在这里dz就掉了啊

519
00:22:34,500 --> 00:22:36,200
那么在这里啊

520
00:22:36,440 --> 00:22:38,029
这个计时器的话

521
00:22:38,029 --> 00:22:41,279
括号里面有三个参数啊

522
00:22:41,279 --> 00:22:44,819
有三个参数后面两个是可以省略的啊

523
00:22:44,819 --> 00:22:48,259
所有的参数后面带问号的都是可以省略的参数

524
00:22:48,259 --> 00:22:51,019
那么第一个参数它要的是一个函数

525
00:22:51,019 --> 00:22:51,559
对不对

526
00:22:51,559 --> 00:22:52,339
要的是一个函数

527
00:22:52,339 --> 00:22:55,299
我们直接括号方法啊

528
00:22:55,299 --> 00:22:57,160
也就是每隔多长时间他会给我们掉下来

529
00:22:57,160 --> 00:22:57,849
这个方法

530
00:22:57,849 --> 00:23:01,059
第二个就是多长时间来一次啊

531
00:23:01,059 --> 00:23:01,839
我们在这里

532
00:23:01,839 --> 00:23:06,640
比如说我们希望每0.5秒掉一下它啊

533
00:23:06,640 --> 00:23:07,779
每0.5秒掉一下

534
00:23:07,779 --> 00:23:10,859
那我们在这里0.5秒掉下它

535
00:23:10,859 --> 00:23:13,230
第三个参数如果要的话

536
00:23:13,230 --> 00:23:19,589
那么意思就是说你这个当前重复几次

537
00:23:19,589 --> 00:23:21,900
如果你不给他就一直重复啊

538
00:23:21,900 --> 00:23:23,519
你如果不给他就一直重复

539
00:23:23,519 --> 00:23:24,680
那么就这样

540
00:23:25,700 --> 00:23:29,640
那么其实它还有第四个参数啊

541
00:23:29,640 --> 00:23:31,380
第四个参数很少用

542
00:23:31,380 --> 00:23:37,279
第四个参数就是第一就是什么多长时间我开始第一次第一次攻击

543
00:23:37,279 --> 00:23:38,720
比如说计时器

544
00:23:38,720 --> 00:23:41,299
比如说如果每十秒出来一个敌人

545
00:23:41,299 --> 00:23:44,460
那你不可能玩家上来场景是空的

546
00:23:44,460 --> 00:23:46,019
等十秒以后出来第一个敌人

547
00:23:46,019 --> 00:23:48,980
有时候我们希望上来就出现呃

548
00:23:48,980 --> 00:23:50,900
就是第一次他是瞬间调用的

549
00:23:50,900 --> 00:23:51,920
然后调用完了以后

550
00:23:51,920 --> 00:23:54,430
然后在每隔一秒调用一次啊

551
00:23:54,430 --> 00:23:55,930
每隔多少多少秒调用一次

552
00:23:55,930 --> 00:24:00,519
那这种情况下你就可以把地四个参数也加上

553
00:24:00,700 --> 00:24:02,019
比如说我掉了100次

554
00:24:02,019 --> 00:24:02,859
创建100个敌人

555
00:24:02,859 --> 00:24:03,490
写个零

556
00:24:03,490 --> 00:24:06,640
这样的话就是每个0.5秒产生一个敌人

557
00:24:06,640 --> 00:24:08,380
产生会产生100次

558
00:24:08,380 --> 00:24:11,950
然后第一次是从零开始调用的

559
00:24:11,950 --> 00:24:14,049
也就是第一次是瞬间调用啊

560
00:24:14,049 --> 00:24:15,619
是这样的一个意思

561
00:24:15,779 --> 00:24:17,819
那我们在这里就不需要后面的啊

562
00:24:17,819 --> 00:24:22,480
我们就是固定的每隔0.5秒啊去调一下这个方法就可以了

563
00:24:24,559 --> 00:24:27,680
那么每隔0.5秒去调用这个方法

564
00:24:27,680 --> 00:24:30,619
那么在这个方法里面我们就可以做

565
00:24:30,619 --> 00:24:33,839
比如说创建子弹了啊

566
00:24:33,839 --> 00:24:35,430
这就是我们要做的事了

567
00:24:35,430 --> 00:24:36,630
创建子弹

568
00:24:36,630 --> 00:24:38,638
创建子弹很简单

569
00:24:38,819 --> 00:24:42,059
首先我们要有子弹的这个预设体啊

570
00:24:42,059 --> 00:24:42,839
它的一个引用

571
00:24:42,839 --> 00:24:48,019
那我们就去需要去写property cc.pre fab

572
00:24:48,019 --> 00:24:48,650
对不对

573
00:24:48,650 --> 00:24:55,470
然后这边呢blitte pre fab c c.pre fab等于now

574
00:24:55,470 --> 00:24:58,500
我们写一个子弹的预设体这样的一个属性

575
00:24:58,500 --> 00:25:01,859
然后这边进行一个关联

576
00:25:01,960 --> 00:25:04,059
那子弹我们还没有拖成预设体

577
00:25:04,059 --> 00:25:05,380
拖成一个预设体

578
00:25:05,380 --> 00:25:09,880
选中玩家把子弹拖到这个预设题这边来

579
00:25:09,880 --> 00:25:12,069
那这样的话预测题就关联成功了

580
00:25:12,069 --> 00:25:13,359
然后回到这边

581
00:25:13,359 --> 00:25:15,619
我们创建子弹就很简单了

582
00:25:15,839 --> 00:25:19,380
直接let ballet

583
00:25:19,380 --> 00:25:22,138
就是最后我们创建的这个子弹变量

584
00:25:22,740 --> 00:25:24,900
cc.instantiate

585
00:25:24,900 --> 00:25:27,539
this.delete pro啊

586
00:25:27,539 --> 00:25:28,799
非常简单

587
00:25:28,799 --> 00:25:31,079
那么最后得到的这个子弹一定要做的事

588
00:25:31,079 --> 00:25:33,869
就是第一个我们要设置它的服务体

589
00:25:33,869 --> 00:25:36,539
它的服务体如果我们希望它在最外层

590
00:25:36,539 --> 00:25:38,470
我们也需要设置啊

591
00:25:38,470 --> 00:25:39,970
那你说它的最外层了

592
00:25:39,970 --> 00:25:42,099
把它设置服务器设置给谁

593
00:25:42,099 --> 00:25:44,460
那我们之前说过

594
00:25:44,559 --> 00:25:47,920
我们这个所有的物体都是在一个场景上的

595
00:25:47,920 --> 00:25:49,720
所以如果你希望设置到最外层

596
00:25:49,720 --> 00:25:52,269
你就直接把它加到一个场景上啊

597
00:25:52,269 --> 00:25:54,190
如果你希望设置成谁的子节点

598
00:25:54,190 --> 00:25:56,109
你就把它的负节点放到这里

599
00:25:56,109 --> 00:25:58,269
那我们在这里希望设置到最外层

600
00:25:58,269 --> 00:26:00,390
所以我们把它加到场景上面

601
00:26:00,390 --> 00:26:01,769
当前的场景怎么获得

602
00:26:01,769 --> 00:26:02,430
很简单

603
00:26:02,430 --> 00:26:03,089
cc点

604
00:26:03,089 --> 00:26:04,230
首先得到我们导演

605
00:26:04,230 --> 00:26:07,799
导演有个方法叫做得到当前的场景啊

606
00:26:07,799 --> 00:26:09,180
得到当前的场景

607
00:26:09,180 --> 00:26:11,039
那这样的话就拿到当前场景

608
00:26:11,039 --> 00:26:14,579
然后我们给它设置成这个子物体了

609
00:26:18,319 --> 00:26:20,900
设置附体设置我们自己这个子弹的副体

610
00:26:20,900 --> 00:26:21,859
对不对

611
00:26:22,799 --> 00:26:24,569
然后设置位置

612
00:26:24,569 --> 00:26:26,069
子弹位置

613
00:26:26,069 --> 00:26:27,720
子弹位置的话

614
00:26:27,720 --> 00:26:36,299
我们希望它的x的位置和我们这个飞机的x位置一样啊

615
00:26:36,299 --> 00:26:39,630
我们希望就是我们在哪子弹出来也在哪个位置

616
00:26:39,630 --> 00:26:43,019
但是出来我希望他是在我这个飞机上面的啊

617
00:26:43,019 --> 00:26:45,099
不希望出来在我飞机中间

618
00:26:45,099 --> 00:26:45,940
是不是那样的话

619
00:26:45,940 --> 00:26:47,799
非子弹就把我自己打死了啊

620
00:26:47,799 --> 00:26:48,400
是不是

621
00:26:48,400 --> 00:26:50,829
所以子弹出来应该是是在上面

622
00:26:50,829 --> 00:26:54,990
所以子弹的这个y轴应该是比我们y轴大的大多少

623
00:26:54,990 --> 00:26:56,970
最少大过我们飞机的一半

624
00:26:56,970 --> 00:26:58,589
我们飞机是多高的

625
00:26:58,589 --> 00:27:00,680
宽是多高

626
00:27:00,680 --> 00:27:02,119
是124

627
00:27:02,119 --> 00:27:06,460
所以我们在这里大概给他一个数值啊

628
00:27:06,460 --> 00:27:13,299
y子弹的y就等于我们这个自己飞机的这个y y坐标

629
00:27:13,299 --> 00:27:14,500
再加上一个

630
00:27:14,500 --> 00:27:17,019
比如说给他个60啊

631
00:27:17,559 --> 00:27:18,700
给他个60

632
00:27:18,700 --> 00:27:20,440
然后我们现在运行一下

633
00:27:20,440 --> 00:27:21,680
看看效果

634
00:27:24,420 --> 00:27:26,099
大概就在这个位置产生了

635
00:27:27,420 --> 00:27:29,440
每隔半秒产生一个

636
00:27:31,240 --> 00:27:31,839
对不对

637
00:27:31,839 --> 00:27:33,878
每隔半秒产生一个ok

638
00:27:37,440 --> 00:27:40,019
那我们现在已经可以产生满屏子弹了啊

639
00:27:40,019 --> 00:27:41,220
产生满屏子弹了

640
00:27:41,220 --> 00:27:46,789
那么这个飞机去攻去打这个子弹的这个逻辑我们也有了

641
00:27:46,789 --> 00:27:49,579
那么接下来我们就该开始编写谁了

642
00:27:49,579 --> 00:27:50,359
编写子弹了

643
00:27:50,359 --> 00:27:51,440
因为子弹还不会飞

644
00:27:51,440 --> 00:27:51,980
对不对

645
00:27:51,980 --> 00:27:54,619
我们就开始编写子弹的逻辑了

646
00:27:54,619 --> 00:27:55,880
那这样大家就可以看出来

647
00:27:55,880 --> 00:27:58,069
我们这个逻辑是一点一点实现的

648
00:27:58,069 --> 00:27:59,990
飞机完了写子弹

649
00:27:59,990 --> 00:28:04,299
是不是飞机完了写子弹

650
00:28:04,779 --> 00:28:06,490
那么对于子弹而言

651
00:28:06,490 --> 00:28:08,480
对于子弹而言

652
00:28:10,220 --> 00:28:12,289
首先子弹也是会移动的

653
00:28:12,289 --> 00:28:14,240
子弹也是会移动的

654
00:28:14,619 --> 00:28:16,779
他是会往屏幕上方去飞的

655
00:28:16,779 --> 00:28:17,440
对不对

656
00:28:17,440 --> 00:28:20,419
那我们在这里把子弹的移动写了

657
00:28:21,619 --> 00:28:27,009
比如说子弹的移动就等于一下呃

658
00:28:27,009 --> 00:28:28,779
我们给他一个变量吧

659
00:28:28,779 --> 00:28:30,398
叫speed好了

660
00:28:30,920 --> 00:28:34,279
因为这个速度如果我们一会儿给他不是很准确

661
00:28:34,279 --> 00:28:37,670
我希望在面板上能去对它进行修改

662
00:28:37,670 --> 00:28:39,420
所以我们给它一个

663
00:28:42,740 --> 00:28:48,920
然后在这里速度我希望我希望的是每秒移动

664
00:28:48,920 --> 00:28:49,789
我们一平

665
00:28:49,789 --> 00:28:52,640
基本上子弹应该飞的速度很快

666
00:28:52,640 --> 00:28:55,730
一秒走一瓶的这个距离吧

667
00:28:55,730 --> 00:28:58,700
就是一秒就能从屏幕的下方飞到屏幕的上方

668
00:28:58,700 --> 00:28:59,900
那个屏幕大概800

669
00:28:59,900 --> 00:29:01,559
我们就给他个800吧

670
00:29:01,799 --> 00:29:04,130
在这里就是800

671
00:29:04,130 --> 00:29:04,549
注意啊

672
00:29:04,549 --> 00:29:07,130
这里我们设置这个给它

673
00:29:07,130 --> 00:29:08,519
让它移动的时候

674
00:29:08,519 --> 00:29:09,960
你不能直接这样就完了

675
00:29:09,960 --> 00:29:12,660
这时候的速度是按帧去算的啊

676
00:29:12,660 --> 00:29:14,799
我们应该给他乘一个dt

677
00:29:14,799 --> 00:29:17,980
这样的话我的y轴每次移动这么多啊

678
00:29:17,980 --> 00:29:21,769
最终的结果就是啊我的y轴一直向上去改变

679
00:29:21,769 --> 00:29:25,339
然后改变的速度就是每秒向上移动呃

680
00:29:25,339 --> 00:29:29,119
这个这个这个每每秒向上移动800像素啊

681
00:29:29,119 --> 00:29:29,980
这样的

682
00:29:31,740 --> 00:29:33,180
如果出了屏幕

683
00:29:34,440 --> 00:29:36,779
我们就要销毁销毁这个子弹

684
00:29:36,779 --> 00:29:38,819
你这个子弹不能无限的这个产生

685
00:29:38,819 --> 00:29:39,650
对不对

686
00:29:39,650 --> 00:29:40,970
你出了这个屏幕

687
00:29:40,970 --> 00:29:41,630
你就销毁

688
00:29:41,630 --> 00:29:44,809
这样的话就能保证这个子弹就是存活的子弹

689
00:29:44,809 --> 00:29:48,000
其实也就是没多少啊

690
00:29:48,640 --> 00:29:49,779
那怎么去做呢

691
00:29:49,779 --> 00:29:54,880
如果我的node.y已经大于了屏幕满分就是800 820多吧

692
00:29:54,880 --> 00:29:56,720
就大于820好了

693
00:29:57,140 --> 00:29:58,220
如果大于820

694
00:29:58,220 --> 00:29:59,990
我就把我自己销毁了

695
00:29:59,990 --> 00:30:02,390
那点node怎么样销毁一个节点

696
00:30:02,390 --> 00:30:03,140
注意啊

697
00:30:03,140 --> 00:30:06,119
diy调用destiny就行了

698
00:30:07,299 --> 00:30:09,160
调用node的啊

699
00:30:09,160 --> 00:30:11,319
我们就会把这个节点瞬间销毁

700
00:30:11,319 --> 00:30:13,279
这是瞬间销毁的啊

701
00:30:13,619 --> 00:30:14,640
一调这个方法

702
00:30:14,640 --> 00:30:16,140
他马上就销毁了啊

703
00:30:16,140 --> 00:30:16,980
马上就销毁了

704
00:30:16,980 --> 00:30:19,659
那么这个就是我们的子弹逻辑

705
00:30:19,660 --> 00:30:21,400
子弹飞行的逻辑啊

706
00:30:21,400 --> 00:30:22,539
只是子弹飞行的逻辑

707
00:30:22,539 --> 00:30:25,460
我们先看一下哎

708
00:30:25,460 --> 00:30:26,960
那这个子弹就能打出去了

709
00:30:26,960 --> 00:30:31,039
每秒向上的速度是一秒走800像素啊

710
00:30:31,039 --> 00:30:32,150
这样的一个速度

711
00:30:32,150 --> 00:30:33,589
然后只要出了屏幕

712
00:30:33,589 --> 00:30:35,940
它就会自动被销毁啊

713
00:30:35,940 --> 00:30:37,740
它就会自动被销毁

714
00:30:37,740 --> 00:30:38,640
那么就这样

715
00:30:41,339 --> 00:30:45,460
o那么现在子弹是可以打到敌人的

716
00:30:45,460 --> 00:30:47,619
子弹是可以打到敌人的

717
00:30:47,619 --> 00:30:48,619
对不对

718
00:30:52,160 --> 00:30:55,970
那么我们现在就要去加敌人

719
00:30:55,970 --> 00:30:57,599
我们想一想啊

720
00:31:00,420 --> 00:31:01,859
我们先把敌人加上

721
00:31:01,859 --> 00:31:02,519
把敌人加上

722
00:31:02,519 --> 00:31:07,619
我们再说这个子弹和敌人的一个啊碰撞的逻辑啊

723
00:31:07,740 --> 00:31:08,700
这个碰撞逻辑

724
00:31:08,700 --> 00:31:10,470
那首先我们先加敌人好了

725
00:31:10,470 --> 00:31:12,210
先加一个敌人

726
00:31:12,210 --> 00:31:15,720
敌人我们把它放到这个位置上

727
00:31:18,380 --> 00:31:20,039
放到这个位置上

728
00:31:20,480 --> 00:31:23,660
那么这个对于这个敌人而言的话啊

729
00:31:23,660 --> 00:31:24,859
对于这个敌人而言的话

730
00:31:24,859 --> 00:31:27,079
我们想想我们怎样去做

731
00:31:27,079 --> 00:31:28,539
怎样去做

732
00:31:29,539 --> 00:31:31,700
首先敌人会和子弹产生碰撞

733
00:31:31,700 --> 00:31:34,579
那么敌人也要去加这个碰撞

734
00:31:34,940 --> 00:31:36,319
敌人也要去加这个碰撞

735
00:31:36,319 --> 00:31:38,859
但是碰撞可以稍微小一些啊

736
00:31:39,299 --> 00:31:41,220
就是我们要打的更精准一些

737
00:31:42,299 --> 00:31:44,180
让我们的子弹对不对

738
00:31:46,799 --> 00:31:49,779
然后我们肯定要写这个敌人的脚本

739
00:31:55,279 --> 00:31:56,779
给这个敌人

740
00:31:59,359 --> 00:32:00,529
给这个敌人

741
00:32:00,529 --> 00:32:01,759
敌人选中敌人

742
00:32:01,759 --> 00:32:03,500
把脚本挂上来

743
00:32:03,640 --> 00:32:05,680
然后打开这个敌人的脚本

744
00:32:05,680 --> 00:32:07,539
那么对于这个敌人的脚本

745
00:32:07,539 --> 00:32:09,519
我们怎么去写

746
00:32:13,599 --> 00:32:19,130
呃那么首先敌人也是会移动的

747
00:32:19,130 --> 00:32:20,359
敌人也是会移动的

748
00:32:20,359 --> 00:32:22,130
所以他也需要这个方法

749
00:32:22,130 --> 00:32:24,200
这个方法每隔多长时间

750
00:32:24,200 --> 00:32:25,220
我们让它移动多少

751
00:32:25,220 --> 00:32:26,259
对不对

752
00:32:28,279 --> 00:32:30,799
那么我们在这里啊

753
00:32:30,799 --> 00:32:32,180
我们在这里

754
00:32:36,359 --> 00:32:37,200
我们看一下啊

755
00:32:37,200 --> 00:32:39,619
敌人我们应该给他什么

756
00:32:40,240 --> 00:32:40,900
先做什么

757
00:32:40,900 --> 00:32:42,400
先做移动销毁

758
00:32:42,400 --> 00:32:43,299
还是先做

759
00:32:43,299 --> 00:32:44,500
我们先做这样吧

760
00:32:44,500 --> 00:32:46,319
先不要去做移动

761
00:32:46,319 --> 00:32:47,699
先做碰撞

762
00:32:47,699 --> 00:32:48,419
就是西安市

763
00:32:48,419 --> 00:32:52,138
最起码我现在在静止的过程当中能被子弹打死啊

764
00:32:52,138 --> 00:32:54,568
这是我敌人第一步做的事

765
00:32:54,568 --> 00:32:56,569
那我敌人有一个方法

766
00:32:56,569 --> 00:32:59,079
我写一个方法叫做死亡方法

767
00:32:59,839 --> 00:33:03,180
我们自己给他写个方法叫做死亡啊

768
00:33:03,180 --> 00:33:06,539
就是只要敌人只要子弹碰到我这个敌人

769
00:33:06,539 --> 00:33:08,789
我就会掉我里面的死亡方法

770
00:33:08,789 --> 00:33:11,559
每次死亡死亡以后

771
00:33:11,559 --> 00:33:12,940
我就会进行销毁

772
00:33:12,940 --> 00:33:14,859
我们先写一个最简单的销毁

773
00:33:14,859 --> 00:33:17,259
就是this node.dio啊

774
00:33:17,259 --> 00:33:18,960
我就把我自己销毁

775
00:33:20,200 --> 00:33:22,119
那么ok我们先做最简单的事

776
00:33:22,119 --> 00:33:24,069
就是当子弹碰到我敌人

777
00:33:24,069 --> 00:33:26,349
我敌人就会掉这个死亡方法

778
00:33:26,349 --> 00:33:28,759
那么这个东西怎么去做啊

779
00:33:28,759 --> 00:33:30,440
这个东西怎么去做

780
00:33:31,920 --> 00:33:33,240
非常简单非常简单

781
00:33:33,240 --> 00:33:35,200
首先我们再回到子弹这里

782
00:33:36,559 --> 00:33:38,930
那么我们现在要做这个碰撞了

783
00:33:38,930 --> 00:33:41,000
碰撞的话大家记得啊

784
00:33:41,000 --> 00:33:42,259
我们一定要做一件事

785
00:33:42,259 --> 00:33:43,339
我们到玩家这里吧

786
00:33:43,339 --> 00:33:44,829
在玩家这里做

787
00:33:44,829 --> 00:33:47,589
因为玩家玩家的脚本只执行一次

788
00:33:47,589 --> 00:33:48,309
只有一个玩家

789
00:33:48,309 --> 00:33:48,789
对不对

790
00:33:48,789 --> 00:33:52,859
我们要开启碰撞检测功能啊

791
00:33:52,859 --> 00:33:54,539
这个一定要记得啊

792
00:33:54,539 --> 00:33:58,019
director.get polication manager

793
00:33:58,019 --> 00:33:59,548
neighbor等于true

794
00:33:59,548 --> 00:34:03,299
这样的话他才能帮我们去检测这样的一个碰撞

795
00:34:03,619 --> 00:34:08,340
然后我们到这个敌人这边

796
00:34:09,119 --> 00:34:10,079
到子弹这边吧

797
00:34:10,079 --> 00:34:10,920
到子弹这边

798
00:34:10,920 --> 00:34:13,840
然后我们去写这个碰撞方法啊

799
00:34:14,480 --> 00:34:16,280
connection enter

800
00:34:16,280 --> 00:34:18,900
就是刚开始碰撞的时候

801
00:34:20,599 --> 00:34:22,159
然后就是碰撞到的物体

802
00:34:22,159 --> 00:34:23,199
对不对

803
00:34:23,358 --> 00:34:24,798
当我开始碰撞的时候

804
00:34:24,798 --> 00:34:25,668
我去判断

805
00:34:25,668 --> 00:34:30,719
如果碰到敌人销毁自己

806
00:34:30,778 --> 00:34:33,400
让敌人死亡

807
00:34:33,539 --> 00:34:37,380
那么在这里首先碰到敌人销毁自己

808
00:34:37,380 --> 00:34:38,099
让敌人死亡

809
00:34:38,099 --> 00:34:39,179
先要让敌人死亡

810
00:34:39,179 --> 00:34:40,110
再销毁自己

811
00:34:40,110 --> 00:34:42,260
否则的话你销毁了自己

812
00:34:42,318 --> 00:34:43,639
你先销毁自己

813
00:34:43,639 --> 00:34:45,139
这个脚本可能就不执行了

814
00:34:45,139 --> 00:34:46,278
对不对啊

815
00:34:46,278 --> 00:34:48,679
所以在这里我们要做的第一件事

816
00:34:48,679 --> 00:34:50,409
让敌人死亡

817
00:34:50,409 --> 00:34:51,489
但是先要判断

818
00:34:51,489 --> 00:34:52,688
如果碰到的是敌人

819
00:34:52,688 --> 00:34:54,059
怎么判断呢

820
00:34:56,219 --> 00:34:57,869
我们昨天讲了一个东西

821
00:34:57,869 --> 00:35:00,130
敌人的碰撞体啊

822
00:35:00,130 --> 00:35:01,329
这里有个tag值

823
00:35:01,329 --> 00:35:02,710
我们给他个一啊

824
00:35:02,710 --> 00:35:04,090
敌人的标签是一

825
00:35:04,090 --> 00:35:06,039
这样的话我们就通过这个来判断

826
00:35:06,039 --> 00:35:07,920
我们回到这里

827
00:35:07,920 --> 00:35:13,429
如果other.tag值等于多少

828
00:35:13,429 --> 00:35:15,559
等于一啊

829
00:35:15,559 --> 00:35:17,358
如果other的type值等于一

830
00:35:17,358 --> 00:35:20,028
那就证明我碰到的肯定是什么东西

831
00:35:20,028 --> 00:35:21,079
肯定是敌人

832
00:35:21,079 --> 00:35:22,119
对不对

833
00:35:22,119 --> 00:35:24,219
通过tag值去判断的啊

834
00:35:24,219 --> 00:35:26,800
所以这个tag值的用法就很好用了

835
00:35:26,800 --> 00:35:28,269
那么我就要干嘛了

836
00:35:28,269 --> 00:35:29,960
销毁敌人

837
00:35:30,559 --> 00:35:33,659
然后再销毁自己

838
00:35:33,838 --> 00:35:35,338
那么销毁自己很简单

839
00:35:35,338 --> 00:35:39,190
this.node点销毁敌人

840
00:35:39,190 --> 00:35:40,449
正常我们也可以

841
00:35:40,449 --> 00:35:43,150
比如说other.node点

842
00:35:43,150 --> 00:35:45,190
但是我们不要这样去写啊

843
00:35:45,190 --> 00:35:46,849
为什么不要这样去写

844
00:35:46,849 --> 00:35:49,070
因为你敌人比如说我们常常做游戏

845
00:35:49,070 --> 00:35:50,210
游戏人物死亡了

846
00:35:50,210 --> 00:35:53,139
并不一定就是说死亡就消失了

847
00:35:53,139 --> 00:35:56,139
他死亡可能会做很多东很多事情

848
00:35:56,139 --> 00:35:58,019
比如说一个人已死亡

849
00:35:58,219 --> 00:35:59,780
他可能还有一个死亡的动画

850
00:35:59,780 --> 00:36:00,829
还会倒在地上

851
00:36:00,829 --> 00:36:03,920
倒地上可能还会停几秒钟才会真正的死亡

852
00:36:03,920 --> 00:36:04,579
对不对

853
00:36:04,579 --> 00:36:07,059
所以在这里你作为一个子弹

854
00:36:07,059 --> 00:36:09,519
你不要去管人家敌人怎么死亡的

855
00:36:09,519 --> 00:36:11,500
你只要通知这个敌人怎样了

856
00:36:11,500 --> 00:36:12,699
他要死亡就行了

857
00:36:12,699 --> 00:36:15,380
所以这里子弹自己的死亡

858
00:36:15,380 --> 00:36:16,460
由于是瞬间的啊

859
00:36:16,460 --> 00:36:18,449
他所以他控制自己的死亡

860
00:36:18,449 --> 00:36:21,329
敌人的销毁敌人的死亡

861
00:36:21,329 --> 00:36:24,829
我在这里就要点get啊

862
00:36:24,829 --> 00:36:26,300
competent

863
00:36:28,398 --> 00:36:29,478
get comment

864
00:36:29,478 --> 00:36:31,338
然后是个这个敌人

865
00:36:31,338 --> 00:36:33,900
我要获取敌人的脚本

866
00:36:36,358 --> 00:36:40,498
enemy enemy control

867
00:36:40,498 --> 00:36:43,179
那么这里注意

868
00:36:43,179 --> 00:36:47,179
i get coment enemy control找不到

869
00:36:47,639 --> 00:36:48,840
那么这里注意啊

870
00:36:48,840 --> 00:36:50,579
有时候他会有这样的情况

871
00:36:50,579 --> 00:36:52,500
enemy control找不到啊

872
00:36:52,500 --> 00:36:56,650
第一个反应是这里类型没有改

873
00:36:56,650 --> 00:36:59,039
enemy control

874
00:36:59,079 --> 00:37:00,489
所以你在这里改一下

875
00:37:00,489 --> 00:37:04,798
这时候你再去写enemy ctrl

876
00:37:04,798 --> 00:37:06,268
你看他就有提示了

877
00:37:06,268 --> 00:37:11,320
而且如果我要去使用别的类里面的这个别的文件里面的类的时候

878
00:37:11,320 --> 00:37:16,400
你会发现它上面自动会导入这样的一行代码

879
00:37:16,798 --> 00:37:18,778
意思就是我要导入这个类

880
00:37:18,778 --> 00:37:20,849
从哪个文件里面导入

881
00:37:20,849 --> 00:37:24,360
从我当前根目录里面的enemy control

882
00:37:24,418 --> 00:37:24,719
哎

883
00:37:24,719 --> 00:37:26,759
这个文件这个文件里面导入

884
00:37:26,759 --> 00:37:29,518
就是从这个这个文件里面导入这个类啊

885
00:37:29,518 --> 00:37:30,838
当然这个是它自己生成的

886
00:37:30,838 --> 00:37:31,498
我们不用管

887
00:37:31,498 --> 00:37:32,809
知道就行了

888
00:37:32,809 --> 00:37:35,150
那在这里拿到敌人的脚本

889
00:37:35,150 --> 00:37:36,650
为什么能拿到敌人的脚本

890
00:37:36,650 --> 00:37:39,079
因为现在代表的是什么

891
00:37:39,079 --> 00:37:42,420
other代表的是敌人的碰撞器

892
00:37:42,420 --> 00:37:43,019
对不对

893
00:37:43,019 --> 00:37:44,099
我们碰到敌人

894
00:37:44,099 --> 00:37:46,139
所以other就是敌人的这个碰撞器

895
00:37:46,139 --> 00:37:50,440
那所以我们就可以通过a字得到组件

896
00:37:50,440 --> 00:37:54,400
因为你得到组件就是可以得到当前这个物体上所有的组件

897
00:37:54,400 --> 00:37:58,539
这里你可以得到spirit或者敌人的敌人的脚本

898
00:37:58,539 --> 00:38:01,179
那都是属于这个敌人身上的组件

899
00:38:01,179 --> 00:38:04,820
所以我们在这里就可以得到敌人的组件

900
00:38:04,820 --> 00:38:07,219
拿到敌人的脚本组件以后

901
00:38:07,219 --> 00:38:09,960
我们就可以调用我们自己的代方法

902
00:38:10,778 --> 00:38:14,289
哎这样的话就相当于我们每次子弹碰到敌人

903
00:38:14,289 --> 00:38:15,938
然后我我把自己销毁了

904
00:38:15,938 --> 00:38:17,679
我还要告诉敌人啊

905
00:38:17,679 --> 00:38:20,250
告诉这个敌人说你要死亡

906
00:38:20,250 --> 00:38:22,110
那么敌人就会执行这个方法

907
00:38:22,110 --> 00:38:24,199
就会也把自己销毁了

908
00:38:24,460 --> 00:38:26,800
当然这个等一下我们会做一些别的事情啊

909
00:38:26,800 --> 00:38:28,960
我们现在先让他立刻销毁

910
00:38:28,960 --> 00:38:30,579
我们现在运行下效果

911
00:38:30,579 --> 00:38:32,059
看看有没有啊

912
00:38:36,219 --> 00:38:37,239
我子弹碰到敌人

913
00:38:37,239 --> 00:38:39,519
大家发现子弹和敌人都销毁了啊

914
00:38:39,519 --> 00:38:43,309
那这样的话我们目前为止就ok了啊

915
00:38:43,309 --> 00:38:46,079
这是我们前奏就已经没有问题了

916
00:38:48,418 --> 00:38:49,559
然后我们继续来

917
00:38:49,559 --> 00:38:50,940
我们继续来

918
00:38:56,639 --> 00:38:58,920
死亡死亡这边做完以后

919
00:38:58,920 --> 00:39:00,539
我们接下来做什么事啊

920
00:39:00,539 --> 00:39:04,938
我们接下来我们把这个死亡给他做好一些啊

921
00:39:04,938 --> 00:39:08,778
我们比如说这也是为了把前面的知识给用上啊

922
00:39:08,778 --> 00:39:10,429
我们希望做一个动态加载

923
00:39:10,429 --> 00:39:12,659
因为这里对于敌人而言

924
00:39:12,659 --> 00:39:14,519
不是只有一个图片的

925
00:39:14,519 --> 00:39:16,019
它有一个这个爆炸图片

926
00:39:16,019 --> 00:39:16,500
对不对

927
00:39:16,500 --> 00:39:18,869
所以我希望如果敌人敌人死完了

928
00:39:18,869 --> 00:39:21,358
敌人就变成爆炸图片

929
00:39:21,358 --> 00:39:23,639
然后比如说半秒以后再死亡

930
00:39:23,639 --> 00:39:25,858
也就是说让我们看上半秒爆炸图片

931
00:39:25,858 --> 00:39:27,059
然后他再销毁

932
00:39:27,059 --> 00:39:29,980
这样的话就是给人感觉特别棒啊

933
00:39:29,980 --> 00:39:34,000
那我们到这边怎样给他这个效果啊

934
00:39:34,000 --> 00:39:38,360
首先我们要干嘛要加载这个爆炸图片

935
00:39:38,360 --> 00:39:39,500
我们就不能直接销毁了

936
00:39:39,500 --> 00:39:42,110
我们要加载爆炸图片

937
00:39:42,110 --> 00:39:43,429
然后给他换图片了

938
00:39:43,429 --> 00:39:47,869
是不是就应该给这个敌人的这个飞机换成爆炸的那张图片

939
00:39:47,869 --> 00:39:51,978
也就是要动态加载我们的这个图片了啊

940
00:39:51,978 --> 00:39:53,898
我们把他名字复制一下

941
00:39:53,898 --> 00:39:56,280
我们要动态加载了

942
00:39:56,980 --> 00:39:58,840
那么动态加载的话就很简单

943
00:39:58,840 --> 00:40:00,699
cc.loader

944
00:40:00,699 --> 00:40:03,539
点load r e s

945
00:40:04,699 --> 00:40:08,000
这里我们动态加载的是这个图片

946
00:40:08,000 --> 00:40:12,518
什么类型spirit frame啊

947
00:40:12,518 --> 00:40:18,800
加载的这个方法我就用这个匿名函数r e s

948
00:40:20,798 --> 00:40:24,248
然后在里面呢我们就可以去写this.node

949
00:40:24,248 --> 00:40:27,250
点get confident

950
00:40:27,250 --> 00:40:30,789
我就要得到敌机的敌机的哪一个组件

951
00:40:30,789 --> 00:40:31,960
就是精灵组件

952
00:40:31,960 --> 00:40:34,659
因为我们要换精灵的图片

953
00:40:35,119 --> 00:40:36,980
他的这个图片就是spirit frame

954
00:40:36,980 --> 00:40:41,420
就换成我们加载完成以后的r e s就行了

955
00:40:41,759 --> 00:40:45,119
然后我们先看一下啊

956
00:40:45,119 --> 00:40:47,340
能不能这个换图片了

957
00:40:48,838 --> 00:40:50,699
你看打到他已经换图片了

958
00:40:50,699 --> 00:40:52,559
但是他现在怎么都不会销毁

959
00:40:52,559 --> 00:40:53,659
对不对

960
00:40:55,159 --> 00:40:57,320
我们现在想让它销毁啊

961
00:40:57,320 --> 00:40:58,699
我们现在想让他销毁

962
00:40:58,699 --> 00:41:05,320
那么在这里比如说给他一个也不应该是稍微停留一下

963
00:41:05,320 --> 00:41:07,210
我们比如说300ms吧

964
00:41:07,210 --> 00:41:10,579
毫秒就销毁

965
00:41:11,378 --> 00:41:12,338
那这个怎么做

966
00:41:12,338 --> 00:41:13,838
我们还要给他一个计时器吗

967
00:41:13,838 --> 00:41:15,398
因为这个计时器是单次的

968
00:41:15,398 --> 00:41:16,469
对不对

969
00:41:16,469 --> 00:41:18,750
单次的话我们用刚才那个方法也可以

970
00:41:18,750 --> 00:41:20,190
然后给它设定一次

971
00:41:20,190 --> 00:41:22,260
或者还有一个延时呃

972
00:41:22,260 --> 00:41:25,000
这个这个计时器方法啊

973
00:41:25,000 --> 00:41:27,099
那么准确这个方法叫做延时方法

974
00:41:27,099 --> 00:41:30,969
就是多少多长时间后执行什么什么事

975
00:41:30,969 --> 00:41:32,900
就是set time out

976
00:41:33,378 --> 00:41:35,298
那么它呢也是两个参数

977
00:41:35,298 --> 00:41:36,559
第一个就是一个方法

978
00:41:36,559 --> 00:41:38,358
第二个就是多长多长时间

979
00:41:38,358 --> 00:41:45,559
但是这里因为这个方法并不是我们coos提供的啊

980
00:41:45,559 --> 00:41:47,480
它是java script这边提供的

981
00:41:47,480 --> 00:41:50,659
所以他的这个单位和我们并没同步啊

982
00:41:50,659 --> 00:41:52,869
他这边是用的毫秒啊

983
00:41:52,869 --> 00:41:55,579
所以我们300ms我们就写300啊

984
00:41:55,798 --> 00:41:57,958
意思不是300秒是300ms

985
00:41:57,958 --> 00:41:59,039
一秒是1000ms

986
00:41:59,039 --> 00:41:59,608
对不对

987
00:41:59,608 --> 00:42:01,259
我们在这里300ms以后

988
00:42:01,259 --> 00:42:03,980
我们就让他销毁

989
00:42:04,278 --> 00:42:06,059
然后我们来看一下效果

990
00:42:11,980 --> 00:42:12,460
打一下

991
00:42:12,460 --> 00:42:14,260
你看闪一下那个爆炸图片

992
00:42:14,260 --> 00:42:15,760
然后就没了这个效果啊

993
00:42:15,760 --> 00:42:17,280
就是我们要的

994
00:42:18,398 --> 00:42:21,199
然后我们回来

995
00:42:21,199 --> 00:42:23,480
那接下来我们就可以做这个敌人的移动了

996
00:42:26,079 --> 00:42:27,280
也是和子弹一样

997
00:42:27,280 --> 00:42:28,780
除了下面的屏幕就销毁

998
00:42:28,780 --> 00:42:29,260
对不对

999
00:42:29,260 --> 00:42:30,519
所以这个逻辑都一样

1000
00:42:30,519 --> 00:42:32,059
就很好写了

1001
00:42:32,059 --> 00:42:33,340
怎么去写

1002
00:42:33,780 --> 00:42:36,619
移动的话出屏幕销毁啊

1003
00:42:36,619 --> 00:42:38,449
先移动再出屏幕销毁

1004
00:42:38,449 --> 00:42:41,360
我们移动就是this.node

1005
00:42:41,360 --> 00:42:44,559
点one减等于歌

1006
00:42:44,918 --> 00:42:47,528
比如说我移动速度就不能那么快了

1007
00:42:47,528 --> 00:42:49,059
我300吧

1008
00:42:49,059 --> 00:42:50,168
一秒走300

1009
00:42:50,168 --> 00:42:52,579
一秒走300像素

1010
00:42:52,878 --> 00:42:54,978
然后如果走完以后

1011
00:42:54,978 --> 00:42:57,978
我的y值小于负的

1012
00:42:57,978 --> 00:43:01,159
比如说850啊

1013
00:43:01,358 --> 00:43:03,728
那么也就是说我肯定出了下面的屏幕了

1014
00:43:03,728 --> 00:43:06,068
我就可以把自己销毁了

1015
00:43:06,068 --> 00:43:11,420
我们来运行看一下效果和子弹的逻辑一模一样啊

1016
00:43:12,139 --> 00:43:13,219
你看往下走

1017
00:43:13,219 --> 00:43:14,329
这个速度可以的

1018
00:43:14,329 --> 00:43:16,639
出去以后它就自己销毁了

1019
00:43:17,398 --> 00:43:18,659
完全没有任何问题

1020
00:43:18,659 --> 00:43:20,429
完全没有任何问题

1021
00:43:20,429 --> 00:43:22,230
我们把它放到这个屏幕最外面

1022
00:43:22,230 --> 00:43:24,090
默认他应该是在屏幕最外面的

1023
00:43:24,090 --> 00:43:24,659
对不对

1024
00:43:24,659 --> 00:43:27,199
比如说默认它是在这个位置

1025
00:43:29,358 --> 00:43:30,378
我们打了他

1026
00:43:30,378 --> 00:43:32,239
我们看到一个很诡异的事情

1027
00:43:32,239 --> 00:43:34,338
我们把它打成爆炸图片的时候

1028
00:43:34,338 --> 00:43:36,090
他还在向下移动

1029
00:43:36,090 --> 00:43:38,010
因为实际上打成爆炸的

1030
00:43:38,010 --> 00:43:40,780
它那个它应该没有移动的功能了啊

1031
00:43:40,780 --> 00:43:42,159
他就应该在原地了啊

1032
00:43:42,159 --> 00:43:43,900
所以这时候我们发现这样一个问题了

1033
00:43:43,900 --> 00:43:45,070
我们再优化一下

1034
00:43:45,070 --> 00:43:47,940
就这个移动你不能随时都移动

1035
00:43:47,940 --> 00:43:48,780
你如果死了

1036
00:43:48,780 --> 00:43:50,940
你就不让就不能移动了

1037
00:43:50,940 --> 00:43:56,340
所以我们在这里我们写一个变量保存一下他是否死亡

1038
00:43:56,838 --> 00:43:58,219
这个是否死亡呢

1039
00:43:58,219 --> 00:43:59,298
他是个布尔值

1040
00:43:59,298 --> 00:44:00,648
默认是false

1041
00:44:00,648 --> 00:44:03,259
那么在移动这里我们要判断

1042
00:44:03,259 --> 00:44:04,920
如果

1043
00:44:06,599 --> 00:44:09,570
this there is代为first

1044
00:44:09,570 --> 00:44:10,800
就是没有死亡

1045
00:44:10,800 --> 00:44:14,429
我才允许你怎样移动啊

1046
00:44:14,429 --> 00:44:15,900
那么当我死亡的时候

1047
00:44:15,900 --> 00:44:19,760
我就会把这个类似点一带设置为true啊

1048
00:44:19,760 --> 00:44:21,380
就证明你要你死亡了

1049
00:44:21,380 --> 00:44:22,219
对不对

1050
00:44:22,219 --> 00:44:24,940
那这时候我们再运行看看效果

1051
00:44:27,858 --> 00:44:28,639
打他哎

1052
00:44:28,639 --> 00:44:29,599
你看打他

1053
00:44:29,599 --> 00:44:30,918
他就会在原地停一下

1054
00:44:30,918 --> 00:44:32,239
然后有这样一个爆炸效果

1055
00:44:32,239 --> 00:44:34,480
这个感觉就非常棒了

1056
00:44:35,159 --> 00:44:37,039
但是现在只有一个敌人

1057
00:44:37,039 --> 00:44:39,880
我们接下来这个敌人肯定不是只有一个

1058
00:44:39,880 --> 00:44:40,960
他有无数个这个敌人

1059
00:44:40,960 --> 00:44:41,500
对不对

1060
00:44:41,500 --> 00:44:44,679
我们就要在屏幕上方创建很多敌人

1061
00:44:44,679 --> 00:44:47,659
然后随机位置往下去飞行

1062
00:44:48,878 --> 00:44:52,340
那么这个屏幕的宽度大概就是400

1063
00:44:52,340 --> 00:44:54,199
我们就随机一个0~400啊

1064
00:44:54,199 --> 00:44:55,639
这样的宽度在0~400

1065
00:44:55,639 --> 00:44:57,590
这个宽度往下一直出飞机

1066
00:44:57,590 --> 00:44:59,659
这个东西怎么去做

1067
00:45:00,659 --> 00:45:01,500
很简单啊

1068
00:45:01,500 --> 00:45:02,130
很简单

1069
00:45:02,130 --> 00:45:04,980
在这里我们要创建一个空节点啊

1070
00:45:04,980 --> 00:45:05,880
创建空间点

1071
00:45:05,880 --> 00:45:09,800
你看和刚才那个创建背景空间点的逻辑一样

1072
00:45:09,800 --> 00:45:13,039
这点我直接在这个敌机上面创建这样空节点

1073
00:45:13,039 --> 00:45:15,789
创建下以后就和敌机位置重合

1074
00:45:15,789 --> 00:45:17,460
我把它再拖出来

1075
00:45:18,278 --> 00:45:20,559
然后这个敌机呢默认是不存在的

1076
00:45:20,559 --> 00:45:22,539
所以我们把它拖成一个预设体

1077
00:45:22,539 --> 00:45:25,059
然后把敌机给删除掉啊

1078
00:45:25,059 --> 00:45:27,179
就是你这个敌机默认是没有的

1079
00:45:27,179 --> 00:45:28,590
只有这一个空的点

1080
00:45:28,590 --> 00:45:32,010
这个点我叫做enemy manager

1081
00:45:32,010 --> 00:45:33,630
是敌机管理的一个点啊

1082
00:45:33,630 --> 00:45:36,360
就是用来管理生成这个敌人飞机的

1083
00:45:36,360 --> 00:45:38,639
那那现在我们要添加一个新的脚本

1084
00:45:38,639 --> 00:45:41,880
这个脚本挂在这个空的节点上面

1085
00:45:42,960 --> 00:45:44,610
他的目的就是要生成地基

1086
00:45:44,610 --> 00:45:47,679
叫enemy control manager

1087
00:45:49,500 --> 00:45:53,900
adam in manager挂载到这个空的节点上面来

1088
00:45:55,480 --> 00:45:58,480
然后我们打开这个脚本

1089
00:45:58,480 --> 00:46:00,429
那么在这个脚本里面

1090
00:46:00,429 --> 00:46:04,300
首先第一件事依然是改改一下

1091
00:46:07,298 --> 00:46:08,528
enemy manager

1092
00:46:08,528 --> 00:46:11,699
然后我们把这个脚本清一下

1093
00:46:15,739 --> 00:46:16,699
那么在这里啊

1094
00:46:16,699 --> 00:46:22,039
在这里我们要做的是每隔比如说每隔两秒钟就创建一个敌机

1095
00:46:22,039 --> 00:46:26,099
那首先遗迹的一个预设体

1096
00:46:28,219 --> 00:46:30,829
敌机预设体我们是要拿到的

1097
00:46:30,829 --> 00:46:32,840
at property

1098
00:46:34,838 --> 00:46:37,838
cc.pre fab对不对

1099
00:46:37,838 --> 00:46:44,099
然后enemy pre cc.pf就等于一个n

1100
00:46:45,699 --> 00:46:51,519
嗯然后在这里我们一定要关联一下这个敌机预设体

1101
00:46:51,519 --> 00:46:53,710
选中这个空的节点

1102
00:46:53,710 --> 00:46:56,019
把敌机拖拽过来啊

1103
00:46:56,019 --> 00:46:57,130
预设体关联好

1104
00:46:57,130 --> 00:46:59,400
然后回来在这边

1105
00:46:59,418 --> 00:47:02,039
这里每

1106
00:47:04,019 --> 00:47:07,079
两秒创建一个dj

1107
00:47:08,699 --> 00:47:13,938
就是this点这个这个这个schedule啊

1108
00:47:13,938 --> 00:47:15,760
那么这个就是

1109
00:47:17,699 --> 00:47:18,420
计时器啊

1110
00:47:18,420 --> 00:47:20,670
一定不要忘了这个计时器很长

1111
00:47:20,670 --> 00:47:23,340
那么这个this schedule

1112
00:47:23,340 --> 00:47:29,739
然后这个后面c9 c9 

1113
00:47:29,739 --> 00:47:34,918
我们这边就直接给他两秒掉一个函数好了啊

1114
00:47:34,918 --> 00:47:37,260
我们其他的也是不用给他

1115
00:47:39,079 --> 00:47:40,860
每隔两秒

1116
00:47:41,079 --> 00:47:43,170
这里注意单位就是秒啊

1117
00:47:43,170 --> 00:47:44,880
这个跟那个定时器就不一样了

1118
00:47:44,880 --> 00:47:46,699
跟那个延时的就不一样了

1119
00:47:46,699 --> 00:47:50,690
因为这个这个计时器是属于cos给我们提供的啊

1120
00:47:50,690 --> 00:47:52,280
cos提供的一个计时器

1121
00:47:52,280 --> 00:47:54,500
那么这个计时器的话

1122
00:47:56,898 --> 00:47:57,318
怎么说呢

1123
00:47:57,318 --> 00:48:01,338
这个计时器啊相对而言会在我们写游戏的时候啊

1124
00:48:01,338 --> 00:48:02,869
会跟我们这个游戏啊

1125
00:48:02,869 --> 00:48:05,639
就是兼容问题会更加小一些啊

1126
00:48:05,639 --> 00:48:09,179
那么一般的话看怎么说啊

1127
00:48:09,179 --> 00:48:10,500
两个都可以啊

1128
00:48:16,059 --> 00:48:17,380
它要一直循环发生

1129
00:48:17,380 --> 00:48:18,699
我就用这个计时器啊

1130
00:48:18,699 --> 00:48:22,130
如果就是每隔多少多长时间后调用一次

1131
00:48:22,130 --> 00:48:23,329
用set time out啊

1132
00:48:24,590 --> 00:48:25,909
当然你也可以

1133
00:48:25,909 --> 00:48:28,760
比如说6号一啊

1134
00:48:28,760 --> 00:48:31,579
这个效果就是跟那个set time out一样的啊

1135
00:48:31,579 --> 00:48:33,500
就是只会调用一次啊

1136
00:48:33,500 --> 00:48:35,639
多少秒后只会调用一次

1137
00:48:39,719 --> 00:48:42,059
然后我们在这里创建敌机好了

1138
00:48:42,059 --> 00:48:47,000
创建敌机light enemy

1139
00:48:47,000 --> 00:48:50,780
就等一个cc点点

1140
00:48:50,780 --> 00:48:53,599
enemy pre创建就很简单了

1141
00:48:53,599 --> 00:48:56,030
创建好以后设置附体

1142
00:48:56,030 --> 00:48:59,139
set parent cc点

1143
00:48:59,139 --> 00:49:02,260
我们还是直接加到我们的这个场景上

1144
00:49:04,239 --> 00:49:05,440
加到我们场景上以后

1145
00:49:05,440 --> 00:49:08,840
我们设置一下我们的y轴

1146
00:49:09,119 --> 00:49:12,179
我们y轴应该是跟我们这个空节点的y轴是一样的

1147
00:49:12,179 --> 00:49:15,300
但是x轴就是横向的

1148
00:49:15,300 --> 00:49:17,280
y轴肯定也在这个位置

1149
00:49:17,280 --> 00:49:21,019
横向的x轴我们就要给它一个随机数了

1150
00:49:21,059 --> 00:49:23,639
随机数怎么给啊

1151
00:49:23,639 --> 00:49:24,539
非常简单

1152
00:49:24,539 --> 00:49:28,260
就等于一个max讲random

1153
00:49:28,260 --> 00:49:30,599
random是一个0~1的随机数

1154
00:49:30,599 --> 00:49:34,610
我们要给他个0~400的随机数乘以个400就行了啊

1155
00:49:34,610 --> 00:49:37,309
如果比如说我们要给一个20~420的随机数

1156
00:49:37,309 --> 00:49:39,230
怎么给你先给个0~400

1157
00:49:39,230 --> 00:49:40,190
再加个20

1158
00:49:40,190 --> 00:49:42,829
这样的随机数就是20~420

1159
00:49:42,829 --> 00:49:43,869
对不对啊

1160
00:49:43,869 --> 00:49:46,030
这就是给了他一个x的一个随机数

1161
00:49:46,030 --> 00:49:47,409
然后现在我们来运行一下

1162
00:49:47,409 --> 00:49:48,900
看下效果

1163
00:49:53,780 --> 00:49:58,449
没了打一下没了打一下没了

1164
00:49:58,449 --> 00:50:00,340
ok是不是就完全没问题了

1165
00:50:04,460 --> 00:50:07,099
打敌机和敌机打我们玩家是一个道理

1166
00:50:07,099 --> 00:50:09,349
如果你希望现在敌机碰到我们玩家

1167
00:50:09,349 --> 00:50:10,849
玩家也死亡

1168
00:50:12,260 --> 00:50:17,230
跟我们的这个刚才写的这个子弹打敌机的逻辑完全一样啊

1169
00:50:17,230 --> 00:50:18,820
完全是一样的

1170
00:50:20,460 --> 00:50:24,719
ok那我们这这个这节课的主要内容就这么多

1171
00:50:28,500 --> 00:50:30,420
然后补充一下

1172
00:50:30,460 --> 00:50:32,650
就是一些很小的知识点啊

1173
00:50:32,650 --> 00:50:35,340
比如说计时器这些内容

1174
00:50:37,219 --> 00:50:39,800
ok那我们这节课就这么多


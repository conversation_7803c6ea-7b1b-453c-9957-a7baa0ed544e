@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    批量重命名工具
echo    删除文件名中的" 中文（自动生成）"
echo ========================================
echo.

:: 检查当前目录是否有需要重命名的文件
set count=0
for %%f in (*中文（自动生成）*) do (
    set /a count+=1
)

if %count%==0 (
    echo 当前目录下没有找到包含"中文（自动生成）"的文件
    echo.
    pause
    exit /b
)

echo 找到 %count% 个需要重命名的文件：
echo.

:: 显示将要重命名的文件
for %%f in (*中文（自动生成）*) do (
    set "filename=%%f"
    set "newname=!filename: 中文（自动生成）=!"
    echo 原文件名: %%f
    echo 新文件名: !newname!
    echo ---
)

echo.
set /p confirm=确认要执行重命名操作吗？(y/n): 

if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 开始重命名...
echo.

set success=0
set failed=0

:: 执行重命名操作
for %%f in (*中文（自动生成）*) do (
    set "filename=%%f"
    set "newname=!filename: 中文（自动生成）=!"
    
    :: 检查目标文件是否已存在
    if exist "!newname!" (
        echo [跳过] %%f - 目标文件已存在
    ) else (
        ren "%%f" "!newname!" 2>nul
        if !errorlevel!==0 (
            echo [成功] %%f
            set /a success+=1
        ) else (
            echo [失败] %%f
            set /a failed+=1
        )
    )
)

echo.
echo ========================================
echo 重命名完成！
echo 成功: %success% 个
echo 失败: %failed% 个
echo ========================================
echo.
pause

1
00:00:09,279 --> 00:00:12,039
ok这节课我们来继续说这个动作啊

2
00:00:12,039 --> 00:00:17,079
那么上节课咱们说了这个带时间属性的这个动作

3
00:00:17,079 --> 00:00:17,649
对不对

4
00:00:17,649 --> 00:00:20,350
就是每个这个动作都是在某个时间内

5
00:00:20,350 --> 00:00:22,149
然后他逐渐完成的

6
00:00:22,149 --> 00:00:25,408
那么这节课咱们说几个瞬时动作

7
00:00:25,408 --> 00:00:27,748
那么其实这个并不是最重要的啊

8
00:00:27,748 --> 00:00:32,618
最重要的就是我们后面会说一些容器性的动作啊

9
00:00:32,618 --> 00:00:35,079
这些容器大家一定要知道啊

10
00:00:35,079 --> 00:00:36,759
那么ok咱们一点点来啊

11
00:00:36,759 --> 00:00:38,740
首先我们还是上还是上一个工程

12
00:00:38,740 --> 00:00:40,939
我们在这里打开脚本

13
00:00:42,240 --> 00:00:44,299
还是对这个方块做操作啊

14
00:00:44,299 --> 00:00:46,918
那首先我们的动作

15
00:00:49,000 --> 00:00:51,270
有个这个cc点瘦

16
00:00:51,270 --> 00:00:51,869
哎

17
00:00:51,869 --> 00:00:55,439
像这些大家可以看右边立即显示

18
00:00:55,439 --> 00:00:56,340
对不对啊

19
00:00:56,340 --> 00:00:58,950
所以像这些已经不用演示了

20
00:00:58,950 --> 00:01:00,149
它都是没有参数的

21
00:01:00,149 --> 00:01:02,189
就是你本来这个如果是隐藏的

22
00:01:02,189 --> 00:01:03,450
你要让他立刻显示

23
00:01:03,450 --> 00:01:04,700
你可以直接用它

24
00:01:05,719 --> 00:01:09,299
那么当然还有比如说有立刻显示

25
00:01:09,700 --> 00:01:11,739
立刻隐藏啊

26
00:01:11,739 --> 00:01:14,079
这些立即性的动作都非常简单啊

27
00:01:14,079 --> 00:01:15,040
咱们在这说一下

28
00:01:15,040 --> 00:01:16,659
大家记一下就ok了

29
00:01:16,659 --> 00:01:18,319
那么还有什么呀

30
00:01:19,359 --> 00:01:27,180
这里有一个是呃啊cc有一个是切换显示

31
00:01:27,180 --> 00:01:27,599
什么意思

32
00:01:27,599 --> 00:01:30,920
就是他自己给你判断显示你调一次

33
00:01:30,920 --> 00:01:32,659
他如果当前没显示

34
00:01:32,659 --> 00:01:33,439
他就给你显示

35
00:01:33,439 --> 00:01:34,879
你再掉一次啊

36
00:01:34,879 --> 00:01:37,700
当前显示了他就给你变成没显示

37
00:01:37,700 --> 00:01:42,489
说白了就是在显示和不显示之间呃进行一个切换啊

38
00:01:43,629 --> 00:01:45,118
这是显示

39
00:01:47,780 --> 00:01:55,519
隐藏切换显示隐藏啊

40
00:01:55,519 --> 00:01:56,959
它是这样的一个意思

41
00:01:56,959 --> 00:02:04,579
然后再往下再说一个翻转翻转我们可以给他两个方向

42
00:02:04,579 --> 00:02:06,590
都可以让它瞬间翻转啊

43
00:02:06,590 --> 00:02:09,209
一个是翻转x方向啊

44
00:02:09,209 --> 00:02:10,889
是沿x轴翻转

45
00:02:10,889 --> 00:02:13,740
还有一个是y轴翻转

46
00:02:13,919 --> 00:02:16,439
那么在这里它是有参数的

47
00:02:16,439 --> 00:02:19,319
我们看一下它的参数是个布尔值啊

48
00:02:19,319 --> 00:02:21,000
是你是不是要反转啊

49
00:02:21,000 --> 00:02:24,490
你true的话就是把x翻转确定要给它翻转

50
00:02:24,490 --> 00:02:26,659
你要是给他a force啊

51
00:02:26,659 --> 00:02:28,699
那么它呢就会变成原来的样子

52
00:02:28,699 --> 00:02:30,740
就是不进行翻转啊

53
00:02:30,740 --> 00:02:35,080
那么这是一个在x轴上翻转的一个呃方法

54
00:02:35,080 --> 00:02:39,639
那么当然就同时也有y轴上是否翻转啊

55
00:02:39,639 --> 00:02:40,539
true或者false

56
00:02:40,539 --> 00:02:42,389
那么它会立刻进行翻转

57
00:02:42,389 --> 00:02:44,020
那么还有什么呀

58
00:02:45,319 --> 00:02:51,579
呃还有一个瞬时动作在这里呃我们说可能没有太大意义

59
00:02:51,579 --> 00:02:52,778
就是action

60
00:02:52,778 --> 00:02:55,680
还有一个叫cc点嗯

61
00:02:55,680 --> 00:02:56,560
call funk

62
00:02:58,438 --> 00:03:01,860
这个啊其实就是让你去填一个

63
00:03:04,058 --> 00:03:05,098
填一个什么呀

64
00:03:05,098 --> 00:03:07,099
填一个回调啊

65
00:03:07,099 --> 00:03:09,020
就是说每次一执行这个动作

66
00:03:09,020 --> 00:03:12,810
这个动作没有任何这个我们可以看得见的操作

67
00:03:12,810 --> 00:03:14,129
这个动作是干嘛的

68
00:03:14,129 --> 00:03:15,330
就是给我们执行一下

69
00:03:15,330 --> 00:03:17,199
我们这个方法就行了啊

70
00:03:17,199 --> 00:03:19,270
你可以在方法里面打印个东西

71
00:03:19,270 --> 00:03:21,610
那这样的话每次一执行这个动作

72
00:03:21,610 --> 00:03:22,030
哎

73
00:03:22,030 --> 00:03:24,069
这里面的内容就被打印出来了啊

74
00:03:24,069 --> 00:03:26,050
就变成一个算是代码动作的

75
00:03:26,050 --> 00:03:26,840
对不对

76
00:03:28,199 --> 00:03:31,259
我们可以给他叫一个回调的动作

77
00:03:34,979 --> 00:03:39,740
那么这些动作啊都是这个瞬间会被执行的啊

78
00:03:39,740 --> 00:03:41,840
瞬间会被执行的非常简单

79
00:03:41,840 --> 00:03:45,139
那么接下来我们来说一下容器动作

80
00:03:46,280 --> 00:03:47,840
什么是容器动作呢

81
00:03:47,840 --> 00:03:51,400
我们比如说啊举个例子啊

82
00:03:52,080 --> 00:03:56,240
我们这个action我们再让它变成这个移动的action

83
00:03:56,240 --> 00:03:58,438
就等于个呃

84
00:03:58,438 --> 00:04:00,118
我们不要用这个移动的了

85
00:04:00,118 --> 00:04:01,599
我们用这个这个这个

86
00:04:03,959 --> 00:04:09,479
飞的in飞的out吧啊c c点先是淡出

87
00:04:20,699 --> 00:04:22,620
我们把回调动作放到这儿

88
00:04:31,800 --> 00:04:33,720
然后我们来执行一下这个动作

89
00:04:33,720 --> 00:04:34,889
this.node

90
00:04:34,889 --> 00:04:38,639
点reaction action

91
00:04:39,420 --> 00:04:41,220
ok我们来运行一下

92
00:04:44,579 --> 00:04:45,600
哎没有了

93
00:04:45,600 --> 00:04:46,170
对不对

94
00:04:46,170 --> 00:04:47,220
逐渐消失

95
00:04:47,220 --> 00:04:49,860
那如果我现在希望做这样一个效果

96
00:04:52,860 --> 00:04:55,860
消失完了以后立刻再给我显示出来

97
00:04:55,860 --> 00:04:56,660
怎么办

98
00:04:58,240 --> 00:05:00,300
那么按大家的做法

99
00:05:00,300 --> 00:05:02,279
很多同学可能会想到一个方法

100
00:05:02,279 --> 00:05:03,300
非常简单

101
00:05:03,300 --> 00:05:05,660
我开一个定时器啊

102
00:05:05,660 --> 00:05:06,889
一秒的定时器

103
00:05:06,889 --> 00:05:08,569
然后我先执行这个动作

104
00:05:08,569 --> 00:05:11,660
然后比如说我再来个set time out

105
00:05:12,060 --> 00:05:13,160
来个定时器

106
00:05:13,160 --> 00:05:15,779
一秒以后我再让他执行什么动作

107
00:05:16,120 --> 00:05:18,160
执行fade in这个动作

108
00:05:18,160 --> 00:05:19,980
就是再让它显示回来

109
00:05:20,339 --> 00:05:22,050
没错这样确实可以

110
00:05:22,050 --> 00:05:26,160
但是在这里咱们要说一个更简单的实现方法啊

111
00:05:26,160 --> 00:05:28,230
那么这个实现方法是什么

112
00:05:28,230 --> 00:05:31,920
就是队列方法或者叫做序列方法

113
00:05:32,300 --> 00:05:34,899
那么这个就属于一个容器动作

114
00:05:36,800 --> 00:05:44,089
我们要做一个这个这个队列或者说一个序列的一个动作啊

115
00:05:44,089 --> 00:05:49,259
那么这个动作我们给它let嗯

116
00:05:49,259 --> 00:05:50,459
要s e q吧

117
00:05:50,459 --> 00:05:52,879
等一个cc.sequence

118
00:05:53,379 --> 00:05:58,180
那么这个动作就是他们并不是说里面让你填什么东西

119
00:05:58,180 --> 00:06:00,798
它让你填一系列的动作啊

120
00:06:00,798 --> 00:06:05,519
然后他会把这一系列的动作挨个给你执行啊

121
00:06:05,519 --> 00:06:07,858
那比如说我们action是这个弹出

122
00:06:07,858 --> 00:06:08,548
对不对

123
00:06:08,548 --> 00:06:12,040
那我们再来一个action 2

124
00:06:12,139 --> 00:06:13,639
等一个cc点

125
00:06:13,639 --> 00:06:14,629
fade out

126
00:06:14,629 --> 00:06:15,699
fade in

127
00:06:15,959 --> 00:06:18,000
淡入淡出淡入

128
00:06:18,000 --> 00:06:18,360
对不对

129
00:06:18,360 --> 00:06:22,339
那我这个里面就可以填action action

130
00:06:24,220 --> 00:06:28,339
那这样的话它就是先淡出再代入啊

131
00:06:28,339 --> 00:06:29,870
它成为一个队列动作

132
00:06:29,870 --> 00:06:34,060
我们就可以在这里执行这个队列动作所困死

133
00:06:34,120 --> 00:06:35,740
那么这就是一个容器动作

134
00:06:35,740 --> 00:06:37,120
你看它本身也是个动作

135
00:06:37,120 --> 00:06:38,079
他也能被执行

136
00:06:38,079 --> 00:06:38,899
对不对

137
00:06:39,879 --> 00:06:42,439
那么在这里我们来运行一下

138
00:06:44,920 --> 00:06:46,740
诶没了诶又出来了

139
00:06:46,740 --> 00:06:47,879
对不对啊

140
00:06:47,879 --> 00:06:49,500
就感觉非常不错啊

141
00:06:49,500 --> 00:06:50,189
非常不错

142
00:06:50,189 --> 00:06:52,379
这就是一个队列动作啊

143
00:06:52,379 --> 00:06:53,939
这就是一个队列动作

144
00:06:53,939 --> 00:06:56,040
那么这时候有一个问题啊

145
00:06:56,040 --> 00:06:59,949
比如说我去你看他就闪那么一次就完了

146
00:06:59,949 --> 00:07:02,800
如果我希望它和什么警告灯一样

147
00:07:02,800 --> 00:07:05,620
他可以闪烁我们指定的次数

148
00:07:05,620 --> 00:07:08,620
比如说我希望它闪三次怎么办

149
00:07:08,620 --> 00:07:09,279
原来同学说

150
00:07:09,279 --> 00:07:13,300
那我能不能后面逗号再action action 2 action action 2啊

151
00:07:13,300 --> 00:07:14,149
不用那样

152
00:07:14,149 --> 00:07:17,089
我们有这样的另外一个容器动作啊

153
00:07:17,089 --> 00:07:19,069
另外一个容器动作啊

154
00:07:19,069 --> 00:07:20,220
非常简单

155
00:07:21,259 --> 00:07:23,738
let另外一个容器动作叫什么

156
00:07:23,738 --> 00:07:24,999
就叫repeat

157
00:07:24,999 --> 00:07:28,180
等于一个cc.sequence

158
00:07:28,519 --> 00:07:33,098
那么这个repeat就是你这里啊

159
00:07:33,098 --> 00:07:35,619
你这里需要让你去填上啊

160
00:07:35,619 --> 00:07:36,908
不是cs sequence

161
00:07:36,908 --> 00:07:39,519
我是cc.repeat啊

162
00:07:40,439 --> 00:07:42,379
cc.repeat很简单

163
00:07:42,379 --> 00:07:44,209
就是让你前面填一个动作

164
00:07:44,209 --> 00:07:46,389
后面是次数啊

165
00:07:46,389 --> 00:07:47,408
然后我们在这里

166
00:07:47,408 --> 00:07:49,088
比如说把这个序列填上

167
00:07:49,088 --> 00:07:51,608
我们就要执行这个序列动作

168
00:07:51,608 --> 00:07:52,629
执行几次

169
00:07:52,629 --> 00:07:54,439
比如说执行三次

170
00:07:54,658 --> 00:07:56,000
我们看一下

171
00:07:56,839 --> 00:07:59,439
然后这里要改一下repeat

172
00:07:59,439 --> 00:08:01,899
执行这个重复动作

173
00:08:04,560 --> 00:08:09,959
你看闪一下一下一下

174
00:08:09,959 --> 00:08:11,160
效果很不错

175
00:08:11,160 --> 00:08:11,939
对不对

176
00:08:12,259 --> 00:08:14,360
那么这个就是重复动作

177
00:08:14,360 --> 00:08:18,160
那有时候我们对于这个重复要求比较高

178
00:08:18,160 --> 00:08:22,149
怎样我希望这个重复动作可以一直重复下去啊

179
00:08:22,149 --> 00:08:23,139
那怎么办

180
00:08:23,139 --> 00:08:27,379
它有一个叫做repeat forever

181
00:08:28,420 --> 00:08:29,819
就是这个repeat forever

182
00:08:29,819 --> 00:08:32,309
就是只要开始了就一直重复啊

183
00:08:32,309 --> 00:08:35,440
它就是不管和次数没关系了啊

184
00:08:35,440 --> 00:08:36,039
repeter fly

185
00:08:36,039 --> 00:08:37,000
我就一直重复

186
00:08:37,000 --> 00:08:38,379
我们运行看一下

187
00:08:42,019 --> 00:08:43,879
闪闪

188
00:08:43,879 --> 00:08:47,120
这时候只要我们不停止这个动作啊

189
00:08:47,120 --> 00:08:49,179
那么他就会在这一直闪

190
00:08:49,580 --> 00:08:52,850
你比如说游戏里面有哪个什么警告灯啊什么的

191
00:08:52,850 --> 00:08:54,500
只要出现他就一直在闪

192
00:08:54,500 --> 00:08:55,669
你就可以用这个去做

193
00:08:55,669 --> 00:08:56,539
对不对

194
00:09:00,679 --> 00:09:01,809
ok啊

195
00:09:01,809 --> 00:09:03,759
那么我们继续来

196
00:09:03,759 --> 00:09:04,740
我们继续

197
00:09:06,759 --> 00:09:07,919
除了这三个

198
00:09:07,919 --> 00:09:09,059
还有一种动作

199
00:09:09,059 --> 00:09:10,320
还有一种动作啊

200
00:09:10,320 --> 00:09:12,120
那我们现在来说另外一种动作

201
00:09:12,120 --> 00:09:13,799
另外一种动作是什么

202
00:09:14,299 --> 00:09:19,559
比如说我希望让一个物体移动怎么办

203
00:09:19,940 --> 00:09:21,860
我希望让一个物体移动啊

204
00:09:21,860 --> 00:09:22,940
我们在这儿重新写吧

205
00:09:22,940 --> 00:09:26,490
就move等一个cc点

206
00:09:26,490 --> 00:09:27,539
move to

207
00:09:27,539 --> 00:09:29,539
我希望它移动到一个位置

208
00:09:30,259 --> 00:09:32,639
比如说三秒钟时间移动到

209
00:09:34,360 --> 00:09:37,440
500逗号500这样的一个位置

210
00:09:37,440 --> 00:09:38,399
然后让它移动

211
00:09:38,399 --> 00:09:39,879
我们先看移动

212
00:09:43,600 --> 00:09:45,480
它会移动没有问题啊

213
00:09:45,480 --> 00:09:51,139
那么如果我现在希望他还有一个动作是可以变颜色

214
00:09:51,139 --> 00:09:51,860
大家记得吧

215
00:09:51,860 --> 00:09:52,700
变颜色

216
00:09:52,700 --> 00:09:55,529
那我现在既想让他移动

217
00:09:55,529 --> 00:09:56,549
又想让它变颜色

218
00:09:56,549 --> 00:09:58,870
但是我不希望它是有顺序的

219
00:09:58,870 --> 00:10:00,620
我希望他是

220
00:10:02,940 --> 00:10:04,440
并列执行的啊

221
00:10:04,440 --> 00:10:07,019
就是你在移动的时候同时变成颜色

222
00:10:07,019 --> 00:10:09,120
那这种并列动作怎么办

223
00:10:10,659 --> 00:10:13,110
并列动作啊

224
00:10:13,110 --> 00:10:14,549
并列动作怎么办

225
00:10:14,549 --> 00:10:16,139
我们再来个颜色的

226
00:10:16,139 --> 00:10:17,730
首先再来个颜色的啊

227
00:10:17,730 --> 00:10:19,399
那个cc点

228
00:10:20,759 --> 00:10:28,200
比如说颜色我们要让他三秒钟变到这个咱们刚才的你都给他

229
00:10:29,759 --> 00:10:32,059
我给个换个颜色吧

230
00:10:34,840 --> 00:10:35,950
换个颜色

231
00:10:35,950 --> 00:10:39,399
那么这时候这两个我要并行执行怎么办

232
00:10:39,399 --> 00:10:42,159
那么这里面就有一个盘

233
00:10:45,240 --> 00:10:49,460
这个东西就是来并行执行的啊

234
00:10:49,460 --> 00:10:50,899
它里面可以填很多动作

235
00:10:50,899 --> 00:10:52,899
跟我们那个队列一样

236
00:10:52,899 --> 00:10:54,730
它也是可以填很多动作的

237
00:10:54,730 --> 00:10:58,960
但是区别就是它里面填的动作都是并列执行的

238
00:10:58,960 --> 00:11:02,139
然后我们可以在这里执行这个动作

239
00:11:02,139 --> 00:11:02,919
这个容器

240
00:11:06,220 --> 00:11:11,590
开始哎你看执行就开始变黄啊

241
00:11:11,590 --> 00:11:15,519
到了这里移动到这里就变成真正的完全的黄色了

242
00:11:15,519 --> 00:11:18,580
那么它呢就可以让多个动作不止两个

243
00:11:18,580 --> 00:11:21,039
让多个动作并行执行啊

244
00:11:21,039 --> 00:11:22,600
并行执行啊

245
00:11:22,600 --> 00:11:26,350
那么这些啊就是我们的容器动作啊

246
00:11:26,350 --> 00:11:27,759
这些就是我们的容器动作

247
00:11:27,759 --> 00:11:28,419
大家注意啊

248
00:11:28,419 --> 00:11:34,120
比如说你看我现在我现在要执行咱们这个序列动作啊

249
00:11:34,120 --> 00:11:35,320
比如说要执行序列动作

250
00:11:35,320 --> 00:11:37,299
你就填序列啊

251
00:11:37,299 --> 00:11:40,120
那么这时候我们先看一下啊

252
00:11:42,279 --> 00:11:43,960
啊这是让他闪烁一下

253
00:11:43,960 --> 00:11:44,799
是不是

254
00:11:45,159 --> 00:11:46,480
那么这里注意啊

255
00:11:46,480 --> 00:11:47,679
咱们还有一个动作

256
00:11:47,679 --> 00:11:49,899
其实在这里常常pad上用场就什么呀

257
00:11:49,899 --> 00:11:51,500
就这个回调函数

258
00:11:51,679 --> 00:11:54,679
cc.cofk啊

259
00:11:54,679 --> 00:11:56,360
比如说每一次动作完了

260
00:11:56,360 --> 00:11:58,220
我都会打印一个东西

261
00:11:58,220 --> 00:11:59,620
你就可以在这里

262
00:12:02,759 --> 00:12:04,500
cc.cofunk

263
00:12:04,500 --> 00:12:04,769
诶

264
00:12:04,769 --> 00:12:05,639
不是不是

265
00:12:05,639 --> 00:12:08,240
这里应该是cf卡

266
00:12:08,240 --> 00:12:09,818
在这个括号里面

267
00:12:12,759 --> 00:12:15,820
我们可以在这直接加一个cfd回调函数

268
00:12:15,820 --> 00:12:19,779
这样的话这个队列这个动作就是先执行第一个动作

269
00:12:19,779 --> 00:12:21,250
再执行第二个动作

270
00:12:21,250 --> 00:12:22,149
动作完了以后

271
00:12:22,149 --> 00:12:24,700
然后来执行我们的代码啊

272
00:12:24,700 --> 00:12:27,480
比如说一个灯啊

273
00:12:27,480 --> 00:12:29,340
一个灯闪烁了三次以后

274
00:12:29,340 --> 00:12:31,019
我们就让闪烁一次啊

275
00:12:31,019 --> 00:12:31,919
就闪烁一次

276
00:12:31,919 --> 00:12:33,169
我们就让它爆炸

277
00:12:33,169 --> 00:12:35,690
就让这个灯所在的这个位置爆炸

278
00:12:35,690 --> 00:12:38,409
你就可以闪烁

279
00:12:38,409 --> 00:12:39,340
写完以后

280
00:12:39,340 --> 00:12:41,379
然后直接写写一个回调

281
00:12:41,379 --> 00:12:43,090
在回调里面执行爆炸操作

282
00:12:43,090 --> 00:12:45,279
那这样的话这个动作一直行

283
00:12:45,379 --> 00:12:48,019
就是闪烁闪烁完了以后爆炸

284
00:12:48,019 --> 00:12:48,830
是不是

285
00:12:48,830 --> 00:12:53,379
那在它里面就可以写一些我们自己的一些代码啊

286
00:12:53,379 --> 00:12:54,700
很好用啊

287
00:12:54,700 --> 00:12:57,250
那么还有一个还有最后一个动作

288
00:12:57,250 --> 00:13:00,460
最后一个动作就是之前在之前说没有意义

289
00:13:00,460 --> 00:13:02,159
还是也是放到这儿才说

290
00:13:02,159 --> 00:13:03,720
比如说我希望灯直行

291
00:13:08,610 --> 00:13:11,490
那么这里就有一个cc点

292
00:13:11,490 --> 00:13:12,740
daily time

293
00:13:13,080 --> 00:13:15,000
这个动作就是什么呀

294
00:13:15,000 --> 00:13:16,590
纯岩石啊

295
00:13:16,590 --> 00:13:18,690
比如说延时一秒就停一秒

296
00:13:18,690 --> 00:13:21,720
这个动作执行完它执行完它停一秒

297
00:13:21,720 --> 00:13:23,669
然后再执行它啊

298
00:13:23,669 --> 00:13:24,899
就是这样的一个意思

299
00:13:24,899 --> 00:13:25,950
那么注意啊

300
00:13:25,950 --> 00:13:27,839
这两个动作的用法啊

301
00:13:29,539 --> 00:13:31,120
这个是单纯的岩石

302
00:13:31,120 --> 00:13:32,019
没有任何意义啊

303
00:13:32,019 --> 00:13:33,830
这个是单纯的回调啊

304
00:13:33,830 --> 00:13:38,809
执行它动作执行完他执行完它执行到它就等一秒一秒完了

305
00:13:38,809 --> 00:13:40,669
直接跳过他执行它啊

306
00:13:40,669 --> 00:13:44,219
执行它就是掉里面的这个回调就ok了

307
00:13:44,779 --> 00:13:48,139
那么这个就是咱们的动作啊

308
00:13:48,139 --> 00:13:48,799
动作部分

309
00:13:48,799 --> 00:13:50,960
那我们动作就说到这里

310
00:13:53,759 --> 00:13:54,759
略略略


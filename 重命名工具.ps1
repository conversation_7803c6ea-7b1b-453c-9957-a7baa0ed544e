# Batch Rename Tool - Remove Chinese Auto-Generated Text
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Batch Rename Tool" -ForegroundColor Green
Write-Host "    Remove Chinese Auto-Generated Text" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Get all files containing Chinese auto-generated text
$files = Get-ChildItem -File | Where-Object { $_.Name -like "*中文*自动生成*" }

if ($files.Count -eq 0) {
    Write-Host "当前目录下没有找到需要重命名的文件" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按回车键退出"
    exit
}

Write-Host "找到 $($files.Count) 个文件需要重命名：" -ForegroundColor Green
Write-Host ""

# 显示将要重命名的文件
foreach ($file in $files) {
    $newName = $file.Name -replace " 中文（自动生成）", ""
    Write-Host "原文件: " -NoNewline -ForegroundColor Cyan
    Write-Host $file.Name -ForegroundColor White
    Write-Host "新文件: " -NoNewline -ForegroundColor Green
    Write-Host $newName -ForegroundColor White
    Write-Host "---"
}

Write-Host ""
$confirm = Read-Host "确认要执行重命名操作吗？(输入 y 确认)"

if ($confirm -eq "y" -or $confirm -eq "Y") {
    Write-Host ""
    Write-Host "开始重命名..." -ForegroundColor Green
    
    $success = 0
    $failed = 0
    
    foreach ($file in $files) {
        try {
            $newName = $file.Name -replace " 中文（自动生成）", ""
            
            if (Test-Path $newName) {
                Write-Host "[跳过] $($file.Name) - 目标文件已存在" -ForegroundColor Yellow
            } else {
                Rename-Item -Path $file.FullName -NewName $newName
                Write-Host "[成功] $($file.Name)" -ForegroundColor Green
                $success++
            }
        }
        catch {
            Write-Host "[失败] $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
            $failed++
        }
    }
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "重命名完成！" -ForegroundColor Green
    Write-Host "成功: $success 个" -ForegroundColor Green
    Write-Host "失败: $failed 个" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Cyan
} else {
    Write-Host "操作已取消" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "按回车键退出"

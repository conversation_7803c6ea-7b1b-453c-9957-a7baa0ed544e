1
00:00:08,960 --> 00:00:12,509
ok这节课我们来继续说这个物理系统

2
00:00:12,509 --> 00:00:14,609
那么这个物理系统啊

3
00:00:14,609 --> 00:00:16,410
咱们已经说了这个面板了

4
00:00:16,410 --> 00:00:20,519
那咱们接下来来说一下这个缸体在脚本里面怎么怎么用

5
00:00:20,519 --> 00:00:23,420
首先啊我们在这里能看到的啊

6
00:00:23,420 --> 00:00:24,739
在这里能看到的属性

7
00:00:24,739 --> 00:00:27,710
通通我们都可以在脚本里面对它进行操作

8
00:00:27,710 --> 00:00:29,300
对钢铁进行操作

9
00:00:29,300 --> 00:00:34,060
那么除此之外还有一些还有几个内容啊

10
00:00:34,060 --> 00:00:36,609
我们是要单独拿出来说一下的啊

11
00:00:36,609 --> 00:00:38,299
那么我们来看一下

12
00:00:38,320 --> 00:00:41,049
首先打开我们这个脚本

13
00:00:41,049 --> 00:00:43,878
在这里我们先要获取一下钢铁

14
00:00:46,719 --> 00:00:53,310
啊boy我们就等于一个呃这个这个这个this点

15
00:00:53,310 --> 00:00:56,700
get component cc点

16
00:00:56,700 --> 00:00:58,238
ready body

17
00:00:58,880 --> 00:01:00,850
那我们就获取了这个钢铁了

18
00:01:00,850 --> 00:01:01,479
about

19
00:01:01,479 --> 00:01:04,209
那么我们可以在这里给它一个力

20
00:01:04,209 --> 00:01:05,519
给它一个力

21
00:01:05,680 --> 00:01:06,760
怎么给他立

22
00:01:06,760 --> 00:01:08,439
首先啊咱们那个面板上的

23
00:01:08,439 --> 00:01:08,859
我说了

24
00:01:08,859 --> 00:01:12,090
你看比如说这个子弹检测是吧

25
00:01:12,090 --> 00:01:14,099
就是超高速的这个检测

26
00:01:14,099 --> 00:01:17,709
这些面板上有的我们在这里都可以操作它

27
00:01:17,709 --> 00:01:20,349
除此之外我们来说一些其他的

28
00:01:20,349 --> 00:01:23,340
那么第一个就是我们可以给它应用一个力

29
00:01:24,159 --> 00:01:25,620
我们可以给它一个力

30
00:01:25,620 --> 00:01:26,790
给它一个力

31
00:01:26,790 --> 00:01:30,450
那么这个力呢我们有三个参数啊

32
00:01:30,450 --> 00:01:35,439
第一个参数我们要给它一个x和y方向上的一个力

33
00:01:35,439 --> 00:01:37,299
那么它是一个结构体啊

34
00:01:37,299 --> 00:01:38,750
第一个参数是个结构体

35
00:01:38,750 --> 00:01:40,319
cc.v2 

36
00:01:40,340 --> 00:01:44,239
那么我们要给它x方向上一个多大的力啊

37
00:01:44,239 --> 00:01:45,799
那么这里是牛啊

38
00:01:45,799 --> 00:01:47,939
多少大多大的一个力

39
00:01:49,260 --> 00:01:51,109
我们给个1000

40
00:01:51,109 --> 00:01:53,539
比如说y轴我们不给它一个力

41
00:01:54,519 --> 00:01:57,659
那么在这里这是第一个啊

42
00:01:57,659 --> 00:02:00,269
就是说第一个要求我们给它一个力

43
00:02:00,269 --> 00:02:02,819
那么在x方向上给它一个多大的力

44
00:02:02,819 --> 00:02:05,599
在y轴方向上给它一个多大的力

45
00:02:05,599 --> 00:02:09,139
那么第二个仍然是一个点

46
00:02:09,139 --> 00:02:10,669
第二个仍然是一个点

47
00:02:10,669 --> 00:02:13,718
这个点是指如果一个物体

48
00:02:13,718 --> 00:02:15,038
比如说一个物体很大

49
00:02:15,038 --> 00:02:15,579
对不对

50
00:02:15,579 --> 00:02:16,538
一个物体很大

51
00:02:16,538 --> 00:02:19,479
那么你给力的时候是给这个物体的哪里

52
00:02:19,479 --> 00:02:22,830
这个力比如说你可能给这个物体下面能力

53
00:02:22,830 --> 00:02:25,169
那么这个物体可能就被你推着走了

54
00:02:25,169 --> 00:02:27,060
但如果这个物体又很高

55
00:02:27,060 --> 00:02:29,310
你给物体靠上的力

56
00:02:29,310 --> 00:02:32,360
可能你就把这个物体给怎样给推倒了

57
00:02:32,360 --> 00:02:33,259
对不对

58
00:02:33,259 --> 00:02:36,199
所以这个力给在一个物体

59
00:02:36,199 --> 00:02:37,580
给在它哪个点上

60
00:02:37,580 --> 00:02:38,900
它也有不同的效果

61
00:02:38,900 --> 00:02:42,120
那么这个就是这个点啊

62
00:02:42,120 --> 00:02:44,189
比如说我们就默认给到他零零点上

63
00:02:44,189 --> 00:02:48,259
然后第最后一个就是立刻我们运用就行了处

64
00:02:48,259 --> 00:02:51,560
那么这是一个比较高级的应用方式

65
00:02:51,560 --> 00:02:52,969
就是你给一个力

66
00:02:52,969 --> 00:02:56,219
然后这个力给他到哪个点上啊

67
00:02:56,219 --> 00:03:00,439
那么但是我们更多的可能会用这个方法

68
00:03:03,438 --> 00:03:05,498
我们更多的可能会用这个方法啊

69
00:03:05,498 --> 00:03:07,118
这个方法就是我们直接给他一个力

70
00:03:07,118 --> 00:03:08,799
就给他中心一个力啊

71
00:03:08,799 --> 00:03:11,560
给他中心一个力就ok了啊

72
00:03:11,560 --> 00:03:14,979
因为很多很多情况下我们不需要做到那么精准啊

73
00:03:14,979 --> 00:03:16,509
所以我们给他中心一个力

74
00:03:16,509 --> 00:03:22,120
那么其实就是比如说给右边一个5000牛的力零

75
00:03:22,800 --> 00:03:27,020
然后只有第二个参数处就行了

76
00:03:27,020 --> 00:03:28,459
它就没有这个点了

77
00:03:28,459 --> 00:03:31,038
然后在这里我们可以运行看一下效果

78
00:03:31,038 --> 00:03:32,899
本来它应该是直接掉下来的

79
00:03:32,899 --> 00:03:33,799
对不对啊

80
00:03:33,799 --> 00:03:35,118
我们这个鸟直接掉下来的

81
00:03:35,118 --> 00:03:37,120
现在看一下诶

82
00:03:37,120 --> 00:03:39,939
你看是不是就是这样一个抛物线的效果了

83
00:03:39,939 --> 00:03:42,289
那就是因为我们给了它一个向右的力

84
00:03:42,289 --> 00:03:44,449
那么它会向右有一点力

85
00:03:44,449 --> 00:03:46,669
同时他又受到重重力的影响

86
00:03:46,669 --> 00:03:50,229
所以他就这样有一个抛物线啊

87
00:03:50,229 --> 00:03:51,719
那这就是给他一个力

88
00:03:52,618 --> 00:03:54,799
那么除了力可以让它运动

89
00:03:54,799 --> 00:03:58,459
我们在面板上看到了它还有一个方向可以让它运动

90
00:03:58,459 --> 00:04:00,460
就是更改它的速度啊

91
00:04:00,460 --> 00:04:04,189
our bo还有一个方式就是更改它的一个速度

92
00:04:04,189 --> 00:04:05,930
有一个旋转的角速度

93
00:04:05,930 --> 00:04:06,349
对不对

94
00:04:06,349 --> 00:04:08,169
还有一个线性速度啊

95
00:04:08,169 --> 00:04:09,189
我们要让它动起来

96
00:04:09,189 --> 00:04:11,110
我们就要更改这个线性速度

97
00:04:11,110 --> 00:04:14,819
那么在这里你可以直接cc.v2 

98
00:04:14,819 --> 00:04:17,939
那么线速度一般不用给他多大啊

99
00:04:17,939 --> 00:04:20,699
因为它这个速度就是每秒多少像素

100
00:04:20,699 --> 00:04:22,509
它是按这个来的了啊

101
00:04:22,509 --> 00:04:24,699
所以在这里比如说给他个500

102
00:04:26,240 --> 00:04:30,040
500比这个力小于了一倍了是吧

103
00:04:30,040 --> 00:04:31,199
然后我们看一下

104
00:04:33,300 --> 00:04:37,610
它的效果你看这既是如此

105
00:04:37,610 --> 00:04:39,860
它右边仍然是非常快的

106
00:04:39,860 --> 00:04:44,019
这样的一个仍然是一个非常大的速度啊

107
00:04:44,019 --> 00:04:49,160
因为500每秒500这个像素是一个很大的一个速度啊

108
00:04:49,180 --> 00:04:50,800
那你要是想让它小一点

109
00:04:50,800 --> 00:04:54,519
你可以给他个50 50的话

110
00:04:54,519 --> 00:04:58,089
应该就那就差不太多了

111
00:04:58,089 --> 00:05:00,399
诶你看跟刚才那个差不太多了

112
00:05:00,399 --> 00:05:01,889
对不对啊

113
00:05:01,889 --> 00:05:04,560
那这两种方式都可以给它一个力

114
00:05:04,560 --> 00:05:05,699
让它动起来啊

115
00:05:05,699 --> 00:05:08,339
只是速度的话跟那个力是不一样的

116
00:05:08,339 --> 00:05:08,819
力的话

117
00:05:08,819 --> 00:05:10,470
这个就是物理学上的力

118
00:05:10,470 --> 00:05:13,110
而速度的话就是每秒多少像素啊

119
00:05:13,110 --> 00:05:14,069
就这样的一个东西

120
00:05:14,069 --> 00:05:16,459
说白了就是两种单位啊

121
00:05:16,459 --> 00:05:19,540
两种实现方法就看你用哪一种了

122
00:05:19,759 --> 00:05:21,259
而且还有一个区别

123
00:05:21,259 --> 00:05:23,240
就是我们这只是给了一次

124
00:05:23,240 --> 00:05:23,540
对不对

125
00:05:23,540 --> 00:05:25,339
比如说力给了一次速度

126
00:05:25,339 --> 00:05:26,319
给了一次

127
00:05:26,319 --> 00:05:27,879
那如果我们在update

128
00:05:27,879 --> 00:05:32,259
我们知道update是一直会一直会调用的

129
00:05:32,259 --> 00:05:36,139
如果我在update里面写给了他一个速度

130
00:05:36,139 --> 00:05:37,639
那么它的结果是什么

131
00:05:37,639 --> 00:05:39,800
那么它的结果是会维持这个速度

132
00:05:39,800 --> 00:05:41,779
因为我们现在设定了一个死的速度

133
00:05:41,779 --> 00:05:42,459
对不对

134
00:05:42,459 --> 00:05:44,529
设定了往右50的速度

135
00:05:44,529 --> 00:05:46,360
所以如果写到update里面

136
00:05:46,360 --> 00:05:48,250
那这个物体就会一直往右走

137
00:05:48,250 --> 00:05:51,040
保证这个往右走有50的一个速度

138
00:05:51,040 --> 00:05:53,800
而力的话就不一样了

139
00:05:53,800 --> 00:05:55,220
力的话属于什么呀

140
00:05:55,920 --> 00:05:57,839
力的话是可以累加的

141
00:05:57,839 --> 00:05:59,100
就是你给他一个速度

142
00:05:59,100 --> 00:06:00,990
比如说给他一个力啊

143
00:06:00,990 --> 00:06:01,709
很小

144
00:06:01,709 --> 00:06:03,240
但是你一直给它一个力

145
00:06:03,240 --> 00:06:04,620
那这个力就会变大了

146
00:06:04,620 --> 00:06:09,000
所以力这个东西如果你要给一个

147
00:06:09,000 --> 00:06:11,639
比如说给一个让它匀速运动啊

148
00:06:11,639 --> 00:06:13,920
这种情况下你给力就不合适了

149
00:06:13,920 --> 00:06:16,490
你只能用速度去做是最合适的

150
00:06:16,490 --> 00:06:18,589
如果比如说就和刚才一样

151
00:06:18,589 --> 00:06:21,709
我们只是想让一个内容就是飞出去啊

152
00:06:21,709 --> 00:06:23,379
想让一个物体飞出去

153
00:06:23,379 --> 00:06:24,370
就和子弹一样

154
00:06:24,370 --> 00:06:26,199
打出去以后我们就不管它了

155
00:06:26,199 --> 00:06:28,139
那么这个子弹飞出去

156
00:06:28,139 --> 00:06:29,970
我们给它一个速度也行

157
00:06:29,970 --> 00:06:31,500
给它一个力都可以啊

158
00:06:31,500 --> 00:06:33,540
反正飞出去我们就不管它了啊

159
00:06:33,540 --> 00:06:35,230
但是就和我说的

160
00:06:35,230 --> 00:06:37,209
你要是让一个物体匀速啊

161
00:06:37,209 --> 00:06:39,850
往右以某一个固定的速度移动

162
00:06:39,850 --> 00:06:42,069
那你尽量用速度来控制

163
00:06:42,069 --> 00:06:43,860
而不要用这个力啊

164
00:06:43,860 --> 00:06:46,319
你要是一直给他同样大的一个力

165
00:06:46,319 --> 00:06:48,120
那它往右边走越来越快

166
00:06:48,120 --> 00:06:49,079
越来越快啊

167
00:06:49,079 --> 00:06:50,720
就变成加速运动

168
00:06:52,740 --> 00:06:55,459
这个就是物理学很简单

169
00:06:55,459 --> 00:06:57,079
是不是一说就应该明白啊

170
00:06:57,079 --> 00:06:59,699
你给一个物体一个力肯定是越来越大了

171
00:06:59,699 --> 00:07:02,038
但是速度给它一个固定的速度啊

172
00:07:02,038 --> 00:07:03,718
它就是一直不变的啊

173
00:07:03,718 --> 00:07:04,620
一个速度

174
00:07:04,620 --> 00:07:11,100
那么现在啊我们再来看一下它的一个碰撞的一个回调啊

175
00:07:11,100 --> 00:07:12,180
碰撞的一个回调

176
00:07:12,180 --> 00:07:13,860
比如说这是一个小鸟

177
00:07:13,860 --> 00:07:14,579
对不对

178
00:07:14,579 --> 00:07:18,860
那么下面比如说我们有个地面啊

179
00:07:18,860 --> 00:07:19,699
比如说我们有个地面

180
00:07:19,699 --> 00:07:22,339
我希望这个小鸟和这个地面能产生碰撞

181
00:07:23,600 --> 00:07:25,569
怎怎么办啊

182
00:07:25,569 --> 00:07:27,639
那么首先我现在运行一下

183
00:07:27,639 --> 00:07:28,660
肯定没有碰撞

184
00:07:28,660 --> 00:07:30,579
因为这个地面没有钢铁

185
00:07:30,579 --> 00:07:32,500
我们给它加一个缸体啊

186
00:07:32,779 --> 00:07:33,740
加个刚体

187
00:07:33,740 --> 00:07:35,660
并且地面是静态的

188
00:07:35,660 --> 00:07:35,959
对不对

189
00:07:35,959 --> 00:07:37,100
地面是肯定是静态的

190
00:07:37,100 --> 00:07:38,930
它就是在这儿不动的

191
00:07:38,930 --> 00:07:40,600
我们运行看一下效果

192
00:07:40,899 --> 00:07:43,269
我发现这个鸟还是掉了下去

193
00:07:43,269 --> 00:07:44,860
这个鸟还是掉了下去

194
00:07:44,860 --> 00:07:46,779
为什么会有这样的情况啊

195
00:07:46,779 --> 00:07:49,060
这个鸟为什么会有这样的一个情况

196
00:07:51,259 --> 00:07:54,459
那是因为我们虽然让它有了物理特性了

197
00:07:54,459 --> 00:07:56,759
但是没有给它碰撞体啊

198
00:07:56,759 --> 00:07:59,639
就是没有给他那个之前和我们一样给它一个边框

199
00:07:59,639 --> 00:08:01,230
给它框起来啊

200
00:08:01,230 --> 00:08:05,800
所以我们在这里我们应该给他一个碰撞体

201
00:08:06,160 --> 00:08:09,720
那么我们这次就不能在这儿给他这三个碰撞组件了

202
00:08:09,720 --> 00:08:11,819
我们就要选物理里面的碰撞组件

203
00:08:11,819 --> 00:08:13,259
你看第一个也是碰撞组件

204
00:08:13,259 --> 00:08:14,990
那么它里面有四种

205
00:08:14,990 --> 00:08:18,470
这三种134啊都是一样的

206
00:08:18,470 --> 00:08:19,490
只有第二种不一样

207
00:08:19,490 --> 00:08:21,579
我们选择第二种能看一下

208
00:08:21,939 --> 00:08:24,220
比如说第二种加到这个啊

209
00:08:24,220 --> 00:08:25,699
这是加到地面上的

210
00:08:26,439 --> 00:08:27,778
地面上不太好看

211
00:08:27,778 --> 00:08:29,309
我加到小鸟身上吧

212
00:08:29,309 --> 00:08:30,399
把第二种

213
00:08:31,120 --> 00:08:33,600
比如说我先给小鸟加这个碰撞啊

214
00:08:33,600 --> 00:08:35,700
你看我如果用第二种

215
00:08:35,700 --> 00:08:39,299
你发现这个碰撞是不是基本上就按照这个图形的样子

216
00:08:39,299 --> 00:08:40,950
自动生成的这样的一个碰撞

217
00:08:40,950 --> 00:08:43,919
这个就是特别精准的一个碰撞啊

218
00:08:43,919 --> 00:08:47,589
但是一般还是没必要用到这种碰撞废性能

219
00:08:47,589 --> 00:08:49,149
所以对于小鸟而言

220
00:08:49,149 --> 00:08:54,438
我一般我们一般可以给它一个圆形碰撞就可以了对吧

221
00:08:55,460 --> 00:08:58,779
然后把你的这个半径给小一点

222
00:09:02,480 --> 00:09:03,879
这就是你的碰撞啊

223
00:09:03,879 --> 00:09:04,179
可以了

224
00:09:04,179 --> 00:09:05,679
我这就认为可以了

225
00:09:05,679 --> 00:09:08,779
而地面的话我们给他一个什么碰撞

226
00:09:08,860 --> 00:09:13,000
地面的话我们可以给他一个盒子碰撞就行了

227
00:09:13,000 --> 00:09:14,779
那么这时候运行一下

228
00:09:17,440 --> 00:09:20,100
可以看这个小鸟是不是从这儿掉下来

229
00:09:20,100 --> 00:09:21,299
然后一直滚滚滚

230
00:09:21,299 --> 00:09:23,129
然后最后掉下去了

231
00:09:23,129 --> 00:09:24,259
是不是

232
00:09:25,159 --> 00:09:27,659
那么如果这里注意啊

233
00:09:27,659 --> 00:09:29,580
咱们可以试一下上节课的这个内容

234
00:09:29,580 --> 00:09:33,000
如果我们把钢铁的这个旋转给它固定了啊

235
00:09:33,000 --> 00:09:34,759
把这个小鸟的我们运行

236
00:09:35,240 --> 00:09:36,309
大家可以看

237
00:09:36,309 --> 00:09:38,379
直接落下来就在这个地面上了

238
00:09:38,379 --> 00:09:38,889
为什么

239
00:09:38,889 --> 00:09:40,419
因为它不让旋转了

240
00:09:40,419 --> 00:09:41,799
他把旋转禁止了

241
00:09:41,799 --> 00:09:43,440
把旋转禁止了

242
00:09:43,440 --> 00:09:46,590
那么由于这个往右边走的力其实很小

243
00:09:46,590 --> 00:09:49,879
所以他直接这个被摩擦力啊

244
00:09:49,879 --> 00:09:51,440
导致它停停了下来

245
00:09:51,440 --> 00:09:52,279
对不对

246
00:09:52,480 --> 00:09:55,059
如果我们把这个物体改成斜的

247
00:09:55,059 --> 00:09:57,190
那它也会维持这个角度

248
00:09:57,190 --> 00:09:58,720
然后滑下去啊

249
00:09:58,720 --> 00:10:02,090
那就这样ok啊

250
00:10:02,090 --> 00:10:04,399
那么现在我们来看一下啊

251
00:10:04,399 --> 00:10:08,120
我们来看一下这个碰撞啊

252
00:10:08,120 --> 00:10:09,919
跟我们之前的碰撞有没有区别

253
00:10:09,919 --> 00:10:13,659
首先我们看一下像这个editor tag值

254
00:10:13,820 --> 00:10:15,649
这个像半径啊

255
00:10:15,649 --> 00:10:16,759
这个offset偏移

256
00:10:16,759 --> 00:10:17,419
这些都一样

257
00:10:17,419 --> 00:10:18,200
对不对

258
00:10:18,200 --> 00:10:21,899
不一样的是这思想我们来看一下啊

259
00:10:22,220 --> 00:10:23,899
先来看其中三项

260
00:10:23,899 --> 00:10:26,039
第一项是个密度啊

261
00:10:26,039 --> 00:10:28,379
密度这个东西是物理上的一个东西

262
00:10:28,379 --> 00:10:31,970
这个呃一般我们不需要去修改它啊

263
00:10:31,970 --> 00:10:33,169
一般都不需要去动它

264
00:10:33,169 --> 00:10:35,200
它就一就放这儿就行了啊

265
00:10:35,200 --> 00:10:36,970
那么在这里有个摩擦

266
00:10:36,970 --> 00:10:39,519
这两个可能我们会改的多一些

267
00:10:39,519 --> 00:10:44,690
摩擦的话就是你这个比如说这个鸟的摩擦摩擦力是多少

268
00:10:44,690 --> 00:10:46,669
如果你比如说鸟的摩擦力零

269
00:10:46,669 --> 00:10:47,450
地面也是零

270
00:10:47,450 --> 00:10:50,009
那这俩之间就不会产生摩擦力了

271
00:10:50,009 --> 00:10:50,249
对吧

272
00:10:50,249 --> 00:10:52,200
就没摩擦力了啊

273
00:10:52,200 --> 00:10:53,519
默认都是有一点的啊

274
00:10:53,519 --> 00:10:54,149
0.2

275
00:10:54,149 --> 00:10:56,379
那么还有一个是弹性系数

276
00:10:56,980 --> 00:10:59,620
弹性系数是0~1之间默认是零

277
00:10:59,620 --> 00:11:02,379
就是默认是它这个材质都是不会弹的

278
00:11:02,379 --> 00:11:03,549
不会反弹的

279
00:11:03,549 --> 00:11:05,519
如果你给个比如说0.5

280
00:11:05,899 --> 00:11:07,279
你在运行

281
00:11:07,899 --> 00:11:08,320
哎

282
00:11:08,320 --> 00:11:10,779
你看落到这儿就弹起来了

283
00:11:10,779 --> 00:11:11,259
对不对

284
00:11:11,259 --> 00:11:12,820
它就是这样的一个效果啊

285
00:11:12,820 --> 00:11:14,019
它是这样一个效果

286
00:11:15,460 --> 00:11:17,379
如果你不给他这个弹性效果

287
00:11:17,379 --> 00:11:19,779
那么他当然就是直接落地面上

288
00:11:19,779 --> 00:11:21,190
直接就停下了啊

289
00:11:21,190 --> 00:11:22,240
比较生硬一点

290
00:11:22,240 --> 00:11:23,078
是不是

291
00:11:25,220 --> 00:11:28,120
那么现在我们已经能产生碰撞了啊

292
00:11:28,120 --> 00:11:29,620
已经能产生碰撞了

293
00:11:29,620 --> 00:11:31,120
那么碰撞完以后

294
00:11:31,120 --> 00:11:33,879
我们怎样去检测这个碰撞在代码里面啊

295
00:11:33,879 --> 00:11:35,320
比如说碰到以后要做什么事

296
00:11:35,320 --> 00:11:36,979
怎么去检测这个碰撞

297
00:11:37,820 --> 00:11:40,000
那么他当然也有两个方法

298
00:11:40,000 --> 00:11:41,769
一个是开始碰撞

299
00:11:41,769 --> 00:11:45,539
一个是结束碰撞

300
00:11:45,799 --> 00:11:49,700
这两个是物理里面最重要的两个也是on

301
00:11:49,700 --> 00:11:50,840
它的方法就变了

302
00:11:50,840 --> 00:11:52,220
之前是on connection enter

303
00:11:52,220 --> 00:11:52,789
对不对

304
00:11:52,789 --> 00:11:57,369
这个是on begin contact啊

305
00:11:57,369 --> 00:11:59,919
开始发生接触这个碰撞了

306
00:12:00,679 --> 00:12:02,919
他的方法就变成这个了啊

307
00:12:02,919 --> 00:12:06,899
然后我们给他一个参数参数的话

308
00:12:06,899 --> 00:12:08,039
它有三个

309
00:12:09,399 --> 00:12:13,839
第一个是一个碰撞的一个碰撞的一个信息

310
00:12:13,839 --> 00:12:15,938
应该说的一个碰撞的一个类

311
00:12:15,938 --> 00:12:17,979
这个类里面包含了很多数据啊

312
00:12:17,979 --> 00:12:19,339
我们一会再说

313
00:12:19,500 --> 00:12:21,899
然后后面两个我们大家就知道了

314
00:12:21,899 --> 00:12:23,610
一个就是我们自己

315
00:12:23,610 --> 00:12:26,639
一个就是other啊

316
00:12:26,639 --> 00:12:29,070
就是我们自己这个碰撞碰撞体

317
00:12:29,070 --> 00:12:32,179
还有我碰到的那个碰撞体

318
00:12:32,179 --> 00:12:33,919
比如说我现在挂在小鸟身上

319
00:12:33,919 --> 00:12:36,080
那这个cf就是小鸟的碰撞体

320
00:12:36,080 --> 00:12:37,580
other就是地面的碰撞体

321
00:12:37,580 --> 00:12:38,479
对不对

322
00:12:38,559 --> 00:12:41,019
后面这两个很好很好理解啊

323
00:12:41,019 --> 00:12:43,958
结束的话按and contact

324
00:12:51,240 --> 00:12:53,960
那我们在这里可以结束碰撞

325
00:12:53,960 --> 00:12:55,580
因为我们一直在碰撞中

326
00:12:55,580 --> 00:12:57,559
所以我们结束碰撞

327
00:12:57,559 --> 00:12:59,059
在这里不太好

328
00:12:59,059 --> 00:13:00,200
不太好测试

329
00:13:00,200 --> 00:13:01,879
我们看一下开始碰撞啊

330
00:13:01,879 --> 00:13:03,379
结束就是一样的了

331
00:13:03,480 --> 00:13:06,360
开始比如说如果发生碰撞

332
00:13:06,360 --> 00:13:09,980
我让他输出一个发生碰撞

333
00:13:11,960 --> 00:13:13,078
发生碰撞

334
00:13:14,320 --> 00:13:17,080
那么在这里我们运行一下

335
00:13:17,080 --> 00:13:19,979
看一下能不能输出出来发生碰撞

336
00:13:21,399 --> 00:13:24,960
哎我们可以看发生碰撞是不是出来了啊

337
00:13:24,960 --> 00:13:25,620
没有问题啊

338
00:13:25,620 --> 00:13:26,340
没有问题

339
00:13:26,340 --> 00:13:27,539
一定要记得啊

340
00:13:27,539 --> 00:13:30,000
这个这个方法之所以能检测到

341
00:13:30,000 --> 00:13:33,080
是因为我们小鸟的这个属性打开了啊

342
00:13:33,080 --> 00:13:36,379
就是他这个是开启这个监听器了

343
00:13:36,379 --> 00:13:38,500
如果你不把它勾上

344
00:13:38,620 --> 00:13:44,080
那么大家可以看它这个如果只有开启了这个这一项

345
00:13:44,080 --> 00:13:45,820
才会调用相应的回调函数

346
00:13:45,820 --> 00:13:46,240
对不对

347
00:13:46,240 --> 00:13:47,980
所以如果你把这个关了

348
00:13:47,980 --> 00:13:49,590
你在这边啊

349
00:13:49,590 --> 00:13:52,019
你是没法调用这两个方法的啊

350
00:13:52,019 --> 00:13:53,279
这个注意一下

351
00:13:55,220 --> 00:13:57,200
那么在这边发生碰撞了以后

352
00:13:57,200 --> 00:13:59,919
我们依然可以通过之前的方式啊

353
00:13:59,919 --> 00:14:01,720
知道我碰撞的是什么

354
00:14:01,720 --> 00:14:02,980
比如说拿到other

355
00:14:02,980 --> 00:14:04,539
你就可以other.tag值

356
00:14:04,539 --> 00:14:05,110
对不对

357
00:14:05,110 --> 00:14:07,740
这和我们之前的那个碰撞检测是一样的

358
00:14:07,740 --> 00:14:08,820
但是除此之外

359
00:14:08,820 --> 00:14:10,620
这种碰撞检测多了一个参数

360
00:14:10,620 --> 00:14:12,159
是第一个contact

361
00:14:12,259 --> 00:14:15,019
它里面包含了一些信息

362
00:14:15,019 --> 00:14:17,809
其中我们必须知道的啊

363
00:14:17,809 --> 00:14:20,578
有这么两个信息啊

364
00:14:20,578 --> 00:14:25,349
那么最少这两个星期有一个我们是特别常用的什么信息

365
00:14:25,349 --> 00:14:26,839
碰撞点

366
00:14:29,059 --> 00:14:30,460
得到碰撞点

367
00:14:30,460 --> 00:14:32,259
我们可能是需要知道

368
00:14:32,259 --> 00:14:35,679
而我们得到的碰撞的那个点是哪个点

369
00:14:35,679 --> 00:14:41,529
比如说在这里就是小鸟到地面上和地面发生碰撞的一瞬间

370
00:14:41,529 --> 00:14:44,710
小鸟和地面哪一个碰撞的点在哪里

371
00:14:44,710 --> 00:14:46,360
我们要得到这一点的位置

372
00:14:46,360 --> 00:14:47,230
比如说从这一点

373
00:14:47,230 --> 00:14:48,549
我们要创建一个火花

374
00:14:48,549 --> 00:14:51,330
这样的话你就可以看到小鸟掉到地面上

375
00:14:51,330 --> 00:14:54,688
然后碰撞点就就这个就是闪过了火花

376
00:14:54,688 --> 00:14:55,349
对对不对

377
00:14:55,349 --> 00:14:57,059
这个效果就会好一些

378
00:14:57,879 --> 00:15:00,039
那所以我们有时候要得到碰撞点

379
00:15:00,039 --> 00:15:01,899
在碰撞点上做一些操作啊

380
00:15:01,899 --> 00:15:03,220
比如说你做设计游戏

381
00:15:03,220 --> 00:15:04,629
那个子弹打到墙上

382
00:15:04,629 --> 00:15:05,860
你要得到碰撞点

383
00:15:05,860 --> 00:15:08,879
然后在碰撞点上给他创建一个火花

384
00:15:08,879 --> 00:15:09,568
是不是这样

385
00:15:09,568 --> 00:15:11,489
你就感觉这个枪打到墙上

386
00:15:11,489 --> 00:15:12,808
墙上在那冒火星

387
00:15:12,808 --> 00:15:13,698
对不对

388
00:15:14,039 --> 00:15:18,919
那这个碰撞点怎么得就用这个contact

389
00:15:18,919 --> 00:15:24,599
就是用它里面有个get word manifold

390
00:15:25,899 --> 00:15:29,240
然后再点hin

391
00:15:30,519 --> 00:15:32,259
这个就是得到碰撞点了

392
00:15:32,259 --> 00:15:33,820
但是它返回的是一个数组

393
00:15:33,820 --> 00:15:36,909
就是因为他可能同时两个点产生碰撞

394
00:15:36,909 --> 00:15:41,940
但是基本上他这里都是呃一个点

395
00:15:41,940 --> 00:15:43,440
所以我们要得到碰撞信息

396
00:15:43,440 --> 00:15:46,039
你直接拿到零就可以了啊

397
00:15:46,039 --> 00:15:47,419
直接拿这个零就可以了

398
00:15:47,419 --> 00:15:49,159
我们在这里输出一下

399
00:15:49,159 --> 00:15:52,620
看一下能不能输出这个点碰撞点

400
00:15:54,279 --> 00:15:56,320
哎我们可以看到发生碰撞

401
00:15:56,320 --> 00:15:59,500
碰撞的点就是150x145.03

402
00:15:59,500 --> 00:16:03,470
也就是这个鸟和这个地面接触的这一个点

403
00:16:03,470 --> 00:16:05,179
就是这个这个点啊

404
00:16:05,179 --> 00:16:07,159
你就可以在这个点创建很多东西了

405
00:16:07,159 --> 00:16:08,059
对不对

406
00:16:13,779 --> 00:16:17,230
那么除了这个碰撞点还能得到什么信息

407
00:16:17,230 --> 00:16:20,039
得到法线信息啊

408
00:16:20,039 --> 00:16:21,269
这个也是要知道的

409
00:16:21,269 --> 00:16:22,980
虽然用的可能没有碰撞点

410
00:16:22,980 --> 00:16:23,399
多了

411
00:16:24,779 --> 00:16:27,750
contact点

412
00:16:27,750 --> 00:16:29,220
首先第一步一样的

413
00:16:29,220 --> 00:16:34,818
that word fold.nova

414
00:16:37,220 --> 00:16:40,259
那么在这里我们

415
00:16:42,860 --> 00:16:45,259
我们要是拿到一个法线啊

416
00:16:45,259 --> 00:16:48,259
那我们就可以把这个法线也是可以给他打出来

417
00:16:48,259 --> 00:16:49,610
我们先打出来看一下

418
00:16:49,610 --> 00:16:51,679
我们先打出来看看再说

419
00:16:56,840 --> 00:16:57,799
哎发生碰撞

420
00:16:57,799 --> 00:17:01,159
你看法线零-10-1

421
00:17:01,159 --> 00:17:02,120
大家想想啊

422
00:17:02,120 --> 00:17:04,049
零-1是个什么情况

423
00:17:04,049 --> 00:17:04,950
x是零

424
00:17:04,950 --> 00:17:05,490
y是-1

425
00:17:05,490 --> 00:17:09,130
是不是就向下的一个呃一个这个这个单位向量

426
00:17:09,130 --> 00:17:11,710
所以其实就是现在碰撞的这样的一个法线

427
00:17:11,710 --> 00:17:13,210
就是这样的一个位置

428
00:17:13,210 --> 00:17:16,299
那么这里注意啊

429
00:17:16,299 --> 00:17:17,980
这里注意什么叫法线啊

430
00:17:17,980 --> 00:17:19,259
什么叫法线

431
00:17:21,279 --> 00:17:22,779
那么什么是法线呢

432
00:17:22,779 --> 00:17:29,119
我们在这里看法线其实就是垂直于某个平面的一个线

433
00:17:29,119 --> 00:17:29,720
就是法线

434
00:17:29,720 --> 00:17:30,740
比如说这是地面

435
00:17:30,740 --> 00:17:31,579
地面的垂直线

436
00:17:31,579 --> 00:17:32,779
就是它的法线

437
00:17:32,779 --> 00:17:37,759
那么这个法线我们从哪一点都可以画出一个法线

438
00:17:37,759 --> 00:17:38,119
对不对

439
00:17:38,119 --> 00:17:40,190
都可以画出垂直于这个面的线

440
00:17:40,190 --> 00:17:43,579
那么当我一个物体发生碰撞的时候

441
00:17:43,579 --> 00:17:45,440
从碰撞点哎

442
00:17:45,440 --> 00:17:49,160
从碰撞的这一个点这个位置打出来的法线

443
00:17:49,160 --> 00:17:54,140
那么就是就是通过这个方式获取的啊

444
00:17:54,140 --> 00:17:55,910
就是通过这个方式获取的

445
00:17:55,910 --> 00:17:57,920
那么这个就是我们的法线啊

446
00:17:57,920 --> 00:18:00,099
就是我们的法线啊

447
00:18:01,180 --> 00:18:03,460
这个有时候会有这样的一个情况啊

448
00:18:03,460 --> 00:18:04,460
比如说

449
00:18:09,579 --> 00:18:10,599
举个例子啊

450
00:18:10,599 --> 00:18:11,380
法线的运用

451
00:18:11,380 --> 00:18:13,539
比如说有一堵墙啊

452
00:18:13,539 --> 00:18:15,339
还是一个子弹去打这堵墙

453
00:18:15,339 --> 00:18:17,079
比如说这个紫色的边缘

454
00:18:17,079 --> 00:18:18,599
就是咱们这堵墙

455
00:18:19,019 --> 00:18:21,480
我一个子弹如果垂直打过来

456
00:18:21,480 --> 00:18:22,559
有一个火花

457
00:18:22,559 --> 00:18:25,200
火花的朝向是这个朝向外面的

458
00:18:25,200 --> 00:18:26,099
对不对啊

459
00:18:26,099 --> 00:18:28,480
火花的朝射方向是这样的

460
00:18:28,480 --> 00:18:31,240
那么如果这个子弹斜着打过来

461
00:18:31,240 --> 00:18:36,059
那么实际上我们有时候也需要这个火花是垂直于墙的

462
00:18:36,059 --> 00:18:37,799
而不希望你斜着打过来

463
00:18:37,799 --> 00:18:39,128
火花是这样的啊

464
00:18:39,128 --> 00:18:40,449
有时候不希望是这样

465
00:18:40,449 --> 00:18:43,118
有时候希望我们一个物体斜着打过来

466
00:18:43,118 --> 00:18:45,338
它的火花仍然是垂直于这个平

467
00:18:45,338 --> 00:18:46,730
垂直于这个平面的

468
00:18:46,730 --> 00:18:49,210
这时候我们就需要让火花怎样

469
00:18:49,210 --> 00:18:52,809
火花的方向和法线的方向就应该一致了

470
00:18:52,809 --> 00:18:55,439
这时候我们就拿到法线就有用了啊

471
00:18:55,439 --> 00:18:56,878
所以注意这个这个东西啊

472
00:18:56,878 --> 00:18:59,029
法线有时候就是做这些事儿啊

473
00:18:59,029 --> 00:19:00,829
当两个物体产生碰撞的时候

474
00:19:00,829 --> 00:19:02,869
就会得到一条垂直线

475
00:19:02,869 --> 00:19:05,799
垂直于这个平面的线啊

476
00:19:05,799 --> 00:19:07,119
那你拿到这个线以后

477
00:19:07,119 --> 00:19:09,339
就看你怎样去利用啊

478
00:19:10,339 --> 00:19:12,319
那目前我们先知道一下行了啊

479
00:19:12,319 --> 00:19:14,480
先知道有这样一个法线这样的一个东西

480
00:19:14,480 --> 00:19:15,859
我们用到的时候再说啊

481
00:19:15,859 --> 00:19:17,119
用到时候再说

482
00:19:18,079 --> 00:19:22,519
那么总之最起码啊最次碰撞点太常用了

483
00:19:22,519 --> 00:19:25,279
这个你一定要记得怎样去得到一个碰撞点啊

484
00:19:25,279 --> 00:19:26,730
怎样得到一个碰撞点

485
00:19:26,730 --> 00:19:32,319
ok那么这节课啊我们就说这么多东西啊

486
00:19:32,319 --> 00:19:34,599
大家把物理的这些东西先记住啊

487
00:19:34,599 --> 00:19:36,920
先记住之后

488
00:19:36,920 --> 00:19:37,898
我们

489
00:19:40,660 --> 00:19:42,700
我们在这个项目里面的时候啊

490
00:19:42,700 --> 00:19:46,900
我们可能基本上都是用到这个物理的这个碰撞啊

491
00:19:46,900 --> 00:19:49,509
就是说之前的第一种碰撞嗯

492
00:19:49,509 --> 00:19:50,950
相对而言就会少一点

493
00:19:50,950 --> 00:19:53,259
当然如果你做一个项目是没有重力的

494
00:19:53,259 --> 00:19:54,730
那么就可以用它啊

495
00:19:54,730 --> 00:19:56,740
如果有重力的就用它啊

496
00:19:56,740 --> 00:19:58,519
这个也是看情况啊

497
00:19:58,759 --> 00:20:01,880
ok那我们这节课就这么多


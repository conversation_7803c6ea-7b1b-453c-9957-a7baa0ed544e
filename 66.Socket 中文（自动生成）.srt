1
00:00:09,240 --> 00:00:13,019
ok这节课我们来说一下什么是这个socket啊

2
00:00:13,019 --> 00:00:14,539
什么是套接字

3
00:00:14,539 --> 00:00:17,780
那么这个翻译过来这个名字还是比较诡异的

4
00:00:17,780 --> 00:00:18,559
对不对

5
00:00:18,559 --> 00:00:24,829
那么嗯socket编程这个东西呃怎么说啊

6
00:00:24,829 --> 00:00:26,269
我们来想一个问题

7
00:00:26,269 --> 00:00:28,910
还是从问题入手啊

8
00:00:28,910 --> 00:00:30,289
直接说概念啊

9
00:00:30,289 --> 00:00:32,000
这个不太好

10
00:00:32,000 --> 00:00:33,869
我们直接从问题入手

11
00:00:33,869 --> 00:00:39,030
比如说我们上节课学了一个正常的一个http的网络请求

12
00:00:39,030 --> 00:00:39,630
对不对

13
00:00:39,630 --> 00:00:41,670
那网络请求我们有什么东西

14
00:00:41,670 --> 00:00:42,899
有个客户端

15
00:00:44,359 --> 00:00:46,060
可能还有一个服务端

16
00:00:48,700 --> 00:00:54,320
那我接下来如果我需要从服务端上获取数据啊

17
00:00:54,320 --> 00:00:57,350
那我就会给服务端发起一个请求

18
00:00:57,350 --> 00:00:59,899
然后服务端返回给我们一个数据

19
00:01:00,119 --> 00:01:00,600
唉

20
00:01:00,600 --> 00:01:03,719
也就是说我们实际上它是这样的一个逻辑诶

21
00:01:03,719 --> 00:01:06,060
发个请求返回一个响应

22
00:01:06,060 --> 00:01:06,980
对不对

23
00:01:06,980 --> 00:01:07,879
没有问题啊

24
00:01:07,879 --> 00:01:12,379
这是咱们之前的这个http请求的这样的一个套路

25
00:01:12,519 --> 00:01:14,759
那么现在大家想一想啊

26
00:01:16,060 --> 00:01:18,510
当我们拿到这个内容以后

27
00:01:18,510 --> 00:01:20,939
拿到副端给我们发送的消息后

28
00:01:20,939 --> 00:01:25,420
那么这个连接大家想我们还保存着吗

29
00:01:25,620 --> 00:01:27,540
那肯定不是就断了

30
00:01:27,540 --> 00:01:28,019
对不对

31
00:01:28,019 --> 00:01:31,840
也就是说正常情况下客户端和服务端是没有任何连接的

32
00:01:31,840 --> 00:01:33,939
当我想要想要数据的时候

33
00:01:33,939 --> 00:01:35,379
我才会发个请求

34
00:01:35,379 --> 00:01:38,420
然后他呢给我们一个响应啊

35
00:01:38,420 --> 00:01:41,000
把我们要的数据通过响应返回给我们

36
00:01:41,000 --> 00:01:42,560
一旦返回给了我们

37
00:01:42,560 --> 00:01:43,819
我们就怎样了

38
00:01:44,099 --> 00:01:45,870
诶这个连接就断了

39
00:01:45,870 --> 00:01:48,750
也就是说我客户端和服务端之间就没关系了

40
00:01:48,750 --> 00:01:51,599
那么这个概念好像什么

41
00:01:51,599 --> 00:01:56,760
好像发短信好像发短信有没有这个感觉

42
00:01:56,760 --> 00:01:59,280
就好像我一个人给你发了个短信

43
00:01:59,280 --> 00:02:00,180
你就会怎样

44
00:02:00,180 --> 00:02:02,159
你就会回复啊

45
00:02:02,159 --> 00:02:03,420
你就会回复

46
00:02:03,840 --> 00:02:05,040
我发一条短信

47
00:02:05,040 --> 00:02:05,579
你回一条

48
00:02:05,579 --> 00:02:06,120
我发一条

49
00:02:06,120 --> 00:02:07,140
你回一条啊

50
00:02:07,140 --> 00:02:08,759
就是这样的一个概念啊

51
00:02:08,759 --> 00:02:10,139
他并不和打电话一样

52
00:02:10,139 --> 00:02:12,090
一直是持续连接着的啊

53
00:02:12,090 --> 00:02:15,278
他只是我用的时候啊

54
00:02:15,278 --> 00:02:19,120
他连一次用的时候连一次是这样的一个状态

55
00:02:19,319 --> 00:02:21,300
而且还有一个特点

56
00:02:21,879 --> 00:02:23,069
还有一个特点

57
00:02:23,069 --> 00:02:25,199
用我们之前的这种请求啊

58
00:02:25,199 --> 00:02:29,639
这种方式我们客户端是可以主动给服务端发消息的

59
00:02:29,639 --> 00:02:33,460
但是服务端并不能主动给客户端发消息

60
00:02:33,460 --> 00:02:35,979
大家想一想是不是有没有这个问题

61
00:02:36,239 --> 00:02:38,549
就比如说我我想要什么数据

62
00:02:38,549 --> 00:02:40,199
我发一个请求给服务端

63
00:02:40,199 --> 00:02:42,088
服务端才可以把数据给我

64
00:02:42,088 --> 00:02:44,449
但是如果我现在没有发请求

65
00:02:44,449 --> 00:02:47,030
服务端能不能主动给你发过来一个数据

66
00:02:47,030 --> 00:02:48,110
这个是发不过来的

67
00:02:48,110 --> 00:02:48,680
对不对

68
00:02:48,680 --> 00:02:50,740
这个是发不过来的啊

69
00:02:50,740 --> 00:02:51,669
一定要注意啊

70
00:02:51,669 --> 00:02:55,569
这是咱们之前学的这个呃普通的网络请求啊

71
00:02:55,569 --> 00:02:56,780
它的一个特点

72
00:02:57,680 --> 00:02:59,439
也就是说我们要数据啊

73
00:02:59,439 --> 00:03:00,460
对于服务端而言

74
00:03:00,460 --> 00:03:01,659
它是被动的啊

75
00:03:01,659 --> 00:03:02,500
他不能主动发

76
00:03:02,500 --> 00:03:03,310
他是被动的

77
00:03:03,310 --> 00:03:06,199
而且每次发完数据我们就会中断了啊

78
00:03:06,199 --> 00:03:08,479
感觉就和这个发短信是啊

79
00:03:09,800 --> 00:03:13,729
那么现在我们做这种游戏啊

80
00:03:13,729 --> 00:03:16,909
很多情况下它不是只有一个客户端

81
00:03:16,909 --> 00:03:18,110
它有多个客户端

82
00:03:18,110 --> 00:03:18,789
对不对

83
00:03:18,789 --> 00:03:24,240
那这些客户端之间可能我会给他怎样产生一个关联

84
00:03:25,199 --> 00:03:32,039
我希望让这两个客户端之间可以怎样联机打游戏啊

85
00:03:32,039 --> 00:03:33,000
可以联机打游戏

86
00:03:33,000 --> 00:03:36,389
也就是说我的数据要持续不断地发过去

87
00:03:36,389 --> 00:03:38,960
他的数据也要持续不断地发过来

88
00:03:39,280 --> 00:03:41,080
那么这样的话大家去想啊

89
00:03:41,080 --> 00:03:42,639
作为两个客户端而言

90
00:03:42,639 --> 00:03:44,509
我能主动给你发

91
00:03:44,509 --> 00:03:46,310
你也能主动给我发

92
00:03:46,310 --> 00:03:50,340
而且我们俩之间只要连接起来就不间断

93
00:03:50,699 --> 00:03:55,620
这样的一个概念其实才是做正常的联机游戏的一个概念

94
00:03:55,620 --> 00:03:56,159
对不对

95
00:03:56,159 --> 00:04:00,179
但是我们之前的http请求就做不到了啊

96
00:04:00,179 --> 00:04:02,278
那么怎样做到

97
00:04:02,278 --> 00:04:04,079
这就是我们套接字了啊

98
00:04:04,079 --> 00:04:06,039
套接字就是用来做这个的了

99
00:04:06,479 --> 00:04:11,379
套接字呢就是可以在终端之间去产生一个连接

100
00:04:11,439 --> 00:04:13,300
产生一个持续的连接

101
00:04:13,300 --> 00:04:18,399
通过这个连接我就可以在两端之间进行双向的一个通信啊

102
00:04:18,399 --> 00:04:20,040
这就是套接字的一个作用

103
00:04:20,040 --> 00:04:22,079
其实它封装了一个协议啊

104
00:04:22,079 --> 00:04:25,480
叫这个tcp ip协议啊

105
00:04:25,480 --> 00:04:28,360
很多上上那个上大学

106
00:04:28,360 --> 00:04:33,240
然后那个如果你学的是这个学的课程里面有计算机的

107
00:04:33,240 --> 00:04:34,259
或者说有网络的

108
00:04:34,259 --> 00:04:35,689
肯定听过这个对不对

109
00:04:35,689 --> 00:04:38,439
往深的去研究这个东西呃

110
00:04:38,439 --> 00:04:40,329
里面还是蛮复杂的啊

111
00:04:40,329 --> 00:04:41,649
但是对于我们而言

112
00:04:41,649 --> 00:04:42,910
我们知道的是什么

113
00:04:42,910 --> 00:04:45,430
是知道的是socket是封装的

114
00:04:45,430 --> 00:04:48,769
它啊封装的它那么使用socket

115
00:04:48,769 --> 00:04:52,540
我们就可以在两端之间建立一条双向的通道

116
00:04:52,540 --> 00:04:54,930
然后去进行一个通信

117
00:04:54,930 --> 00:04:58,379
那么这时候这个通道有两个端

118
00:04:58,379 --> 00:04:58,949
对不对

119
00:04:58,949 --> 00:04:59,939
有两端

120
00:05:00,639 --> 00:05:04,639
每一端它就叫一个socket啊

121
00:05:04,639 --> 00:05:06,259
每一端就叫一个socket

122
00:05:06,259 --> 00:05:07,490
也叫一个套接字

123
00:05:07,490 --> 00:05:15,430
所以socket通信其实就是两个终端之间建立一个呃这个通信的管道啊

124
00:05:15,430 --> 00:05:20,970
然后这这两个管道两个头就叫socket

125
00:05:20,970 --> 00:05:22,379
举个例子

126
00:05:22,379 --> 00:05:26,060
这个就相当于我们打电话了啊

127
00:05:26,060 --> 00:05:27,319
不相当于发短信了

128
00:05:27,319 --> 00:05:28,279
打电话了啊

129
00:05:28,279 --> 00:05:29,060
我和你打电话

130
00:05:29,060 --> 00:05:29,899
你给我打电话

131
00:05:29,899 --> 00:05:32,810
那么这个电话现在就是我们的socket

132
00:05:32,810 --> 00:05:36,399
也就是说建立一个通信会产生几个socket

133
00:05:36,399 --> 00:05:38,798
产生两个两个客户端

134
00:05:38,798 --> 00:05:41,019
一个客户端会产生一个socket

135
00:05:41,019 --> 00:05:43,389
然后我在我的socket里面说话

136
00:05:43,389 --> 00:05:44,499
你能收得到

137
00:05:44,499 --> 00:05:46,730
你在你的socket里面说话

138
00:05:46,730 --> 00:05:48,230
我也能听得到

139
00:05:48,230 --> 00:05:51,050
所以实际上就是大家去想正常去打电话

140
00:05:51,050 --> 00:05:53,810
这个过程电话就是我们的什么呀

141
00:05:53,810 --> 00:05:55,069
就是我们的socket啊

142
00:05:55,069 --> 00:05:56,449
我对着我的socket说话

143
00:05:56,449 --> 00:05:57,740
我对着电话说话

144
00:05:57,740 --> 00:05:58,800
你能收到

145
00:05:58,800 --> 00:06:00,300
你对着你的电话说话

146
00:06:00,300 --> 00:06:01,139
我也能收到

147
00:06:01,139 --> 00:06:01,620
对不对

148
00:06:01,620 --> 00:06:03,079
是这样一个概念啊

149
00:06:03,098 --> 00:06:06,459
所以这个啊这个大家一定要搞清楚

150
00:06:07,540 --> 00:06:13,000
那么之前讲课就是咱们这个概念图啊

151
00:06:13,000 --> 00:06:14,680
已经算是最简单的了啊

152
00:06:14,680 --> 00:06:16,060
就是之前我去讲

153
00:06:16,060 --> 00:06:19,069
不是咱们这种h5 游戏开发的话

154
00:06:19,069 --> 00:06:22,040
如果比如说主打这种端游的这种有性情

155
00:06:22,040 --> 00:06:26,778
或者说比如说其他的这种软件开发

156
00:06:26,778 --> 00:06:29,778
那么我绝对不会说这么简单

157
00:06:29,778 --> 00:06:30,658
就讲完了

158
00:06:30,658 --> 00:06:35,189
这个tcp这边就要去详细讲解一下它里面是怎么回事了

159
00:06:35,189 --> 00:06:41,519
但是对于咱们h5 的这种h5 开发h5 用的这种socket啊

160
00:06:41,519 --> 00:06:45,240
它其实叫做web socket

161
00:06:46,800 --> 00:06:49,709
算是又对socket进行了一个封装

162
00:06:49,709 --> 00:06:53,009
那么这个web socket相对而言就会简单很多啊

163
00:06:53,009 --> 00:06:56,430
所以大家就没必没必要去了解的很复杂啊

164
00:06:56,430 --> 00:06:58,060
没必要把它了解很复杂

165
00:06:58,060 --> 00:07:01,839
但是最基础的你要知道socket是用来干嘛的啊

166
00:07:01,839 --> 00:07:03,300
什么是socket

167
00:07:03,718 --> 00:07:05,519
那么这样的话我们大家就明白了

168
00:07:05,519 --> 00:07:08,579
我们如果把它最后写成代码肯定是这样的

169
00:07:08,579 --> 00:07:12,160
每个客户端有一个socket对象啊

170
00:07:12,160 --> 00:07:15,639
我在这个socket对象里面肯定有个方法可以去写入

171
00:07:15,639 --> 00:07:18,620
这边socket就可以去收消息

172
00:07:18,620 --> 00:07:20,750
然后这边socket同样也可以写入

173
00:07:20,750 --> 00:07:22,980
这边socket就干嘛接收

174
00:07:22,980 --> 00:07:23,490
对不对

175
00:07:23,490 --> 00:07:24,629
就去接收

176
00:07:24,629 --> 00:07:28,230
ok那么这样的话确实就可以做成一个联机游戏了

177
00:07:28,230 --> 00:07:30,459
但是现在又有一个问题了

178
00:07:31,459 --> 00:07:35,319
联机游戏不是我们的目的啊

179
00:07:35,319 --> 00:07:36,490
联机游戏不是目的

180
00:07:36,490 --> 00:07:37,180
我们想啊

181
00:07:37,180 --> 00:07:39,949
如果我们现在要做一个网络游戏的话

182
00:07:39,949 --> 00:07:41,509
在一个网络当中

183
00:07:41,509 --> 00:07:42,529
一个网络游戏

184
00:07:42,529 --> 00:07:46,569
一个区里面能同时玩的人是不是太多了

185
00:07:46,569 --> 00:07:48,910
一个网络游戏能同时玩的人太多了

186
00:07:48,910 --> 00:07:51,470
比如说有很多客户端都可以去同时玩

187
00:07:51,470 --> 00:07:56,389
如果你要做到让这些客户端之间啊都在一个场景里面啊

188
00:07:56,389 --> 00:07:57,620
他们的数据都同步

189
00:07:57,620 --> 00:07:58,720
那怎么办

190
00:07:58,720 --> 00:08:00,490
你是不是你就要去想

191
00:08:00,490 --> 00:08:01,839
首先建个套接字

192
00:08:01,839 --> 00:08:03,579
它们俩连接再建个套接字

193
00:08:03,579 --> 00:08:05,019
它俩连接再建个套接字

194
00:08:05,019 --> 00:08:06,579
它俩连接再建个套接字

195
00:08:06,579 --> 00:08:07,449
它来连接

196
00:08:07,449 --> 00:08:10,779
还有一个特点是它的连接再建一个他俩连接

197
00:08:10,779 --> 00:08:15,718
也就是说我们需要让他们每一个客户端之间都建立这样一个链接

198
00:08:15,718 --> 00:08:16,259
对不对

199
00:08:16,259 --> 00:08:17,218
那大家去想

200
00:08:17,218 --> 00:08:19,918
你看咱们才四个终端

201
00:08:19,918 --> 00:08:25,980
咱们就要建立起1234566个套接字连接啊

202
00:08:25,980 --> 00:08:29,819
也就是说我们四个四个终端就要建立六个连接

203
00:08:29,819 --> 00:08:32,500
才能达到联网的这样的一个状况

204
00:08:32,500 --> 00:08:34,330
那如果大家想一个游戏里面

205
00:08:34,330 --> 00:08:36,549
我们有几千上万个人一块玩

206
00:08:36,549 --> 00:08:37,340
对不对

207
00:08:37,340 --> 00:08:39,200
比如说就说1000人

208
00:08:39,200 --> 00:08:40,970
如果有1000个人同时玩

209
00:08:40,970 --> 00:08:44,839
你在1000个人里面要要做这种连接的话

210
00:08:45,139 --> 00:08:46,639
那最后做成什么样了

211
00:08:46,639 --> 00:08:47,480
做成网状了

212
00:08:47,480 --> 00:08:48,299
对不对

213
00:08:48,940 --> 00:08:50,580
那这个就太复杂了

214
00:08:50,580 --> 00:08:52,259
也不也不方便于我们管理

215
00:08:52,259 --> 00:08:52,919
对不对

216
00:08:52,919 --> 00:08:55,679
那么有什么方法可以解决

217
00:08:55,679 --> 00:08:57,419
有什么方法可以解决

218
00:08:59,019 --> 00:09:05,440
那么ok现在我们我们想一想啊

219
00:09:05,440 --> 00:09:07,000
还是拿这个电话而言

220
00:09:07,000 --> 00:09:07,240
嗯

221
00:09:07,240 --> 00:09:08,769
拿这个电话举例子啊

222
00:09:08,769 --> 00:09:10,240
实际上大家想啊

223
00:09:10,240 --> 00:09:12,679
我们的手机之间啊

224
00:09:13,340 --> 00:09:14,379
我就用它吧

225
00:09:16,480 --> 00:09:18,620
那比如说现在这些都是手机了

226
00:09:21,440 --> 00:09:25,500
实际上手机之间是真的能互相直接连接起来吗

227
00:09:25,759 --> 00:09:28,759
这些手机之间实际上是真的会互相连接吗

228
00:09:28,759 --> 00:09:29,600
我给你打电话

229
00:09:29,600 --> 00:09:30,200
咱俩就连接

230
00:09:30,200 --> 00:09:31,039
我跟他打电话

231
00:09:31,039 --> 00:09:32,360
他俩就连接吗

232
00:09:32,360 --> 00:09:37,600
其实手机光有手机的话达不到这样的作用

233
00:09:37,600 --> 00:09:39,070
它一定有一个什么呀

234
00:09:39,070 --> 00:09:41,190
比如说信号塔啊

235
00:09:41,190 --> 00:09:42,899
比如说这个联通移动的信号塔

236
00:09:42,899 --> 00:09:43,500
对不对

237
00:09:43,500 --> 00:09:46,080
然后我所有的手机信号干嘛干嘛

238
00:09:46,080 --> 00:09:48,000
跟这个信号塔连接

239
00:09:48,000 --> 00:09:49,259
跟信号塔连接

240
00:09:49,259 --> 00:09:50,700
跟信号塔连接

241
00:09:50,700 --> 00:09:52,438
跟信号塔连接

242
00:09:53,299 --> 00:09:55,850
也就是说实际上我手机之间不连接

243
00:09:55,850 --> 00:09:57,500
但是我都跟信号塔连接

244
00:09:57,500 --> 00:10:01,149
那这个信号塔在这里做的是什么一个转发的功能

245
00:10:01,149 --> 00:10:01,750
比如说我说了

246
00:10:01,750 --> 00:10:03,070
我要给一个人打电话

247
00:10:03,070 --> 00:10:04,789
我把信息给了谁

248
00:10:04,789 --> 00:10:05,750
给了信号塔

249
00:10:05,750 --> 00:10:07,639
信号塔帮我做什么呀

250
00:10:07,639 --> 00:10:09,259
转发转发到这个手机

251
00:10:09,259 --> 00:10:11,870
也就是说你给谁打电话啊

252
00:10:11,870 --> 00:10:15,289
并不是说这个消息真的就是只有你们俩知道

253
00:10:15,289 --> 00:10:16,610
那这样的话大家去想

254
00:10:16,610 --> 00:10:17,570
很多人去打电话

255
00:10:17,570 --> 00:10:18,169
一打电话

256
00:10:19,789 --> 00:10:21,519
说你你俩打电话对吧

257
00:10:21,519 --> 00:10:22,720
这个通话记录啊

258
00:10:22,720 --> 00:10:24,580
显示你俩有什么不轨的行为

259
00:10:24,580 --> 00:10:25,558
对不对啊

260
00:10:26,458 --> 00:10:27,899
还是干嘛的对吧

261
00:10:27,899 --> 00:10:29,099
你俩就很惊呆了

262
00:10:29,099 --> 00:10:30,269
我给他打电话

263
00:10:31,169 --> 00:10:31,529
对不对

264
00:10:31,529 --> 00:10:36,330
这就证明你们两个手机之间并不是直接的产生了一个连接啊

265
00:10:36,330 --> 00:10:37,590
你再给他打电话的时候

266
00:10:37,590 --> 00:10:40,340
你是通过了一个第三者

267
00:10:40,340 --> 00:10:47,220
也就是说这个第三者是知道谁给谁通过画的通话记录是什么啊

268
00:10:47,220 --> 00:10:48,179
通话的内容啊

269
00:10:48,179 --> 00:10:49,860
我们都是通过这个信号塔啊

270
00:10:49,860 --> 00:10:51,240
这里他都是可以知道的

271
00:10:51,240 --> 00:10:52,860
他做了一个中转啊

272
00:10:52,860 --> 00:10:54,559
他是做了这样一个中转的

273
00:10:54,779 --> 00:10:56,340
那么注意这个问题啊

274
00:10:56,340 --> 00:10:58,539
注意这个问题啊

275
00:10:59,740 --> 00:11:01,960
当然这个这个具体怎么做

276
00:11:01,960 --> 00:11:03,879
具体这个信号塔和手机之间的

277
00:11:03,879 --> 00:11:04,659
咱们不去想啊

278
00:11:04,659 --> 00:11:06,159
咱们就大概理解一下啊

279
00:11:06,159 --> 00:11:08,749
大概想一下这样是这样一个结果就行了啊

280
00:11:08,749 --> 00:11:10,359
我们不深究这个东西

281
00:11:10,460 --> 00:11:16,970
那么实际上通过这个图我们就能想到我们做游戏也是这样的

282
00:11:16,970 --> 00:11:19,779
我们可以专门做一个服务端

283
00:11:20,200 --> 00:11:22,210
专门做一个服务端啊

284
00:11:22,210 --> 00:11:25,960
这个服务端啊我们用一个pc肯定是这个肯定是个pc

285
00:11:25,960 --> 00:11:26,639
对不对

286
00:11:26,639 --> 00:11:27,899
客户端的话是随意

287
00:11:27,899 --> 00:11:28,440
手机也行

288
00:11:28,440 --> 00:11:29,460
电脑也行啊

289
00:11:29,460 --> 00:11:31,799
你你看你做的这个游戏是什么样的

290
00:11:31,799 --> 00:11:35,740
那么如果我要去做联网游戏

291
00:11:37,179 --> 00:11:40,710
我肯定我和其他客户端一个一个去连

292
00:11:40,710 --> 00:11:42,059
这个是最费劲的啊

293
00:11:42,059 --> 00:11:44,240
最省事的是什么办法

294
00:11:44,240 --> 00:11:47,779
我每个客户端跟我服务端相连啊

295
00:11:47,779 --> 00:11:50,339
我每个客户端跟我的这个服务端相连

296
00:11:52,480 --> 00:11:53,399
连好以后

297
00:11:53,399 --> 00:11:57,240
比如说我要通知其他客户端啊

298
00:11:57,240 --> 00:11:58,599
说我上线了

299
00:11:58,820 --> 00:12:01,159
或者说我在世界上发了一句话

300
00:12:01,159 --> 00:12:02,539
所有的客户端都能看见

301
00:12:02,539 --> 00:12:04,039
那结果是怎样的

302
00:12:04,039 --> 00:12:06,230
并不是我直接给其他客户端发

303
00:12:06,230 --> 00:12:08,500
而是我发了一条消息

304
00:12:08,500 --> 00:12:10,809
我把消息发送给这个服务端

305
00:12:10,809 --> 00:12:12,370
服务端作为一个中转

306
00:12:12,370 --> 00:12:13,979
他帮我们怎样了

307
00:12:14,059 --> 00:12:16,639
帮我们去进行一个转发啊

308
00:12:16,639 --> 00:12:19,259
帮我们去进行了一个转发

309
00:12:19,340 --> 00:12:21,740
那么当他把消息转发以后

310
00:12:21,740 --> 00:12:24,610
别的客户端就会收到我们的消息了啊

311
00:12:24,610 --> 00:12:28,539
所以看似是我一个人给所有的人都发了个消息啊

312
00:12:28,539 --> 00:12:29,799
在世界上发了个消息

313
00:12:29,799 --> 00:12:32,159
实际上我们是把消息发给谁了

314
00:12:32,159 --> 00:12:33,120
发给服务端

315
00:12:33,120 --> 00:12:35,850
然后服务端能把这个消息发给其他客户端

316
00:12:35,850 --> 00:12:36,750
对不对

317
00:12:36,750 --> 00:12:38,220
甚至你私聊也一样

318
00:12:38,220 --> 00:12:40,179
比如说我就要给他发消息

319
00:12:40,279 --> 00:12:42,860
但是实际上你也是把消息给服务端

320
00:12:42,860 --> 00:12:45,629
告诉服务端说我要给哪个客户端发个消息

321
00:12:45,629 --> 00:12:51,340
服务端就怎样收到以后就把消息转发给这个客户端了啊

322
00:12:51,340 --> 00:12:52,240
这个注意一下

323
00:12:53,799 --> 00:12:56,019
那么接下来我们就要说一个东西啊

324
00:12:56,019 --> 00:12:57,359
就要说一个东西了

325
00:12:58,360 --> 00:13:00,580
在一个电脑里面啊

326
00:13:00,580 --> 00:13:01,979
在一个电脑里面

327
00:13:04,220 --> 00:13:08,960
我们有很多网络程序其实都在用这套接字啊

328
00:13:08,960 --> 00:13:10,519
其实都在用这套接字

329
00:13:10,519 --> 00:13:14,259
也就是说比如说如果我们有一台电脑

330
00:13:14,259 --> 00:13:15,519
这里就是拿电脑举例子啊

331
00:13:15,519 --> 00:13:17,500
比如说比如说我们的一台电脑啊

332
00:13:17,500 --> 00:13:21,580
我们的一台电脑我同时打开了很多网络程序啊

333
00:13:21,580 --> 00:13:22,539
它都有套接字

334
00:13:22,539 --> 00:13:24,340
甚至我开了多个游戏啊

335
00:13:24,340 --> 00:13:26,099
比如说就说多个游戏吧

336
00:13:26,480 --> 00:13:29,889
多个游戏每个游戏它会连一个什么呀

337
00:13:29,889 --> 00:13:30,970
连一个服务器

338
00:13:30,970 --> 00:13:33,549
也就是说我这里有好多游戏服务器啊

339
00:13:33,549 --> 00:13:37,409
这里游戏服务器a b c我同时开了三个网络游戏

340
00:13:37,409 --> 00:13:38,759
三种不同的网络游戏

341
00:13:38,759 --> 00:13:43,080
也就是说我一个电脑同时和三个服务器连接了

342
00:13:44,759 --> 00:13:45,240
对不对

343
00:13:45,240 --> 00:13:46,169
这个没有问题吧

344
00:13:46,169 --> 00:13:48,970
啊我这这个只能拿电脑举例子

345
00:13:48,970 --> 00:13:50,590
因为电脑才好多开嘛

346
00:13:50,590 --> 00:13:51,159
对不对

347
00:13:51,159 --> 00:13:52,450
我电脑开三个游戏

348
00:13:52,450 --> 00:13:55,009
我同时和三个服务器相连是没问题的

349
00:13:55,009 --> 00:13:56,690
但是这时候大家去想一个问题啊

350
00:13:56,690 --> 00:13:59,029
为什么这些数据串不了啊

351
00:13:59,029 --> 00:14:03,938
这三个服务器都是和我这一个pc一个客户端去沟通的

352
00:14:04,000 --> 00:14:11,240
那么为什么我知道诶我接收的数据就是从哪个服务器上发来的啊

353
00:14:11,240 --> 00:14:13,159
比如说诶他发个数据

354
00:14:13,159 --> 00:14:14,389
他给我发个数据

355
00:14:14,389 --> 00:14:16,850
我就知道诶这是这个游戏服务器发的

356
00:14:16,850 --> 00:14:17,960
那他给我发个数据

357
00:14:17,960 --> 00:14:19,970
我也知道是这个游戏服务器发的

358
00:14:19,970 --> 00:14:21,649
那他给我发个这个数据

359
00:14:21,649 --> 00:14:23,870
我也知道是这个服务器给我发的

360
00:14:23,870 --> 00:14:28,559
我怎么就能对和我连接的这些服务器分得那么清楚

361
00:14:30,679 --> 00:14:34,399
那么这时候就需要到咱们的一个概念了啊

362
00:14:34,639 --> 00:14:37,159
首先我们说的第一个概念啊

363
00:14:38,539 --> 00:14:39,740
ip地址

364
00:14:41,059 --> 00:14:45,500
ip地址的话其实简单ip地址就是咱们上节课也说过啊

365
00:14:45,500 --> 00:14:47,419
是这个终端的身份证

366
00:14:47,419 --> 00:14:47,899
对不对

367
00:14:47,899 --> 00:14:52,120
比如说************

368
00:14:52,820 --> 00:14:55,659
每一个终端只要连上网都有一个类似的地址

369
00:14:55,659 --> 00:14:57,299
这个地址是唯一的啊

370
00:14:57,299 --> 00:14:58,080
这个地址是唯一的

371
00:14:58,080 --> 00:15:00,779
比如说我们这个客户端可能地址是这个

372
00:15:00,779 --> 00:15:03,279
那我们这三个服务端

373
00:15:03,279 --> 00:15:06,460
那服务端可能又是三个不同的啊

374
00:15:06,460 --> 00:15:07,960
但是他肯定不会相同

375
00:15:07,960 --> 00:15:08,320
对不对

376
00:15:08,320 --> 00:15:09,639
肯定不会相同

377
00:15:10,759 --> 00:15:15,759
他们ip ip地址就相当于你身份证号一样是唯一的

378
00:15:16,100 --> 00:15:22,179
那么这时候终端在网络当中它的唯一标识是ip地址

379
00:15:22,179 --> 00:15:24,039
那么每一个设备当中

380
00:15:24,039 --> 00:15:25,269
每一个终端里面

381
00:15:25,269 --> 00:15:27,549
它又有更细的这个划分

382
00:15:27,549 --> 00:15:30,179
更细的划分叫什么叫做端口

383
00:15:32,720 --> 00:15:38,470
每个终端提供了0~65535啊

384
00:15:38,470 --> 00:15:43,159
这么大范围的一个就是65536个啊

385
00:15:43,159 --> 00:15:45,620
一共这么多个端口来供你使用

386
00:15:45,620 --> 00:15:50,120
每一个端口可以和外界去进行连接

387
00:15:50,120 --> 00:15:55,519
也就是说实际上我们和外界最多能进行多少多少个网络连接

388
00:15:55,519 --> 00:15:57,639
就是这么多个啊

389
00:15:57,639 --> 00:16:00,639
你多了这么多个就就端口就不够用了啊

390
00:16:00,639 --> 00:16:04,860
说白了就和这个你去银行

391
00:16:04,860 --> 00:16:06,899
银行他就是一个房子

392
00:16:06,899 --> 00:16:07,379
对不对

393
00:16:07,379 --> 00:16:08,610
那你进去以后

394
00:16:08,610 --> 00:16:09,929
你进了这个房子还不行

395
00:16:09,929 --> 00:16:11,610
你还得去找1号窗口

396
00:16:11,610 --> 00:16:12,509
2号窗口

397
00:16:12,509 --> 00:16:15,519
那么这个窗口就和这个端口的意思一样

398
00:16:16,139 --> 00:16:23,240
比如说我们和第一个游戏服务器相连的这个套接字啊

399
00:16:23,240 --> 00:16:25,279
每个套接字它都有一个端口

400
00:16:25,279 --> 00:16:27,318
比如说是1234

401
00:16:28,379 --> 00:16:33,120
那么和第二个游戏服务器相连的啊

402
00:16:33,120 --> 00:16:37,720
这个套接字可能是1235啊

403
00:16:37,720 --> 00:16:39,909
第三个可能就是1236等等

404
00:16:39,909 --> 00:16:42,559
那所以就是因为每一个连接

405
00:16:42,559 --> 00:16:44,690
然后我们这边有个套接字啊

406
00:16:44,690 --> 00:16:47,570
有个有个这个端口端口是不同的啊

407
00:16:47,570 --> 00:16:51,759
所以我们就可以去区分不同应用的这个网络

408
00:16:51,759 --> 00:16:55,340
连接了不同应用的网络连接了啊

409
00:16:55,340 --> 00:16:56,000
再举个例子

410
00:16:56,000 --> 00:16:56,899
你去想想

411
00:16:56,899 --> 00:16:59,480
比如说我们电脑同时登录这个qq和微信

412
00:16:59,480 --> 00:17:00,458
对不对

413
00:17:00,779 --> 00:17:03,269
为什么别人给你发个微信

414
00:17:03,269 --> 00:17:04,859
你的qq是舞蹈

415
00:17:04,859 --> 00:17:07,019
只有你微信能收到啊

416
00:17:07,019 --> 00:17:09,888
因为你的微信他就知道啊

417
00:17:09,888 --> 00:17:12,858
和我去和服务器连接的端口是什么啊

418
00:17:12,858 --> 00:17:14,419
qq是另外一个端口

419
00:17:14,419 --> 00:17:16,900
所以比如说qq发消息唉

420
00:17:16,900 --> 00:17:18,039
比如说这个是qq

421
00:17:19,480 --> 00:17:22,609
比如说这个是微信啊

422
00:17:22,609 --> 00:17:24,410
比如说qq发个消息

423
00:17:24,410 --> 00:17:25,940
我这边1234收到了

424
00:17:25,940 --> 00:17:27,868
那我就知道1234是qq

425
00:17:27,868 --> 00:17:30,730
所以我就知道这个消息是qq的

426
00:17:30,730 --> 00:17:33,970
而微信发个消息其实是发到了端口1235上

427
00:17:33,970 --> 00:17:35,109
那我就知道了啊

428
00:17:35,109 --> 00:17:36,009
原来是1235

429
00:17:36,009 --> 00:17:37,910
那这个消息是微信啊

430
00:17:37,910 --> 00:17:39,789
所以实际上就是这么一回事

431
00:17:39,789 --> 00:17:41,109
非常的简单啊

432
00:17:41,109 --> 00:17:41,829
非常简单

433
00:17:41,829 --> 00:17:44,559
那所以其实大家去想啊

434
00:17:44,880 --> 00:17:46,898
准确的去说

435
00:17:48,240 --> 00:17:50,579
如果我们要找到一个详细地址

436
00:17:50,579 --> 00:17:55,420
往往就是一个ip地址加上一个端口

437
00:17:55,420 --> 00:17:58,180
这是一个最详细的地址了

438
00:17:58,180 --> 00:18:00,579
那么所以常常我们会见到这样的写法

439
00:18:00,579 --> 00:18:03,680
比如说前面有个ip地址

440
00:18:04,119 --> 00:18:07,029
然后冒号后面跟个多少多少

441
00:18:07,029 --> 00:18:09,609
那么汽车冒号后面这个代表就是端口

442
00:18:09,609 --> 00:18:11,079
就是我要玩

443
00:18:11,079 --> 00:18:13,029
我要和这个人建立个链接

444
00:18:13,029 --> 00:18:14,279
给他发消息

445
00:18:14,500 --> 00:18:17,740
那么和他建立的连接端口是多少啊

446
00:18:17,740 --> 00:18:19,299
就是后面的这个数值啊

447
00:18:19,299 --> 00:18:21,960
就是这样的一个意思啊

448
00:18:21,960 --> 00:18:22,680
那ok啊

449
00:18:22,680 --> 00:18:25,720
我们在这扯了一会儿啊

450
00:18:25,720 --> 00:18:29,680
那实际上我们就是主要是说有一个叫做ip

451
00:18:29,680 --> 00:18:31,660
一个叫做端口嗯

452
00:18:31,660 --> 00:18:35,140
应该大部分人对这两个东西已经知道了啊

453
00:18:35,140 --> 00:18:36,579
最起码已经听过啊

454
00:18:36,579 --> 00:18:40,410
如果你是当然如果你是完全没有接触过计算机

455
00:18:40,410 --> 00:18:42,059
计算机的啊

456
00:18:42,059 --> 00:18:43,960
就是你的这个什么学科啊

457
00:18:43,960 --> 00:18:44,980
乱七八糟的啊

458
00:18:46,839 --> 00:18:48,220
可能你不太理解啊

459
00:18:48,220 --> 00:18:49,480
但如果你接触过的话

460
00:18:49,480 --> 00:18:54,440
实际上我刚才说的那些可能你早都没有什么问题了啊

461
00:18:55,859 --> 00:18:58,200
那么我们知道了这个啊

462
00:18:58,200 --> 00:19:01,869
那么实际上咱们在咱们做的是h5 游戏

463
00:19:01,869 --> 00:19:02,349
对不对

464
00:19:02,349 --> 00:19:05,210
咱们像这个引擎主要做的是h5 游戏

465
00:19:05,210 --> 00:19:06,589
也就是网页游戏

466
00:19:06,589 --> 00:19:09,619
那么他用的我们刚才说了叫做web socket

467
00:19:09,619 --> 00:19:10,910
web socket

468
00:19:10,910 --> 00:19:14,009
它的特点其实也是一样的啊

469
00:19:14,009 --> 00:19:17,759
它也是有这样的一个服务器啊

470
00:19:17,759 --> 00:19:22,339
叫做服务端的套接字啊

471
00:19:22,339 --> 00:19:23,450
服务端的套接字

472
00:19:23,450 --> 00:19:26,980
然后每个客户端也有个套接字

473
00:19:27,079 --> 00:19:30,470
然后我每个客户端和服务端去相连

474
00:19:30,470 --> 00:19:34,670
最后然后我们就可以做到这样的一个网络情况

475
00:19:34,670 --> 00:19:36,640
网络的一个通信了啊

476
00:19:36,640 --> 00:19:38,680
网络游戏就是这样去做的嘛

477
00:19:38,680 --> 00:19:43,069
啊那这样的话同时我们也能想清一个问题了啊

478
00:19:43,069 --> 00:19:45,690
我们看一下这么多客户端连接服务端

479
00:19:45,690 --> 00:19:48,059
服务端怎么知道我和哪个客户端相连的

480
00:19:48,059 --> 00:19:49,900
就是因为每次连的时候

481
00:19:49,900 --> 00:19:51,759
我都分配一个不同的端口

482
00:19:51,759 --> 00:19:54,750
比如说1234和这个客户端连啊

483
00:19:54,750 --> 00:19:59,279
这个socket 1234和第二个客户端连的socket啊

484
00:19:59,279 --> 00:20:01,440
可能是1235

485
00:20:01,440 --> 00:20:04,859
也就是说和不同客户端相连的套接字有不同的端口

486
00:20:04,859 --> 00:20:05,819
对不对

487
00:20:08,900 --> 00:20:09,799
ok啊

488
00:20:09,799 --> 00:20:12,440
那我们这节课知道的这个通信了啊

489
00:20:12,440 --> 00:20:15,920
那就进入到我们的真正的内容了啊

490
00:20:15,920 --> 00:20:17,319
真正的内容了

491
00:20:20,359 --> 00:20:24,589
我们web socket用的服务器是要用一个pc

492
00:20:24,589 --> 00:20:29,480
然后在javascript环境里面做的啊

493
00:20:29,480 --> 00:20:33,509
也就是说我们这个服务器的语言我们要用javascript去做

494
00:20:33,509 --> 00:20:35,369
那么javascript啊

495
00:20:35,369 --> 00:20:38,279
现在他的这个服务端运行环境啊

496
00:20:38,279 --> 00:20:42,859
做的最好的就是node.gs啊

497
00:20:42,859 --> 00:20:46,039
也就是说这个note.js就是一个语言环境啊

498
00:20:46,039 --> 00:20:49,909
在这个环境里面我们就可以去执行javascript这个脚本

499
00:20:49,909 --> 00:20:53,730
所以如果我们现在想做这个web script啊

500
00:20:53,730 --> 00:20:54,539
every web

501
00:20:54,539 --> 00:20:57,150
如果我们现在想做这个web socket啊

502
00:20:57,150 --> 00:20:59,380
就是这个套接字编程的话

503
00:20:59,380 --> 00:21:00,940
那么首先我们要做服务端

504
00:21:00,940 --> 00:21:02,420
先把服务端做好

505
00:21:02,420 --> 00:21:03,920
那么服务端怎么做

506
00:21:03,920 --> 00:21:07,319
首先我们要有这个环境

507
00:21:07,319 --> 00:21:08,640
有了这个环境

508
00:21:08,640 --> 00:21:10,769
我们才能继续往下去做

509
00:21:10,769 --> 00:21:13,539
那么这个环境怎样去装呢

510
00:21:15,180 --> 00:21:23,180
大家直接啊在这个网上去到它的官网里面note.gs搜一下就出来了

511
00:21:23,180 --> 00:21:25,200
官网直接下载

512
00:21:26,299 --> 00:21:28,250
下载一个长期支持版

513
00:21:28,250 --> 00:21:29,180
最新版

514
00:21:29,180 --> 00:21:30,559
我下的是长期支持版啊

515
00:21:30,559 --> 00:21:31,759
其实都行

516
00:21:31,980 --> 00:21:35,099
然后你下载完以后直接双击它

517
00:21:35,099 --> 00:21:37,980
下一步下一步给他安完就ok了啊

518
00:21:37,980 --> 00:21:40,339
按完按完以后

519
00:21:40,519 --> 00:21:46,339
右键我的电脑属性高级设置啊

520
00:21:46,339 --> 00:21:47,119
如果是win 7

521
00:21:47,119 --> 00:21:48,619
可能直接就是这个窗口

522
00:21:48,619 --> 00:21:51,259
在这里面选高级里面有个环境变量

523
00:21:51,259 --> 00:21:59,819
然后里面有个pass pass pass里面大家双击看一下下面有没有这两个路径

524
00:21:59,819 --> 00:22:00,599
如果没有的话

525
00:22:00,599 --> 00:22:01,980
把他加上啊

526
00:22:01,980 --> 00:22:04,259
加上确定就ok了啊

527
00:22:04,259 --> 00:22:05,789
然后如果你是win 7的话

528
00:22:07,799 --> 00:22:09,960
完了以后最好重启一下系统

529
00:22:11,000 --> 00:22:14,150
那这样的话这个环境才算搭建完成啊

530
00:22:14,150 --> 00:22:15,319
如果是win 7的话

531
00:22:15,319 --> 00:22:16,220
可能没有这个列表

532
00:22:16,220 --> 00:22:17,500
win 7的话

533
00:22:20,700 --> 00:22:25,299
win 7的话可能直接应该是我看bg

534
00:22:28,000 --> 00:22:30,819
win 7的话应该是他这个变量应该是只有一行

535
00:22:30,819 --> 00:22:32,079
你双击以后没有这个列表

536
00:22:32,079 --> 00:22:33,400
只有一行啊

537
00:22:33,400 --> 00:22:35,809
然后你就直接在最后面啊

538
00:22:35,809 --> 00:22:37,670
在最最后面写个分号

539
00:22:37,670 --> 00:22:39,049
然后加上这一行

540
00:22:39,049 --> 00:22:40,549
然后再写个分号啊

541
00:22:40,549 --> 00:22:42,410
就是每一行后面加个分号

542
00:22:42,410 --> 00:22:44,289
再把这一行写上啊

543
00:22:44,289 --> 00:22:45,369
也就是说加个分号

544
00:22:45,369 --> 00:22:46,329
写上这个路径

545
00:22:46,329 --> 00:22:47,049
再加个分号

546
00:22:47,049 --> 00:22:48,940
写上这个路径就可以了

547
00:22:50,279 --> 00:22:52,460
ok啊那这个环境变量啊

548
00:22:52,460 --> 00:22:54,859
我们就算创建完成了

549
00:22:54,859 --> 00:22:58,619
然后怎样能验证我们的环境是否ok呢

550
00:22:58,779 --> 00:23:04,130
打开我们windows的命令提示符啊

551
00:23:04,130 --> 00:23:05,900
win 7和win 10的位置不一样

552
00:23:05,900 --> 00:23:07,759
如果你便捷打开的话

553
00:23:07,759 --> 00:23:10,388
它有个命令叫cmd啊

554
00:23:10,388 --> 00:23:11,648
你就可以打开它

555
00:23:11,648 --> 00:23:12,429
打开它以后

556
00:23:12,429 --> 00:23:14,940
你直接node gv

557
00:23:14,940 --> 00:23:16,319
如果这里能出现版本

558
00:23:16,319 --> 00:23:19,500
就证明node.js这个环境就装完了

559
00:23:19,500 --> 00:23:25,920
那这时候你的电脑本身我们就把它作为一个呃服务端了啊

560
00:23:25,920 --> 00:23:30,640
那么它呢也就是说我们的电脑现在就可以运行javascript啊

561
00:23:30,640 --> 00:23:33,799
就可以在node环境下运行javascript了

562
00:23:34,180 --> 00:23:36,250
那么这节课我们先说这么多啊

563
00:23:36,250 --> 00:23:41,319
基本上这节课没讲什么太多东西啊

564
00:23:41,539 --> 00:23:46,039
嗯讲的这些可能对于好多同学而言也是感觉比较废话啊

565
00:23:46,039 --> 00:23:49,589
因为很多同学可能真正学过电脑的多多少少

566
00:23:49,589 --> 00:23:52,519
ip端口其实已经已经知道了

567
00:23:52,859 --> 00:23:56,880
呃但是如果你不知道的同学啊

568
00:23:56,880 --> 00:23:58,470
这个不知道的同学

569
00:23:58,470 --> 00:24:00,299
你就要自己去琢磨一下啊

570
00:24:00,299 --> 00:24:00,839
琢磨一下

571
00:24:00,839 --> 00:24:01,898
理解一下

572
00:24:02,059 --> 00:24:03,559
ok那我们这节课先这样

573
00:24:03,559 --> 00:24:05,900
我们下节课继续吧


1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,740 --> 00:00:12,769
ok那么我们这节课来说一下项目发布啊

3
00:00:12,769 --> 00:00:18,519
那么我们知道我们cos creator他做的游戏主要是运行在这个网页上的啊

4
00:00:18,519 --> 00:00:21,370
当然它也支持于支持其他平台

5
00:00:21,370 --> 00:00:21,760
对不对

6
00:00:21,760 --> 00:00:22,989
也支持其他平台

7
00:00:22,989 --> 00:00:25,280
那么不管是什么平台

8
00:00:25,280 --> 00:00:26,780
我们要做发布的话

9
00:00:26,780 --> 00:00:27,500
怎样发布

10
00:00:27,500 --> 00:00:30,519
在这里我们选择项目有一个构建发布

11
00:00:33,159 --> 00:00:39,520
我们选择它在这里大家可以看有效有这个很多内容啊

12
00:00:39,520 --> 00:00:41,560
其中第二项是发布平台

13
00:00:41,579 --> 00:00:42,960
那么在这个平台里面

14
00:00:42,960 --> 00:00:48,119
大家可以找到有这个网页版移动网页跟那个普通的这个电脑网页

15
00:00:48,140 --> 00:00:52,039
然后facebook微信游戏qq player安卓版本

16
00:00:52,039 --> 00:00:56,310
然后windows版本是不是还有oppo vivo它的一个支持

17
00:00:56,310 --> 00:00:59,219
那么在这里呃

18
00:00:59,219 --> 00:01:01,259
大家可能发现首先少一个啊

19
00:01:01,259 --> 00:01:02,579
没有这个ios

20
00:01:02,579 --> 00:01:04,859
那么如果你要发布ios版本

21
00:01:04,859 --> 00:01:06,450
你的这个cos creator啊

22
00:01:06,450 --> 00:01:10,480
就是它整个一套你都要在苹果系统下面去开发

23
00:01:10,480 --> 00:01:12,459
那么苹果系统开发的话

24
00:01:12,459 --> 00:01:16,379
他这边就可以去有这个ios版本啊

25
00:01:17,400 --> 00:01:20,239
那么对于windows上

26
00:01:20,239 --> 00:01:21,980
如果大家在windows上开发的话

27
00:01:21,980 --> 00:01:23,359
你就找不见那个ios

28
00:01:23,359 --> 00:01:24,680
这是注意的啊

29
00:01:24,680 --> 00:01:28,700
啊就是说你苹果的游戏必须在苹果的系统下面啊

30
00:01:28,700 --> 00:01:30,680
你才可以去进行发布

31
00:01:30,680 --> 00:01:31,939
那么安卓的话

32
00:01:31,939 --> 00:01:33,500
比如说你想发布安卓版本

33
00:01:33,500 --> 00:01:35,579
在这里选择安卓安卓版本

34
00:01:35,599 --> 00:01:36,680
那注意啊

35
00:01:36,680 --> 00:01:37,760
还没有结束啊

36
00:01:37,760 --> 00:01:42,420
不是说选择安卓版本就可以去啊构建发布了

37
00:01:42,420 --> 00:01:45,480
如果你要呃发布安卓版本

38
00:01:45,480 --> 00:01:47,680
你要在这里文件设置

39
00:01:51,280 --> 00:01:53,079
在这里注意有两个路径

40
00:01:53,079 --> 00:01:53,859
一个是ndk

41
00:01:53,859 --> 00:01:55,760
一个是安卓sdk啊

42
00:01:55,760 --> 00:01:59,900
那也就是说你需要从网上去下载nd k跟sdk啊

43
00:01:59,900 --> 00:02:03,040
就安卓的这安卓n dk和安卓sdk

44
00:02:03,060 --> 00:02:06,799
然后在这两个呃路径这里啊

45
00:02:06,799 --> 00:02:09,860
你选择把它的路径对应好啊

46
00:02:09,860 --> 00:02:11,300
路径路径对应好

47
00:02:11,300 --> 00:02:14,740
这样的话你才可以去打包安卓的应用啊

48
00:02:14,740 --> 00:02:15,639
打包安卓应用

49
00:02:15,639 --> 00:02:16,300
打包完了以后

50
00:02:16,300 --> 00:02:19,330
最后生成的就是安装a p k啊

51
00:02:19,330 --> 00:02:25,039
但是我们更多的我们最主要的在这里

52
00:02:29,520 --> 00:02:32,759
就是发布这个一个是网页版本

53
00:02:32,759 --> 00:02:34,620
还有一个就是微信版本啊

54
00:02:34,620 --> 00:02:36,060
就是现在微信小游戏很火

55
00:02:36,060 --> 00:02:36,740
是不是

56
00:02:36,740 --> 00:02:38,479
然后最主要的就是这两个

57
00:02:38,479 --> 00:02:39,979
当然你也可以去发布

58
00:02:39,979 --> 00:02:41,340
比如说qq play

59
00:02:41,340 --> 00:02:43,439
还有这个比如oppo vivo的啊

60
00:02:43,439 --> 00:02:45,400
那这几个平台都是可以发布的

61
00:02:45,400 --> 00:02:48,400
那我们在这里首先来说一下普通的这个网页平台

62
00:02:48,400 --> 00:02:52,240
比如说我们在这里选择一个桌面的这个web平台啊

63
00:02:52,240 --> 00:02:54,199
就是发布一个网页平台

64
00:02:54,460 --> 00:02:58,719
那么在这里第二项第一项是游戏名称

65
00:02:58,719 --> 00:03:00,759
就是你发不出去的游戏名称叫什么

66
00:03:00,759 --> 00:03:03,020
第二个就是路径

67
00:03:03,340 --> 00:03:06,520
就是最后生成的这个网页的路径在哪里

68
00:03:06,520 --> 00:03:09,979
它默认这个点就是指在当前的项目文件夹下

69
00:03:10,000 --> 00:03:12,069
然后给你生成一个build文件夹

70
00:03:12,069 --> 00:03:14,719
然后在这个里面去嗯

71
00:03:14,719 --> 00:03:18,060
它会把生成的这个网页内容全都放到这个问题

72
00:03:18,979 --> 00:03:22,039
那么接下来选择一个初始场景啊

73
00:03:22,039 --> 00:03:23,419
比如说是start还是game

74
00:03:23,419 --> 00:03:25,960
这是一个初始的场景嗯

75
00:03:25,960 --> 00:03:29,960
然后剩下的在这里有一个分辨率啊

76
00:03:29,960 --> 00:03:32,419
是这个预览的一个分辨率啊

77
00:03:32,419 --> 00:03:36,300
这个分辨率的话就是你自己去嗯设置啊

78
00:03:36,300 --> 00:03:38,639
这个游戏比如说在网页里面打开以后

79
00:03:38,639 --> 00:03:40,860
它的分辨率是多少啊

80
00:03:40,860 --> 00:03:43,939
然后你完事以后就去选择构建

81
00:03:46,919 --> 00:03:50,520
构建这里可能比较慢啊

82
00:03:50,520 --> 00:03:51,120
可能比较慢

83
00:03:51,120 --> 00:03:52,979
这个大家一定要耐心去等啊

84
00:03:52,979 --> 00:03:54,319
一定要耐心去等

85
00:03:59,680 --> 00:04:01,240
千万不要急啊

86
00:04:01,240 --> 00:04:05,680
有的同学在这里就觉得自己电脑死机了还是怎么了啊

87
00:04:06,919 --> 00:04:09,139
其实没有他在这里构建啊

88
00:04:09,139 --> 00:04:11,379
他这个确实是比较慢的

89
00:04:29,199 --> 00:04:32,620
ok那我这边就完成完成了

90
00:04:32,639 --> 00:04:34,800
完成了以后我们就可以点运行了

91
00:04:34,800 --> 00:04:36,480
他就直接在网页上面打开了

92
00:04:36,480 --> 00:04:38,519
其实就跟在这里运行一样了啊

93
00:04:38,519 --> 00:04:42,339
因为在这里我们默认调试就是在浏览器上运行的

94
00:04:42,339 --> 00:04:44,500
那在这里我们先不要点运行

95
00:04:44,500 --> 00:04:46,060
我们在这里看一下发布路径

96
00:04:46,060 --> 00:04:46,959
右边有个打开

97
00:04:46,959 --> 00:04:48,240
我们选择打开

98
00:04:49,000 --> 00:04:53,259
然后大家就可以看到在我们项目文件夹下啊

99
00:04:53,259 --> 00:04:55,029
他就打开我们这个项目文件夹了

100
00:04:55,029 --> 00:04:59,060
在这里面有个build build里面就有一个web桌面版本

101
00:04:59,060 --> 00:05:00,480
把它打开

102
00:05:00,800 --> 00:05:05,959
大家就可以发现哎这里面就有很多内容

103
00:05:05,959 --> 00:05:09,589
其中一个是index html啊

104
00:05:09,589 --> 00:05:13,300
那么实际上我们要是打开这个文件的话

105
00:05:14,560 --> 00:05:17,860
那实际上啊在这里打开这个文件

106
00:05:17,860 --> 00:05:19,839
它就是多了一个加载的过程

107
00:05:19,839 --> 00:05:24,899
那么它跟我们在这里点击运行实际上是一样的啊

108
00:05:24,899 --> 00:05:26,939
实际上效果是一样的啊

109
00:05:26,939 --> 00:05:31,000
但是我们最后打包出来的内容啊

110
00:05:31,000 --> 00:05:33,819
它很多情况下你需要真正调试它的话

111
00:05:33,819 --> 00:05:36,139
你不能在这里直接在本地打开

112
00:05:36,160 --> 00:05:39,399
你需要把它传到一个网络空间上去啊

113
00:05:39,399 --> 00:05:40,180
在本地打开

114
00:05:40,180 --> 00:05:43,139
有时候呃是没效果的啊

115
00:05:43,139 --> 00:05:43,800
是没效果的

116
00:05:43,800 --> 00:05:44,459
就和刚才一样

117
00:05:44,459 --> 00:05:47,699
你可能看到他一直在加载啊等等各种状态

118
00:05:47,720 --> 00:05:51,079
那么如果你希望打包出来以后啊

119
00:05:51,079 --> 00:05:52,339
然后也可以正常运行它

120
00:05:52,339 --> 00:05:55,339
你就需要把它传到一个网络空间上

121
00:05:55,339 --> 00:05:59,000
如果嗯大家在自己公司里面

122
00:05:59,000 --> 00:06:00,259
比如说上班了以后

123
00:06:00,259 --> 00:06:01,519
在这个公司里面

124
00:06:01,519 --> 00:06:05,360
然后呃一般公司会有自己的一个网络空间啊

125
00:06:05,360 --> 00:06:08,740
你呢就是要把这些内容放到公司的网络空间下面

126
00:06:08,740 --> 00:06:10,540
公司会给你一个域名啊

127
00:06:10,540 --> 00:06:11,529
就是网络域名

128
00:06:11,529 --> 00:06:14,379
然后通过这个域名就可以打开这个游戏

129
00:06:15,420 --> 00:06:21,560
那么如果你这个呃没有公司这个空间的话

130
00:06:21,560 --> 00:06:22,879
你自己做这个调试

131
00:06:22,879 --> 00:06:23,660
做测试的话

132
00:06:23,660 --> 00:06:25,579
你可以从网上找一些免费的空间

133
00:06:25,579 --> 00:06:29,000
当然免费的效果有些并不是很好啊

134
00:06:29,000 --> 00:06:30,800
所以你是如果一个空间不行的话

135
00:06:30,800 --> 00:06:33,060
你可以再试一个空间啊

136
00:06:33,079 --> 00:06:36,319
那么在这里你就去搜这个htp空间

137
00:06:36,319 --> 00:06:38,959
搜索一下http空间以后申请下号

138
00:06:38,959 --> 00:06:40,699
他一般就会给你一个域名啊

139
00:06:40,699 --> 00:06:41,959
就是给你一个网址

140
00:06:41,959 --> 00:06:43,920
然后对应一个空间

141
00:06:43,920 --> 00:06:48,060
在这里把你的这些内容上传到空间上面

142
00:06:48,060 --> 00:06:49,680
然后通过域名啊

143
00:06:49,680 --> 00:06:53,360
通过域名就可以打开你的这个游戏了啊

144
00:06:53,360 --> 00:06:55,220
就可以正常打开这个游戏啊

145
00:06:55,220 --> 00:06:59,439
那么这样的话你把这个域名发给你的这个朋友啊

146
00:06:59,439 --> 00:07:04,240
他们都可以去进行这个这个游戏的这个ur了啊

147
00:07:04,920 --> 00:07:09,959
也就是说现在就是你算是把这个游戏真正结尾了啊

148
00:07:09,959 --> 00:07:12,120
不光是你自己可以去进行游玩啊

149
00:07:12,120 --> 00:07:14,639
你只要最后把它打包传到这个空间里面

150
00:07:14,639 --> 00:07:15,600
拿到域名

151
00:07:15,600 --> 00:07:18,240
然后把这个域名给任何一个人啊

152
00:07:18,240 --> 00:07:22,339
那任何一个人都可以去进行这个游戏的这个游玩了啊

153
00:07:22,600 --> 00:07:24,160
呃

154
00:07:24,160 --> 00:07:27,639
那么这个啊这个就是这样的啊

155
00:07:27,639 --> 00:07:28,660
这个就是这样子

156
00:07:30,819 --> 00:07:36,240
当然很多情况下在公司里面他可能到这个

157
00:07:36,240 --> 00:07:38,069
比如说你把游戏做完以后啊

158
00:07:38,069 --> 00:07:40,319
这些东西最后不用上传啊

159
00:07:40,319 --> 00:07:41,639
这些一套不用你管啊

160
00:07:41,639 --> 00:07:43,759
你可能给了这个前端工作人员

161
00:07:43,779 --> 00:07:46,480
也就专门去负责做网页的啊

162
00:07:46,480 --> 00:07:48,220
这一块的人员把这个东西给了他

163
00:07:48,220 --> 00:07:49,779
他呢就会给你传好了

164
00:07:49,800 --> 00:07:50,430
哈哈哈

165
00:07:50,430 --> 00:07:52,600
很多公司可能是这样啊

166
00:07:52,620 --> 00:07:54,120
啊当然不管是怎样啊

167
00:07:54,120 --> 00:07:54,720
不管怎样

168
00:07:54,720 --> 00:07:59,519
我们要做的最起码会把这个呃文件啊给它打包出来啊

169
00:07:59,519 --> 00:08:00,639
给他打包出来

170
00:08:01,500 --> 00:08:05,339
ok那么我们这节课就是这么多内容啊

171
00:08:05,339 --> 00:08:08,279
下节课我们来说一下这个微信平台的打包啊

172
00:08:08,279 --> 00:08:12,660
这个是呃准确来说微信打包应该是我们cos creator啊

173
00:08:12,660 --> 00:08:15,750
用的最多的这样的一个打包碗啊

174
00:08:15,750 --> 00:08:17,819
因为现在微信小游戏是非常火的

175
00:08:17,819 --> 00:08:18,540
对不对

176
00:08:18,600 --> 00:08:22,079
那ok我们下节课再说


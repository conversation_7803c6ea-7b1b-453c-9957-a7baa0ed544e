Write-Host "Batch Rename Tool - Remove Chinese Auto-Generated Text" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

$files = Get-ChildItem -File | Where-Object { $_.Name -like "*中文*自动生成*" }

if ($files.Count -eq 0) {
    Write-Host "No files found with Chinese auto-generated text" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

Write-Host "Found $($files.Count) files to rename:" -ForegroundColor Green
Write-Host ""

foreach ($file in $files) {
    $newName = $file.Name -replace " 中文（自动生成）", ""
    Write-Host "Will rename: $($file.Name)" -ForegroundColor Cyan
    Write-Host "        to: $newName" -ForegroundColor Green
    Write-Host ""
}

$confirm = Read-Host "Continue with rename operation? (y/n)"

if ($confirm -eq "y" -or $confirm -eq "Y") {
    Write-Host "Starting rename operation..." -ForegroundColor Green
    
    $success = 0
    foreach ($file in $files) {
        try {
            $newName = $file.Name -replace " 中文（自动生成）", ""
            if (Test-Path $newName) {
                Write-Host "SKIP: $($file.Name) - Target exists" -ForegroundColor Yellow
            } else {
                Rename-Item -Path $file.FullName -NewName $newName
                Write-Host "DONE: $($file.Name)" -ForegroundColor Green
                $success++
            }
        }
        catch {
            Write-Host "FAIL: $($file.Name)" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "Rename completed! Successfully renamed $success files" -ForegroundColor Green
} else {
    Write-Host "Operation cancelled" -ForegroundColor Yellow
}

Read-Host "Press Enter to exit"

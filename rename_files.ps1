# 批量重命名文件脚本 - 删除" 中文（自动生成）"
# 使用方法：在PowerShell中运行此脚本

# 获取当前目录下所有包含" 中文（自动生成）"的文件
$files = Get-ChildItem -File | Where-Object { $_.Name -like "*中文（自动生成）*" }

Write-Host "找到 $($files.Count) 个需要重命名的文件："
Write-Host ""

# 显示将要进行的重命名操作
foreach ($file in $files) {
    $newName = $file.Name -replace " 中文（自动生成）", ""
    Write-Host "将重命名: $($file.Name) -> $newName"
}

Write-Host ""
$confirm = Read-Host "确认要执行重命名操作吗？(y/n)"

if ($confirm -eq "y" -or $confirm -eq "Y" -or $confirm -eq "yes") {
    Write-Host ""
    Write-Host "开始重命名..."
    
    $successCount = 0
    $errorCount = 0
    
    foreach ($file in $files) {
        try {
            $newName = $file.Name -replace " 中文（自动生成）", ""
            $newPath = Join-Path $file.Directory $newName
            
            # 检查目标文件是否已存在
            if (Test-Path $newPath) {
                Write-Host "警告: 目标文件已存在，跳过: $newName" -ForegroundColor Yellow
                continue
            }
            
            Rename-Item -Path $file.FullName -NewName $newName
            Write-Host "✓ 成功重命名: $($file.Name) -> $newName" -ForegroundColor Green
            $successCount++
        }
        catch {
            Write-Host "✗ 重命名失败: $($file.Name) - 错误: $($_.Exception.Message)" -ForegroundColor Red
            $errorCount++
        }
    }
    
    Write-Host ""
    Write-Host "重命名完成！成功: $successCount 个，失败: $errorCount 个" -ForegroundColor Cyan
} else {
    Write-Host "操作已取消。" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "按任意键退出..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

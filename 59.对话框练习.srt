1
00:00:13,900 --> 00:00:15,699
咱们来做一个对话啊

2
00:00:15,699 --> 00:00:16,989
对话的一个场景

3
00:00:16,989 --> 00:00:20,260
那么实际上这个ui真正我们要把它用起来

4
00:00:20,260 --> 00:00:23,609
肯定要这个数据支持的一个支持啊

5
00:00:23,609 --> 00:00:26,719
那咱们现在还没学这个数据啊

6
00:00:26,719 --> 00:00:31,010
所以咱们就用简单的这个呃简单的方式啊

7
00:00:31,010 --> 00:00:33,530
咱们来实现这样一个功能啊

8
00:00:33,530 --> 00:00:35,869
那么首先啊这个对话啊

9
00:00:35,869 --> 00:00:38,459
咱们先把素材拿上来看一下

10
00:00:40,560 --> 00:00:41,899
那么在素材里面

11
00:00:41,899 --> 00:00:42,979
首先我们有背景

12
00:00:42,979 --> 00:00:43,738
对不对

13
00:00:43,738 --> 00:00:45,058
首先我们有背景

14
00:00:45,058 --> 00:00:46,228
然后还有说话的人

15
00:00:46,228 --> 00:00:47,908
然后还有这个对话框

16
00:00:47,908 --> 00:00:49,859
这时候大家看一下这个说话的人

17
00:00:49,859 --> 00:00:53,549
实际上是他是没有这个脸部的啊

18
00:00:53,549 --> 00:00:56,460
那么咱们说一下大部分游戏啊

19
00:00:56,460 --> 00:01:00,079
就是现在很多游戏他都是一个完整的人啊

20
00:01:00,079 --> 00:01:02,359
他比如说一个人物有不同的表情

21
00:01:02,359 --> 00:01:05,590
那他就给你放上不同的这个图片啊

22
00:01:05,590 --> 00:01:07,209
就是什么表情换什么图片

23
00:01:07,209 --> 00:01:09,129
什么表情换什么图片啊

24
00:01:09,129 --> 00:01:11,629
如果按这按那种方式去做的话

25
00:01:11,629 --> 00:01:13,310
呃会简单很多

26
00:01:13,310 --> 00:01:14,659
但是缺点也有

27
00:01:14,659 --> 00:01:17,099
就是太费内存空间啊

28
00:01:17,099 --> 00:01:17,938
就你做一个游戏

29
00:01:17,938 --> 00:01:20,780
比如说一个人有80个表情啊

30
00:01:20,780 --> 00:01:21,980
你就有80张图片

31
00:01:21,980 --> 00:01:23,000
这就太麻烦了

32
00:01:23,000 --> 00:01:23,450
对不对

33
00:01:26,789 --> 00:01:30,819
那所以咱们在这儿就是用一种更好一点的方式啊

34
00:01:30,819 --> 00:01:32,468
就是比如这个人有很多表情

35
00:01:32,468 --> 00:01:33,938
但是他身体是不动的

36
00:01:33,938 --> 00:01:36,189
所以身体都是这一张图片

37
00:01:36,189 --> 00:01:38,170
剩下的就是有不同的脸部图片

38
00:01:38,170 --> 00:01:40,920
不同的嘴部图片就可以了

39
00:01:41,099 --> 00:01:42,359
那大家就可以看一下

40
00:01:42,359 --> 00:01:43,260
比如说脸部啊

41
00:01:43,260 --> 00:01:44,760
咱们这儿有两个嘴部

42
00:01:44,760 --> 00:01:46,370
也有两个图片啊

43
00:01:46,370 --> 00:01:49,939
然后我们就可以通过这个就可以给它搭配出啊

44
00:01:49,939 --> 00:01:51,659
搭配出不同的表情

45
00:01:51,900 --> 00:01:56,439
那么首先我们先把这个呃内容拖上来啊

46
00:01:56,439 --> 00:01:58,480
我们先搭建咱们的这个界面

47
00:01:58,480 --> 00:01:59,560
首先有个背景

48
00:01:59,560 --> 00:02:02,000
我们把背景放上来

49
00:02:02,719 --> 00:02:04,000
那背景放上来以后

50
00:02:04,000 --> 00:02:07,780
我们看一下这个背景的大小是480x272

51
00:02:07,780 --> 00:02:13,179
所以canvas我们也来个480x272啊

52
00:02:13,179 --> 00:02:14,939
我们就按它的这个大小

53
00:02:17,459 --> 00:02:19,998
那么这个就是咱们的这个背景

54
00:02:19,998 --> 00:02:21,739
那么有了背景以后啊

55
00:02:21,739 --> 00:02:22,579
有了背景以后

56
00:02:22,579 --> 00:02:26,240
在背景里面咱们是有这个对话框的

57
00:02:26,240 --> 00:02:26,719
对不对

58
00:02:26,719 --> 00:02:27,500
除了对话框

59
00:02:27,500 --> 00:02:28,280
我们还有人物

60
00:02:28,280 --> 00:02:30,280
那首先我们先把人物放上来

61
00:02:30,419 --> 00:02:32,039
这是我们的人物啊

62
00:02:32,039 --> 00:02:35,729
我们下一点大概在这个位置就ok了

63
00:02:35,729 --> 00:02:37,020
然后还有对话框

64
00:02:37,020 --> 00:02:38,800
对话框也拖拽上来

65
00:02:39,520 --> 00:02:41,280
对话框的话比较大

66
00:02:41,280 --> 00:02:45,500
咱们给它呃缩放改一下

67
00:02:57,919 --> 00:03:00,280
ok比如这是咱们的对话框

68
00:03:00,280 --> 00:03:02,439
这是咱们的人物对话框是在前面的

69
00:03:02,439 --> 00:03:03,159
对不对

70
00:03:03,159 --> 00:03:04,900
然后对于这个人物而言啊

71
00:03:04,900 --> 00:03:07,300
他是有这个脸部和嘴部的

72
00:03:07,300 --> 00:03:09,248
所以我们在这里呃

73
00:03:09,248 --> 00:03:10,808
把这个脸部拖上来

74
00:03:10,808 --> 00:03:11,829
一个嘴部拖上来

75
00:03:11,829 --> 00:03:12,539
一个

76
00:03:14,360 --> 00:03:15,699
这是脸部

77
00:03:15,699 --> 00:03:17,560
我们对齐一些

78
00:03:18,300 --> 00:03:19,819
差不多就行

79
00:03:19,819 --> 00:03:21,530
下面有点空隙

80
00:03:21,530 --> 00:03:22,759
上面有点空隙

81
00:03:22,759 --> 00:03:24,319
我们给它稍微拉伸一点啊

82
00:03:24,319 --> 00:03:25,719
拉伸两个像素

83
00:03:29,259 --> 00:03:30,360
ok差不多了

84
00:03:30,360 --> 00:03:31,590
是不是差不多了

85
00:03:31,590 --> 00:03:33,210
嘴部也一样

86
00:03:33,210 --> 00:03:34,599
拖上来一个

87
00:03:40,039 --> 00:03:41,719
这个左右也有空隙

88
00:03:41,719 --> 00:03:43,919
我们也是把他左右拉伸一点

89
00:03:52,680 --> 00:03:54,979
上下其实可以不用拉伸

90
00:03:54,979 --> 00:03:56,139
我们看一下啊

91
00:04:00,438 --> 00:04:03,560
上下应该也拉伸一点吧

92
00:04:13,159 --> 00:04:14,500
差不多就行了啊

93
00:04:14,500 --> 00:04:16,839
我们在这里没必要给他对那么齐啊

94
00:04:16,839 --> 00:04:18,220
我们没必要给它对那么齐

95
00:04:18,220 --> 00:04:19,750
我们这个就是做个例子啊

96
00:04:19,750 --> 00:04:23,379
呃真正如果如果你的游戏是用这种方式做的

97
00:04:23,379 --> 00:04:27,879
美工就会告诉你你这个位置应该摆在多少多少多少啊

98
00:04:27,879 --> 00:04:30,060
这个位置摆在多少多少是多少

99
00:04:30,079 --> 00:04:32,420
那么它呢会给你一个坐标啊

100
00:04:32,420 --> 00:04:34,160
你按照坐标去放就行了啊

101
00:04:34,160 --> 00:04:35,899
咱们这是自己在这儿拼接呢

102
00:04:35,899 --> 00:04:37,769
实际上这个方法不对是吧

103
00:04:37,769 --> 00:04:39,329
看很容易看错啊

104
00:04:39,329 --> 00:04:41,009
就是就算你现在看着合适

105
00:04:41,009 --> 00:04:44,399
很可能像素也会差上好几个像素啊

106
00:04:44,459 --> 00:04:46,079
那么对于对话框来说

107
00:04:46,079 --> 00:04:47,519
上面也是有内容的

108
00:04:47,519 --> 00:04:49,300
什么内容文本

109
00:04:51,180 --> 00:04:52,879
这个文本好大啊

110
00:04:52,879 --> 00:04:55,899
那首先这个我们给它取个名叫做title

111
00:04:57,420 --> 00:05:01,220
它是用来显示我们的这个名字的啊

112
00:05:01,220 --> 00:05:02,779
但是他的这个标题有点大

113
00:05:02,779 --> 00:05:04,779
我们小点

114
00:05:06,060 --> 00:05:08,639
然后把它放在这个位置

115
00:05:11,300 --> 00:05:13,060
别的效果我也就不给了

116
00:05:13,060 --> 00:05:14,680
颜色也就会默认的了啊

117
00:05:14,680 --> 00:05:18,310
这个大家可以自己去自己去调

118
00:05:18,310 --> 00:05:20,579
再来个lb放下面

119
00:05:22,319 --> 00:05:24,740
那么这个label的话

120
00:05:26,000 --> 00:05:29,740
首先咱们字体肯定要比这个名字小

121
00:05:29,740 --> 00:05:30,160
对不对

122
00:05:30,160 --> 00:05:31,389
所以我们来个15号

123
00:05:31,389 --> 00:05:33,160
然后我们要填充满这个

124
00:05:33,160 --> 00:05:35,500
但是现在大家发现不能填充

125
00:05:35,500 --> 00:05:40,559
是因为咱们这个呃模式没有给它进行一个设置啊

126
00:05:40,559 --> 00:05:41,899
我们设置一下

127
00:05:43,459 --> 00:05:47,300
你忘了的再回去看一下这个雷管

128
00:05:52,980 --> 00:05:56,779
如果你不设置它这个对这个level的大小啊

129
00:05:56,779 --> 00:05:58,500
永远贴着你的文字

130
00:05:58,800 --> 00:06:01,319
必须你在这里给它设置一下模式啊

131
00:06:01,319 --> 00:06:04,579
你才可以去拖拽整个这个level的一个大小

132
00:06:04,579 --> 00:06:09,019
但是这时候大家大家发现他说话的内容是从中间显示的

133
00:06:09,019 --> 00:06:10,370
也不是我们要的效果

134
00:06:10,370 --> 00:06:13,029
所以我们把它改成左对齐

135
00:06:13,029 --> 00:06:14,050
上对齐

136
00:06:14,050 --> 00:06:15,250
这样就对了

137
00:06:15,250 --> 00:06:17,050
就是谁谁谁说了什么话

138
00:06:17,050 --> 00:06:18,769
话的内容就从这里开始

139
00:06:18,769 --> 00:06:19,550
第一行

140
00:06:19,550 --> 00:06:20,029
第二行

141
00:06:20,029 --> 00:06:20,629
第三行

142
00:06:20,629 --> 00:06:21,529
第四行啊

143
00:06:21,529 --> 00:06:22,759
这种方式就对了

144
00:06:22,759 --> 00:06:25,519
然后我们这个label给它命名叫做

145
00:06:27,139 --> 00:06:29,168
content啊就ok了

146
00:06:29,168 --> 00:06:30,439
非常简单

147
00:06:30,439 --> 00:06:34,160
那么在这里我们这个人物的表情会动啊

148
00:06:34,160 --> 00:06:37,519
这个对话框里面的内容也会改变

149
00:06:37,899 --> 00:06:40,540
所以这为了方便我们编写脚本

150
00:06:41,980 --> 00:06:44,379
我们先先创建脚本的文件夹

151
00:06:45,180 --> 00:06:50,600
我们单独的给这个就是这这个角色啊

152
00:06:50,600 --> 00:06:51,980
给它添加一个脚本

153
00:06:51,980 --> 00:06:53,899
也给对话框添加一个脚本

154
00:06:53,899 --> 00:06:57,500
然后我们再添加一个主的脚本来控制他们俩啊

155
00:06:57,500 --> 00:06:58,220
就是人物角色

156
00:06:58,220 --> 00:06:59,629
我们给它一个脚本啊

157
00:06:59,629 --> 00:07:01,220
对话内容我们给它一个脚本

158
00:07:01,220 --> 00:07:03,949
最后整体我们用一个脚本来进行控制啊

159
00:07:03,949 --> 00:07:05,730
这样的话会好一些

160
00:07:05,730 --> 00:07:07,019
逻辑性会好一些

161
00:07:07,019 --> 00:07:09,709
那我们在这里创建一个脚本啊

162
00:07:09,709 --> 00:07:14,000
第一个脚本是给这个这个角色炮姐是吧

163
00:07:15,720 --> 00:07:17,899
那实际上在这里呃

164
00:07:17,899 --> 00:07:20,420
咱们是在这做的是表情的切换啊

165
00:07:20,420 --> 00:07:23,560
如果你呃你从网上找的素材

166
00:07:23,560 --> 00:07:26,500
大部分就是直接就是切换整个一个图片了啊

167
00:07:26,500 --> 00:07:33,220
那种方式就简单很多了啊哈那咱在这里脚本拖上来

168
00:07:33,220 --> 00:07:35,779
然后我们在这双击打开它

169
00:07:37,399 --> 00:07:38,879
改下名字

170
00:07:41,180 --> 00:07:42,220
对于他而言

171
00:07:42,220 --> 00:07:43,360
它里面有两个子节点

172
00:07:43,360 --> 00:07:44,620
一个是表情的

173
00:07:44,620 --> 00:07:47,639
一个是嗯嘴部表情的啊

174
00:07:47,639 --> 00:07:48,540
一个是脸上表情

175
00:07:48,540 --> 00:07:49,620
一个是嘴部表情的

176
00:07:49,620 --> 00:07:51,300
所以咱们在这里给他一个方法

177
00:07:51,300 --> 00:07:53,879
方法就是设置表情啊

178
00:07:53,879 --> 00:07:57,100
设置表情我们叫set in mage

179
00:07:58,379 --> 00:08:00,350
因为它里面就这一个方法

180
00:08:00,350 --> 00:08:03,949
然后我们给外界传来一个字符串啊

181
00:08:03,949 --> 00:08:05,750
是脸部的图片

182
00:08:05,750 --> 00:08:08,040
字符串和嘴部的图片

183
00:08:08,779 --> 00:08:11,139
它的名称字符串啊

184
00:08:12,459 --> 00:08:16,360
然后在这里我们加载素材

185
00:08:17,139 --> 00:08:22,649
就是外界传进来的是一个脸部的这个图片名称和一个嘴部的图片名称

186
00:08:22,649 --> 00:08:26,870
然后咱们就应该通过名称把这个图片加载上来显示出来

187
00:08:26,870 --> 00:08:28,639
那在这里我们一个一个加载

188
00:08:28,639 --> 00:08:29,620
首先

189
00:08:32,659 --> 00:08:37,798
首先我们face.cc.x pride frame

190
00:08:39,179 --> 00:08:42,019
我们先加载的是脸部的啊

191
00:08:42,019 --> 00:08:44,058
然后加载完了以后

192
00:08:49,720 --> 00:08:52,259
在这里this node点

193
00:08:52,259 --> 00:08:56,929
而我们的第一个子节点是不是就是第一个子节点啊

194
00:08:56,929 --> 00:08:59,600
就是我们脸部的这个子节点

195
00:08:59,600 --> 00:09:00,169
对不对

196
00:09:00,169 --> 00:09:02,149
第一个子节点就是脸部的子节点

197
00:09:02,149 --> 00:09:04,379
我们取得它的精灵组件

198
00:09:04,379 --> 00:09:06,000
给它赋值就行了

199
00:09:07,139 --> 00:09:08,419
非常简单

200
00:09:11,580 --> 00:09:13,070
精灵组件

201
00:09:13,070 --> 00:09:15,860
然后它的这个图片啊

202
00:09:15,860 --> 00:09:20,399
显示的图片我们就等于加载加载完成的这个图片啊

203
00:09:22,240 --> 00:09:25,958
然后第二个嘴部的也是一样的

204
00:09:33,980 --> 00:09:37,679
嘴部的这个ar sp一样的

205
00:09:44,899 --> 00:09:46,659
这是第二个子节点

206
00:09:46,659 --> 00:09:47,479
对不对

207
00:09:50,080 --> 00:09:53,120
啊那这时候突然想到大家可以看啊

208
00:09:53,120 --> 00:09:55,700
这行跟上面这行基本上是一样的

209
00:09:55,700 --> 00:09:57,259
我都是在这敲代码的

210
00:10:00,960 --> 00:10:04,860
很多同学一看比如说一样的跨一复制就粘贴了是吧

211
00:10:04,860 --> 00:10:05,639
觉得省事

212
00:10:11,620 --> 00:10:13,059
千万不要复制粘贴啊

213
00:10:13,059 --> 00:10:15,110
你能自己打就自己打啊

214
00:10:15,110 --> 00:10:16,730
甚至你这敲一遍代码不够

215
00:10:16,730 --> 00:10:17,990
你都要多敲两遍了

216
00:10:17,990 --> 00:10:19,129
对不对啊

217
00:10:19,129 --> 00:10:20,509
你更不要用复制粘贴了

218
00:10:20,509 --> 00:10:22,879
但是在以后写程序的过程当中

219
00:10:23,940 --> 00:10:26,240
能复制粘贴就可以复制粘贴

220
00:10:26,240 --> 00:10:27,679
那个就无所谓了啊

221
00:10:27,679 --> 00:10:28,700
而且还省事儿

222
00:10:28,700 --> 00:10:30,279
对不对哈哈

223
00:10:32,889 --> 00:10:34,000
千万不要来

224
00:10:35,259 --> 00:10:37,620
ok这是炮姐的这个脚本啊

225
00:10:37,620 --> 00:10:42,679
我们接下来给它创建第二个脚本来控制它的这个对话电话

226
00:10:42,679 --> 00:10:44,779
比如说我们叫做message

227
00:10:46,860 --> 00:10:48,779
message box对话框

228
00:10:50,519 --> 00:10:53,099
然后在这里我们创建第二个脚本

229
00:10:55,360 --> 00:11:00,039
message control把它加上来

230
00:11:02,919 --> 00:11:04,578
打开这个脚本

231
00:11:05,059 --> 00:11:07,159
那么在这里啊

232
00:11:07,159 --> 00:11:13,820
我们这个脚本跟那个脚本基本上可以说是基本上可以说是一样的啊

233
00:11:13,820 --> 00:11:15,219
首先改个名

234
00:11:16,299 --> 00:11:18,250
然后给他加一个方法

235
00:11:18,250 --> 00:11:19,929
方法是干嘛的

236
00:11:19,929 --> 00:11:25,480
刷新消息叫set message

237
00:11:27,159 --> 00:11:30,149
传进来一个说话的人物名称

238
00:11:30,149 --> 00:11:31,710
还有说话的内容

239
00:11:31,710 --> 00:11:32,279
对不对

240
00:11:32,279 --> 00:11:34,289
对话的名称和对话的内容

241
00:11:34,289 --> 00:11:36,929
然后这个就相当相对而言简单一些

242
00:11:36,929 --> 00:11:38,549
就不用去加载图片了啊

243
00:11:38,549 --> 00:11:42,600
你直接去呃得到我子节点上面的label

244
00:11:42,600 --> 00:11:44,970
然后给它去赋值这个string就可以了

245
00:11:44,970 --> 00:11:48,799
所以在这边取得第一个子节点

246
00:11:51,080 --> 00:11:56,458
第一个子节点就是咱们的这个名字叫什么对吧

247
00:11:57,179 --> 00:11:59,580
然后从它上面得到这个label组件

248
00:11:59,580 --> 00:12:02,039
然后string就等于我们的name

249
00:12:02,039 --> 00:12:06,220
第二个第二个子节点啊

250
00:12:06,220 --> 00:12:12,279
我们从它上面也是得到这个cc.n l l这个组件

251
00:12:14,139 --> 00:12:16,859
然后就等于一个content

252
00:12:19,759 --> 00:12:21,200
那么ok啊

253
00:12:21,200 --> 00:12:23,000
这个就完事了啊

254
00:12:23,000 --> 00:12:26,129
这个脚本非常简单非常简单就完事了

255
00:12:26,129 --> 00:12:28,649
那最后我们要写一个脚本

256
00:12:28,649 --> 00:12:31,230
这个脚本就是用来控制这两个脚本的啊

257
00:12:31,230 --> 00:12:33,029
就是用来控制这两个脚本的

258
00:12:33,029 --> 00:12:38,279
那我们来回来创建一个脚本

259
00:12:38,279 --> 00:12:42,019
这个脚本比如说我们现在这两个脚本啊

260
00:12:42,019 --> 00:12:44,509
这两个节点一个节点挂了一个脚本对吧

261
00:12:44,509 --> 00:12:46,370
这个脚本控制我们的表情

262
00:12:46,370 --> 00:12:49,000
这个脚本控制我们的消息

263
00:12:49,000 --> 00:12:53,289
那么最大的这个脚本我们比如说就放在这个背景上面来控制它们啊

264
00:12:53,289 --> 00:12:55,259
那我们在这里取个名字

265
00:12:55,340 --> 00:12:58,979
比如说就放到背景上面或者背景的

266
00:13:03,120 --> 00:13:06,559
然后我们打开它打开它

267
00:13:06,879 --> 00:13:09,039
然后在这边的话啊

268
00:13:09,039 --> 00:13:11,240
在这边的话嗯

269
00:13:11,539 --> 00:13:12,919
我们每一个消息

270
00:13:12,919 --> 00:13:15,440
因为我们想要消息是不是一条一条的

271
00:13:15,440 --> 00:13:20,330
所以实际上消息应该是以某种格式保存在一个文件里啊

272
00:13:20,330 --> 00:13:22,279
或者说你从网上去获取

273
00:13:22,279 --> 00:13:27,700
但是现在的话我们嗯就是没有学过数据啊

274
00:13:27,700 --> 00:13:30,639
所以我们在这里去处理这个呃

275
00:13:30,639 --> 00:13:31,299
对话的话

276
00:13:31,299 --> 00:13:32,740
我们就用这种方式

277
00:13:32,740 --> 00:13:34,899
那首先我们要一个类

278
00:13:35,159 --> 00:13:36,840
每每一个啊

279
00:13:36,840 --> 00:13:39,519
这个类的每个对象都代表一条消息

280
00:13:40,179 --> 00:13:43,139
那我们在这里直接去添加这个类啊

281
00:13:43,519 --> 00:13:45,799
你也可以去自己创建一个文件啊

282
00:13:45,799 --> 00:13:47,419
但是像这种没必要啊

283
00:13:47,419 --> 00:13:51,360
因为这个类估计就会在这一个文件里面用了啊

284
00:13:51,360 --> 00:13:53,279
所以你直接放到一块就行了

285
00:13:53,279 --> 00:13:54,899
一个文件放两类对吧

286
00:13:54,899 --> 00:13:56,000
没有问题啊

287
00:13:56,340 --> 00:13:58,950
在这里对于消息类而言

288
00:13:58,950 --> 00:14:01,950
每一个消息有对有说话人的名字

289
00:14:01,950 --> 00:14:03,960
说话人的内容

290
00:14:03,960 --> 00:14:06,389
有说话人的表情

291
00:14:06,389 --> 00:14:10,149
有说话人的嘴部啊

292
00:14:10,149 --> 00:14:12,429
当然你要是用的那种大图的话

293
00:14:12,429 --> 00:14:13,659
这两个就不用了

294
00:14:13,659 --> 00:14:16,659
你就可以直接来一个显示的大图片啊

295
00:14:16,659 --> 00:14:18,889
就是人物图片就ok了啊

296
00:14:18,889 --> 00:14:20,559
那就简单很多了

297
00:14:21,659 --> 00:14:25,559
那么每一个这个类的对象都是一条消息

298
00:14:25,559 --> 00:14:28,799
那咱们为了创建这个对象的时候

299
00:14:28,799 --> 00:14:33,799
方便我们在这里把它的构造方法写一下

300
00:14:33,940 --> 00:14:34,840
构造方法

301
00:14:34,840 --> 00:14:36,460
外界传进来四个内容

302
00:14:36,460 --> 00:14:38,500
刚好就是这四个内容啊

303
00:14:38,500 --> 00:14:39,698
名字

304
00:14:41,740 --> 00:14:43,500
对话的内容

305
00:14:46,399 --> 00:14:48,820
然后脸部图片的名称

306
00:14:48,820 --> 00:14:53,210
嘴部图片的名称这四个

307
00:14:53,210 --> 00:14:56,419
然后在里面我们就赋值就行了

308
00:15:01,019 --> 00:15:04,590
然后this.face就等于一个face

309
00:15:04,590 --> 00:15:07,839
this.mos就等于一个mos

310
00:15:08,899 --> 00:15:10,019
ok啊

311
00:15:10,620 --> 00:15:13,799
那么这样的话我们这个构造方法也有了

312
00:15:13,799 --> 00:15:16,769
那以后我们创建这个消息的时候啊

313
00:15:16,769 --> 00:15:18,698
就就方便很多了

314
00:15:18,820 --> 00:15:20,500
那接下来在这边

315
00:15:20,500 --> 00:15:23,200
首先我既然作为一个管理者啊

316
00:15:23,200 --> 00:15:25,299
那我这个类先把自己改个名

317
00:15:25,980 --> 00:15:27,120
我作为一个管理者

318
00:15:27,120 --> 00:15:28,919
所以所以我要拿到什么呀

319
00:15:28,919 --> 00:15:34,059
拿到这个人物和消息的控制器啊

320
00:15:34,059 --> 00:15:35,559
我肯定得能控制这两个

321
00:15:35,559 --> 00:15:36,438
对不对

322
00:15:36,919 --> 00:15:39,259
我这个啊我作为一个大的控制器

323
00:15:39,259 --> 00:15:41,840
我肯定能控制你们这两个脚本控制器

324
00:15:41,840 --> 00:15:43,339
所以在这里

325
00:15:45,600 --> 00:15:46,679
我们这样吧

326
00:15:46,679 --> 00:15:48,059
我们直接用另外一种方式

327
00:15:48,059 --> 00:15:49,019
我们拖拽一下

328
00:15:49,019 --> 00:15:50,240
关联一下

329
00:15:53,059 --> 00:15:57,099
创建一个属性是炮姐这个脚本控制器

330
00:16:00,059 --> 00:16:01,980
同样含一个脚本控制器

331
00:16:04,019 --> 00:16:05,639
其实这两个就是咱们刚才写的

332
00:16:05,639 --> 00:16:06,578
对不对

333
00:16:08,480 --> 00:16:10,339
message control

334
00:16:14,659 --> 00:16:19,129
等于那然后我们写完以后

335
00:16:19,129 --> 00:16:20,720
message啊

336
00:16:20,720 --> 00:16:21,200
写错了

337
00:16:21,200 --> 00:16:22,979
message control

338
00:16:23,480 --> 00:16:24,740
然后这边写完以后

339
00:16:24,740 --> 00:16:27,450
我们到这边背景

340
00:16:27,450 --> 00:16:28,919
这两个就可以关联了啊

341
00:16:28,919 --> 00:16:30,958
我们直接把这两个拖过来

342
00:16:32,820 --> 00:16:34,740
当然你也可以不用这种方式

343
00:16:34,740 --> 00:16:35,940
你也可以和刚才一样

344
00:16:35,940 --> 00:16:39,059
用这个父子节点的方式获取子节点

345
00:16:39,059 --> 00:16:41,559
然后得到里面的组件也是可以的

346
00:16:42,860 --> 00:16:45,200
我们只是什么都用一用啊

347
00:16:46,279 --> 00:16:48,820
那么在这里我们想啊

348
00:16:48,820 --> 00:16:51,639
一这个对话是一条消息接一个消息

349
00:16:51,639 --> 00:16:55,149
也就是说这个类产生的每一个对象都是一个消息

350
00:16:55,149 --> 00:16:59,419
所以我们要有一个这个对象的一个数组来保存我们所有的消息

351
00:16:59,419 --> 00:17:01,220
然后一个消息一个消息的去读

352
00:17:01,220 --> 00:17:01,820
对不对

353
00:17:01,820 --> 00:17:04,898
所以在这里我们要有一个消息数组

354
00:17:07,619 --> 00:17:08,980
message

355
00:17:15,259 --> 00:17:18,858
然后当前是第几条消息

356
00:17:21,140 --> 00:17:26,240
啊我们需要用一个索引来保存一下当前的消息啊

357
00:17:26,240 --> 00:17:27,898
条数是第几条

358
00:17:27,920 --> 00:17:29,930
然后在下面再start的这篇

359
00:17:29,930 --> 00:17:33,878
这边我们来出始化数组

360
00:17:34,319 --> 00:17:35,339
messages

361
00:17:35,339 --> 00:17:36,180
就等一个

362
00:17:36,180 --> 00:17:37,200
这是我们的数组

363
00:17:37,200 --> 00:17:38,119
对不对

364
00:17:39,660 --> 00:17:40,980
我们看一下

365
00:17:40,980 --> 00:17:43,259
找不到啊

366
00:17:43,259 --> 00:17:45,240
this.messages

367
00:17:48,720 --> 00:17:53,279
然后在这边我们每我们我们里面要放很多条消息

368
00:17:53,279 --> 00:17:56,609
每一条消息就是一个message的对象

369
00:17:56,609 --> 00:17:57,029
对不对

370
00:17:57,029 --> 00:17:58,109
所以我们就可以啊

371
00:17:58,109 --> 00:17:58,890
一条消息

372
00:17:59,970 --> 00:18:01,500
两条消息

373
00:18:02,119 --> 00:18:04,160
三条消息我们就可以这样去做了

374
00:18:04,160 --> 00:18:06,519
比如说我们就来三条消息啊

375
00:18:06,519 --> 00:18:08,740
三个消息就是三个message的对象

376
00:18:08,740 --> 00:18:09,339
对不对

377
00:18:09,339 --> 00:18:10,900
每个对象我们在创建的时候

378
00:18:10,900 --> 00:18:13,309
是不是要给他传这四个内容

379
00:18:13,309 --> 00:18:14,809
那我们在这里来传一下

380
00:18:14,809 --> 00:18:17,509
比如说第一个是谁说话啊

381
00:18:17,509 --> 00:18:19,190
炮姐叫御坂美琴

382
00:18:19,190 --> 00:18:22,170
是不是啊

383
00:18:22,170 --> 00:18:23,039
说了什么话

384
00:18:23,039 --> 00:18:25,109
比如说今天天气不错

385
00:18:25,109 --> 00:18:26,919
我们这随便写的啊

386
00:18:26,980 --> 00:18:30,640
然后他呢显示哪个脸部表情和嘴部表情

387
00:18:30,640 --> 00:18:35,559
能看一下脸部表情和嘴部表情

388
00:18:37,200 --> 00:18:40,799
我们这儿比如说给他随便换一个啊

389
00:18:41,420 --> 00:18:43,099
第二个是嘴部表情

390
00:18:43,099 --> 00:18:45,659
嘴部表情我刚才看了一下

391
00:18:46,519 --> 00:18:47,779
这就是嘴部表情

392
00:18:48,859 --> 00:18:50,599
然后我们在这里啊

393
00:18:50,599 --> 00:18:54,059
就是可以去播放不同的脸部表情和嘴部表情

394
00:18:54,059 --> 00:18:56,039
比如说每个都是两个表情是吧

395
00:18:56,039 --> 00:18:59,279
你现在想让它播放哪个表情啊

396
00:18:59,279 --> 00:19:02,849
你就在这里啊填上谁的名称就可以了

397
00:19:02,849 --> 00:19:08,079
然后比如说你看这是他说的第一句话

398
00:19:08,079 --> 00:19:09,160
然后我们第二句话

399
00:19:09,160 --> 00:19:10,059
第三句话

400
00:19:10,059 --> 00:19:12,059
今天天气不错是吧

401
00:19:14,140 --> 00:19:16,099
来喝点饮料

402
00:19:18,460 --> 00:19:21,460
然后比如说这个背景是个贩卖机是吧

403
00:19:21,460 --> 00:19:25,200
可惜贩卖机又坏了

404
00:19:28,319 --> 00:19:28,980
对不对

405
00:19:28,980 --> 00:19:30,240
然后在这里每次说话

406
00:19:30,240 --> 00:19:33,319
比如说我们都让他变一下表情

407
00:19:35,119 --> 00:19:38,059
当然你这个可以自己去定义他的表情啊

408
00:19:38,059 --> 00:19:39,740
这个我在这就随便了

409
00:19:39,839 --> 00:19:40,680
然后注意啊

410
00:19:40,680 --> 00:19:43,019
也就是说默认我们一共有这三句话啊

411
00:19:43,019 --> 00:19:45,779
但是我们目前一句话还没有去说

412
00:19:45,779 --> 00:19:49,380
那么这种游戏啊往往在没有说的时候

413
00:19:49,380 --> 00:19:51,500
比如说这句话你还没有说

414
00:19:51,880 --> 00:19:53,380
没说话的时候

415
00:19:53,380 --> 00:19:56,279
这个界面我们是隐藏的啊

416
00:19:56,279 --> 00:19:57,299
一说话它才出来

417
00:19:57,299 --> 00:19:57,720
对不对

418
00:19:57,720 --> 00:19:59,220
也就是说默认这个是隐藏的

419
00:19:59,220 --> 00:20:00,599
我们给它隐藏掉

420
00:20:00,599 --> 00:20:02,099
开始说话了

421
00:20:02,099 --> 00:20:03,660
然后我们再让它显示出来

422
00:20:03,660 --> 00:20:08,159
那我们在这里鼠标点击兑换

423
00:20:09,059 --> 00:20:10,200
鼠标点击兑换的话

424
00:20:10,200 --> 00:20:11,400
我们就要用到事件了

425
00:20:11,400 --> 00:20:12,279
对不对

426
00:20:14,359 --> 00:20:17,539
那在这里我们c事件啊

427
00:20:17,539 --> 00:20:21,960
我们用的就是c.node点英文tape.must down

428
00:20:22,220 --> 00:20:23,779
我们按鼠标的这个事件

429
00:20:23,779 --> 00:20:24,799
对不对

430
00:20:25,880 --> 00:20:30,079
然后我们事件的回调我们在里面写啊

431
00:20:30,079 --> 00:20:31,539
我们在里面去写

432
00:20:31,779 --> 00:20:35,500
首先我们要要要每一次啊点一下鼠标

433
00:20:35,500 --> 00:20:37,119
都从里面去加载一条消息

434
00:20:37,119 --> 00:20:38,079
对不对

435
00:20:38,359 --> 00:20:39,680
那么加载第几条

436
00:20:39,680 --> 00:20:42,440
我们就用这个索引啊来去计算

437
00:20:42,440 --> 00:20:44,180
那么在这里要做一个判断

438
00:20:44,180 --> 00:20:45,140
什么判断

439
00:20:45,140 --> 00:20:47,059
如果这个索引

440
00:20:48,740 --> 00:20:52,309
小于等于这个数组的长度

441
00:20:52,309 --> 00:20:55,609
我们才允许他去取消息啊

442
00:20:55,609 --> 00:20:56,660
小于它的长度

443
00:20:56,660 --> 00:20:58,519
因为如果他你看这个消息

444
00:20:58,519 --> 00:21:00,730
比如说他已经到了3万

445
00:21:00,730 --> 00:21:02,230
你再从数组里面去取

446
00:21:02,230 --> 00:21:03,369
是不是直接越界了

447
00:21:03,369 --> 00:21:05,349
他直接012就没有三

448
00:21:05,349 --> 00:21:06,490
你在这里要取三

449
00:21:06,490 --> 00:21:07,869
所以就会导致越界啊

450
00:21:07,869 --> 00:21:11,009
所以我们千万不敢那样在这里做好判断了

451
00:21:11,009 --> 00:21:13,319
然后在这里再做一件事

452
00:21:13,579 --> 00:21:17,670
如果对话面板没显示

453
00:21:17,670 --> 00:21:19,859
那我们给它显示出来就行了

454
00:21:19,859 --> 00:21:21,630
那在这里怎么判断呢

455
00:21:21,630 --> 00:21:26,170
就是如果因为对话面板是不是就是这个脚本所在的面板

456
00:21:26,170 --> 00:21:32,500
所以我们在这里this.message control它的节点的active啊

457
00:21:32,500 --> 00:21:34,210
如果为false

458
00:21:34,210 --> 00:21:36,309
我们就让他做一件事

459
00:21:36,309 --> 00:21:38,299
就让它为

460
00:21:39,839 --> 00:21:41,099
true啊就可以了

461
00:21:41,099 --> 00:21:42,630
就把对话面板显示出来

462
00:21:42,630 --> 00:21:45,299
然后再读消息

463
00:21:47,759 --> 00:21:51,650
读消息的话就是每一次我们读一个消息

464
00:21:51,650 --> 00:21:53,329
然后创建一个临时变量是吧

465
00:21:53,329 --> 00:21:54,230
每次拿一个消息

466
00:21:54,230 --> 00:21:58,319
消息就是从数组里面this.message control

467
00:21:58,799 --> 00:22:01,839
热点index啊

468
00:22:01,839 --> 00:22:03,460
就是这个消息读完以后

469
00:22:03,460 --> 00:22:04,509
我们让它自增

470
00:22:04,509 --> 00:22:05,140
对不对

471
00:22:05,140 --> 00:22:06,609
每次读完消息后

472
00:22:06,609 --> 00:22:08,500
他就进行一个自增

473
00:22:09,420 --> 00:22:12,359
然后我们再怎样显示消息

474
00:22:12,359 --> 00:22:14,009
显示消息就简单了

475
00:22:14,009 --> 00:22:16,559
因为这两个脚本我们都是写完现成的

476
00:22:16,559 --> 00:22:19,220
所以我们可以直接图片

477
00:22:24,779 --> 00:22:26,919
message.face

478
00:22:27,500 --> 00:22:31,700
然后message.mouse

479
00:22:32,859 --> 00:22:34,460
然后第二个

480
00:22:36,200 --> 00:22:37,609
message control

481
00:22:37,609 --> 00:22:38,119
对不对

482
00:22:38,119 --> 00:22:45,400
点上set message就是message.name

483
00:22:45,400 --> 00:22:49,680
然后还有一个message.content

484
00:22:51,299 --> 00:22:52,170
对不对

485
00:22:52,170 --> 00:22:54,779
这个就是我们的一个消息啊

486
00:22:54,779 --> 00:22:57,039
就是你拿到消息后啊

487
00:22:57,039 --> 00:23:01,980
去把这个消息的这个内容啊全给了我们这两个脚本就ok了

488
00:23:04,400 --> 00:23:09,650
那么这样的话大家可以看出来这个结果就是算是比较好的结构啊

489
00:23:09,650 --> 00:23:11,930
虽然我们做了很简单的一个事情

490
00:23:11,930 --> 00:23:15,500
但是很多同学在做这种事情的时候啊

491
00:23:15,500 --> 00:23:16,940
就是在我没有去讲的时候

492
00:23:16,940 --> 00:23:17,599
他会怎样

493
00:23:17,599 --> 00:23:19,289
他直接写一个脚本

494
00:23:19,289 --> 00:23:21,210
在这个脚本里面做了所有的事情

495
00:23:21,210 --> 00:23:23,700
所以最后这个脚本会显得非常乱啊

496
00:23:23,700 --> 00:23:28,759
那么大家可以看一下我们目前做的这个内容其实很简单啊

497
00:23:28,759 --> 00:23:29,960
只是读个消息

498
00:23:29,960 --> 00:23:31,369
但是即便如此

499
00:23:31,369 --> 00:23:33,650
我们也把脚本分为三组

500
00:23:33,650 --> 00:23:36,630
两组两个脚本是做功能的啊

501
00:23:36,630 --> 00:23:39,240
功能性的这两个脚本啊

502
00:23:39,240 --> 00:23:42,660
我们是你看它本身只是有有这个功能啊

503
00:23:42,660 --> 00:23:45,109
但是它本身并没有去掉这个功能

504
00:23:45,109 --> 00:23:46,670
谁去使用这个功能呢

505
00:23:46,670 --> 00:23:48,710
最后这个大的脚本啊

506
00:23:48,710 --> 00:23:50,869
它是用来处理逻辑的啊

507
00:23:50,869 --> 00:23:53,559
所以在它里面来做了所有的这个事情

508
00:23:53,900 --> 00:23:54,799
那ok啊

509
00:23:54,799 --> 00:23:56,799
我们现在来运行一下

510
00:23:58,720 --> 00:24:00,460
把这个f p s关了

511
00:24:00,460 --> 00:24:02,200
那么大家可以看一下啊

512
00:24:02,200 --> 00:24:04,779
这个我们已经显示出来了

513
00:24:04,779 --> 00:24:07,500
然后我们在这里就可以点击一下

514
00:24:16,559 --> 00:24:17,819
内容没有发生改变

515
00:24:17,819 --> 00:24:19,680
也就是说这边还是有问题的

516
00:24:19,680 --> 00:24:21,720
我们来看一下哪里有问题啊

517
00:24:29,180 --> 00:24:33,619
我们一点点去检查这两个脚本

518
00:24:33,619 --> 00:24:36,890
首先应该是没有问题啊

519
00:24:36,890 --> 00:24:39,019
关联的都是没有问题的

520
00:24:39,680 --> 00:24:43,740
运行完我看一下是哪里的问题呢

521
00:24:46,400 --> 00:24:49,059
脸部表情也没有发生变化

522
00:25:00,779 --> 00:25:02,700
呃这边应该是没问题

523
00:25:02,700 --> 00:25:05,099
这边这个逻辑我们应该是没有问题

524
00:25:05,099 --> 00:25:07,519
this.index应该是零

525
00:25:13,619 --> 00:25:14,519
读消息

526
00:25:15,960 --> 00:25:18,339
读了消息哪里面的内容

527
00:25:19,740 --> 00:25:21,099
message

528
00:25:31,779 --> 00:25:32,920
我们来检查一下

529
00:25:32,920 --> 00:25:33,579
检查一下

530
00:25:35,140 --> 00:25:36,640
我们现在来运行一下啊

531
00:25:36,640 --> 00:25:38,180
我们来运行一下

532
00:25:39,460 --> 00:25:42,269
看一下他的输出点一下啊

533
00:25:42,269 --> 00:25:44,190
大家可以看到这里会报错

534
00:25:44,190 --> 00:25:47,220
它告诉我们不能读取face属性

535
00:25:47,220 --> 00:25:50,180
然后在50行pg control 50行里面

536
00:25:50,180 --> 00:25:51,529
所以我们来检查一下

537
00:25:51,529 --> 00:25:53,599
那么大家一定要会通过这种

538
00:25:53,599 --> 00:25:56,059
就是你如果比如说哪里没有正常执行

539
00:25:56,059 --> 00:25:58,420
你要看一下他有没有输出错误啊

540
00:25:58,420 --> 00:25:59,589
有错有错误的话

541
00:25:59,589 --> 00:26:02,619
按照错误去进行这个检查啊

542
00:26:02,619 --> 00:26:06,460
咱们很多同学这个有时候比如说问我问题啊

543
00:26:06,460 --> 00:26:08,349
他私聊问我一个问题

544
00:26:08,349 --> 00:26:09,670
他告诉我哎

545
00:26:09,670 --> 00:26:12,220
比如说这个就是啊这个东西不显示

546
00:26:12,220 --> 00:26:13,500
为什么

547
00:26:14,039 --> 00:26:15,119
那这个东西问我

548
00:26:15,119 --> 00:26:16,859
我肯定也不能一下

549
00:26:16,859 --> 00:26:18,059
我就知道这是为什么

550
00:26:18,059 --> 00:26:19,829
因为导致的原因可能太多

551
00:26:19,829 --> 00:26:22,980
你必须看一下系统给你的提示对吧

552
00:26:22,980 --> 00:26:24,059
这个提示的很明显

553
00:26:24,059 --> 00:26:25,240
我们看一下啊

554
00:26:25,880 --> 00:26:27,859
50行不能读取face

555
00:26:27,859 --> 00:26:28,759
不能读取face

556
00:26:28,759 --> 00:26:30,859
就证明message有问题

557
00:26:30,859 --> 00:26:32,740
message为什么有问题

558
00:26:33,299 --> 00:26:35,700
好我们这个数组我们这儿写错了

559
00:26:35,700 --> 00:26:36,839
我们从数组里面读

560
00:26:36,839 --> 00:26:38,720
应该是message

561
00:26:38,759 --> 00:26:40,500
从数组里面拿东西啊

562
00:26:40,500 --> 00:26:42,569
因为他这边this点

563
00:26:42,569 --> 00:26:45,960
然后他这边自己去给你去呃

564
00:26:45,960 --> 00:26:47,190
提示出后面的内容

565
00:26:47,190 --> 00:26:51,079
然后提示这个呢它提示出这个因为开头一样啊

566
00:26:52,119 --> 00:26:53,440
所以这里就有问题了

567
00:26:53,440 --> 00:26:55,878
那这次我们再运行看一下效果

568
00:26:59,900 --> 00:27:02,599
就说诶这个人我们在这点一下

569
00:27:02,599 --> 00:27:04,519
你看表情变了吧

570
00:27:04,519 --> 00:27:05,750
今天天气不错

571
00:27:05,750 --> 00:27:07,049
再点一下

572
00:27:07,049 --> 00:27:07,829
来喝点饮料

573
00:27:07,829 --> 00:27:09,559
你看表情

574
00:27:10,000 --> 00:27:11,980
可惜贩卖机又坏了对吧

575
00:27:11,980 --> 00:27:13,359
所以他就每说一句话

576
00:27:13,359 --> 00:27:15,579
脸上那个表情就会有一个变化

577
00:27:15,579 --> 00:27:18,220
这种方式是最节省内存的啊

578
00:27:18,220 --> 00:27:19,240
整体而言

579
00:27:19,240 --> 00:27:21,940
而且你就是用这种方式去做你的游戏

580
00:27:21,940 --> 00:27:24,019
可能容量也会小很多

581
00:27:25,160 --> 00:27:27,940
ok啊那么这就是这节课的内容啊

582
00:27:31,539 --> 00:27:33,279
不要照着我的来写啊

583
00:27:33,279 --> 00:27:36,059
你可以从网上自己去找一下嗯

584
00:27:36,059 --> 00:27:38,819
这种这种游戏的图片啊

585
00:27:38,819 --> 00:27:40,059
人物啊

586
00:27:40,119 --> 00:27:43,359
然后你自己可以来创建一个新的工程啊

587
00:27:43,359 --> 00:27:44,740
然后自己来写一遍啊

588
00:27:44,740 --> 00:27:47,689
你自己能写出来的话就没什么问题了啊

589
00:27:47,689 --> 00:27:48,828
当然你要不懂的话

590
00:27:48,828 --> 00:27:50,269
你先跟着我的来啊

591
00:27:50,269 --> 00:27:51,259
跟着我的来

592
00:27:51,259 --> 00:27:52,729
感觉差不太多了

593
00:27:52,729 --> 00:27:56,059
然后你最好自己再去写一下啊

594
00:27:56,900 --> 00:27:59,599
ok这节课就这么多


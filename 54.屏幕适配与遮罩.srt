1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,880 --> 00:00:13,169
ok这节课咱们来说一下屏幕适配和遮罩

3
00:00:13,169 --> 00:00:15,689
那么我们先从遮罩说起

4
00:00:15,689 --> 00:00:17,420
什么是遮罩啊

5
00:00:17,440 --> 00:00:20,530
那么大家有这个qq或者微信

6
00:00:20,530 --> 00:00:24,460
那么它里面的头像是不是一个圆形的头像

7
00:00:24,480 --> 00:00:29,100
但是大家在上传头像的时候给的是什么样的头像啊

8
00:00:29,100 --> 00:00:31,320
那大部分我们给的肯定是一个方形的

9
00:00:31,320 --> 00:00:32,039
对不对

10
00:00:32,060 --> 00:00:34,460
但是我们方形的头像传上去以后

11
00:00:34,460 --> 00:00:38,140
最终它显示的只是这个头像的圆形部分啊

12
00:00:38,140 --> 00:00:40,100
就他给你截了一个圆形

13
00:00:40,100 --> 00:00:42,439
然后圆形外面的部分就不会显示了

14
00:00:42,439 --> 00:00:43,729
四个角就不显示了

15
00:00:43,729 --> 00:00:46,520
只会给你显示这个圆形内部的东西

16
00:00:46,520 --> 00:00:49,740
那么这种东西就叫遮罩啊

17
00:00:49,740 --> 00:00:51,659
那么遮罩有圆形的

18
00:00:51,659 --> 00:00:53,039
当然也有方形的啊

19
00:00:53,039 --> 00:00:54,630
那么这些都是属于遮罩

20
00:00:54,630 --> 00:00:57,759
我们在这边来看一下遮罩是怎么回事啊

21
00:00:57,780 --> 00:01:03,039
首先我们这里比如说创建一个空的节点啊

22
00:01:03,039 --> 00:01:03,850
放在这里

23
00:01:03,850 --> 00:01:05,739
比如说我们给他一点宽高

24
00:01:05,739 --> 00:01:07,920
比如说是100x100

25
00:01:08,140 --> 00:01:11,840
那么这个节点是一个空的节点啊

26
00:01:11,840 --> 00:01:12,980
只是有一个框框

27
00:01:12,980 --> 00:01:13,519
对不对

28
00:01:13,519 --> 00:01:16,299
宽高是100x100的这样的一个节点

29
00:01:16,299 --> 00:01:20,420
那么在这里我们比如说给它推一个子物体上来

30
00:01:21,340 --> 00:01:23,920
比如说是这个小鸟小鸟

31
00:01:23,920 --> 00:01:27,739
比如说是200x200啊

32
00:01:27,739 --> 00:01:28,819
这样的一个小鸟

33
00:01:28,819 --> 00:01:30,500
那么这时候大家注意啊

34
00:01:30,500 --> 00:01:33,959
这个小鸟你看小鸟是这么大

35
00:01:33,959 --> 00:01:34,980
它的父物

36
00:01:34,980 --> 00:01:35,640
体是这么大

37
00:01:35,640 --> 00:01:37,019
对不对

38
00:01:37,019 --> 00:01:40,689
那么我们在这里就做一个操作

39
00:01:40,689 --> 00:01:42,430
让它通过遮罩

40
00:01:42,430 --> 00:01:45,819
然后只显示这个蓝色方块里面的内容啊

41
00:01:45,819 --> 00:01:46,939
外面的我不要了

42
00:01:46,959 --> 00:01:48,159
怎么做啊

43
00:01:48,159 --> 00:01:48,760
很简单

44
00:01:48,760 --> 00:01:52,120
在这里选中这个方块啊

45
00:01:52,799 --> 00:01:54,870
然后我们添加组件

46
00:01:54,870 --> 00:01:58,469
在这里面呢大家去找一个渲染组件

47
00:01:58,469 --> 00:02:01,019
渲染组件里面有一个mask

48
00:02:02,120 --> 00:02:04,579
找到这个mask啊

49
00:02:04,579 --> 00:02:05,420
找到mask

50
00:02:05,420 --> 00:02:08,139
然后选中它

51
00:02:08,340 --> 00:02:16,379
那么这时候大家就可以看是不是哎他就被我们这边呃这绕起来了啊

52
00:02:16,379 --> 00:02:19,199
就是我们只能看到我们这个方块里面的内容

53
00:02:19,199 --> 00:02:21,259
外面的就就看不到了

54
00:02:21,280 --> 00:02:23,659
当然这个只对父子节点有效啊

55
00:02:23,680 --> 00:02:26,080
就是父节点是这个小方块

56
00:02:26,080 --> 00:02:30,039
它能把它的子节点啊给遮罩在复节点的里面

57
00:02:30,039 --> 00:02:32,879
那么遮罩里面呃

58
00:02:32,879 --> 00:02:33,599
我们看一下

59
00:02:33,599 --> 00:02:34,949
还有一个反向遮罩

60
00:02:34,949 --> 00:02:37,560
反向遮罩就是刚好相反掏空了

61
00:02:37,560 --> 00:02:38,099
对不对

62
00:02:38,099 --> 00:02:40,780
就我内部的东西不显示啊

63
00:02:40,780 --> 00:02:42,159
我内部东西不显示

64
00:02:42,159 --> 00:02:44,319
然后在这里有一个形状

65
00:02:44,319 --> 00:02:46,360
形状的话呃

66
00:02:46,360 --> 00:02:47,199
三种

67
00:02:47,199 --> 00:02:49,180
一种是使用精灵的啊

68
00:02:49,180 --> 00:02:49,840
就是第三种

69
00:02:49,840 --> 00:02:51,550
这里我们要给它一个精灵

70
00:02:51,550 --> 00:02:54,180
按照这个精灵的样式去减啊

71
00:02:54,199 --> 00:02:55,460
我们不用那种

72
00:02:55,460 --> 00:02:58,759
一般就是前两种方块或者是圆形

73
00:02:59,159 --> 00:03:00,900
你看要是我们选了圆形

74
00:03:00,900 --> 00:03:03,060
是不是就变成头像的样式了啊

75
00:03:03,060 --> 00:03:08,580
就是你不管放上来什么样的图片都是圆形的去显示啊

76
00:03:09,080 --> 00:03:11,180
那么这个就是遮罩很简单

77
00:03:11,180 --> 00:03:12,500
它在我们的渲染组件里

78
00:03:12,500 --> 00:03:14,150
渲染组件还有很多东西啊

79
00:03:14,150 --> 00:03:16,340
比如说你看咱们的这个label

80
00:03:16,360 --> 00:03:19,280
还有leo的这个呃

81
00:03:19,280 --> 00:03:20,870
这个这个这个什么呀

82
00:03:20,870 --> 00:03:21,800
描边对不对

83
00:03:21,800 --> 00:03:23,569
咱们之前描边也做过

84
00:03:23,569 --> 00:03:26,620
咱们用的是这个富文本啊

85
00:03:26,620 --> 00:03:28,719
然后用他的标签来做的描边

86
00:03:28,719 --> 00:03:30,819
那实际上我们创建一个level

87
00:03:30,819 --> 00:03:32,139
再加上这样一个组件

88
00:03:32,139 --> 00:03:34,599
它就能自己带的描边效果哈哈

89
00:03:34,620 --> 00:03:35,939
所以这里面有很多组件

90
00:03:35,939 --> 00:03:36,780
大家愿意的话

91
00:03:36,780 --> 00:03:38,900
可以自己加上来看一看啊

92
00:03:40,560 --> 00:03:43,169
那我们给它remove掉

93
00:03:43,169 --> 00:03:44,740
这个就是遮罩

94
00:03:45,659 --> 00:03:47,909
然后我们现在来说第二个啊

95
00:03:47,909 --> 00:03:50,500
我们把这个小鸟拖出来好了

96
00:03:52,939 --> 00:03:56,080
那么首先现在这个是不是我们的屏幕啊

97
00:03:56,139 --> 00:03:58,120
这个紫色方块是我们屏幕

98
00:03:58,120 --> 00:03:59,319
所以我们运行完以后

99
00:03:59,319 --> 00:04:01,479
它会出现在我们屏幕的右上角

100
00:04:01,479 --> 00:04:02,530
没有问题吧

101
00:04:02,530 --> 00:04:07,569
但是这时候如果我们哎用的是这个

102
00:04:07,569 --> 00:04:10,139
我们看一下啊

103
00:04:13,099 --> 00:04:16,110
嗯它的还都能出现啊

104
00:04:16,110 --> 00:04:18,509
如果我们现在把屏幕进行修改

105
00:04:18,509 --> 00:04:20,879
然后我们去看一下这个小鸟的位置

106
00:04:20,879 --> 00:04:23,399
大家发现是不是位置发生了一些变化

107
00:04:23,399 --> 00:04:26,300
比如说你看我在这个默认情况下

108
00:04:26,300 --> 00:04:29,300
距离上面和距离右边都是这么一点距离

109
00:04:29,300 --> 00:04:30,860
而且都是一样的距离

110
00:04:30,860 --> 00:04:32,540
但是比如说我换成pad以后

111
00:04:32,540 --> 00:04:36,379
大家就发现右边这个距离明显就增多增多了

112
00:04:36,379 --> 00:04:37,699
换成苹果以后

113
00:04:37,699 --> 00:04:39,800
上面的距离明显就增多了

114
00:04:39,800 --> 00:04:44,160
那这样的话我们就会有这样一个问题

115
00:04:44,160 --> 00:04:46,860
如果我们做一套ui啊

116
00:04:46,860 --> 00:04:48,899
当然这里提到一点啊

117
00:04:48,899 --> 00:04:50,699
如果我们正常做ui的情况下

118
00:04:50,699 --> 00:04:52,920
你ui尽量放到canvas下面啊

119
00:04:52,920 --> 00:04:55,879
它是以因为covs是屏幕啊

120
00:04:55,879 --> 00:04:58,040
我们以屏幕作为副节点啊

121
00:04:58,040 --> 00:05:01,199
这样的话才是一个正常的一个样式

122
00:05:01,220 --> 00:05:05,779
你这个所以建议大家所有的ui啊都放在这个canvas下面

123
00:05:05,779 --> 00:05:08,600
咱们在这个讲讲课的时候啊

124
00:05:08,600 --> 00:05:12,600
有时候那就没必要这么准放到这个下面啊

125
00:05:12,600 --> 00:05:14,220
所以我们就可能拖到外面了

126
00:05:14,220 --> 00:05:16,170
但是尽量放到卡姆斯下面

127
00:05:16,170 --> 00:05:17,860
然后这时候再运行

128
00:05:17,860 --> 00:05:23,000
哎这时候啊我们再看它的效果啊

129
00:05:23,000 --> 00:05:25,339
那效果跟刚才还是差不太多是吧

130
00:05:25,339 --> 00:05:26,300
还是差不太多

131
00:05:26,300 --> 00:05:28,360
那我们在这里要说什么

132
00:05:29,019 --> 00:05:31,120
而现在我有副节点了

133
00:05:31,120 --> 00:05:32,500
我副节点是canvas

134
00:05:32,500 --> 00:05:33,970
也就是整个屏幕啊

135
00:05:33,970 --> 00:05:36,730
我子节点就是这个那么子节点

136
00:05:36,730 --> 00:05:37,600
比如说大家想啊

137
00:05:37,600 --> 00:05:41,180
我就想往右右上方放这样的一个小鸟啊

138
00:05:41,180 --> 00:05:43,639
就这个游戏不管在什么屏幕上运行

139
00:05:43,639 --> 00:05:45,709
它的右上方永远有这样一个小鸟

140
00:05:45,709 --> 00:05:50,220
因为这个小鸟可能比如说在游戏里面代表的就是一个小地图啊

141
00:05:50,220 --> 00:05:53,009
就比如小地图我可能就要放到右上角

142
00:05:53,009 --> 00:05:55,379
比如说人物头像我就要放到左上角

143
00:05:55,379 --> 00:05:59,680
但是我们发现我们在这里是放到右上角了

144
00:05:59,680 --> 00:06:01,600
但是一旦运行完以后

145
00:06:01,600 --> 00:06:03,759
它不一定紧紧的放在右上角

146
00:06:03,759 --> 00:06:05,740
因为它的设备是可能不同的

147
00:06:05,740 --> 00:06:06,500
对不对

148
00:06:06,500 --> 00:06:08,029
那这个东西怎么办

149
00:06:08,029 --> 00:06:09,920
这时候我们就要做屏幕适配了

150
00:06:09,920 --> 00:06:11,000
适配怎么做

151
00:06:11,019 --> 00:06:14,860
我们想对谁做屏幕适配就要给谁加一个组件

152
00:06:14,860 --> 00:06:16,600
这个组件叫什么

153
00:06:18,699 --> 00:06:21,740
这个这个组件选择ui组件

154
00:06:21,740 --> 00:06:23,420
我们选择最后一项

155
00:06:23,420 --> 00:06:25,790
还刚好在ui组件的最后一项

156
00:06:25,790 --> 00:06:29,689
这一项我们就是用来做屏幕适配的啊

157
00:06:29,689 --> 00:06:32,279
大家发现我们把它加上来以后

158
00:06:32,399 --> 00:06:34,139
上下左右啊

159
00:06:34,139 --> 00:06:36,019
就出来这样的一个图了

160
00:06:36,160 --> 00:06:37,959
那么这个组件啊

161
00:06:37,959 --> 00:06:43,519
wget这个容器组件到底是怎样给我们去做这个屏幕适配的

162
00:06:43,519 --> 00:06:44,800
非常简单

163
00:06:44,839 --> 00:06:48,199
首先它这个图有两个方块儿

164
00:06:48,199 --> 00:06:50,060
大方块表示我的父节点

165
00:06:50,060 --> 00:06:52,579
小方块表示我的当前这个节点

166
00:06:52,579 --> 00:06:54,420
所以我们要放到canvas下面

167
00:06:54,439 --> 00:06:59,930
这样的话canvas在这里就是整个屏幕是不是就是这个副节点了啊

168
00:06:59,930 --> 00:07:03,439
也就是说现在最外层就是我们的屏幕啊

169
00:07:03,439 --> 00:07:05,360
因为它的父节点就是屏幕啊

170
00:07:05,360 --> 00:07:07,000
所以最外层就是我们的屏幕

171
00:07:07,000 --> 00:07:09,540
而中间这个就是我们这个小鸟本身

172
00:07:09,540 --> 00:07:12,180
那么他们的关系现在是没有固定的

173
00:07:12,180 --> 00:07:13,860
所以会导致我们那样的情况

174
00:07:13,860 --> 00:07:15,180
就是不同屏幕啊

175
00:07:15,180 --> 00:07:16,899
有不同的显示方式

176
00:07:16,899 --> 00:07:18,220
怎样固定起来

177
00:07:18,220 --> 00:07:19,360
很简单

178
00:07:19,360 --> 00:07:21,680
当我们选中右边以后

179
00:07:21,699 --> 00:07:23,439
那么它这里会有一个距离

180
00:07:23,439 --> 00:07:27,639
就是当前我这个子节点和复节点右边的一个边距

181
00:07:27,639 --> 00:07:28,660
现在是个四

182
00:07:28,660 --> 00:07:30,879
就证明没有紧紧的贴合在一起

183
00:07:30,879 --> 00:07:32,980
你看还差这么四个像素

184
00:07:32,980 --> 00:07:34,259
对不对啊

185
00:07:34,259 --> 00:07:36,060
这个如果你希望仅仅贴合的话

186
00:07:36,060 --> 00:07:37,259
你可以给它改成零

187
00:07:37,259 --> 00:07:40,500
这样的话它就会紧紧的右边和负节点

188
00:07:40,500 --> 00:07:42,040
的右边贴合在一起

189
00:07:43,139 --> 00:07:46,170
这样的话就代表不管屏幕怎样变

190
00:07:46,170 --> 00:07:48,779
我和我的右边永远贴合

191
00:07:48,779 --> 00:07:50,500
我们运行看一下效果

192
00:07:51,480 --> 00:07:53,699
ok我们看这是正常的效果

193
00:07:53,699 --> 00:07:54,720
然后我们换设备

194
00:07:54,720 --> 00:07:57,779
我们发现它并没有贴合起来

195
00:07:58,660 --> 00:08:01,980
那么诶这个是怎么回事啊

196
00:08:02,819 --> 00:08:03,839
我们看一下

197
00:08:03,839 --> 00:08:05,759
比如说换成pad pad以后

198
00:08:05,759 --> 00:08:08,000
他这边还是会有空隙的

199
00:08:08,000 --> 00:08:09,860
我们明明让他贴到右边了啊

200
00:08:09,860 --> 00:08:10,759
这是怎么回事

201
00:08:10,759 --> 00:08:11,329
注意啊

202
00:08:11,329 --> 00:08:14,000
我们做这种呃

203
00:08:14,000 --> 00:08:15,379
怎么说屏幕适配的时候

204
00:08:15,379 --> 00:08:17,839
这里不要把这两个勾都选上啊

205
00:08:17,839 --> 00:08:20,000
要不然的话还是会出现呃

206
00:08:20,000 --> 00:08:22,189
刚才看到的那种情况啊

207
00:08:22,189 --> 00:08:23,720
我们只留一个啊

208
00:08:23,720 --> 00:08:26,660
就是你要不然就一个依照这个宽来设计

209
00:08:26,660 --> 00:08:28,839
要不然就依照高来设计啊

210
00:08:29,199 --> 00:08:31,839
那么我们现在就把这个勾给勾上啊

211
00:08:31,839 --> 00:08:32,840
固定高

212
00:08:32,840 --> 00:08:34,940
那这时候啊我们保留一个

213
00:08:34,940 --> 00:08:40,159
然后小鸟这边我们让它与右边永远保持零像素

214
00:08:40,159 --> 00:08:40,879
我们运行一下

215
00:08:40,879 --> 00:08:41,700
看效果

216
00:08:41,980 --> 00:08:44,440
首先pad没错了

217
00:08:44,440 --> 00:08:48,690
手机不同的手机默认

218
00:08:48,690 --> 00:08:51,779
那么这时候大家就发现他是不是永远贴着右边了

219
00:08:51,779 --> 00:08:53,360
他永远贴着右边了

220
00:08:53,379 --> 00:08:56,379
那我们如果希望它永远贴着右上角的

221
00:08:56,379 --> 00:08:58,139
你再把上给它固定

222
00:08:58,139 --> 00:09:00,600
现在它离右上都是零像素

223
00:09:00,600 --> 00:09:04,679
也就是说他这个精灵不管怎样去动

224
00:09:04,899 --> 00:09:09,460
永远会显示在我们右上角的位置啊

225
00:09:09,460 --> 00:09:11,320
你看右上角对吧

226
00:09:11,919 --> 00:09:16,419
默认右上角什么pad pad迷你是吧

227
00:09:16,419 --> 00:09:18,059
华为啊

228
00:09:18,059 --> 00:09:19,500
你你用什么样的

229
00:09:19,500 --> 00:09:21,000
你要你用这种横屏的

230
00:09:21,000 --> 00:09:21,960
用竖屏的

231
00:09:21,960 --> 00:09:24,399
它都是在右上角去显示

232
00:09:24,419 --> 00:09:27,000
这样的话就做了这样一个屏幕适配了

233
00:09:27,000 --> 00:09:28,409
所以非常简单啊

234
00:09:28,409 --> 00:09:32,539
那一般的话我们就勾一个边或者一个角啊

235
00:09:32,559 --> 00:09:34,720
比如说我们现在在上右啊

236
00:09:34,720 --> 00:09:37,139
你要是把它放在右下角

237
00:09:37,139 --> 00:09:39,480
就应该让它贴合右下角的话

238
00:09:39,480 --> 00:09:40,720
你就改成右下

239
00:09:40,720 --> 00:09:42,399
如果并不是很贴合的话

240
00:09:42,399 --> 00:09:43,899
也也没关系啊

241
00:09:43,899 --> 00:09:45,639
你这里可以给他一点距离啊

242
00:09:45,639 --> 00:09:49,799
就是跟右边跟上边同时保持多远的距离

243
00:09:49,799 --> 00:09:52,059
这样的话它永远在这个距离啊

244
00:09:52,940 --> 00:09:56,080
永远保持这么长的距离啊

245
00:09:56,080 --> 00:09:58,120
你不管怎样用什么样的屏幕

246
00:09:58,120 --> 00:10:00,039
它永远都是这样的一个距离

247
00:10:00,759 --> 00:10:03,100
那么在这里啊

248
00:10:03,100 --> 00:10:05,200
除了用这种方式啊

249
00:10:05,200 --> 00:10:07,120
我们下面还提供了一种方式

250
00:10:07,120 --> 00:10:10,080
永远在副物体的水平居中

251
00:10:10,379 --> 00:10:12,929
永远在负物体的垂直居中

252
00:10:12,929 --> 00:10:14,519
如果这两个都勾选上

253
00:10:14,519 --> 00:10:18,090
你永远在这个副体的正中心啊

254
00:10:18,090 --> 00:10:20,100
那么还提供了这样两种方式

255
00:10:20,100 --> 00:10:23,639
所以具体大家是贴合边还是贴合角

256
00:10:23,639 --> 00:10:24,899
还是放在居中位置

257
00:10:24,899 --> 00:10:28,519
这个就看你下面这里有一个选项

258
00:10:29,639 --> 00:10:31,500
下面这里有个选项啊

259
00:10:31,500 --> 00:10:33,000
那么这个是什么意思啊

260
00:10:33,000 --> 00:10:36,129
这个节点就是我们拖过来一个节点啊

261
00:10:36,129 --> 00:10:38,940
那么它呢就不它本身默认

262
00:10:38,940 --> 00:10:40,799
比如说我这个子节点啊

263
00:10:40,799 --> 00:10:42,120
这个小方块代表子节点

264
00:10:42,120 --> 00:10:43,679
这个大方块代表负节点

265
00:10:43,679 --> 00:10:44,039
对不对

266
00:10:44,039 --> 00:10:44,879
代表canvas

267
00:10:44,879 --> 00:10:46,200
现在代表屏幕

268
00:10:46,200 --> 00:10:48,679
如果我们拖上来一个节点啊

269
00:10:48,679 --> 00:10:50,179
比如说我们创建一个新的节点

270
00:10:50,179 --> 00:10:51,620
把新的节点拖上来

271
00:10:51,620 --> 00:10:54,200
那么新的节点就会变成这个大方块

272
00:10:54,200 --> 00:10:56,860
也就是说现在我们去计算它的相对位置

273
00:10:56,860 --> 00:11:03,309
就计算当前这个精灵与新节点的一个呃这个这样这样的一个关系了啊

274
00:11:03,309 --> 00:11:05,919
所以这个东西一般我不用给他啊

275
00:11:05,919 --> 00:11:10,320
一般就是计算当前节点与负节点的关系就行了啊

276
00:11:10,340 --> 00:11:18,879
那么在这里有一个模式对其模式就是什么时候会进行一次计算啊

277
00:11:18,879 --> 00:11:23,039
有一个一次的就是说比如说我上来屏幕是什么样

278
00:11:23,039 --> 00:11:25,379
我就给你第一次约束好诶

279
00:11:25,379 --> 00:11:28,259
我们离右上保持300 300的距离

280
00:11:28,259 --> 00:11:30,659
然后这时候屏幕如果再发生变化

281
00:11:30,659 --> 00:11:32,610
我就不会进行适配了啊

282
00:11:32,610 --> 00:11:35,019
我就不会在这个右上角了

283
00:11:35,019 --> 00:11:37,840
那么这就是只会贴合一次

284
00:11:37,840 --> 00:11:42,600
然后有一个就是永远都啊就是他一直在计算

285
00:11:42,600 --> 00:11:46,620
一直保持自己在右上角300 300的这样的一个位置

286
00:11:46,620 --> 00:11:48,039
这样太费性能了

287
00:11:48,059 --> 00:11:49,440
默认是选择的

288
00:11:49,440 --> 00:11:52,679
第二种就是只有屏幕每次发生变化以后

289
00:11:52,679 --> 00:11:55,539
就屏幕比如说从横屏改成竖屏了啊

290
00:11:55,539 --> 00:11:57,190
或者屏幕大小发生变化了

291
00:11:57,190 --> 00:11:59,710
屏幕大小发生变化的时候

292
00:11:59,710 --> 00:12:00,820
我会计算一次

293
00:12:00,820 --> 00:12:02,679
那这个是最合适的啊

294
00:12:02,679 --> 00:12:04,120
只要屏幕不发生变化

295
00:12:04,120 --> 00:12:07,059
我就不计算屏幕只要一变化我就计算

296
00:12:07,059 --> 00:12:11,240
然后来放到屏幕右上角300x300的一个位置啊

297
00:12:11,259 --> 00:12:12,759
那么三种模式啊

298
00:12:12,759 --> 00:12:14,679
我们一般就是第二种就ok了

299
00:12:15,059 --> 00:12:20,399
那这样的话这个就是一个呃屏幕适配啊

300
00:12:20,399 --> 00:12:23,009
那这就是一个屏幕适配呃

301
00:12:23,009 --> 00:12:24,299
遮罩跟屏幕适配

302
00:12:24,299 --> 00:12:25,320
咱们就说完了啊

303
00:12:25,320 --> 00:12:26,759
都是比较简单的啊

304
00:12:26,759 --> 00:12:29,120
大家自己琢磨一下啊

305
00:12:29,120 --> 00:12:33,679
自己来导一些精灵来做一个动动手

306
00:12:33,679 --> 00:12:35,240
是不是来做一做啊

307
00:12:35,240 --> 00:12:38,059
看一看这个遮罩和这个屏幕适配

308
00:12:38,059 --> 00:12:40,159
这个是怎么去做的啊

309
00:12:40,159 --> 00:12:42,440
很多同学应该是会一看视频

310
00:12:42,440 --> 00:12:45,080
从第一集夸夸夸连着看啊

311
00:12:45,080 --> 00:12:47,120
然后看看看的时候觉得简单

312
00:12:47,120 --> 00:12:48,620
是不是觉得这个没问题

313
00:12:48,620 --> 00:12:50,500
看完了让自己去做

314
00:12:50,580 --> 00:12:51,720
忘了啊

315
00:12:51,720 --> 00:12:53,159
还能去翻视频啊

316
00:12:53,159 --> 00:12:55,080
是不是有这有这种情况发生啊

317
00:12:55,080 --> 00:12:57,480
所以你千万不要来

318
00:12:57,480 --> 00:12:58,259
就算简单

319
00:12:58,259 --> 00:12:59,940
自己要动手操作一遍啊

320
00:12:59,940 --> 00:13:01,019
不要看视频

321
00:13:01,019 --> 00:13:06,480
就是自己能从头啊到尾能自己操作一遍就ok了行

322
00:13:06,480 --> 00:13:07,779
那我们这节课就这么多


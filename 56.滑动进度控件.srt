1
00:00:09,880 --> 00:00:13,449
ok咱们这节课来讲这个滑动视图啊

2
00:00:13,449 --> 00:00:14,320
滑动视图

3
00:00:14,320 --> 00:00:16,980
那么滑动视图就是咱们的这个view

4
00:00:16,980 --> 00:00:18,960
其实它也是用来布局的啊

5
00:00:18,960 --> 00:00:21,239
那我们先把它加上来

6
00:00:21,239 --> 00:00:21,780
直接运行

7
00:00:21,780 --> 00:00:22,719
看一下效果

8
00:00:22,820 --> 00:00:24,859
我们发现右边有个这个进度条

9
00:00:24,859 --> 00:00:28,129
我们可以看到它当前的一个滑动的一个位置

10
00:00:28,129 --> 00:00:31,489
然后我们可以拖拽它上下进行滑动

11
00:00:31,489 --> 00:00:32,950
是不是这样的一个东西

12
00:00:32,950 --> 00:00:36,579
那么这个这个控件的话

13
00:00:37,000 --> 00:00:38,200
我们看一下

14
00:00:38,200 --> 00:00:40,539
其实它是有很多东西组成的

15
00:00:40,539 --> 00:00:41,920
首先它有一个八

16
00:00:41,920 --> 00:00:42,689
对不对

17
00:00:42,689 --> 00:00:47,079
这个bar就是在右边显示我们当前的位置的这样的一个东西啊

18
00:00:47,079 --> 00:00:51,880
那么当人大半里面这样还有一个小的这样的一个小八

19
00:00:51,880 --> 00:00:53,469
是不是在这里

20
00:00:53,469 --> 00:00:55,719
你看他们俩也是一个嵌套的关系啊

21
00:00:55,719 --> 00:00:56,838
嵌套的关系

22
00:00:57,479 --> 00:00:59,179
那么它现在是纵向的

23
00:00:59,179 --> 00:01:01,878
我们可以手动在这里点出这个sc

24
00:01:01,878 --> 00:01:04,939
在这里你看有个scar bar的这样的一个组件

25
00:01:04,939 --> 00:01:07,579
你可以把它的垂直修改成水平啊

26
00:01:07,579 --> 00:01:08,989
这些你都可以自己去做

27
00:01:08,989 --> 00:01:09,920
改成水平的

28
00:01:09,920 --> 00:01:11,379
它就变成横向的b了

29
00:01:11,379 --> 00:01:15,399
这里还有它的一个你看和副节点的一个关系

30
00:01:16,019 --> 00:01:20,269
这个就控制这个bar与复节点最外层的这样的一个关系

31
00:01:20,269 --> 00:01:23,239
它就是让这个b永远显示在最右边

32
00:01:23,239 --> 00:01:25,250
永远上下贴边

33
00:01:25,250 --> 00:01:28,290
所以它最右边保持零像素

34
00:01:28,290 --> 00:01:30,870
最上边和下边同时保持零像素

35
00:01:30,870 --> 00:01:35,239
这样的话我们这个组件左右拉伸

36
00:01:35,239 --> 00:01:36,439
它永远贴在右边

37
00:01:36,439 --> 00:01:37,340
上下拉伸

38
00:01:37,340 --> 00:01:40,010
它上下由于要都要保持零

39
00:01:40,010 --> 00:01:42,789
那么它上下就会进行一个拉伸了啊

40
00:01:42,789 --> 00:01:44,769
那么这就是这样的一个东西

41
00:01:44,769 --> 00:01:49,698
你看我们也是多看几个系统自己的设定好的这样的一个布局

42
00:01:49,698 --> 00:01:52,159
我们也就会呃更会用了啊

43
00:01:52,159 --> 00:01:52,789
对不对

44
00:01:52,789 --> 00:01:55,739
然后在这里嗯

45
00:01:55,739 --> 00:01:56,700
第二组啊

46
00:01:56,700 --> 00:01:58,980
第二组就是这样的一个view啊

47
00:01:58,980 --> 00:02:00,420
就是整个的一个视图啊

48
00:02:00,420 --> 00:02:02,760
就是scaview里面还有一个小的will

49
00:02:02,760 --> 00:02:04,469
这个view呢和它一样大

50
00:02:04,469 --> 00:02:06,269
那么要这个view是干嘛的

51
00:02:06,269 --> 00:02:09,699
这个view大家可以看右边有一个遮罩啊

52
00:02:09,699 --> 00:02:13,900
说白了这个view就是意思就是超过我这个视图外的内容

53
00:02:13,900 --> 00:02:15,389
我是不显示的啊

54
00:02:15,389 --> 00:02:16,889
他是做了一个遮罩的功能

55
00:02:16,889 --> 00:02:18,689
是不是咱们之前的遮罩功能

56
00:02:18,689 --> 00:02:21,169
你看咱们前面用的组件什么遮罩呀

57
00:02:21,169 --> 00:02:21,830
布局啊

58
00:02:21,830 --> 00:02:22,840
这都建了

59
00:02:22,939 --> 00:02:25,219
然后再往下面有个content

60
00:02:25,219 --> 00:02:28,460
content就是真正会显示的内容啊

61
00:02:28,460 --> 00:02:30,349
content就是真正显示的内容

62
00:02:30,349 --> 00:02:32,479
你看content是不是这么长

63
00:02:32,479 --> 00:02:36,650
而之所以content比它本身的内容长

64
00:02:36,650 --> 00:02:38,629
它才能有上下拖动

65
00:02:38,629 --> 00:02:41,479
如果content内容变成这么短了

66
00:02:41,639 --> 00:02:42,960
你看counter呢

67
00:02:42,960 --> 00:02:45,759
如果真正的内容变成这么短了

68
00:02:45,759 --> 00:02:47,439
你看上下压根儿不能拖拽了

69
00:02:47,439 --> 00:02:49,240
它就成了一个普通的视图了

70
00:02:49,240 --> 00:02:50,500
所以注意啊

71
00:02:50,500 --> 00:02:55,590
scav其实就是默认的这个窗口是个显示窗口

72
00:02:55,590 --> 00:02:58,319
真正的窗口实际上是这个content

73
00:02:58,319 --> 00:03:01,800
然后在这个content里面大家可以看是不是有内容啊

74
00:03:01,800 --> 00:03:09,120
啊有内容你可以把比如说把这个item多拷贝几份往下放

75
00:03:09,459 --> 00:03:11,859
你看比如说第四份出去了

76
00:03:11,859 --> 00:03:12,399
大家可以看

77
00:03:12,399 --> 00:03:13,598
因为遮罩组件

78
00:03:13,598 --> 00:03:15,818
因为它们的服务体是不是有遮罩功能

79
00:03:15,818 --> 00:03:17,979
所以出去了就看不见了啊

80
00:03:17,979 --> 00:03:19,680
出去的你看看不见了

81
00:03:20,219 --> 00:03:22,319
那我们运行完以后

82
00:03:22,319 --> 00:03:26,139
大家就发现你看是不是就变成这样的了啊

83
00:03:26,139 --> 00:03:28,360
那么这时候大家发现我是手动拖拽的

84
00:03:28,360 --> 00:03:32,379
大家也可以给他们附体挂载上咱们上节课的那个布局

85
00:03:32,379 --> 00:03:33,069
layout

86
00:03:33,069 --> 00:03:35,860
让它垂直垂直排列啊

87
00:03:35,860 --> 00:03:38,139
就是自动的垂直排列也是可以的

88
00:03:38,139 --> 00:03:40,930
那么这就是一个scav滑动视图啊

89
00:03:40,930 --> 00:03:41,979
滑动视图

90
00:03:43,938 --> 00:03:47,258
那么滑动视图我们选中它最外层

91
00:03:47,258 --> 00:03:48,169
我们看一下

92
00:03:48,169 --> 00:03:50,930
那么它最右边有一个最大的组件啊

93
00:03:50,930 --> 00:03:52,280
就是这个cu组件

94
00:03:52,280 --> 00:03:54,139
对于cv组件来说

95
00:03:54,139 --> 00:03:56,239
我们看一下啊

96
00:03:56,239 --> 00:03:59,020
首先有一个content就是内容

97
00:03:59,020 --> 00:04:00,310
内容就是最大的

98
00:04:00,310 --> 00:04:02,110
这个就是这个content啊

99
00:04:02,110 --> 00:04:03,250
他这是做了一个关联

100
00:04:03,250 --> 00:04:04,569
这个我们也不需要管啊

101
00:04:04,569 --> 00:04:06,439
不需要去动他

102
00:04:06,439 --> 00:04:11,120
然后当前的呃滑动是水平方向的还是垂直方向的

103
00:04:11,120 --> 00:04:12,349
还是都可以滑动

104
00:04:12,349 --> 00:04:13,729
默认是垂直的啊

105
00:04:13,729 --> 00:04:15,409
因为我们基本上滑动垂直的多

106
00:04:15,409 --> 00:04:15,949
对不对

107
00:04:15,949 --> 00:04:17,560
尤其是在手机上

108
00:04:17,778 --> 00:04:20,238
那么当然你也可以给它做成横向的

109
00:04:20,238 --> 00:04:20,658
横向的

110
00:04:20,658 --> 00:04:22,608
你就要把这个垂直的关了

111
00:04:22,608 --> 00:04:24,769
改成水平的啊

112
00:04:24,769 --> 00:04:29,240
那么再往下默认这个勾是勾上的

113
00:04:29,240 --> 00:04:31,639
这个是是否开启呃

114
00:04:31,639 --> 00:04:33,379
这个这个滚动的一个惯性

115
00:04:33,379 --> 00:04:35,860
他说啊其实是什么意思

116
00:04:35,959 --> 00:04:37,160
因为有惯性

117
00:04:37,160 --> 00:04:39,079
所以我们稍微往上滑一点

118
00:04:39,079 --> 00:04:41,170
大家可以看滑一点

119
00:04:41,170 --> 00:04:43,449
它还会自动有一个减速的过程

120
00:04:43,449 --> 00:04:45,490
这个减速的过程就是惯性

121
00:04:45,490 --> 00:04:47,560
如果我把这个勾去掉了

122
00:04:47,560 --> 00:04:48,959
就不要惯性了

123
00:04:49,639 --> 00:04:51,788
你看我只要一停

124
00:04:51,788 --> 00:04:53,129
我鼠标一停

125
00:04:53,129 --> 00:04:54,750
他立刻就停下来了

126
00:04:55,769 --> 00:04:56,670
感觉啊

127
00:04:56,670 --> 00:04:58,589
那一般我们这个不要动

128
00:04:58,589 --> 00:05:02,910
它下面有个数值是从零开始到一的啊

129
00:05:02,910 --> 00:05:05,009
那么这个数值就代表惯性的

130
00:05:05,009 --> 00:05:08,199
这个你如果开启惯性会有一个减速

131
00:05:08,199 --> 00:05:11,160
这个其实就代表一个减速的一个快慢啊

132
00:05:11,160 --> 00:05:15,060
从0~1减速是呃零是永不停止

133
00:05:15,060 --> 00:05:17,079
就是零是最快的啊

134
00:05:17,079 --> 00:05:19,028
零的减速效果是最弱的

135
00:05:19,028 --> 00:05:21,720
一代表立刻停止啊

136
00:05:21,720 --> 00:05:24,000
所以e的减速效果是最快的啊

137
00:05:24,000 --> 00:05:25,680
你鼠标一一松开

138
00:05:25,680 --> 00:05:26,819
它立刻就停了啊

139
00:05:26,819 --> 00:05:28,759
0.75啊

140
00:05:28,759 --> 00:05:30,170
默认是这样一个数值

141
00:05:30,170 --> 00:05:31,459
那你可以去修改一下

142
00:05:31,459 --> 00:05:32,540
比如改成0.2

143
00:05:32,540 --> 00:05:34,160
然后再运行看一下

144
00:05:34,379 --> 00:05:35,399
稍微动一下

145
00:05:35,399 --> 00:05:38,158
你看我就稍微动了一下

146
00:05:38,158 --> 00:05:40,588
他基本上就直接到到头了

147
00:05:40,588 --> 00:05:41,779
到头了

148
00:05:41,779 --> 00:05:45,980
那么这样的话就是这个滑动的效果太好了啊

149
00:05:45,980 --> 00:05:47,899
当然这样的话我们也不太好操作了

150
00:05:47,899 --> 00:05:51,180
所以一般的话他默认就给了个0.75

151
00:05:51,180 --> 00:05:52,360
再往下

152
00:05:52,740 --> 00:05:54,420
这个是反弹效果

153
00:05:54,420 --> 00:05:55,800
是不是需要反弹效果

154
00:05:55,800 --> 00:05:57,600
我们看一下反弹效果啊

155
00:05:57,860 --> 00:05:59,660
到了明明已经到上面了

156
00:05:59,660 --> 00:06:01,339
还可以有这个反弹效果啊

157
00:06:01,339 --> 00:06:03,439
那么这个就是这个属性控制的

158
00:06:03,439 --> 00:06:07,660
然后这个是反弹的一个时间啊

159
00:06:07,660 --> 00:06:10,000
其实就是反弹效果的一个速度啊

160
00:06:10,000 --> 00:06:11,680
反弹效果的一个速度啊

161
00:06:11,680 --> 00:06:13,540
其实跟上面这个完全是一样的

162
00:06:13,540 --> 00:06:16,009
只不过上面这一对是控制减速的

163
00:06:16,009 --> 00:06:18,288
这一对是控制反弹的啊

164
00:06:19,309 --> 00:06:24,790
这里关联的这个cba就是最右边的这个条啊

165
00:06:24,790 --> 00:06:27,189
那么我们目前一般都是垂直的

166
00:06:27,189 --> 00:06:29,769
所以它这里垂直有个关联水平没有

167
00:06:29,769 --> 00:06:31,389
如果你改成水平的了

168
00:06:31,389 --> 00:06:34,899
你再把bar那边咱们那会儿看到的给它改成水平的

169
00:06:34,899 --> 00:06:36,850
然后把它就可以重新关联

170
00:06:36,850 --> 00:06:42,560
就把这个呃scar bar从垂直的给它关联成水平的

171
00:06:43,180 --> 00:06:47,129
那么再往下就是滑动的一个事件啊

172
00:06:47,129 --> 00:06:49,519
这个事件和这个按钮一样

173
00:06:49,519 --> 00:06:50,540
你给个事件

174
00:06:50,540 --> 00:06:52,310
然后在这里就可以去关联事件

175
00:06:52,310 --> 00:06:55,750
然后滑动的话嗯就会触发这个事件啊

176
00:06:55,750 --> 00:06:58,000
就会触发这个事件呃

177
00:06:58,000 --> 00:06:58,990
那么空调的话

178
00:06:58,990 --> 00:07:03,759
咱们嗯比如说如果在脚本上面

179
00:07:03,759 --> 00:07:06,040
我们也可以给它通过脚本的方式

180
00:07:06,040 --> 00:07:07,839
比如说往里面去添加这些东西

181
00:07:07,839 --> 00:07:08,959
对不对啊

182
00:07:08,959 --> 00:07:10,579
这些都是可以操作的啊

183
00:07:10,579 --> 00:07:14,420
我们我们目前就只是给大家去介绍了一下空间的使用

184
00:07:14,860 --> 00:07:19,678
等我们在这个后面用上这个脚本的时候啊

185
00:07:19,678 --> 00:07:23,069
我们就可能就会比如说把这个scav拖上来

186
00:07:23,069 --> 00:07:25,389
默认我们里面什么也不让他有

187
00:07:25,389 --> 00:07:29,470
然后通过脚本的方式给它上面去添加子物体啊

188
00:07:29,470 --> 00:07:33,740
这样就会变成一个类似于动态加载这样的一个东西了

189
00:07:33,740 --> 00:07:34,160
对不对

190
00:07:35,240 --> 00:07:35,819
嗯

191
00:07:35,819 --> 00:07:36,600
ok啊

192
00:07:36,600 --> 00:07:39,180
那么其实大家现在也会去添加子节点

193
00:07:39,180 --> 00:07:41,079
你自己可以去尝试添加一下

194
00:07:41,079 --> 00:07:42,279
写个脚本啊

195
00:07:42,279 --> 00:07:45,500
通过脚本给这个康特的去添加一些子物体

196
00:07:46,019 --> 00:07:48,600
ok那么这个就是滑动视图

197
00:07:48,759 --> 00:07:50,740
其实对于滑动视图而言

198
00:07:50,740 --> 00:07:52,060
这是一个滑动视图

199
00:07:52,060 --> 00:07:53,589
这里还有一个滑动视图

200
00:07:53,589 --> 00:07:58,079
只不过这个滑动视图并不是说哎可以正常滑动了

201
00:07:58,079 --> 00:08:00,180
它是按页数去滑动的啊

202
00:08:00,180 --> 00:08:03,019
我们重新拖一个什么意思

203
00:08:03,019 --> 00:08:03,680
运行一下

204
00:08:03,680 --> 00:08:04,480
大家可以看

205
00:08:04,480 --> 00:08:05,800
默认有三页啊

206
00:08:05,800 --> 00:08:07,480
我们和我们见过这种组件

207
00:08:07,480 --> 00:08:08,019
对不对

208
00:08:08,019 --> 00:08:09,009
默认有三页

209
00:08:09,009 --> 00:08:10,240
现在显示哪一页

210
00:08:10,240 --> 00:08:12,269
下面这个点你就显示哪一个点

211
00:08:12,269 --> 00:08:15,449
然后每一页上面我们可以放内容啊

212
00:08:15,449 --> 00:08:16,529
配置一啊

213
00:08:16,529 --> 00:08:17,370
你可以放内容

214
00:08:17,370 --> 00:08:18,509
配置二可以放内容

215
00:08:18,509 --> 00:08:19,959
配置三可以放内容

216
00:08:19,959 --> 00:08:23,319
然后每一页你可以去修改它的颜色

217
00:08:23,319 --> 00:08:25,839
或者说给它添加一个别的背景啊

218
00:08:25,839 --> 00:08:27,160
现在是纯色背景

219
00:08:27,160 --> 00:08:28,819
你可以加个别的背景

220
00:08:28,819 --> 00:08:31,519
然后整个大的背景是这个白色的背景

221
00:08:31,519 --> 00:08:32,480
是不是这是最大的

222
00:08:32,480 --> 00:08:34,860
这个背景你也可以去修改啊

223
00:08:34,860 --> 00:08:39,759
然后实际上这个布局就是按分页布局啊

224
00:08:39,759 --> 00:08:40,899
分页布局啊

225
00:08:40,899 --> 00:08:41,919
刚才的是滑动布局

226
00:08:41,919 --> 00:08:42,879
这个分页布局

227
00:08:42,879 --> 00:08:45,879
分页布局跟滑动布局是比较类似的

228
00:08:45,879 --> 00:08:53,099
在这边一般我们也不会去修改它的太多的一个太多的一个内容啊

229
00:08:53,799 --> 00:08:58,559
那么顶多就是我们在这个里面一般拿这个空间就是修改一下背景

230
00:08:58,559 --> 00:09:01,389
修改一下这个每一页的这个颜色

231
00:09:01,389 --> 00:09:01,870
对不对

232
00:09:01,870 --> 00:09:03,549
或者说页数太多

233
00:09:03,549 --> 00:09:04,990
三页我可能就两页

234
00:09:04,990 --> 00:09:07,379
所以我在这直接删除页啊

235
00:09:07,379 --> 00:09:09,840
一般也就做这个操作就ok了啊

236
00:09:15,370 --> 00:09:17,960
那我们会这些就ok了

237
00:09:17,960 --> 00:09:21,200
呃其实就是给每一页做不同的内容

238
00:09:21,200 --> 00:09:24,078
然后去在这里去显示不同的页数啊

239
00:09:24,559 --> 00:09:25,850
嗯ok啊

240
00:09:25,850 --> 00:09:28,460
那么这个东西没什么太多说的

241
00:09:28,460 --> 00:09:32,159
然后还有几个1234567个

242
00:09:32,159 --> 00:09:33,539
是不是呃

243
00:09:33,539 --> 00:09:34,539
那么

244
00:09:36,620 --> 00:09:37,840
progress啊

245
00:09:37,840 --> 00:09:38,799
我们再说一个好了

246
00:09:38,799 --> 00:09:40,809
我们再说一个progress

247
00:09:40,809 --> 00:09:43,059
那么这个组件啊

248
00:09:43,059 --> 00:09:44,899
这个组件是干嘛的

249
00:09:45,200 --> 00:09:46,370
我们放大看

250
00:09:46,370 --> 00:09:48,559
这个是显示一个百分比的

251
00:09:48,559 --> 00:09:50,339
显示一个百分比的

252
00:09:52,779 --> 00:09:54,179
那么这个百分比啊

253
00:09:54,179 --> 00:09:55,320
这个就是蛮有用的了

254
00:09:55,320 --> 00:09:57,299
他常常去给我们显示一些进度

255
00:09:57,299 --> 00:09:57,960
对不对

256
00:09:57,960 --> 00:10:01,029
然后我们在右边我们可以看到它的属性

257
00:10:01,029 --> 00:10:02,340
它的属性

258
00:10:02,340 --> 00:10:04,740
首先这里有个横向还是纵向的

259
00:10:04,740 --> 00:10:05,700
默认是横向的

260
00:10:05,700 --> 00:10:06,960
你可以调成纵向的

261
00:10:06,960 --> 00:10:07,379
对不对

262
00:10:07,379 --> 00:10:09,580
一般就是用前两个横向纵向的

263
00:10:09,580 --> 00:10:12,250
然后在这里这是当前的进度

264
00:10:12,250 --> 00:10:13,539
因为是个百分比

265
00:10:13,539 --> 00:10:14,980
所以是从0~1啊

266
00:10:14,980 --> 00:10:15,919
这里是一

267
00:10:15,919 --> 00:10:17,600
然后我们发现从0~1以后

268
00:10:17,600 --> 00:10:19,429
这个东西一并没有填满

269
00:10:19,429 --> 00:10:21,259
一的话进度应该已经满了

270
00:10:21,259 --> 00:10:22,220
但是一并没有满

271
00:10:22,220 --> 00:10:23,099
为什么

272
00:10:23,099 --> 00:10:26,639
那是因为当当前如果是一的话啊

273
00:10:26,820 --> 00:10:28,230
这里是个100

274
00:10:28,230 --> 00:10:32,779
这个长度就代表当我百分比到百分百的时候

275
00:10:32,779 --> 00:10:34,159
长度最长是多长

276
00:10:34,159 --> 00:10:35,750
那如果你希望一的话

277
00:10:35,750 --> 00:10:36,679
它能填满

278
00:10:36,679 --> 00:10:39,960
你就要给他和服物体一样宽

279
00:10:39,960 --> 00:10:41,759
大家可以看服务器是300是吧

280
00:10:41,759 --> 00:10:43,299
所以我就给个300

281
00:10:43,519 --> 00:10:45,830
那这样的话在一的时候他就满了

282
00:10:45,830 --> 00:10:48,080
然后我们在这里去修改进度哎

283
00:10:48,080 --> 00:10:49,458
就有这样的效果了

284
00:10:49,879 --> 00:10:53,360
当然父子物体的这个上面的这个显示的样式

285
00:10:53,360 --> 00:10:55,549
你可以你看精灵啊

286
00:10:55,549 --> 00:10:58,370
你还是可以通过你的图片去修改的啊

287
00:10:58,370 --> 00:11:01,649
你可以通过你的图片去进行一个呃修改

288
00:11:01,649 --> 00:11:03,690
修改应该在这里选中这个bar

289
00:11:03,690 --> 00:11:06,659
比如说修改个样式

290
00:11:06,659 --> 00:11:10,259
你看现在填充的是不是就变了啊

291
00:11:10,259 --> 00:11:12,360
所以这些基本上你能看到的啊

292
00:11:12,360 --> 00:11:16,559
都是可以去进行修改的哈哈

293
00:11:17,419 --> 00:11:19,700
然后还有一个反向的反向的

294
00:11:19,700 --> 00:11:20,899
你看这就颠倒过来了

295
00:11:20,899 --> 00:11:21,259
对不对

296
00:11:21,259 --> 00:11:22,299
颠倒过来了

297
00:11:22,299 --> 00:11:23,080
ok啊

298
00:11:23,080 --> 00:11:25,120
那么这个空间非常简单啊

299
00:11:25,120 --> 00:11:26,259
这就是进度啊

300
00:11:26,259 --> 00:11:28,519
那么我们这节课就说了这么多

301
00:11:29,340 --> 00:11:30,340
略略略


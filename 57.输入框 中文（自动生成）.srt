1
00:00:09,199 --> 00:00:14,300
ok那么咱们这节课来说说一下这个输入框这样的一个控件啊

2
00:00:14,300 --> 00:00:15,800
那么我们大家知道啊

3
00:00:15,800 --> 00:00:19,289
在这个不管是游戏还是应用啊

4
00:00:19,289 --> 00:00:22,620
有很多地儿可能我们是需要这个输入框的

5
00:00:22,620 --> 00:00:24,660
比如说输入账号密码

6
00:00:24,660 --> 00:00:25,530
对不对

7
00:00:25,530 --> 00:00:27,480
输入这个邮箱地址

8
00:00:31,219 --> 00:00:33,979
那么在聊天框里面我们可能输入一些语句

9
00:00:33,979 --> 00:00:34,880
然后发送出去

10
00:00:34,880 --> 00:00:35,420
对不对

11
00:00:35,420 --> 00:00:37,909
那么这些都需要这个输入框

12
00:00:37,909 --> 00:00:40,950
那么我们接下来来说一下这个输入框

13
00:00:40,950 --> 00:00:44,238
那输入框在我们这个里面叫editor box

14
00:00:45,600 --> 00:00:47,509
我们拖上一个输入框

15
00:00:47,509 --> 00:00:49,039
拖上一个输入框

16
00:00:49,380 --> 00:00:51,240
那么输入框里面啊

17
00:00:51,240 --> 00:00:52,859
大家可以看一下啊

18
00:00:52,859 --> 00:00:54,479
那么我运行一下啊

19
00:00:54,479 --> 00:00:55,770
我们来看一下

20
00:00:55,770 --> 00:00:56,909
这是一个输入框

21
00:00:56,909 --> 00:01:00,280
我们可以看一下默认输入框里面是有文字内容的

22
00:01:00,280 --> 00:01:04,299
然后文字内容文字内容意思就是啊在此输入

23
00:01:04,299 --> 00:01:04,870
对不对

24
00:01:04,870 --> 00:01:07,000
那么我们在这里实际上点一下

25
00:01:07,000 --> 00:01:09,290
我们发现原本的内容就没了

26
00:01:09,290 --> 00:01:11,939
但是我们可以输入新的我们自己的内容

27
00:01:12,299 --> 00:01:14,760
当我们把我们的内容全删了以后

28
00:01:14,760 --> 00:01:16,920
我们发现诶这个文本又出现了

29
00:01:16,920 --> 00:01:17,609
对不对

30
00:01:17,609 --> 00:01:23,069
所以实际上在我们这里的这个输入框呢是由两个两部分文本构成

31
00:01:23,069 --> 00:01:24,909
我们看一下它的组成

32
00:01:24,909 --> 00:01:26,590
除了有一个背景图片

33
00:01:26,590 --> 00:01:28,989
那这个背景图片我们是可以自己去换的

34
00:01:28,989 --> 00:01:32,000
它还有两个label啊

35
00:01:32,000 --> 00:01:33,079
它有两个lb

36
00:01:33,079 --> 00:01:35,359
一个叫正常的文本text

37
00:01:35,359 --> 00:01:37,519
一个叫place holder啊

38
00:01:37,519 --> 00:01:40,680
那么这两个的区别是什么

39
00:01:40,680 --> 00:01:41,879
那么在这里说一下啊

40
00:01:41,879 --> 00:01:45,980
首先这个text就是我们正常输入的文本啊

41
00:01:45,980 --> 00:01:47,840
就是我们默认是没有输入的

42
00:01:47,840 --> 00:01:49,430
所以这个文本默认是空

43
00:01:49,430 --> 00:01:50,480
我们输入什么

44
00:01:50,480 --> 00:01:52,200
这个文本对应的就是什么

45
00:01:52,200 --> 00:01:53,819
这个placeholder什么意思

46
00:01:53,819 --> 00:01:55,390
就是占位符啊

47
00:01:55,390 --> 00:01:56,109
就是占位符

48
00:01:56,109 --> 00:01:59,590
意思就是如果你上面这个是什么也没有输入的

49
00:01:59,590 --> 00:02:02,379
那么它就会显示下面这个label的文本

50
00:02:02,379 --> 00:02:07,769
那么这个label的文本默认显示的就是这个enter text here

51
00:02:07,769 --> 00:02:08,699
对不对

52
00:02:08,939 --> 00:02:10,860
所以这个这一串东西啊

53
00:02:10,860 --> 00:02:12,060
我们就叫它占位符

54
00:02:12,060 --> 00:02:13,050
这个注意一下

55
00:02:13,050 --> 00:02:16,629
那这个占位符和这个文本我们都可以进行一些修改

56
00:02:16,629 --> 00:02:17,889
那我们选中它

57
00:02:17,889 --> 00:02:20,349
我们在右边看一下它的这个属性啊

58
00:02:20,349 --> 00:02:22,000
我们需要知道的有哪些

59
00:02:22,000 --> 00:02:22,810
我们看一下

60
00:02:22,810 --> 00:02:24,009
发现属性还挺多

61
00:02:24,009 --> 00:02:25,020
对不对啊

62
00:02:25,020 --> 00:02:27,479
但是实际上常用的也就没几个了啊

63
00:02:27,479 --> 00:02:28,590
我们来说一下啊

64
00:02:28,590 --> 00:02:34,139
首先第一个此阵此阵就代表我们真正输入的文本

65
00:02:34,139 --> 00:02:35,878
大家可以看我这边一输入

66
00:02:35,878 --> 00:02:37,258
这边也就跟着输入了

67
00:02:37,258 --> 00:02:39,419
但是我把这个删完了以后

68
00:02:40,199 --> 00:02:42,319
我们这个内容就没了啊

69
00:02:42,319 --> 00:02:43,939
然后这个占位符啊

70
00:02:43,939 --> 00:02:45,379
站位文本又出现了

71
00:02:45,379 --> 00:02:46,060
对不对

72
00:02:46,060 --> 00:02:47,919
第二个是个背景图片啊

73
00:02:47,919 --> 00:02:49,599
就是这个背景实际上很难看

74
00:02:49,599 --> 00:02:49,960
对不对

75
00:02:49,960 --> 00:02:51,740
我们知道很多输入框啊

76
00:02:51,740 --> 00:02:53,330
他比如说圆角呀

77
00:02:53,330 --> 00:02:55,520
或者说带些什么图片背景啊

78
00:02:55,520 --> 00:02:56,000
很好看

79
00:02:56,000 --> 00:02:56,599
对不对

80
00:02:56,599 --> 00:03:00,389
那么如果比如说呃自己的公司啊

81
00:03:00,389 --> 00:03:03,750
或者说这个美工啊给你提供了这个素材

82
00:03:03,750 --> 00:03:05,780
然后你就直接拖过来就可以去用

83
00:03:06,800 --> 00:03:10,569
那么再往下这个这是什么意思

84
00:03:10,569 --> 00:03:11,930
我们看一下啊

85
00:03:11,930 --> 00:03:13,849
我们把这个下拉菜单打开

86
00:03:13,849 --> 00:03:15,650
我们发现它有很多种啊

87
00:03:15,650 --> 00:03:17,810
什么default dsend是吧

88
00:03:17,810 --> 00:03:19,069
search go next

89
00:03:19,069 --> 00:03:20,780
这些是什么意思啊

90
00:03:20,780 --> 00:03:22,530
呃大家想一想啊

91
00:03:22,530 --> 00:03:24,300
我们在手机上面

92
00:03:24,300 --> 00:03:26,909
我们在手机上面一定是手机上面啊

93
00:03:26,909 --> 00:03:28,469
这一项跟电脑无关

94
00:03:28,469 --> 00:03:29,610
在手机上面

95
00:03:29,610 --> 00:03:31,789
比如说我们打开一个呃

96
00:03:31,789 --> 00:03:32,930
我们看到一个输入框

97
00:03:32,930 --> 00:03:35,090
我我用手指点它

98
00:03:35,090 --> 00:03:36,379
键盘就会出来

99
00:03:36,379 --> 00:03:37,610
那么键盘出来以后

100
00:03:37,610 --> 00:03:40,479
键盘右下角有一个回车键啊

101
00:03:40,479 --> 00:03:42,520
尤其是苹果系统是最明显的

102
00:03:42,520 --> 00:03:44,319
回车键是蓝色的

103
00:03:44,319 --> 00:03:44,889
对不对

104
00:03:44,889 --> 00:03:46,629
整个键盘都是灰的啊

105
00:03:46,629 --> 00:03:48,750
可能这个回车是个蓝色的

106
00:03:48,750 --> 00:03:51,449
那么这个蓝色的按钮上面会有文本

107
00:03:52,650 --> 00:03:53,069
对不对

108
00:03:53,069 --> 00:03:54,060
会有文本

109
00:03:54,379 --> 00:03:58,229
那比如说是回车还是发送还是什么的

110
00:03:58,229 --> 00:04:04,379
那么这个这一这一个选项就是定义了你键盘弹出来以后

111
00:04:04,379 --> 00:04:08,229
回车的回车键的这个样式啊

112
00:04:08,229 --> 00:04:09,009
样式名称

113
00:04:09,009 --> 00:04:10,689
比如说我们这儿选了down

114
00:04:10,689 --> 00:04:14,169
那么你这个如果把这个应用跑在手机上的话

115
00:04:14,169 --> 00:04:19,579
当我们一点它这个键盘提示出来这个蓝色按钮上面就是down啊

116
00:04:19,579 --> 00:04:20,379
就是这样的

117
00:04:20,920 --> 00:04:22,660
当然如果你是中文系统

118
00:04:22,660 --> 00:04:24,160
可能它显示的会是中文

119
00:04:24,160 --> 00:04:25,600
不是这个英文啊

120
00:04:25,600 --> 00:04:28,480
那么这个其实就定义一个回车键啊

121
00:04:28,480 --> 00:04:30,730
也就是那个蓝色按钮的那个样式

122
00:04:30,730 --> 00:04:33,439
那么再往下看啊

123
00:04:33,439 --> 00:04:37,620
这个输入标识这个其实啊意思是什么

124
00:04:37,620 --> 00:04:39,569
比如说你输入的是个英文

125
00:04:39,569 --> 00:04:42,779
然后他们会帮你把开头字母大写呀

126
00:04:42,779 --> 00:04:44,649
或者是全部大写啊等等

127
00:04:44,649 --> 00:04:49,319
但是实际上这种对于咱们在这个国内环境而言啊

128
00:04:49,319 --> 00:04:50,550
其实是没什么用

129
00:04:50,550 --> 00:04:52,500
但是这一项里面有一个很有用

130
00:04:52,500 --> 00:04:55,738
就是这个第一个选项我能选中它

131
00:04:55,738 --> 00:04:58,019
我们看一下它是密码

132
00:04:59,060 --> 00:04:59,740
对不对

133
00:04:59,740 --> 00:05:00,519
它是密码

134
00:05:00,519 --> 00:05:03,139
那我们现在选中它们运行一下

135
00:05:04,959 --> 00:05:05,920
我们看一下

136
00:05:07,360 --> 00:05:11,779
那么这时候它上面就不会显示铭文了啊

137
00:05:11,779 --> 00:05:14,540
在我们一按它就会显示这个密码样式了

138
00:05:14,540 --> 00:05:17,160
那所以这个东西我们自己也能想出来

139
00:05:18,360 --> 00:05:20,600
这个这个肯定是每个游戏啊

140
00:05:20,600 --> 00:05:24,430
应该说每个游戏呃或者应用都会用得上这个东西啊

141
00:05:24,430 --> 00:05:25,810
因为你现在肯定都有密码

142
00:05:25,810 --> 00:05:28,810
多多少少都会有这种不让人看见的东西

143
00:05:28,810 --> 00:05:29,709
对不对啊

144
00:05:29,709 --> 00:05:32,430
当然如果你写的不是网络应用

145
00:05:32,430 --> 00:05:33,449
那那可能用不着

146
00:05:35,850 --> 00:05:39,699
那么再往下这个啊啊我们看一下啊

147
00:05:39,699 --> 00:05:40,899
它是上面也写了

148
00:05:40,899 --> 00:05:42,069
安妮表示多行

149
00:05:42,069 --> 00:05:43,600
其他的都是单行

150
00:05:43,600 --> 00:05:44,829
那么在这里啊

151
00:05:44,829 --> 00:05:47,420
其实呃我们把它展开

152
00:05:47,420 --> 00:05:48,199
我们看一下啊

153
00:05:48,199 --> 00:05:50,120
它有很多种选项

154
00:05:50,120 --> 00:05:51,800
对于电脑上而言

155
00:05:51,800 --> 00:05:53,860
默认的这个就是单行

156
00:05:53,860 --> 00:05:55,899
除此之外还有一个安妮是多行

157
00:05:55,899 --> 00:05:57,279
如果选中安妮以后

158
00:05:57,279 --> 00:05:58,420
这个是可以折行的

159
00:06:00,560 --> 00:06:01,699
我们试一下啊

160
00:06:01,699 --> 00:06:02,899
现在是你哎

161
00:06:02,899 --> 00:06:05,720
你看它本身已经可以有很多行了

162
00:06:05,720 --> 00:06:06,600
对不对

163
00:06:07,098 --> 00:06:11,470
那么但是对于手机而言

164
00:06:11,470 --> 00:06:13,029
对于手机而言

165
00:06:13,029 --> 00:06:16,899
中间这些啊我们都可以尝试一下

166
00:06:16,899 --> 00:06:17,500
我们知道啊

167
00:06:17,500 --> 00:06:21,259
比如说因为这个安卓啊

168
00:06:21,259 --> 00:06:23,779
安卓我们大家知道这个这个键盘啊

169
00:06:23,779 --> 00:06:25,199
每一个系统啊

170
00:06:25,199 --> 00:06:27,839
每一个输入法它可以把键盘定义的完全不一样

171
00:06:27,839 --> 00:06:30,319
所以它的这个标准太不统一了啊

172
00:06:30,319 --> 00:06:32,240
你像苹果的话就很很统一

173
00:06:32,240 --> 00:06:33,800
所以我们拿苹果来举例子啊

174
00:06:33,800 --> 00:06:37,379
比如说你在苹果上面弹出来的键盘

175
00:06:37,379 --> 00:06:39,600
本来是英文的键盘是吧

176
00:06:39,600 --> 00:06:41,639
26位这个26个字符

177
00:06:41,639 --> 00:06:42,689
这个英文键盘

178
00:06:42,689 --> 00:06:46,709
如果我们在这里大家可以看选择一个email地址

179
00:06:46,709 --> 00:06:48,810
那么它除了这26个字母

180
00:06:48,810 --> 00:06:50,579
它还它多加了一个按键

181
00:06:50,579 --> 00:06:52,019
就是那个at符号啊

182
00:06:52,019 --> 00:06:54,399
因为为了方便你去编写邮箱

183
00:06:54,399 --> 00:06:54,759
对不对

184
00:06:54,759 --> 00:06:57,728
所以这其实就是一个输入键盘的样式啊

185
00:06:57,728 --> 00:07:03,620
依然是嗯只对这个呃手机啊有效果啊

186
00:07:03,620 --> 00:07:05,848
比如说这个就是电话号码

187
00:07:05,848 --> 00:07:07,439
也就是说如果我们选了这个

188
00:07:07,439 --> 00:07:10,918
我们点一下这里在手机上会弹出一个键盘

189
00:07:10,918 --> 00:07:13,600
但是这个键盘只有纯数字啊

190
00:07:13,600 --> 00:07:14,980
这些安卓苹果都一样

191
00:07:14,980 --> 00:07:16,180
只有纯数字

192
00:07:16,180 --> 00:07:17,860
就是它它是没有键盘的

193
00:07:17,860 --> 00:07:21,360
因为他认为你在这里是要输入一个电话号码的啊

194
00:07:21,360 --> 00:07:22,500
它没有英文字母啊

195
00:07:22,500 --> 00:07:23,519
它没有英文字母

196
00:07:23,519 --> 00:07:25,199
它是纯数字的

197
00:07:26,240 --> 00:07:28,699
然后比如说这里有个url啊

198
00:07:28,699 --> 00:07:29,298
有个u i l

199
00:07:29,298 --> 00:07:31,999
那么它就会有ur l的一些便捷输入的

200
00:07:31,999 --> 00:07:33,319
比如说它里面有斜杠

201
00:07:33,319 --> 00:07:36,560
然冒号呀啊这些可能都直接给你放到键盘上面

202
00:07:36,560 --> 00:07:37,790
为了你输入方面

203
00:07:37,790 --> 00:07:41,899
所以实际上就是你这个这个输入框啊

204
00:07:41,899 --> 00:07:43,279
它是做什么的啊

205
00:07:43,279 --> 00:07:45,740
你在这里就可以选择它对应的一些样式

206
00:07:45,740 --> 00:07:48,079
如果你不确定你会选择单行的

207
00:07:48,079 --> 00:07:49,040
就让他随意

208
00:07:49,040 --> 00:07:50,000
什么都能输啊

209
00:07:50,000 --> 00:07:51,860
反正输完以后就是一行的啊

210
00:07:51,860 --> 00:07:53,939
我只限定你是一行就可以了

211
00:07:54,540 --> 00:07:57,529
当然如果你是做一些比如说电脑上的呀

212
00:07:57,529 --> 00:07:58,519
电脑上的应用

213
00:07:58,519 --> 00:07:59,420
这个就无所谓了

214
00:07:59,420 --> 00:08:00,800
这俩都无所谓啊

215
00:08:00,800 --> 00:08:03,920
这个这个这个还有这个是可以设个密码啊

216
00:08:05,319 --> 00:08:08,079
然后再往下这个是字体的一个大小

217
00:08:08,079 --> 00:08:08,980
我们可以看一下啊

218
00:08:08,980 --> 00:08:10,180
这里面默认是个20

219
00:08:10,180 --> 00:08:11,920
你如果这个选择大的话

220
00:08:11,920 --> 00:08:13,540
字体就会更大

221
00:08:13,639 --> 00:08:17,149
然后再往下面行高啊

222
00:08:17,149 --> 00:08:18,379
如果你多行的话

223
00:08:18,379 --> 00:08:21,589
它每个行啊行高这个是可以设置一下的啊

224
00:08:21,589 --> 00:08:23,048
我们这里是40

225
00:08:23,048 --> 00:08:24,848
再往下这些其实都很简单

226
00:08:24,848 --> 00:08:26,588
什么字体颜色你可以随便去改

227
00:08:26,588 --> 00:08:27,369
默认是白色的

228
00:08:27,369 --> 00:08:29,790
我们刚才可以看到输入以后啊

229
00:08:29,790 --> 00:08:30,629
就是白颜色的

230
00:08:30,629 --> 00:08:34,519
你要是改个红颜色的在运行

231
00:08:35,679 --> 00:08:37,440
你看马上就变成红色了

232
00:08:37,440 --> 00:08:38,519
对不对啊

233
00:08:38,519 --> 00:08:40,318
这些都是非常简单啊

234
00:08:41,019 --> 00:08:42,389
这里大家注意

235
00:08:42,389 --> 00:08:42,840
你看

236
00:08:44,940 --> 00:08:48,659
这串文本和这串文本是不是就一样了

237
00:08:48,659 --> 00:08:50,639
那么这个就是占位符的文本

238
00:08:50,639 --> 00:08:53,470
占位符的文本不是让用户去输的

239
00:08:53,470 --> 00:08:54,669
是我们输的啊

240
00:08:54,669 --> 00:08:56,830
我们在这写上占位占位文本

241
00:08:56,830 --> 00:08:58,750
然后只要用户没有输入任何东西

242
00:08:58,750 --> 00:08:59,860
我们这里就显示

243
00:08:59,860 --> 00:09:02,490
比如说这里常常会写什么呀

244
00:09:02,490 --> 00:09:07,740
在此输入再次输入密码啊

245
00:09:07,740 --> 00:09:08,820
就类似于这种东西

246
00:09:08,820 --> 00:09:11,039
这样的话用户一看这个输入框就知道啊

247
00:09:11,039 --> 00:09:12,240
我这是输密码的

248
00:09:12,240 --> 00:09:13,828
他就会在这输密码

249
00:09:13,828 --> 00:09:14,578
对不对

250
00:09:14,578 --> 00:09:16,499
这个就是一个提示啊

251
00:09:16,499 --> 00:09:18,028
运行你看再次输入密码

252
00:09:18,028 --> 00:09:21,139
但是用户实际上一输入占位符就没了

253
00:09:21,139 --> 00:09:22,070
用户一删了

254
00:09:22,070 --> 00:09:23,059
他又出来了

255
00:09:25,159 --> 00:09:28,259
ok那么这是占位符啊

256
00:09:28,259 --> 00:09:30,600
它的一个字体的大小颜色啊

257
00:09:30,600 --> 00:09:34,350
占位符它也可以设置文字大小颜色啊

258
00:09:34,350 --> 00:09:35,850
这个也没什么说的

259
00:09:35,850 --> 00:09:36,899
和上面一样啊

260
00:09:36,899 --> 00:09:40,149
然后这个这个是限定文字个数

261
00:09:40,149 --> 00:09:41,440
比如说现在是个八

262
00:09:41,440 --> 00:09:44,289
所以现在我就算再怎么连着按

263
00:09:44,289 --> 00:09:47,730
最多大家可以看八个字儿没了啊

264
00:09:47,730 --> 00:09:50,950
所以在这里比如说你的密码限定多少位啊

265
00:09:50,950 --> 00:09:52,779
你就在这儿可以给它设置一下

266
00:09:52,779 --> 00:09:54,549
就最多多少位啊

267
00:09:54,549 --> 00:09:58,909
然后超过这些就就就超过这些就不让你输入了啊

268
00:09:58,909 --> 00:10:01,340
这个是可以在设置的啊

269
00:10:01,340 --> 00:10:04,399
像这这这俩基本上不太会用这个

270
00:10:04,399 --> 00:10:08,179
比如说你看保证当前输入框永远在视图之上啊

271
00:10:08,179 --> 00:10:09,970
一般我们就不用它

272
00:10:09,970 --> 00:10:11,649
我们也能自己通过代码保证

273
00:10:11,649 --> 00:10:12,789
是不是啊

274
00:10:12,789 --> 00:10:14,669
这个也没什么太大意义

275
00:10:14,669 --> 00:10:19,519
嗯这个是和网页的一些内容配合的啊

276
00:10:19,779 --> 00:10:20,980
大家可以看这个属性

277
00:10:20,980 --> 00:10:22,480
只有在web上面修改才有意义

278
00:10:22,480 --> 00:10:23,318
对不对

279
00:10:23,860 --> 00:10:27,340
那么也就是说它常用的属性就是在这个之上啊

280
00:10:27,340 --> 00:10:28,349
这些内容

281
00:10:28,349 --> 00:10:29,729
那么从这儿开始

282
00:10:29,729 --> 00:10:31,528
这下面有四项默认都是零

283
00:10:31,528 --> 00:10:32,158
对不对

284
00:10:32,158 --> 00:10:34,719
那么这四个呢都是回调

285
00:10:35,279 --> 00:10:37,318
都是回调啊

286
00:10:37,960 --> 00:10:40,200
比如说我们你看我们给他一个一

287
00:10:40,200 --> 00:10:41,850
这里面是不是又出回调了

288
00:10:41,850 --> 00:10:43,620
跟那个按钮啊

289
00:10:43,620 --> 00:10:45,250
跟按钮完全一样

290
00:10:45,250 --> 00:10:46,750
只是这是为什么

291
00:10:46,750 --> 00:10:47,889
这里有四个啊

292
00:10:47,889 --> 00:10:50,230
那是因为这四个方法啊

293
00:10:50,230 --> 00:10:52,809
这四个回调它调用的时机是不同的

294
00:10:52,809 --> 00:10:54,539
我们在这里来说一下啊

295
00:10:54,539 --> 00:10:57,610
大家可以看第一个是开始输入

296
00:10:57,610 --> 00:11:00,159
开始编辑的时候会调用一下

297
00:11:00,159 --> 00:11:02,919
也就是说我们如果给他一个事件啊

298
00:11:02,919 --> 00:11:04,779
给他关联一个事件的话

299
00:11:05,120 --> 00:11:07,700
那么什么这个方法啊

300
00:11:07,700 --> 00:11:08,899
什么时候会调用呢

301
00:11:08,899 --> 00:11:12,279
只要我们鼠标点一下这个输入框啊

302
00:11:12,279 --> 00:11:15,580
或者是我们手指在这个手机上面按一下这个输入框

303
00:11:15,580 --> 00:11:16,669
开始编辑了

304
00:11:16,669 --> 00:11:18,950
这个回调就会被调用一次啊

305
00:11:18,950 --> 00:11:20,559
它就会被调用一次

306
00:11:20,600 --> 00:11:23,960
然后这个呢大家可以看文本改变

307
00:11:23,960 --> 00:11:28,370
所以实际上这里我们要是给它关关联一个回调的话

308
00:11:28,370 --> 00:11:30,649
只要上面的文本发生改变

309
00:11:30,649 --> 00:11:32,629
比如说我们按一个按键改变了

310
00:11:32,629 --> 00:11:34,009
删或者删除等等

311
00:11:34,009 --> 00:11:35,659
只要上面的文本发生改变

312
00:11:35,659 --> 00:11:37,019
都会掉下去

313
00:11:37,019 --> 00:11:38,818
这个方法啊

314
00:11:39,379 --> 00:11:42,610
比如说这里你要做个很简单的操作

315
00:11:42,610 --> 00:11:43,779
按一个按键

316
00:11:44,860 --> 00:11:46,469
然后发一个音

317
00:11:46,469 --> 00:11:48,818
就给人的感觉是好像按键盘

318
00:11:48,818 --> 00:11:50,558
然后有声音也一样啊

319
00:11:50,558 --> 00:11:52,538
就是按一个按键发个声音怎么办

320
00:11:52,538 --> 00:11:54,339
你就可以写个这个回调

321
00:11:54,339 --> 00:11:57,649
然后每一次在这个回调里面播放一个按键的声音

322
00:11:57,649 --> 00:11:59,299
是不是比如叮一声

323
00:11:59,299 --> 00:12:01,279
这样的话用户就会怎样

324
00:12:01,279 --> 00:12:05,299
随便只要它输入内容就会叮叮叮叮就一直响哈哈

325
00:12:05,299 --> 00:12:07,610
这就是文字改变就会调这个回调

326
00:12:07,610 --> 00:12:11,500
然后这个呢当编辑模式结束的时候

327
00:12:11,519 --> 00:12:13,559
当编辑模式结束的时候是什么

328
00:12:13,559 --> 00:12:14,279
是结束的时候

329
00:12:14,279 --> 00:12:15,979
你看你在这输内容吧

330
00:12:19,149 --> 00:12:21,879
或者是在这个手机上面点到旁边

331
00:12:21,879 --> 00:12:24,330
或者是我们选中另外一个输入框了

332
00:12:24,330 --> 00:12:27,269
那么这个输入框呢那个光标不闪了

333
00:12:27,269 --> 00:12:27,809
对不对

334
00:12:27,809 --> 00:12:31,919
那么这时候呢他就算是编辑完成了

335
00:12:32,100 --> 00:12:34,620
当编辑结束的时候就会调这个方法

336
00:12:34,620 --> 00:12:36,570
也就是说这三个是编辑开始

337
00:12:36,570 --> 00:12:38,039
这是只要编辑中

338
00:12:38,039 --> 00:12:39,419
然后一改变就会调用

339
00:12:39,419 --> 00:12:44,370
这是编辑结束会调用最后一个return

340
00:12:44,370 --> 00:12:45,230
什么意思

341
00:12:45,230 --> 00:12:47,029
就是按了那个回车啊

342
00:12:47,029 --> 00:12:50,110
就是我们要比如说输完密码按下回车

343
00:12:50,110 --> 00:12:53,590
或者说在手机上面弹出来的那个键盘右下角

344
00:12:53,590 --> 00:12:55,269
咱们刚才说的那个蓝色的按钮

345
00:12:55,269 --> 00:12:56,529
对不对啊

346
00:12:56,529 --> 00:13:00,129
就是弹出来的键盘右下角一个回车按钮啊

347
00:13:00,129 --> 00:13:01,759
你点一下那个回车按钮

348
00:13:01,759 --> 00:13:04,340
其实那个按钮默认是没有任何意义的

349
00:13:04,340 --> 00:13:05,269
没有作用的

350
00:13:05,269 --> 00:13:07,839
但是在这里你就可以按一下按钮

351
00:13:07,839 --> 00:13:09,818
就会关联一个回调

352
00:13:09,818 --> 00:13:12,799
在这个回调里面你就可以做一些事儿啊

353
00:13:12,799 --> 00:13:13,820
你就可以做一些事

354
00:13:13,820 --> 00:13:19,460
那么这个呢就是呃其实就算是回车按钮的这样的一个事件了啊

355
00:13:19,460 --> 00:13:21,059
这个是按钮的事件

356
00:13:22,279 --> 00:13:25,000
那么这就是这四个事件啊

357
00:13:25,000 --> 00:13:26,200
都是非常简单

358
00:13:26,200 --> 00:13:27,309
大家可以试试

359
00:13:27,309 --> 00:13:30,120
那么it box啊

360
00:13:30,120 --> 00:13:32,340
咱们知道这么多就足够用了啊

361
00:13:32,340 --> 00:13:33,179
足够用了

362
00:13:33,179 --> 00:13:35,519
那我们下节课再说其他内容啊

363
00:13:35,519 --> 00:13:38,519
我们这节课就说一下editor box就好了


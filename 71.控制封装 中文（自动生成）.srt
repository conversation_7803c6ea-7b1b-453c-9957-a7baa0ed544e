1
00:00:09,400 --> 00:00:14,580
ok啊我们这节课来说一下我们的这个移动嗯

2
00:00:14,580 --> 00:00:18,300
那么对于我们这种移动而言

3
00:00:18,300 --> 00:00:20,280
对于我们2d游戏里面的移动而言

4
00:00:20,280 --> 00:00:23,030
其实最容易做的啊

5
00:00:23,030 --> 00:00:25,280
我们最好的一个做法是这样的

6
00:00:25,280 --> 00:00:26,839
给它画一个轴

7
00:00:27,239 --> 00:00:30,480
这个轴呢是由x轴

8
00:00:30,480 --> 00:00:32,920
还有一个y轴组成啊

9
00:00:32,920 --> 00:00:35,279
一个轴x轴和y轴组成

10
00:00:43,740 --> 00:00:45,859
啊那么这个轴是什么意思啊

11
00:00:45,859 --> 00:00:46,759
那大家想啊

12
00:00:46,759 --> 00:00:48,238
如果一个人啊

13
00:00:48,238 --> 00:00:50,338
我希望让他在2d游戏世界里面

14
00:00:50,338 --> 00:00:51,598
比如说这个就是我们主角

15
00:00:51,598 --> 00:00:53,729
我希望让他可以向上移动

16
00:00:53,729 --> 00:00:57,270
向左向右以及左上右上这样去移动

17
00:00:57,270 --> 00:01:00,469
实际上大家想是不是就是在一个轴内去移动的

18
00:01:00,469 --> 00:01:03,520
在一个轴内沿着这个轴向上移动

19
00:01:03,520 --> 00:01:04,390
向右移动

20
00:01:04,390 --> 00:01:05,769
向下向左移动

21
00:01:05,769 --> 00:01:07,090
以及左上右上

22
00:01:07,090 --> 00:01:11,569
是不是实际上一个物体的移动就是在一个轴内去进行移动的

23
00:01:11,569 --> 00:01:12,890
可以看出来吧

24
00:01:12,890 --> 00:01:15,290
那么所以我们在这里创建一个轴

25
00:01:15,290 --> 00:01:20,060
那么在这个轴我们给它设定x轴

26
00:01:20,060 --> 00:01:21,799
也就是水平轴的话

27
00:01:21,799 --> 00:01:27,939
最右边我们给它一个数值是一最左边给它个数值是-1

28
00:01:29,980 --> 00:01:32,280
那么上面和下面也一样

29
00:01:32,280 --> 00:01:33,480
对于垂直轴而言

30
00:01:33,480 --> 00:01:34,680
最上面是一

31
00:01:34,680 --> 00:01:36,060
最下面是多少

32
00:01:36,060 --> 00:01:37,060
是-1

33
00:01:37,079 --> 00:01:40,280
所以实际上我现在希望的就是默认情况下

34
00:01:40,280 --> 00:01:42,260
如果这个物体在中心点

35
00:01:42,260 --> 00:01:44,120
那么它的位置是零零啊

36
00:01:44,120 --> 00:01:45,920
它在这个轴里面的位置是零零

37
00:01:45,920 --> 00:01:47,950
也就意味着它是不移动的

38
00:01:47,950 --> 00:01:50,019
当我往右移动的话

39
00:01:50,040 --> 00:01:51,989
也就是我在这个位置的话

40
00:01:51,989 --> 00:01:53,370
我的y轴是零

41
00:01:55,019 --> 00:01:56,259
变成一

42
00:01:56,459 --> 00:01:57,359
变成一

43
00:01:57,359 --> 00:02:01,500
也就意味着我现在需要x轴去进行移动

44
00:02:01,500 --> 00:02:02,849
往哪里去移动

45
00:02:02,849 --> 00:02:05,840
往一的方向去进行移动啊

46
00:02:05,840 --> 00:02:07,430
往e的方向去进行移动

47
00:02:07,430 --> 00:02:14,659
也就是说现在我们可以通过一个坐标来描述当前一个物体的移动

48
00:02:16,919 --> 00:02:21,580
比如说现在啊这个坐标零零

49
00:02:21,659 --> 00:02:23,939
那就代表当前物体不移动

50
00:02:23,939 --> 00:02:26,969
一逗号零就代表物体向右边移动

51
00:02:26,969 --> 00:02:30,330
零逗号一就代表物体向左边移动啊

52
00:02:30,330 --> 00:02:31,319
错了错了

53
00:02:31,500 --> 00:02:35,280
-1逗号零就代表物体向左边移动

54
00:02:35,280 --> 00:02:37,319
零逗号一代表向上移动

55
00:02:37,319 --> 00:02:41,288
零逗号-1代表物体向下移动

56
00:02:41,288 --> 00:02:44,349
一逗号一代表物体向右上移动

57
00:02:44,349 --> 00:02:45,340
对不对

58
00:02:45,340 --> 00:02:50,500
然后一逗号-1代表向左上移动啊

59
00:02:50,500 --> 00:02:53,500
-1逗号-1是左下移动

60
00:02:53,500 --> 00:02:58,780
然后一逗号-1代表右下移动

61
00:02:58,780 --> 00:03:04,338
所以实际上通过这个轴我们就发现我们可以啊

62
00:03:04,338 --> 00:03:06,019
除了它默认不移动以外

63
00:03:06,019 --> 00:03:08,118
它实际上就是一个八方向的移动

64
00:03:08,118 --> 00:03:08,598
对不对

65
00:03:08,598 --> 00:03:11,719
你看123456788方向的移动

66
00:03:11,719 --> 00:03:13,900
通过八种八个坐标

67
00:03:13,900 --> 00:03:16,780
我们就描述了当前这个物体的移动了啊

68
00:03:16,780 --> 00:03:18,460
你是向哪边移动的

69
00:03:18,460 --> 00:03:21,750
对于2d游戏八坐标完全够了

70
00:03:21,750 --> 00:03:23,580
甚至如果在这里简化的话

71
00:03:23,580 --> 00:03:26,060
我们常常不希望它斜着移动啊

72
00:03:26,060 --> 00:03:28,039
我们只需要用这四个坐标啊

73
00:03:28,039 --> 00:03:29,539
就是四方向就ok了

74
00:03:29,539 --> 00:03:32,969
用这四个坐标来描述当前物体的移动

75
00:03:32,969 --> 00:03:35,639
所以实际上我们在这里就要封装一个类

76
00:03:35,639 --> 00:03:38,169
这个类呢通过这个类啊

77
00:03:38,169 --> 00:03:41,110
这个类的作用就是给我们提供这样的一个坐标啊

78
00:03:41,110 --> 00:03:43,780
提供这样的一个坐标就可以了啊

79
00:03:44,179 --> 00:03:49,399
那ok我们现在来看一下这个类怎样去进行一个实现

80
00:03:49,399 --> 00:03:51,079
实际上很多游戏引擎里面

81
00:03:51,079 --> 00:03:55,699
它他默认就给我们提供了这样的一个坐标啊

82
00:03:55,699 --> 00:03:57,560
但是我们cs里面并没有啊

83
00:03:57,560 --> 00:03:59,939
所以我们必须手动去封装一下

84
00:04:01,659 --> 00:04:07,360
首先我们这里呃创建一个脚本脚本

85
00:04:07,360 --> 00:04:09,039
比如说我们叫input啊

86
00:04:09,039 --> 00:04:13,590
也就是说这个脚本不是用来当做组件用的

87
00:04:13,590 --> 00:04:16,778
也就是说它不会说拖到这边当做一个组件用

88
00:04:16,778 --> 00:04:20,918
他呢就是为了提供一下我们当前的这个坐标啊

89
00:04:20,918 --> 00:04:22,980
代表我们想移动的坐标

90
00:04:23,500 --> 00:04:24,860
我们打开它

91
00:04:26,040 --> 00:04:27,620
因为它不作为组件

92
00:04:27,620 --> 00:04:30,639
所以它这些都是没必要的

93
00:04:31,360 --> 00:04:35,620
我们直接只是需要知道它是input这个类就可以了

94
00:04:38,740 --> 00:04:40,379
唉实际的干干净净

95
00:04:40,379 --> 00:04:42,720
也就是说它在里面实际上没有任何东西

96
00:04:42,720 --> 00:04:46,759
它主要就是用来封装我们的呃移动的啊

97
00:04:46,759 --> 00:04:49,100
呃就是封装我们这个移动坐标的

98
00:04:49,279 --> 00:04:51,740
那这个坐标有x有y对不对

99
00:04:51,740 --> 00:04:55,339
有x有y那么在这里呃

100
00:04:55,339 --> 00:04:58,699
首先为了把当前这个类啊用起来更加方便

101
00:04:58,699 --> 00:05:00,230
因为我们大家想很多

102
00:05:00,230 --> 00:05:01,100
在游戏里面

103
00:05:01,100 --> 00:05:06,060
很多地儿可能都需要用到我们这个移动的这个移动的这个坐标

104
00:05:06,439 --> 00:05:09,620
那么所以我们要把它写成一个单例

105
00:05:09,620 --> 00:05:12,069
单例忘了的可以去回顾一下啊

106
00:05:12,069 --> 00:05:13,149
写这个单例的话

107
00:05:13,149 --> 00:05:15,730
也就意味着哎我要把它做成一个单例了

108
00:05:15,730 --> 00:05:19,220
那我现在先要写一个静态的变量

109
00:05:19,220 --> 00:05:20,779
变量的类型呢

110
00:05:20,779 --> 00:05:23,899
就是它本身默认是

111
00:05:23,899 --> 00:05:28,959
那然后呢我要写一个get方法

112
00:05:32,800 --> 00:05:34,709
属性的get方法啊

113
00:05:34,709 --> 00:05:37,199
忘了的还是去回顾一下

114
00:05:38,240 --> 00:05:41,319
如果我们使用这个属性的话

115
00:05:41,319 --> 00:05:45,139
这个这个这个instance这个变量为空

116
00:05:45,420 --> 00:05:49,540
那我们在这里就要把它实例化一个

117
00:05:53,500 --> 00:05:56,069
return this on instance

118
00:05:56,069 --> 00:06:01,180
这样的话外界想使用我们的这个呃单位

119
00:06:01,180 --> 00:06:02,379
他就只能怎样去用

120
00:06:02,379 --> 00:06:06,410
比如说你在外界只能input.instance

121
00:06:06,410 --> 00:06:09,680
你看首先这个instance找不见

122
00:06:10,970 --> 00:06:13,459
你找见的只能是这个instance啊

123
00:06:13,459 --> 00:06:14,269
只能是它

124
00:06:14,269 --> 00:06:18,139
它的话实际上看似掉了一个呃变量

125
00:06:18,139 --> 00:06:19,730
实际上它掉了一个属性

126
00:06:19,730 --> 00:06:22,009
这个属性就是这这一串方法

127
00:06:22,009 --> 00:06:24,089
这一串方法内容也很简单

128
00:06:24,089 --> 00:06:26,129
首先他判断这个变量是不是空

129
00:06:26,129 --> 00:06:27,730
如果如果是空的话

130
00:06:27,730 --> 00:06:30,800
就实例化一下当前的这个类

131
00:06:31,579 --> 00:06:32,990
如果不为空

132
00:06:32,990 --> 00:06:36,759
那么他就直接把当前这个变量返回回去就行了

133
00:06:36,759 --> 00:06:39,129
也就是说你第一次调这个属性的时候

134
00:06:39,129 --> 00:06:42,110
他肯定会进到if里面实例化一个对象

135
00:06:42,110 --> 00:06:44,329
然后如果你第二行还调用它了

136
00:06:44,329 --> 00:06:46,250
你第二次调用instance的时候

137
00:06:46,250 --> 00:06:47,449
这时候再进来

138
00:06:47,449 --> 00:06:49,310
他判断这个现在就不为空了

139
00:06:49,310 --> 00:06:53,279
他可能直接就return还是单利啊

140
00:06:53,279 --> 00:06:55,439
这块这块内容没什么说的啊

141
00:06:55,439 --> 00:06:57,329
就是我们把它做成一个单位

142
00:06:57,329 --> 00:07:00,319
这个单位里面有这个坐标

143
00:07:00,319 --> 00:07:05,620
这个坐标我们往往往往把它分为这个水平轴和垂直轴

144
00:07:09,040 --> 00:07:10,920
啊为什么分成这两个

145
00:07:10,920 --> 00:07:15,839
因为很多游戏群里面都是呃分成了水平轴和垂直轴

146
00:07:15,839 --> 00:07:19,500
水平轴代表的就是这个轴啊

147
00:07:19,500 --> 00:07:21,420
它的值就是零一-1

148
00:07:21,420 --> 00:07:24,420
垂直轴就是这个零一-1啊

149
00:07:24,420 --> 00:07:26,160
那之所以我们也这样用

150
00:07:26,160 --> 00:07:30,339
主要也就是为了让大家一个就是呃用熟了以后

151
00:07:30,339 --> 00:07:33,560
如果大家后面去转游戏引擎的话

152
00:07:33,579 --> 00:07:35,980
你就会很好去转哈

153
00:07:35,980 --> 00:07:37,240
很方便去转

154
00:07:39,399 --> 00:07:40,920
这是水平轴啊

155
00:07:40,920 --> 00:07:41,819
horizontal

156
00:07:41,819 --> 00:07:42,990
垂直轴

157
00:07:42,990 --> 00:07:44,300
vertical

158
00:07:45,980 --> 00:07:47,319
默认都是零啊

159
00:07:47,319 --> 00:07:48,579
默认是非移动状态

160
00:07:48,579 --> 00:07:49,449
对不对

161
00:07:49,449 --> 00:07:53,620
然后当前这个类有个这个初始化方法

162
00:07:55,100 --> 00:07:56,889
这个就是我们的初始化方法

163
00:07:56,889 --> 00:08:00,579
还有同学说为什么不用start方法呢

164
00:08:00,579 --> 00:08:01,720
一定要记住啊

165
00:08:01,720 --> 00:08:08,639
start update那些是我们当前类作为组件的子类的时候才能使用的啊

166
00:08:08,639 --> 00:08:10,319
那是组件的方法

167
00:08:10,319 --> 00:08:13,560
我们现在已经不继承于组建了

168
00:08:13,560 --> 00:08:13,980
对不对

169
00:08:13,980 --> 00:08:15,939
我们把后面的继承都删了

170
00:08:16,740 --> 00:08:22,160
你看这里这个继承我们如果不继承于组件

171
00:08:22,160 --> 00:08:24,290
我们单独作为一个单独的类而言

172
00:08:24,290 --> 00:08:26,060
这是他的初始化方法啊

173
00:08:26,060 --> 00:08:27,199
一定要记着啊

174
00:08:28,139 --> 00:08:29,418
在初始化方法里面

175
00:08:29,418 --> 00:08:31,699
我们要监听键盘

176
00:08:31,699 --> 00:08:34,099
按下和抬起

177
00:08:39,360 --> 00:08:41,299
通过键按下和抬起

178
00:08:41,299 --> 00:08:43,629
我们就可以怎样呃

179
00:08:43,629 --> 00:08:47,799
给他这两个数字进行一个改变啊

180
00:08:48,019 --> 00:08:50,120
首先我们来监听键盘

181
00:08:50,120 --> 00:08:52,698
按下system

182
00:08:54,460 --> 00:08:56,639
大家去边写边理解啊

183
00:08:57,960 --> 00:09:02,980
system.invent system

184
00:09:04,179 --> 00:09:07,960
大写点invent tap

185
00:09:07,960 --> 00:09:11,049
点k大

186
00:09:11,049 --> 00:09:11,860
对不对

187
00:09:11,860 --> 00:09:12,899
k大

188
00:09:14,200 --> 00:09:16,019
那么这是第一个参数

189
00:09:16,019 --> 00:09:18,240
就是监听的这个事件

190
00:09:18,240 --> 00:09:20,220
就是键盘按下的事件

191
00:09:20,220 --> 00:09:23,539
监听到了以后会传来一个回调

192
00:09:23,840 --> 00:09:25,438
英文回调

193
00:09:27,519 --> 00:09:29,759
那么在这个里面我们要做什么事呢

194
00:09:29,759 --> 00:09:33,509
非常简单判断一下你按的这个键

195
00:09:33,509 --> 00:09:35,578
按键的这个内容

196
00:09:35,899 --> 00:09:45,179
event.k code就等于一个cc点红点mk.w

197
00:09:45,179 --> 00:09:48,899
我们希望w a s d来控制我们的这个移动

198
00:09:48,899 --> 00:09:51,840
所以w a s d就来控制我们这个轴

199
00:09:51,840 --> 00:09:54,710
我们希望按w的时候

200
00:09:54,710 --> 00:09:56,240
按w的时候

201
00:09:56,240 --> 00:09:58,139
这个数字就会变成一

202
00:09:58,340 --> 00:10:00,470
按s的话就会变成-1

203
00:10:00,470 --> 00:10:02,509
按a就变成-1

204
00:10:02,509 --> 00:10:05,110
按d会变成一

205
00:10:05,110 --> 00:10:10,360
这样的话w a s d实际上就会在这些坐标之间进行产生一个变化了

206
00:10:10,360 --> 00:10:11,318
对不对

207
00:10:13,960 --> 00:10:16,740
那么如果是w怎么办了

208
00:10:16,740 --> 00:10:20,039
那就是this点它是影响的是垂直

209
00:10:20,039 --> 00:10:21,720
是不是垂直轴

210
00:10:21,720 --> 00:10:23,850
垂直轴就会变成一啊

211
00:10:23,850 --> 00:10:24,929
垂直轴就变成一

212
00:10:24,929 --> 00:10:27,120
否则的话我们再来判断

213
00:10:28,539 --> 00:10:29,259
否则的话

214
00:10:29,259 --> 00:10:34,339
如果点k code等于

215
00:10:37,100 --> 00:10:37,899
s的话

216
00:10:39,399 --> 00:10:41,958
那么垂直轴就会变成-1

217
00:10:42,820 --> 00:10:45,070
这是垂直方向的啊

218
00:10:45,070 --> 00:10:47,019
我们再来写水平方向

219
00:10:47,019 --> 00:10:48,318
如果

220
00:10:50,500 --> 00:10:54,500
点k code等于

221
00:10:59,919 --> 00:11:01,299
如果等于a的话

222
00:11:02,860 --> 00:11:05,000
那么它呢等于-1

223
00:11:05,960 --> 00:11:07,578
否则的话

224
00:11:10,220 --> 00:11:14,859
如果invent jn k code等于

225
00:11:19,559 --> 00:11:21,809
a然后就是d对不对

226
00:11:21,809 --> 00:11:24,509
d的话就是水平轴就变成一

227
00:11:24,509 --> 00:11:27,029
那这样的话当前这个坐标轴就有了

228
00:11:27,029 --> 00:11:29,460
但是当我们抬起来的时候

229
00:11:29,460 --> 00:11:30,960
当键盘抬起来的时候

230
00:11:30,960 --> 00:11:32,250
比如说我们按了d了

231
00:11:32,250 --> 00:11:33,539
这个数值变成一了

232
00:11:33,539 --> 00:11:35,460
只要我们抬起这个数值

233
00:11:35,460 --> 00:11:38,080
我们要给它怎样归零的啊

234
00:11:38,080 --> 00:11:38,500
你不能

235
00:11:38,500 --> 00:11:40,419
你比如说我按下u它这个键

236
00:11:40,419 --> 00:11:41,799
它这个数字一直为一

237
00:11:41,799 --> 00:11:42,659
这样不行

238
00:11:42,659 --> 00:11:44,279
只有我按下去的时候

239
00:11:44,279 --> 00:11:45,779
它这个数字为一啊

240
00:11:45,779 --> 00:11:49,070
那所以这时候如果我们抬起的话

241
00:11:49,070 --> 00:11:50,210
如果抬起的话

242
00:11:50,210 --> 00:11:52,190
我要给当前的轴归零

243
00:11:52,190 --> 00:11:55,179
所以键盘抬起我们也要写一下呃

244
00:11:55,179 --> 00:11:58,339
其实就是我先给他拿过来

245
00:11:58,700 --> 00:12:00,019
就是不是k down了

246
00:12:00,019 --> 00:12:02,159
就是ky up

247
00:12:02,519 --> 00:12:04,889
那如果我们现在按了w

248
00:12:04,889 --> 00:12:07,019
如果我现在按了上

249
00:12:07,019 --> 00:12:08,698
并且

250
00:12:10,759 --> 00:12:13,950
这个数值呢等于一啊

251
00:12:13,950 --> 00:12:15,750
就证明我现在不但按了w

252
00:12:15,750 --> 00:12:19,769
而且我当我当时确实呃作为一个准确的判断

253
00:12:19,769 --> 00:12:22,679
就是当前这个轴这个数值确实唯一

254
00:12:22,679 --> 00:12:27,720
那我在这里干嘛就把它变回成零啊

255
00:12:27,720 --> 00:12:28,799
变回成零

256
00:12:28,799 --> 00:12:30,979
下面都是一样的操作

257
00:12:31,539 --> 00:12:36,568
如果ios并且当前这个为-1

258
00:12:36,568 --> 00:12:39,028
就证明你现在确实按照s呢

259
00:12:39,028 --> 00:12:40,948
然后人也在往下走啊

260
00:12:40,948 --> 00:12:42,568
就这个轴确实也是-1

261
00:12:42,568 --> 00:12:44,078
我也给它归成零

262
00:12:44,659 --> 00:12:46,549
左右也是一样的啊

263
00:12:46,549 --> 00:12:47,809
左右也是一样的

264
00:12:47,809 --> 00:12:52,179
并且this.horizontal等于-1

265
00:12:53,039 --> 00:12:54,480
那他就归为零

266
00:12:55,500 --> 00:12:56,500
这个

267
00:12:58,000 --> 00:13:01,698
如果this.hental等于一

268
00:13:02,080 --> 00:13:04,000
我给它归为零啊

269
00:13:04,000 --> 00:13:05,740
这样的话就是上下左右

270
00:13:05,740 --> 00:13:07,379
你按了w a s d

271
00:13:07,440 --> 00:13:12,549
然后这个垂直和水平轴就会在一-1之间来回变化

272
00:13:12,549 --> 00:13:14,350
但是如果你抬起了某个键

273
00:13:14,350 --> 00:13:15,970
它可能就会变成零啊

274
00:13:15,970 --> 00:13:16,990
它就会变成零

275
00:13:16,990 --> 00:13:18,639
非常简单啊非常简单

276
00:13:18,639 --> 00:13:22,039
那当前这个input就算完成了

277
00:13:22,120 --> 00:13:24,399
那么这个这时候注意啊

278
00:13:24,399 --> 00:13:25,480
我们还没有去用

279
00:13:25,480 --> 00:13:28,659
我们只是封装的一个键盘控制的这样的一个类

280
00:13:28,659 --> 00:13:30,919
这个类没什么作用啊

281
00:13:30,919 --> 00:13:35,149
这个单例类只是负责去进行我们这个按键输入的

282
00:13:35,149 --> 00:13:40,779
那这时候我们再回来再来创建主角类

283
00:13:41,720 --> 00:13:42,860
玩家控制类

284
00:13:42,860 --> 00:13:44,659
现在玩家其实还没有脚本呢

285
00:13:44,659 --> 00:13:45,080
对不对

286
00:13:45,080 --> 00:13:49,399
我们再来一个plear ctrl玩家控制类

287
00:13:49,399 --> 00:13:53,019
那我先把这个玩家拖出来

288
00:13:53,559 --> 00:13:57,019
把这个脚本挂玩家身上

289
00:13:57,840 --> 00:13:59,720
预设题保存一下

290
00:13:59,720 --> 00:14:01,438
再把玩家删掉

291
00:14:02,539 --> 00:14:03,740
明白这个操作吧

292
00:14:03,740 --> 00:14:06,559
这个操作是为了让我们这个预设体身上

293
00:14:06,559 --> 00:14:09,779
你看也挂载上我们这个脚本啊

294
00:14:09,779 --> 00:14:11,519
让预设体身上挂载上那个脚本

295
00:14:13,200 --> 00:14:14,078
ok啊

296
00:14:15,919 --> 00:14:17,299
场景没有保存

297
00:14:17,299 --> 00:14:18,559
我们应该保存一下场景

298
00:14:18,559 --> 00:14:20,458
要不然崩了就没有了

299
00:14:21,159 --> 00:14:25,179
那在这里玩家脚本我们也加到这个玩家预设体身上了

300
00:14:25,179 --> 00:14:27,490
我们就可以编写这个玩家脚本了

301
00:14:27,490 --> 00:14:29,320
在这个里面啊

302
00:14:29,320 --> 00:14:30,479
在这个里面

303
00:14:34,919 --> 00:14:36,320
ok我们继续啊

304
00:14:36,320 --> 00:14:37,370
接了个电话

305
00:14:37,370 --> 00:14:44,979
那么在这首先这个玩家类我们叫pyl control fler control

306
00:14:45,139 --> 00:14:47,240
那么对于玩家移动啊

307
00:14:47,240 --> 00:14:48,320
大家想移动而言

308
00:14:48,320 --> 00:14:50,960
这个东西一定要有一个东西叫做速度啊

309
00:14:50,960 --> 00:14:52,980
就是你移动的速度有多快呢

310
00:14:52,980 --> 00:14:55,200
我们在这儿给它写成一个属性

311
00:14:55,200 --> 00:14:56,669
方便我们修改

312
00:14:56,669 --> 00:14:58,200
比如说默认情况下

313
00:14:58,200 --> 00:15:01,059
我希望它的速度是20

314
00:15:01,059 --> 00:15:02,080
是20

315
00:15:02,080 --> 00:15:03,460
这时候怎样移动

316
00:15:03,460 --> 00:15:04,840
因为每一帧都可以移动

317
00:15:04,840 --> 00:15:07,840
所以在update里面我们就可以编写一下它的移动了

318
00:15:07,840 --> 00:15:11,080
那么它的移动啊我们怎样去写

319
00:15:11,080 --> 00:15:11,740
非常简单

320
00:15:11,740 --> 00:15:15,929
首先x方向的移动就是this node.x它本身的位置

321
00:15:17,490 --> 00:15:18,809
移动量怎么算

322
00:15:18,809 --> 00:15:23,700
就是速度乘以个dt啊

323
00:15:23,700 --> 00:15:25,379
一定要乘以一个dt啊

324
00:15:25,379 --> 00:15:26,519
为什么乘以dt

325
00:15:26,519 --> 00:15:28,559
这里的dt我们之前说过

326
00:15:28,559 --> 00:15:31,859
他是代表真的一个时间间隔

327
00:15:34,139 --> 00:15:39,149
如果不乘以它这个速度默认的单位是每一帧移动多少

328
00:15:39,149 --> 00:15:40,980
这里20代表的20像素

329
00:15:40,980 --> 00:15:41,750
对不对

330
00:15:41,750 --> 00:15:43,129
如果这里不乘以dt

331
00:15:43,129 --> 00:15:45,710
那这个速度就代表每帧移动20

332
00:15:45,710 --> 00:15:46,490
大家想啊

333
00:15:46,490 --> 00:15:47,750
一帧移动20

334
00:15:47,750 --> 00:15:49,339
这个速度太快了

335
00:15:49,339 --> 00:15:50,719
而且设备不同

336
00:15:50,719 --> 00:15:52,759
就是说白了就是比如说我设备好

337
00:15:52,759 --> 00:15:53,658
我的帧数高

338
00:15:53,658 --> 00:15:54,798
你设备不好

339
00:15:54,798 --> 00:15:56,500
你设备可能帧数低

340
00:15:56,500 --> 00:15:58,029
有这样的情况对不对

341
00:15:58,029 --> 00:16:02,149
那么如果我们按帧数去计算速度的话

342
00:16:02,149 --> 00:16:04,490
可能导致我们两个设备上运行起来

343
00:16:04,490 --> 00:16:07,139
发现玩家的移动速度都是不同的

344
00:16:07,200 --> 00:16:09,089
那么为了统一速度

345
00:16:09,089 --> 00:16:11,969
我们这个速度往往是以秒为单位的

346
00:16:11,969 --> 00:16:13,408
就一秒移动多少

347
00:16:13,408 --> 00:16:17,019
那这里的速度我也希望它变成一秒移动20像素

348
00:16:17,019 --> 00:16:18,519
怎样才能给了

349
00:16:18,519 --> 00:16:21,269
怎样才能把这个速度从帧转成秒

350
00:16:21,269 --> 00:16:21,870
很简单

351
00:16:21,870 --> 00:16:23,610
就乘以一下dt就行了啊

352
00:16:23,610 --> 00:16:25,740
所以这类似就类似于一个公式一样

353
00:16:25,740 --> 00:16:29,190
你就知道哎这里一个速度默认它是以真为单位的

354
00:16:29,190 --> 00:16:31,639
一针20 20像素

355
00:16:31,639 --> 00:16:34,639
现在乘一个dt就变成一秒20像素了

356
00:16:35,059 --> 00:16:37,549
然后最后还要乘以一个方向

357
00:16:37,549 --> 00:16:42,249
方向的话就是我们的那个轴input.instance

358
00:16:42,249 --> 00:16:44,649
得到单位里面的水平方向

359
00:16:44,649 --> 00:16:46,629
因为它的话无非就三个数值

360
00:16:46,629 --> 00:16:48,850
零一-11-1

361
00:16:48,850 --> 00:16:53,769
在这里实际上大家想在乘法里面无非就代表零就是不移动零完了

362
00:16:53,769 --> 00:16:59,240
这里面全是零一的话就代表它是一个正正的一个速度

363
00:16:59,240 --> 00:17:02,299
-1的话一相乘就变成一个负的速度了

364
00:17:02,299 --> 00:17:03,559
是不是啊

365
00:17:03,559 --> 00:17:05,759
所以实际上就这样一回事啊

366
00:17:07,099 --> 00:17:07,940
非常简单

367
00:17:07,940 --> 00:17:09,349
y的话也是一样的

368
00:17:09,349 --> 00:17:11,720
拿它本身的y轴坐标乘以一下

369
00:17:11,720 --> 00:17:15,318
要移动的速度乘以dt再乘以一下

370
00:17:15,420 --> 00:17:18,759
这边无非就变成了个水平轴上了

371
00:17:19,318 --> 00:17:23,480
你拿到一个水平轴的这样的一个呃数值

372
00:17:25,880 --> 00:17:30,980
那么其实啊在这里这样的一个轴

373
00:17:30,980 --> 00:17:32,599
我们之前学过向量

374
00:17:32,599 --> 00:17:33,380
对不对

375
00:17:33,380 --> 00:17:38,319
大家就可以把它理解为它是一个要移动的向量哈

376
00:17:38,319 --> 00:17:40,119
就是要移动的向量啊

377
00:17:40,119 --> 00:17:41,680
是要往哪个方向移动啊

378
00:17:41,680 --> 00:17:42,940
因为它向量永远是一

379
00:17:42,940 --> 00:17:43,359
对不对

380
00:17:43,359 --> 00:17:47,559
所以这边我们的这个轴大小就是个一-11-1啊

381
00:17:47,559 --> 00:17:50,039
你就可以这样去理解啊

382
00:17:50,039 --> 00:17:54,420
所以就是乘这就是速度乘以一下他要移动的向量啊

383
00:17:54,420 --> 00:17:55,529
就是这样的一个意思

384
00:17:55,529 --> 00:18:00,039
移动的方向一或者-1在这里就代表右或者是左

385
00:18:00,039 --> 00:18:02,980
这个一-1就代表上或者下哈

386
00:18:02,980 --> 00:18:04,500
就是方向对不对

387
00:18:04,640 --> 00:18:07,130
那现在我们写完以后就ok了

388
00:18:07,130 --> 00:18:08,480
这个移动就完成了

389
00:18:08,480 --> 00:18:10,319
所以大家实际上看一下

390
00:18:10,359 --> 00:18:12,579
对于这个移动的封装而言

391
00:18:12,579 --> 00:18:14,140
只要封装好了

392
00:18:14,140 --> 00:18:16,680
这时候剩下的就是怎样的

393
00:18:16,680 --> 00:18:19,439
就是使用使用起来很简单啊

394
00:18:19,439 --> 00:18:20,338
就是封装麻烦

395
00:18:20,338 --> 00:18:21,778
所以封装一次以后

396
00:18:21,778 --> 00:18:23,219
真正再去使用的话

397
00:18:23,219 --> 00:18:24,449
直接去用就好了

398
00:18:24,449 --> 00:18:25,419
对不对

399
00:18:28,400 --> 00:18:29,079
ok啊

400
00:18:31,599 --> 00:18:33,119
运行一下看看效果

401
00:18:34,299 --> 00:18:38,250
w a s d.一下屏幕啊

402
00:18:38,250 --> 00:18:39,779
焦焦距一定要对了

403
00:18:39,779 --> 00:18:41,400
那这时候大家可以看一下摄像头

404
00:18:41,400 --> 00:18:42,719
跟随我们主角

405
00:18:42,719 --> 00:18:43,739
我们主角移动

406
00:18:43,739 --> 00:18:45,959
而且当主角移动到树后面的时候

407
00:18:48,410 --> 00:18:50,029
把我们主角遮盖住了

408
00:18:50,029 --> 00:18:52,309
但是主角像这个草地啊

409
00:18:52,309 --> 00:18:55,079
上面我们主角都是可以遮住他们的

410
00:18:55,079 --> 00:18:56,099
这是没有问题的

411
00:18:58,859 --> 00:19:00,380
ok那行

412
00:19:00,380 --> 00:19:02,180
那这个东西我们就说这么多啊

413
00:19:02,180 --> 00:19:03,799
如果大家想了解更多的

414
00:19:03,799 --> 00:19:07,109
其实大家可以更仔细的去看啊

415
00:19:07,109 --> 00:19:08,670
剩下的内容就非常简单了啊

416
00:19:08,670 --> 00:19:09,630
你自己去看一下

417
00:19:09,630 --> 00:19:10,500
可以

418
00:19:11,299 --> 00:19:13,970
比如说翻转呀什么的啊

419
00:19:13,970 --> 00:19:16,400
这些都很简单了啊

420
00:19:16,400 --> 00:19:19,700
但是实际上这种类型的游戏啊

421
00:19:19,700 --> 00:19:21,859
就这种顶视图的这种游戏

422
00:19:21,859 --> 00:19:30,349
rpg游戏我们现在都很少用这个cos去做哈哈这种游戏也是逐渐被淘汰啊

423
00:19:30,349 --> 00:19:31,369
我也很心塞

424
00:19:31,369 --> 00:19:33,440
其实我很喜欢玩这一类型游戏啊

425
00:19:33,440 --> 00:19:34,619
哈很喜欢玩

426
00:19:34,619 --> 00:19:35,519
以前玩这个

427
00:19:35,519 --> 00:19:38,099
不管是最终幻想还是什么的啊

428
00:19:38,099 --> 00:19:39,299
都是这种类型游戏啊

429
00:19:39,299 --> 00:19:40,369
非常有意思

430
00:19:40,369 --> 00:19:43,309
但是现在的话出一出一款游戏

431
00:19:43,309 --> 00:19:48,809
一款游戏基本上全是这种用3d去做的

432
00:19:48,809 --> 00:19:52,319
2d的话基本上现在就主要是去做这种

433
00:19:52,319 --> 00:19:54,420
比如说微信上的这种小游戏了啊

434
00:19:54,420 --> 00:19:56,619
就很少去做这种大型游戏了

435
00:19:58,220 --> 00:19:59,019
ok啊行

436
00:19:59,019 --> 00:20:00,579
那我们多的就不说了啊

437
00:20:00,579 --> 00:20:02,769
我们这节课就这么多

438
00:20:02,769 --> 00:20:05,059
我们下节课继续

439
00:20:10,079 --> 00:20:11,059
略略略


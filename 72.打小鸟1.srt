1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,779 --> 00:00:12,000
ok这节课咱们来做一个小游戏啊

3
00:00:12,019 --> 00:00:16,530
那么详细也是相对而言嗯

4
00:00:16,530 --> 00:00:18,120
并不是很难啊

5
00:00:18,120 --> 00:00:19,859
也是比较简单的啊

6
00:00:19,859 --> 00:00:23,660
但是它里面呢会用到我们常用的很多东西啊

7
00:00:23,660 --> 00:00:25,640
比如说我们之前的这个动作呀

8
00:00:25,640 --> 00:00:29,239
以及我们最开始讲的这个回调啊

9
00:00:29,239 --> 00:00:30,699
我们都会用得上

10
00:00:30,940 --> 00:00:34,600
那我们在这里来练习一下啊

11
00:00:34,600 --> 00:00:36,659
通过这个项目来练习一下

12
00:00:37,939 --> 00:00:41,000
那首先导入我们的这个素材啊

13
00:00:41,000 --> 00:00:45,619
我们这个素材一共是这么几张图片啊

14
00:00:45,619 --> 00:00:47,299
大家可以看一下一个背景图片

15
00:00:47,299 --> 00:00:47,899
对不对

16
00:00:47,899 --> 00:00:49,859
我们可以直接把它拖上来

17
00:00:50,039 --> 00:00:56,460
那么背景图片的大小我们看一下是250x190啊

18
00:00:56,700 --> 00:00:58,399
250190

19
00:00:58,420 --> 00:01:00,700
我们让窗口也这么大啊

20
00:01:00,700 --> 00:01:04,209
这样的话是刚好和我们这个图片一般大了

21
00:01:04,209 --> 00:01:05,739
我们现在就可以运行一下

22
00:01:05,739 --> 00:01:06,680
看效果

23
00:01:07,280 --> 00:01:08,420
运行完以后

24
00:01:08,420 --> 00:01:11,239
大家就可以看到我们这个浏览器上啊

25
00:01:11,239 --> 00:01:12,819
就出现我们这个界面了

26
00:01:16,560 --> 00:01:18,359
那么在这里啊

27
00:01:18,359 --> 00:01:23,219
我们这个游戏主要是有这样一只鸟还是鸭子

28
00:01:23,219 --> 00:01:24,799
哈哈啊

29
00:01:24,799 --> 00:01:28,299
然后它呢是从这个槽里面飞出来啊

30
00:01:28,299 --> 00:01:31,060
然后是从草的这一个范围里面啊

31
00:01:31,060 --> 00:01:34,040
不一定是从哪边出来向上飞啊

32
00:01:34,040 --> 00:01:36,079
向上飞的话也不一定飞到哪里

33
00:01:36,079 --> 00:01:37,040
它有可能是这样飞

34
00:01:37,040 --> 00:01:37,879
有可能这样飞

35
00:01:37,879 --> 00:01:38,659
也有可能这样飞

36
00:01:38,659 --> 00:01:39,680
有可能这样飞啊

37
00:01:39,680 --> 00:01:40,819
怎样都有可能

38
00:01:40,819 --> 00:01:42,019
这是随机的

39
00:01:42,019 --> 00:01:42,650
对不对

40
00:01:42,650 --> 00:01:44,719
然后我们手指去点啊

41
00:01:44,719 --> 00:01:46,719
因为我们这个做完以后

42
00:01:46,719 --> 00:01:49,060
比如说大家要发布在这个手机上啊

43
00:01:49,060 --> 00:01:50,379
一般就用手去点了

44
00:01:50,379 --> 00:01:50,980
对不对

45
00:01:50,980 --> 00:01:55,900
所以我们在这里来个触触摸的一个事件啊

46
00:01:55,900 --> 00:01:58,420
当我们触摸到这个小鸟的时

47
00:01:58,420 --> 00:02:01,299
候这个小鸟呢就会掉下来啊

48
00:02:01,299 --> 00:02:02,739
掉下来以后我们就加分

49
00:02:02,739 --> 00:02:05,890
我们可以给它来个分数分值去加分

50
00:02:05,890 --> 00:02:07,480
然后如果有一个小鸟

51
00:02:07,480 --> 00:02:09,400
你没有点中它飞出去了

52
00:02:09,400 --> 00:02:11,368
那么游戏结束啊

53
00:02:11,368 --> 00:02:12,658
就是这样的一个游戏

54
00:02:12,658 --> 00:02:14,218
然后逻辑很简单啊

55
00:02:14,218 --> 00:02:16,019
但实际上大家去想想

56
00:02:16,019 --> 00:02:19,620
在微信上面看的这个网页上的小游戏啊

57
00:02:19,620 --> 00:02:21,659
基本上也没有很复杂的啊

58
00:02:21,659 --> 00:02:23,479
都是比较简单的啊

59
00:02:23,479 --> 00:02:25,400
只是做游戏最重要的是什么

60
00:02:25,400 --> 00:02:30,030
最重要的是一个嗯创新的一个思想啊

61
00:02:30,030 --> 00:02:31,800
很多游戏其实非常简单

62
00:02:31,800 --> 00:02:33,960
包括咱们之前做的那个flappy bird

63
00:02:33,960 --> 00:02:34,960
对不对啊

64
00:02:34,960 --> 00:02:36,520
都是比较简单的游戏

65
00:02:36,520 --> 00:02:39,219
但是人家最开始就能有这种想法

66
00:02:39,219 --> 00:02:40,300
就能有这种思路

67
00:02:40,300 --> 00:02:42,099
人家就做出来了啊

68
00:02:42,099 --> 00:02:43,219
所以就火爆了

69
00:02:43,240 --> 00:02:46,360
那火爆并不代表他那个游戏技术很高

70
00:02:46,360 --> 00:02:47,379
对不对啊

71
00:02:47,379 --> 00:02:48,159
技术简单

72
00:02:48,159 --> 00:02:51,479
但是人家这个游戏性就比较高啊

73
00:02:51,479 --> 00:02:55,680
所以大家自己就是没平时没事的时候啊

74
00:02:55,680 --> 00:02:58,800
你就可以去想想哎自己可以做一款什么样的游戏啊

75
00:02:58,800 --> 00:03:01,000
玩起来怎样玩有意思啊

76
00:03:01,000 --> 00:03:03,719
大家可以自己去多想想这种东西啊

77
00:03:03,719 --> 00:03:04,680
然后去做

78
00:03:04,680 --> 00:03:10,259
不要天天净追求这个要用多高的技术写一个多厉害的游戏啊

79
00:03:10,259 --> 00:03:14,840
人家用户啊最终看到的是这个游戏好不好玩

80
00:03:14,840 --> 00:03:16,280
人家不管你代码什么呀

81
00:03:16,280 --> 00:03:18,360
对不对啊

82
00:03:18,360 --> 00:03:19,259
ok啊

83
00:03:19,259 --> 00:03:22,000
那咱们看看咱们这个项目

84
00:03:22,319 --> 00:03:26,979
首先这个小鸟不管是飞出来还是掉下来啊

85
00:03:26,979 --> 00:03:28,900
你应该是从这个草的后面

86
00:03:28,900 --> 00:03:29,469
对不对

87
00:03:29,469 --> 00:03:30,280
那这个怎么做

88
00:03:30,280 --> 00:03:31,960
我们在这里可以看啊

89
00:03:32,560 --> 00:03:35,379
这里有一个草啊

90
00:03:35,379 --> 00:03:36,340
这里有一个草

91
00:03:36,340 --> 00:03:41,039
这个草实际上跟这个下半部分长得是完全一样的啊

92
00:03:41,039 --> 00:03:42,509
长得是完全一样的

93
00:03:42,509 --> 00:03:44,550
那么我们把它拖上来

94
00:03:44,550 --> 00:03:46,360
也放到这个位置

95
00:03:46,840 --> 00:03:48,969
也放到这个位置

96
00:03:48,969 --> 00:03:50,979
那这样的话大家可以看

97
00:03:50,979 --> 00:03:52,120
实际上他俩就重合了

98
00:03:52,120 --> 00:03:52,740
对不对

99
00:03:52,740 --> 00:03:55,680
那如果我们把小鸟放到他们俩的中间

100
00:03:55,680 --> 00:03:57,810
这时候出现的效果啊

101
00:03:57,810 --> 00:03:58,860
拖拖错了

102
00:03:58,860 --> 00:03:59,699
拖小鸟

103
00:03:59,840 --> 00:04:02,509
大家可以看是不是就这样的一个效果了

104
00:04:02,509 --> 00:04:05,960
就小鸟是可以从这个草地里面飞出来的啊

105
00:04:05,960 --> 00:04:07,400
这就是层级的一个问题

106
00:04:07,400 --> 00:04:07,819
对不对

107
00:04:07,819 --> 00:04:08,860
层级的问题

108
00:04:08,860 --> 00:04:13,599
所以咱们要保证每一个诞生出来的小鸟都在这个层级里面

109
00:04:13,599 --> 00:04:16,990
中间这个层级里面这个怎么保证啊

110
00:04:16,990 --> 00:04:17,980
这个怎么保证

111
00:04:17,980 --> 00:04:20,199
而且我们一会儿要出来很多小鸟

112
00:04:20,199 --> 00:04:21,160
不是这一个小鸟

113
00:04:21,160 --> 00:04:21,879
对不对

114
00:04:21,879 --> 00:04:24,220
所以最好的方案就是这样的

115
00:04:25,079 --> 00:04:29,839
在这个位置我们创建一个空的节点

116
00:04:30,459 --> 00:04:34,660
空的节点空的节点我们叫做bird manager

117
00:04:34,660 --> 00:04:36,490
叫做小鸟的管理器

118
00:04:36,490 --> 00:04:40,060
然后我们每次只要创建小鸟

119
00:04:40,060 --> 00:04:43,579
都把这个小鸟诶付给这个管理器啊

120
00:04:43,579 --> 00:04:45,439
当做它的子类啊

121
00:04:45,439 --> 00:04:46,699
当做他的子类

122
00:04:47,319 --> 00:04:50,420
然后这样的话就是小鸟

123
00:04:50,420 --> 00:04:51,620
你只要是它的子类

124
00:04:51,620 --> 00:04:53,899
你肯定是在这两层中间啊

125
00:04:53,899 --> 00:04:56,439
所以你肯定是能被这个草草地遮住

126
00:04:56,439 --> 00:04:58,329
但是你这个又能遮住背景

127
00:04:58,329 --> 00:05:00,100
对不对啊

128
00:05:00,779 --> 00:05:02,939
ok啊那我们就是要这个效果啊

129
00:05:02,939 --> 00:05:05,100
大家首先第一步把层笔记搞清楚啊

130
00:05:05,100 --> 00:05:07,079
我们要做的游戏层面就是这样啊

131
00:05:07,079 --> 00:05:08,519
不断地诞生出小鸟

132
00:05:08,519 --> 00:05:12,220
然后加到这个呃布尔的manager下面啊

133
00:05:12,220 --> 00:05:13,600
作为它的子节点

134
00:05:13,600 --> 00:05:16,959
这样的话它的层级就能保证在这里啊

135
00:05:16,959 --> 00:05:20,600
在这个位置在中间的位置上嗯

136
00:05:21,399 --> 00:05:23,649
那么我们就开始去写代码

137
00:05:23,649 --> 00:05:24,399
代码的话

138
00:05:24,399 --> 00:05:26,980
首先管理器肯定是要有代码的

139
00:05:26,980 --> 00:05:29,439
它是会帮我们不断去创建小鸟的

140
00:05:29,459 --> 00:05:31,980
而这个小鸟本身也是有代码的

141
00:05:31,980 --> 00:05:33,060
它的代码是什么

142
00:05:33,060 --> 00:05:37,160
就是从当前位置随先随机一个顶点的位置

143
00:05:37,160 --> 00:05:39,860
然后我们怎样让它飞出去啊

144
00:05:39,860 --> 00:05:41,240
让它朝外面飞出去

145
00:05:41,240 --> 00:05:43,790
这就是小鸟本身的代码啊

146
00:05:43,790 --> 00:05:47,540
那所以我们一步一步来先写小鸟的代码啊

147
00:05:47,540 --> 00:05:48,879
先写小鸟的代码

148
00:05:53,100 --> 00:05:55,860
首先我们这边创建一个文件夹

149
00:06:05,459 --> 00:06:11,829
然后把这个小鸟的这个脚本给它加上来

150
00:06:11,829 --> 00:06:13,259
打开这个脚本

151
00:06:14,879 --> 00:06:16,560
打开脚本

152
00:06:16,560 --> 00:06:18,839
打开脚本以后啊

153
00:06:18,839 --> 00:06:21,060
先把里面内容清一清

154
00:06:23,879 --> 00:06:26,100
然后在这边我们去想一想

155
00:06:26,100 --> 00:06:28,439
对于小鸟来说啊

156
00:06:28,439 --> 00:06:29,939
对于小鸟来说

157
00:06:30,399 --> 00:06:31,779
它的功能

158
00:06:35,379 --> 00:06:38,439
首先小鸟它是有一个存活状态的

159
00:06:38,439 --> 00:06:40,939
就是我们点到这个小鸟

160
00:06:40,939 --> 00:06:42,259
它会从天上掉下来

161
00:06:42,259 --> 00:06:43,459
掉下来的过程当中

162
00:06:43,459 --> 00:06:45,980
你再点它是不是都应该无效呀

163
00:06:46,000 --> 00:06:49,899
啊那么我们怎样才能做到它朝天上飞的时候

164
00:06:49,899 --> 00:06:52,329
我们点它有效掉落的时候就无效呢

165
00:06:52,329 --> 00:06:55,850
那这个我们可以用一个生命值来控制啊

166
00:06:55,850 --> 00:06:57,379
像这种点一下就死的

167
00:06:57,379 --> 00:07:00,480
一般默认就是给他一滴血啊

168
00:07:00,480 --> 00:07:01,379
默认就一滴血

169
00:07:01,379 --> 00:07:03,959
然后我们点他一下血就少少一格

170
00:07:03,959 --> 00:07:04,500
对不对

171
00:07:04,500 --> 00:07:07,980
然后这样的话我们就能判断这个小鸟当前是死是活了

172
00:07:07,980 --> 00:07:08,879
对不对啊

173
00:07:08,879 --> 00:07:10,639
生命值肯定是要有的

174
00:07:14,480 --> 00:07:16,399
然后一个目标位置啊

175
00:07:16,399 --> 00:07:18,800
就是飞翔的目标位置

176
00:07:27,519 --> 00:07:30,040
目标位置

177
00:07:30,040 --> 00:07:32,779
目标位置的话

178
00:07:32,839 --> 00:07:34,699
你就应该是在哪里的

179
00:07:34,699 --> 00:07:36,860
大家可以看把这个小鸟拖上来

180
00:07:36,879 --> 00:07:39,639
就是在上面这一串位置了啊

181
00:07:39,639 --> 00:07:40,899
上面这一串位置

182
00:07:40,899 --> 00:07:43,860
那么上面这一串我们可以看一下它的位置

183
00:07:44,360 --> 00:07:48,319
y轴是在180多啊

184
00:07:48,319 --> 00:07:50,329
y轴是在180多

185
00:07:50,329 --> 00:07:53,449
然后x轴啊

186
00:07:53,449 --> 00:07:55,970
比如说y我们给他185

187
00:07:55,970 --> 00:07:59,560
x我们让它可以飞到这里或者这里啊

188
00:07:59,560 --> 00:08:04,399
所以大概就是左边的-110到正的110这样的一个位置

189
00:08:04,939 --> 00:08:14,970
ok那我们也就是说它的这个位置是正-110~185啊

190
00:08:14,970 --> 00:08:16,410
随机一个x

191
00:08:16,410 --> 00:08:18,959
然后纵轴的话是固定的185

192
00:08:18,959 --> 00:08:21,899
这样的话就是最终位置就是在上面随意取了一点

193
00:08:21,899 --> 00:08:22,620
对不对

194
00:08:22,620 --> 00:08:25,829
然后我们要最终到达这个目标位置啊

195
00:08:25,829 --> 00:08:29,730
然后小鸟飞翔是要有这个速度的啊

196
00:08:29,730 --> 00:08:33,929
速度我们先给一个默认值啊

197
00:08:33,929 --> 00:08:36,379
然后如果不合适了再说啊

198
00:08:38,100 --> 00:08:40,440
那么目前就可以去写代码了啊

199
00:08:40,440 --> 00:08:48,500
就可以写代码了呃首先我们上来需要给它一个随机目标点

200
00:08:51,620 --> 00:08:53,360
就这个目标点啊

201
00:08:53,360 --> 00:08:55,600
上来我们应该给他确定一下

202
00:08:56,879 --> 00:08:58,860
怎么确定这个随机目标点呢

203
00:08:58,860 --> 00:08:59,580
非常简单

204
00:08:59,580 --> 00:09:03,179
首先x是给他一个-110~110之间

205
00:09:03,179 --> 00:09:03,779
是不是

206
00:09:03,779 --> 00:09:05,539
那怎样给他这个值

207
00:09:05,539 --> 00:09:11,799
我们通过一个随机数得到零和一的一个随机数乘以一个220

208
00:09:11,799 --> 00:09:14,200
那就是0~220之间的随机数

209
00:09:14,200 --> 00:09:15,340
再减去个110

210
00:09:15,340 --> 00:09:19,220
就是负的110和正的10之间一个随机数就可以了

211
00:09:19,220 --> 00:09:19,700
对不对

212
00:09:19,700 --> 00:09:22,299
这是x y的话

213
00:09:22,299 --> 00:09:23,980
我们就给他个185

214
00:09:23,980 --> 00:09:27,460
这就是我们随机的一个目标的一个位置啊

215
00:09:27,460 --> 00:09:30,059
就是只要这个小鸟诞生出来

216
00:09:30,080 --> 00:09:33,659
我们就会给他一个随机的一个目标点啊

217
00:09:34,139 --> 00:09:35,360
呃

218
00:09:37,100 --> 00:09:39,919
我们单独不要写到这个start里面吧

219
00:09:39,919 --> 00:09:41,600
我们写到这个fly里面

220
00:09:41,600 --> 00:09:42,559
再写个方法

221
00:09:42,559 --> 00:09:44,860
叫fly写到他的里面

222
00:09:45,539 --> 00:09:46,759
然后

223
00:09:49,179 --> 00:09:53,919
这个整个飞翔的让小鸟飞翔相关的方法我们都写到它里面啊

224
00:09:53,919 --> 00:09:55,120
这样的话比较好一点

225
00:09:55,120 --> 00:09:57,559
就是如果我们这个小鸟有更多的操作

226
00:09:57,559 --> 00:09:59,480
你就可以再写更多的方法啊

227
00:09:59,480 --> 00:10:02,480
然后这样这样的话你就可以在这再调其他的方法

228
00:10:02,480 --> 00:10:04,519
就不至于全写到start里面

229
00:10:04,519 --> 00:10:06,299
最后如果代码特别多的话

230
00:10:06,320 --> 00:10:06,980
就很乱

231
00:10:06,980 --> 00:10:09,919
是不是start里面就什么都有啊

232
00:10:09,919 --> 00:10:14,179
呃那么在这里随机目标点有了

233
00:10:14,179 --> 00:10:15,559
我们就可以怎样了

234
00:10:15,559 --> 00:10:16,220
让它飞了

235
00:10:16,220 --> 00:10:18,059
但是这里注意一个问题啊

236
00:10:19,139 --> 00:10:21,480
我们这个小鸟有可能这样飞的

237
00:10:21,480 --> 00:10:22,679
也有可能这样飞

238
00:10:22,679 --> 00:10:24,210
但是我扇动翅膀

239
00:10:24,210 --> 00:10:25,980
我现在是面朝左边

240
00:10:25,980 --> 00:10:27,000
如果向右边飞

241
00:10:27,000 --> 00:10:27,720
这个很诡异

242
00:10:27,720 --> 00:10:28,299
对不对

243
00:10:28,320 --> 00:10:29,940
所以我们向右边飞的话

244
00:10:29,940 --> 00:10:31,230
就要给它翻转过来

245
00:10:31,230 --> 00:10:32,600
怎样翻转呢

246
00:10:32,620 --> 00:10:35,200
翻转就是把它的缩放给他个-1

247
00:10:35,200 --> 00:10:37,960
这个就做的很简单的翻转了啊

248
00:10:37,960 --> 00:10:39,799
那这个东西怎么做

249
00:10:42,100 --> 00:10:43,899
你怎么知道他在左边

250
00:10:43,899 --> 00:10:45,220
右边其实非常简单

251
00:10:45,220 --> 00:10:56,190
我们判断它的x翻转精灵判断一下目标点的x就是这个target position的x

252
00:10:56,190 --> 00:11:01,009
如果他比我们这个当前的x要大

253
00:11:01,009 --> 00:11:03,820
也就是说目标点比当前的x要大

254
00:11:03,840 --> 00:11:06,740
那就证明我是向右边飞了啊

255
00:11:06,740 --> 00:11:08,809
因为右边的x是比左边大嘛

256
00:11:08,809 --> 00:11:09,559
对不对

257
00:11:09,559 --> 00:11:12,019
所以比我这个小鸟的这个x要大

258
00:11:12,019 --> 00:11:14,320
就证明我是向右边飞了

259
00:11:14,340 --> 00:11:16,080
向右边飞的话

260
00:11:16,240 --> 00:11:19,149
我这边就要给他进行一个翻转了

261
00:11:19,149 --> 00:11:24,720
this.node.x轴的一个翻转等于个-1就可以了

262
00:11:25,799 --> 00:11:27,659
翻转完了我就要做什么了

263
00:11:27,659 --> 00:11:28,879
做个移动

264
00:11:29,139 --> 00:11:30,159
移动的话

265
00:11:30,159 --> 00:11:31,899
我们就可以直接用动作啊

266
00:11:31,899 --> 00:11:35,620
我们之前学的这个动作来去做了move to啊

267
00:11:35,620 --> 00:11:36,820
因为我们有目标点

268
00:11:36,820 --> 00:11:38,440
然后我们move to过去就行了

269
00:11:38,440 --> 00:11:40,299
但是这里面要注意的是什么

270
00:11:40,299 --> 00:11:41,320
做动作的时候

271
00:11:41,320 --> 00:11:45,539
第一个有一个呃时间啊

272
00:11:45,539 --> 00:11:47,039
是多长时间飞过去

273
00:11:47,039 --> 00:11:50,309
那么这个时间怎么怎样算啊

274
00:11:50,309 --> 00:11:51,480
时间怎样算

275
00:11:51,480 --> 00:11:52,740
时间其实很简单

276
00:11:52,740 --> 00:11:54,960
就是距离除以一个速度

277
00:11:54,960 --> 00:11:56,039
距离除以速度

278
00:11:56,039 --> 00:11:57,360
因为咱们有速度

279
00:11:57,360 --> 00:11:59,340
所以我们拿到一个距离

280
00:11:59,340 --> 00:12:00,360
就是飞翔的距离

281
00:12:00,360 --> 00:12:01,379
再除以一个速度

282
00:12:01,379 --> 00:12:04,129
是不是就是它的一个时间啊

283
00:12:04,129 --> 00:12:05,629
没问题吧

284
00:12:05,629 --> 00:12:11,440
因为速度乘时间等于距离嘛

285
00:12:11,440 --> 00:12:11,919
对不对

286
00:12:11,919 --> 00:12:14,200
所以我们距离除以速度

287
00:12:14,200 --> 00:12:15,940
就等于我们这里要的时间啊

288
00:12:15,940 --> 00:12:17,179
就是动作时间了

289
00:12:17,200 --> 00:12:18,820
那么距离的话怎么算

290
00:12:18,820 --> 00:12:21,580
距离的话呃

291
00:12:22,500 --> 00:12:23,779
很简单

292
00:12:23,799 --> 00:12:28,059
我们直接给它做一个轴的距离判定就行了啊

293
00:12:28,059 --> 00:12:30,639
就是说比如说从这里到这里啊

294
00:12:30,639 --> 00:12:33,460
看似距离是这样斜着的一条线啊

295
00:12:33,460 --> 00:12:36,240
但是其实没必要计算斜着的这条线

296
00:12:36,259 --> 00:12:38,299
你拿一个轴

297
00:12:38,299 --> 00:12:39,860
比如说x轴或者y轴

298
00:12:39,860 --> 00:12:42,200
拿一个轴去计算距离就可以了啊

299
00:12:42,200 --> 00:12:43,580
用一个轴去计算就可以了

300
00:12:43,580 --> 00:12:45,539
比如说我们这里拿y轴去计算

301
00:12:45,539 --> 00:12:55,929
那就是this.target就是我终点的目标点的y轴减去我起始点的y轴

302
00:12:55,929 --> 00:12:57,759
这个距离就就是什么了

303
00:12:57,759 --> 00:13:00,460
就是从这一点到上面这一段的距离了

304
00:13:00,460 --> 00:13:01,240
对不对

305
00:13:01,240 --> 00:13:08,899
那么这个距离呢除以一个this.speed啊

306
00:13:08,899 --> 00:13:11,539
就是小鸟的飞翔的一个速度

307
00:13:11,539 --> 00:13:13,820
那最后得到的结果就是时间了啊

308
00:13:13,820 --> 00:13:15,179
得到的结果就是时间了

309
00:13:15,179 --> 00:13:18,299
当然如果大家想去用斜线去计算的话

310
00:13:18,299 --> 00:13:19,440
也是可以的啊

311
00:13:19,440 --> 00:13:22,759
我们这里就是用比较简单的方式去计算一个轴了

312
00:13:22,980 --> 00:13:25,799
然后拿到一个时间以后啊

313
00:13:25,799 --> 00:13:27,450
拿到一个时间以后

314
00:13:27,450 --> 00:13:28,779
我们

315
00:13:30,379 --> 00:13:32,480
呃第二个参数啊

316
00:13:32,480 --> 00:13:33,379
就是目标点

317
00:13:33,379 --> 00:13:38,580
目标点我们直接把这个this.target position放这就可以了啊

318
00:13:38,580 --> 00:13:39,539
这个非常简单

319
00:13:39,539 --> 00:13:42,340
那这样的话这个move就是我们的move动作

320
00:13:42,360 --> 00:13:45,419
移动动作我们可以尝试一下执行一下啊

321
00:13:45,419 --> 00:13:46,320
这个动作

322
00:13:49,919 --> 00:13:50,860
木

323
00:13:54,860 --> 00:13:57,620
然后我们在这里来运行一下

324
00:13:58,940 --> 00:14:01,519
大家可以看是不是就飞上去了

325
00:14:01,519 --> 00:14:02,720
我们再重新运行一下

326
00:14:02,720 --> 00:14:05,700
看一下它是不是移动的位置就改变了

327
00:14:05,700 --> 00:14:07,320
你看脑袋也转了

328
00:14:07,320 --> 00:14:07,919
也改变了

329
00:14:07,919 --> 00:14:08,850
对不对

330
00:14:08,850 --> 00:14:10,139
ok啊

331
00:14:10,960 --> 00:14:17,139
那么现在它还是呃静止的

332
00:14:17,139 --> 00:14:18,759
我们把动画给它加上

333
00:14:18,759 --> 00:14:20,460
我们把动画给它加上

334
00:14:20,600 --> 00:14:22,100
点一下动画编辑器

335
00:14:22,100 --> 00:14:24,379
我们给它去添加一个动画

336
00:14:27,320 --> 00:14:30,019
那么它的话呢应该会有两个动画

337
00:14:30,019 --> 00:14:32,090
第一个就是正常的飞的动画

338
00:14:32,090 --> 00:14:34,580
还有一个这个鸟会有一个掉落的动画

339
00:14:34,580 --> 00:14:35,360
对不对

340
00:14:35,360 --> 00:14:38,299
所以在这里我们再给它加一个带

341
00:14:38,299 --> 00:14:39,830
就是死亡的动画啊

342
00:14:39,830 --> 00:14:42,289
然后在这里我们来去编辑动画

343
00:14:42,289 --> 00:14:44,200
首先选到飞到这里

344
00:14:44,220 --> 00:14:45,960
帧帧数调低一点

345
00:14:45,960 --> 00:14:48,419
比如说一秒给他走上八帧

346
00:14:48,419 --> 00:14:51,889
然后在这里飞翔动画

347
00:14:51,889 --> 00:14:53,200
我们

348
00:14:55,299 --> 00:14:56,980
把这三针拖过来

349
00:14:56,980 --> 00:14:59,139
这三针就是它的山翅膀的一个动画

350
00:14:59,139 --> 00:14:59,899
对不对

351
00:15:00,100 --> 00:15:02,620
然后循环loop

352
00:15:02,620 --> 00:15:04,269
然后保存一下

353
00:15:04,269 --> 00:15:05,980
然后死亡

354
00:15:07,679 --> 00:15:09,149
一样的啊

355
00:15:09,149 --> 00:15:11,220
拖过来

356
00:15:11,220 --> 00:15:13,620
死亡

357
00:15:14,559 --> 00:15:15,879
那么因为它就一整

358
00:15:15,879 --> 00:15:17,740
所以循环不循环都无所谓了啊

359
00:15:17,740 --> 00:15:19,500
保存关闭就可以了

360
00:15:19,500 --> 00:15:21,240
那么这个小鸟上来

361
00:15:21,240 --> 00:15:24,120
我们是不是应该让它默认就执行一个动画

362
00:15:24,120 --> 00:15:29,190
执行我们的飞翔动画上来就执行

363
00:15:29,190 --> 00:15:33,299
ok那这样的话小鸟的一个飞行动画就完了

364
00:15:33,299 --> 00:15:38,809
把当前场景保存为game运行一下

365
00:15:38,809 --> 00:15:40,039
诶

366
00:15:40,039 --> 00:15:43,320
大家就可以看小鸟是不是飞上去了

367
00:15:43,960 --> 00:15:47,779
重新来又飞上去了

368
00:15:47,779 --> 00:15:50,240
还是斜向斜向上啊

369
00:15:50,240 --> 00:15:51,799
这就是一个随机啊

370
00:15:51,799 --> 00:15:56,220
你看这次是不是就向右上方了啊

371
00:15:56,220 --> 00:16:01,259
左上方右上方左上方啊

372
00:16:01,259 --> 00:16:03,450
这基本上是垂直的了

373
00:16:03,450 --> 00:16:07,000
就说这个它都是有各种可能的

374
00:16:07,679 --> 00:16:11,759
那么小鸟的出生也会在这这个地址啊

375
00:16:11,759 --> 00:16:14,259
这个草地的后面进行一个随机啊

376
00:16:14,259 --> 00:16:18,940
但是他的这个初始位置的随机并不在他本身上去做啊

377
00:16:18,940 --> 00:16:20,100
所以这个不用管

378
00:16:24,899 --> 00:16:28,440
ok那么接下来啊

379
00:16:28,440 --> 00:16:31,080
接下来我们想想我们要做什么操作啊

380
00:16:31,120 --> 00:16:33,700
呃小鸟会飞翔了

381
00:16:33,700 --> 00:16:35,889
我们现在就是要触摸这个小鸟

382
00:16:35,889 --> 00:16:39,840
让它进行有这样一个掉落的这样的一个动作啊

383
00:16:40,120 --> 00:16:42,980
那么我看一下啊

384
00:16:44,620 --> 00:16:46,240
啊这也十几分钟

385
00:16:46,240 --> 00:16:49,480
那大家先自己去做一下啊

386
00:16:49,480 --> 00:16:50,919
大家先做到这里啊

387
00:16:50,919 --> 00:16:52,210
把这点内容做完

388
00:16:52,210 --> 00:16:54,100
然后我们下节课啊

389
00:16:54,100 --> 00:16:56,200
我们在做这个点击

390
00:16:56,200 --> 00:16:59,259
让它掉落的啊这样的一个内容啊

391
00:16:59,259 --> 00:17:02,000
那大家先把这节课的内容给大家做一下

392
00:17:08,720 --> 00:17:09,259
啊


1
00:00:09,240 --> 00:00:13,259
ok这节课咱们来继续说这个数据格式

2
00:00:13,539 --> 00:00:15,339
那么数据格式的话

3
00:00:15,339 --> 00:00:16,839
咱们已经已经说过了

4
00:00:16,839 --> 00:00:17,710
有很多种

5
00:00:17,710 --> 00:00:20,048
咱们杰森呢只是其中一种

6
00:00:20,048 --> 00:00:21,428
但是对于我们而言

7
00:00:21,428 --> 00:00:23,708
大家只需要把杰森搞清楚

8
00:00:23,708 --> 00:00:25,748
基本上就没问题了啊

9
00:00:25,748 --> 00:00:29,859
但是我们也需要知道其他格式大概是什么样子啊

10
00:00:29,859 --> 00:00:32,500
你也要对其他格式有所了解

11
00:00:32,579 --> 00:00:38,490
为什么我们说jason在在我们这里面啊是非常常用的啊

12
00:00:38,490 --> 00:00:42,020
那么首先啊不管在什么语言里面

13
00:00:42,020 --> 00:00:45,259
jason已经是最常用的数据格式了

14
00:00:45,259 --> 00:00:48,240
其次这个叫杰森

15
00:00:48,240 --> 00:00:48,789
对不对

16
00:00:48,789 --> 00:00:52,990
那么它呢其实是从javascript这边诞生出来的啊

17
00:00:52,990 --> 00:00:56,810
它是javascript里面用来描述对象的一种数据格式

18
00:00:56,810 --> 00:00:59,030
最后因为它太好用了

19
00:00:59,030 --> 00:01:01,780
所以它才被子被抽出来

20
00:01:01,780 --> 00:01:05,280
然后就是所有的语言都用这种数据格式了

21
00:01:05,659 --> 00:01:09,959
所以实际上他呢准确来说啊

22
00:01:09,959 --> 00:01:16,129
和javascript里面嗯应该是配合起来是最好用的啊

23
00:01:16,129 --> 00:01:21,909
那么其次我们typescript是javascript里面的一个超级

24
00:01:21,909 --> 00:01:23,530
这个咱们在最开始就说了

25
00:01:23,530 --> 00:01:24,730
对不对啊

26
00:01:24,730 --> 00:01:29,579
那所以其实咱们这个语言完全也可以说是javascript啊

27
00:01:29,579 --> 00:01:30,840
毕竟我们在跑的时候

28
00:01:30,840 --> 00:01:31,980
比如说程序硬跑

29
00:01:31,980 --> 00:01:36,510
它实际上还是把我们的这个type script翻译成了javascript

30
00:01:36,510 --> 00:01:37,129
对不对

31
00:01:37,129 --> 00:01:41,629
所以其实对我们这个语言用jason其实是最合适的啊

32
00:01:41,629 --> 00:01:42,689
是最合适的

33
00:01:42,689 --> 00:01:44,340
但是即便如此

34
00:01:44,620 --> 00:01:50,700
我们还是要知道一下其他这个呃xml和csv的这样的一个格式

35
00:01:51,959 --> 00:01:53,959
那如果万一用得上了

36
00:01:53,959 --> 00:01:56,120
那大家不至于说看不懂是吧

37
00:01:56,120 --> 00:01:58,118
最起码要把这个东西可以看懂

38
00:01:58,500 --> 00:02:02,680
首先啊我们比如说现在我们这里有个person

39
00:02:02,680 --> 00:02:03,219
对不对

40
00:02:03,219 --> 00:02:04,299
是个李逍遥

41
00:02:04,299 --> 00:02:06,250
比如说李逍遥是一个人啊

42
00:02:06,250 --> 00:02:10,550
那么他他呢i d是这个十绘制两种武功

43
00:02:10,550 --> 00:02:11,180
对不对

44
00:02:11,180 --> 00:02:12,469
会这两种武功

45
00:02:12,469 --> 00:02:15,780
那么如果这是一个person啊

46
00:02:15,780 --> 00:02:16,860
我们再来一个person

47
00:02:16,860 --> 00:02:19,000
第二个person可能叫王小虎

48
00:02:19,019 --> 00:02:20,519
然后i d是别的

49
00:02:20,519 --> 00:02:21,088
对不对

50
00:02:21,088 --> 00:02:23,459
比如说现在就有这两个person对象啊

51
00:02:23,459 --> 00:02:24,179
一个李逍遥

52
00:02:24,179 --> 00:02:25,049
一个王小虎

53
00:02:25,049 --> 00:02:30,379
我现在想把这两个这两个对象放到一个数组里面

54
00:02:30,379 --> 00:02:34,289
然后把这个数组存到本地啊

55
00:02:34,289 --> 00:02:37,330
那么首先这两个对象数组啊

56
00:02:37,330 --> 00:02:39,699
就是比如说我们现在要存一个person数组

57
00:02:39,699 --> 00:02:43,360
我们就要把它转成什么了字符串了

58
00:02:43,360 --> 00:02:48,080
那么当然转成字符串对象数组转字符串我们就需要干嘛了

59
00:02:48,919 --> 00:02:51,099
需要用到一种数据格式了啊

60
00:02:51,099 --> 00:02:55,969
你要用哪种数据格式来进行一个这样的一个解析啊

61
00:02:55,969 --> 00:02:57,800
那么首先我们一个一个说

62
00:02:57,800 --> 00:02:59,210
先说杰森

63
00:02:59,210 --> 00:03:03,400
杰森jason的话

64
00:03:03,579 --> 00:03:05,079
它是长什么样

65
00:03:05,079 --> 00:03:06,938
首先如果是个person数组

66
00:03:06,938 --> 00:03:07,598
咱们说过了

67
00:03:07,598 --> 00:03:09,490
在jason里面数组是个括号

68
00:03:09,490 --> 00:03:11,349
其次里面如果有两个对象

69
00:03:11,349 --> 00:03:12,370
比如说一个李逍遥

70
00:03:12,370 --> 00:03:13,150
一个王小虎

71
00:03:13,150 --> 00:03:17,778
每一个对象在json里面都是一个大括号啊

72
00:03:17,778 --> 00:03:18,919
都是一个大括号

73
00:03:18,919 --> 00:03:23,498
然后呢在每一个对象里面都有键值对啊

74
00:03:23,498 --> 00:03:29,300
比如说有个这个这个id id是个十

75
00:03:30,259 --> 00:03:35,180
然后第二个name是个李逍遥

76
00:03:36,118 --> 00:03:37,098
还有个武功

77
00:03:37,098 --> 00:03:37,729
对不对

78
00:03:37,729 --> 00:03:40,278
那么武功这边又是一个数组了

79
00:03:40,278 --> 00:03:41,658
那么他其实大家可以看一下

80
00:03:41,658 --> 00:03:42,618
他就是来回嵌套

81
00:03:42,618 --> 00:03:43,289
对不对

82
00:03:43,289 --> 00:03:44,250
又是个数组的

83
00:03:44,250 --> 00:03:48,939
在这个数组里面有什么降龙18掌

84
00:03:51,539 --> 00:03:54,500
啊然后独孤九剑对不对

85
00:03:54,739 --> 00:03:56,688
那么这是第一个人的

86
00:03:56,688 --> 00:03:59,179
那现在比如说第二个人王小虎

87
00:03:59,179 --> 00:04:01,960
那肯定跟他的格式完全一样啊

88
00:04:01,960 --> 00:04:04,840
比如说可能年龄就变了啊

89
00:04:04,840 --> 00:04:06,400
i d就变了啊

90
00:04:06,400 --> 00:04:07,240
不是年龄

91
00:04:07,240 --> 00:04:10,599
然后比如说name叫王小虎

92
00:04:12,459 --> 00:04:13,919
一般在jason里面

93
00:04:13,919 --> 00:04:14,579
数组里面

94
00:04:14,579 --> 00:04:15,598
比如说有很多对象

95
00:04:15,598 --> 00:04:17,668
那这些对象肯定格式是一样的

96
00:04:17,668 --> 00:04:20,459
这样的话便利我们循环解析

97
00:04:20,459 --> 00:04:21,259
对不对

98
00:04:22,379 --> 00:04:24,560
比如说他也会武功是吧

99
00:04:24,560 --> 00:04:27,600
比如说他的是什么磨刀是吧

100
00:04:28,360 --> 00:04:32,100
啊都忘了很多很多年前玩的游戏了

101
00:04:32,100 --> 00:04:33,100
啊哈哈

102
00:04:37,339 --> 00:04:39,160
有其他武功你可以再去加

103
00:04:39,160 --> 00:04:39,579
对不对

104
00:04:39,579 --> 00:04:40,750
总之这是一个数字

105
00:04:40,750 --> 00:04:41,949
再写上一个吧

106
00:04:41,949 --> 00:04:43,019
比如说

107
00:04:44,699 --> 00:04:47,119
好像还有一个武功叫翻云覆雨

108
00:04:49,519 --> 00:04:55,079
哈哈那么这呢就是我们通过json的格式描述了这两个对象啊

109
00:04:55,079 --> 00:04:59,290
也就是说它最终最终生成的字符串就是从这里到这里

110
00:04:59,290 --> 00:05:01,149
我是为了让大家看得清啊

111
00:05:01,149 --> 00:05:03,250
所以写成了这样的一个格式了啊

112
00:05:03,250 --> 00:05:04,149
就是代换行了

113
00:05:04,149 --> 00:05:05,769
这样的话大家看得清一些

114
00:05:05,769 --> 00:05:08,829
但实际上它本身可能也就是在一行的

115
00:05:08,829 --> 00:05:09,699
对不对

116
00:05:10,098 --> 00:05:11,478
那么这是json格式

117
00:05:11,478 --> 00:05:13,158
我们上节课上节课已经说了

118
00:05:13,158 --> 00:05:15,279
那接下来我们来说一下xml

119
00:05:16,399 --> 00:05:19,300
xml实际上就是节点的方式

120
00:05:21,819 --> 00:05:24,970
咱们说过副文本里面有这个节点

121
00:05:24,970 --> 00:05:25,509
对不对

122
00:05:25,509 --> 00:05:26,350
什么什么什么

123
00:05:26,350 --> 00:05:28,220
然后这是名字啊

124
00:05:28,220 --> 00:05:32,089
然后这边什么什么什么我们已经说过这种节点的方式了

125
00:05:32,089 --> 00:05:34,579
那么实际上叉ml啊

126
00:05:34,579 --> 00:05:36,300
但是这边有个斜杠

127
00:05:36,439 --> 00:05:40,250
实际上xml就是使用这种节点的方式来描述数据的

128
00:05:40,250 --> 00:05:43,189
比如说如果我们用xml来描述xm

129
00:05:43,189 --> 00:05:45,829
首先最外层根部只能有一个节点啊

130
00:05:45,829 --> 00:05:47,480
我们可以叫它为根节点

131
00:05:47,480 --> 00:05:49,310
这里可以随便命名啊

132
00:05:49,310 --> 00:05:50,920
我比如说就叫root

133
00:05:51,038 --> 00:05:52,569
那么在这里面呢

134
00:05:52,569 --> 00:05:54,158
它由于有两个对象

135
00:05:54,158 --> 00:05:54,699
对不对

136
00:05:54,699 --> 00:05:55,928
那么就是两个

137
00:05:55,928 --> 00:06:00,858
所以我们要写两个节点内容先不管

138
00:06:00,858 --> 00:06:03,199
但是我知道有两个person啊

139
00:06:06,360 --> 00:06:10,300
那么每个person里面又有

140
00:06:10,879 --> 00:06:14,740
比如说id是个十啊

141
00:06:14,740 --> 00:06:16,420
当然它里面可能有属性

142
00:06:16,420 --> 00:06:18,279
所以比如说举个例子啊

143
00:06:18,279 --> 00:06:21,500
比如说i d可能是用属性写的啊

144
00:06:21,500 --> 00:06:24,259
这个在咱们那个符文本里面也见过了

145
00:06:24,259 --> 00:06:25,259
对不对啊

146
00:06:25,259 --> 00:06:26,220
这个代表一个属性

147
00:06:26,220 --> 00:06:28,259
你看属性也可以描述他信息

148
00:06:31,079 --> 00:06:34,959
比如说i d可能是属性来来描述啊

149
00:06:34,959 --> 00:06:38,709
这个name当然具体怎样去描述这个你自己定啊

150
00:06:38,709 --> 00:06:40,160
你想怎样都行

151
00:06:40,579 --> 00:06:41,779
name啊

152
00:06:41,779 --> 00:06:43,519
比如说李逍遥

153
00:06:46,319 --> 00:06:49,180
那么然后比如说武功

154
00:06:51,759 --> 00:06:55,000
武功那武功由于又是个数组啊

155
00:06:55,000 --> 00:06:57,370
所以里面可能又有很多啊

156
00:06:57,370 --> 00:07:01,120
但是在这里由于它里面每一项都是个字符串啊

157
00:07:01,120 --> 00:07:04,418
所以你也可以就直接在这写

158
00:07:04,418 --> 00:07:07,059
比如说降龙18掌啊

159
00:07:07,059 --> 00:07:08,738
我就给他写成一个字符串

160
00:07:08,738 --> 00:07:10,319
独孤九剑

161
00:07:10,399 --> 00:07:11,990
那么这就是一个人

162
00:07:11,990 --> 00:07:14,319
同时我们可能还有第二个人

163
00:07:16,360 --> 00:07:17,220
对不对

164
00:07:17,220 --> 00:07:22,089
然后第二个人可能i d等于二啊

165
00:07:22,089 --> 00:07:23,379
他就不叫李逍遥了

166
00:07:23,379 --> 00:07:26,949
他就叫王小虎了啊

167
00:07:26,949 --> 00:07:31,720
然后第二个王小虎的武功就是他

168
00:07:32,019 --> 00:07:32,920
对不对

169
00:07:32,920 --> 00:07:35,199
那么这个就是xml的描述方式

170
00:07:35,199 --> 00:07:36,449
大家可以对比一下

171
00:07:36,449 --> 00:07:39,000
他是用节点的方式来进行描述的

172
00:07:39,000 --> 00:07:40,649
那么他跟jason啊

173
00:07:40,649 --> 00:07:44,069
为什么说jason jason就会比他更流行

174
00:07:44,069 --> 00:07:48,750
因为它描述数据比较这个清晰也比较简单

175
00:07:48,750 --> 00:07:49,410
对不对

176
00:07:49,410 --> 00:07:50,550
而且大家可以看啊

177
00:07:50,550 --> 00:07:53,250
比如说我们存本地或者网络传输的话

178
00:07:53,250 --> 00:07:55,740
我们希望这个数据容量尽量小

179
00:07:55,740 --> 00:07:56,939
那大家可以看一下

180
00:07:56,939 --> 00:07:58,680
在这边除了我们必要的信息

181
00:07:58,680 --> 00:08:01,829
比如说十李逍遥想用18掌独孤九剑

182
00:08:01,829 --> 00:08:03,428
这是必要的信息之外

183
00:08:03,428 --> 00:08:06,129
其他的像杰森也就是个什么大括号中括号

184
00:08:06,129 --> 00:08:10,149
逗号这个这些符号它占不了多大

185
00:08:10,149 --> 00:08:13,300
而这个对于jason对于xml而言

186
00:08:13,560 --> 00:08:16,019
它这个节点很占容量

187
00:08:16,019 --> 00:08:17,399
而且它都是有个头

188
00:08:17,399 --> 00:08:18,120
还有个尾

189
00:08:18,120 --> 00:08:19,399
有个头有个尾

190
00:08:19,399 --> 00:08:25,939
所以实际上最后大家会发现通过xml描述的这个数据容量会比杰森大啊

191
00:08:25,939 --> 00:08:27,620
这也是它不好的一点

192
00:08:28,038 --> 00:08:30,379
什么时候xm 2的优势能出来啊

193
00:08:30,379 --> 00:08:32,359
因为它是有节点的

194
00:08:32,359 --> 00:08:34,219
所以他描述数据很清晰

195
00:08:34,219 --> 00:08:35,019
对不对

196
00:08:35,039 --> 00:08:37,320
那么当数据量很复杂的时候

197
00:08:37,320 --> 00:08:39,480
比如说数据量特别庞大的时候

198
00:08:39,480 --> 00:08:42,539
这时候如果全用这个jason什么括号

199
00:08:42,539 --> 00:08:44,600
一个括号嵌套一个括号啊

200
00:08:44,600 --> 00:08:47,659
你发现嵌套无数层就很很很晕啊

201
00:08:47,659 --> 00:08:48,620
看起来就很晕

202
00:08:48,620 --> 00:08:50,720
那所以当数据量很大的时候

203
00:08:50,720 --> 00:08:52,759
xml就会派上用场了啊

204
00:08:52,759 --> 00:08:54,320
他就会清晰一些了啊

205
00:08:54,320 --> 00:08:55,799
但是对于我们而言

206
00:08:55,799 --> 00:08:57,720
一般这个游戏里面的数据啊

207
00:08:57,720 --> 00:09:00,059
jason足够用啊

208
00:09:00,059 --> 00:09:01,279
jason足够用

209
00:09:01,580 --> 00:09:03,559
那么最后一个csv

210
00:09:03,559 --> 00:09:05,330
比如说csv是什么

211
00:09:05,330 --> 00:09:10,460
csv就是最开始比如说大家不想用这两种格式啊

212
00:09:10,460 --> 00:09:12,740
想一个最最简单最省容量的格式

213
00:09:12,740 --> 00:09:14,019
就这样去写了

214
00:09:14,220 --> 00:09:15,360
比如说李逍遥

215
00:09:15,360 --> 00:09:18,240
他自己就知道第一个是i d是实

216
00:09:18,240 --> 00:09:21,330
第二个是李逍遥啊

217
00:09:21,330 --> 00:09:24,669
第三个武功对不对

218
00:09:24,669 --> 00:09:26,950
他就降龙18掌

219
00:09:30,860 --> 00:09:33,379
然后第二行就是第二个人的信息

220
00:09:33,379 --> 00:09:36,080
比如二王小虎

221
00:09:36,080 --> 00:09:37,519
然后他的武功

222
00:09:37,519 --> 00:09:39,559
比如说磨刀是吧

223
00:09:41,440 --> 00:09:43,379
什么翻云覆雨

224
00:09:44,399 --> 00:09:48,480
那么这就是csv的描述方式很简单

225
00:09:48,480 --> 00:09:50,299
它就是用逗号隔开

226
00:09:50,299 --> 00:09:52,519
用逗号隔开每一个信息

227
00:09:52,519 --> 00:09:55,879
比如说我给我把这个存到本地了啊

228
00:09:55,879 --> 00:09:58,279
我就是通过字符串拼接啊

229
00:09:58,279 --> 00:09:59,809
每一个属性诶

230
00:09:59,809 --> 00:10:00,980
我往这儿填一个

231
00:10:00,980 --> 00:10:02,059
然后用逗号隔开

232
00:10:02,059 --> 00:10:03,080
再加第二个属性

233
00:10:03,080 --> 00:10:04,639
逗号隔开第三个属性

234
00:10:04,639 --> 00:10:07,279
这样的话如果一个人玩了第二个人

235
00:10:07,279 --> 00:10:09,899
就第二行通过这种方式存到本地

236
00:10:09,899 --> 00:10:15,360
最好的好处就是它它的额外符号也就只有逗号了

237
00:10:15,360 --> 00:10:16,799
他是最省容量的

238
00:10:16,799 --> 00:10:17,159
对不对

239
00:10:17,159 --> 00:10:18,330
它是最省容量的

240
00:10:18,330 --> 00:10:21,139
但是坏处就是你的你的记住

241
00:10:21,460 --> 00:10:23,169
比如说用逗号分割

242
00:10:23,169 --> 00:10:24,740
分割完以后

243
00:10:25,240 --> 00:10:27,909
第一个逗号前面的内容啊是什么

244
00:10:27,909 --> 00:10:29,679
第二个逗号前面的内容是什么

245
00:10:29,679 --> 00:10:31,690
第三个逗号的内容前面是什么

246
00:10:31,690 --> 00:10:33,289
这个你要自己记住

247
00:10:33,289 --> 00:10:34,490
你要是自己忘了

248
00:10:34,490 --> 00:10:36,110
你拿个cs文件

249
00:10:36,110 --> 00:10:37,850
csv文件在这看

250
00:10:37,850 --> 00:10:39,029
你看不懂

251
00:10:39,029 --> 00:10:39,960
你看不懂

252
00:10:39,960 --> 00:10:41,479
这个就很麻烦了

253
00:10:42,139 --> 00:10:46,899
而且这我说的是就是这个数据格式完全由你自己创建的情况下

254
00:10:46,899 --> 00:10:47,500
是这样的

255
00:10:47,500 --> 00:10:51,710
比如说如果别人现在创建了个格式给你

256
00:10:51,710 --> 00:10:53,879
如果别人给你个json格式

257
00:10:53,879 --> 00:10:55,139
你自己分析一下

258
00:10:55,139 --> 00:10:56,519
你完全能分析出来

259
00:10:56,519 --> 00:10:58,500
别人给你个xml格式

260
00:10:58,500 --> 00:11:00,599
你去分析一下也能分析出来

261
00:11:00,599 --> 00:11:03,028
如果别人给你个csv文件

262
00:11:03,028 --> 00:11:05,458
那比如说现在这个人除了这个

263
00:11:05,458 --> 00:11:07,409
他还有年龄

264
00:11:07,409 --> 00:11:08,859
有攻击力

265
00:11:09,340 --> 00:11:10,840
有这个防御值

266
00:11:10,840 --> 00:11:13,240
那大家看如果再多几条信息

267
00:11:13,240 --> 00:11:16,549
你能分清楚这这些分别是什么意思吗

268
00:11:16,549 --> 00:11:17,149
你都不知道

269
00:11:17,149 --> 00:11:18,169
你必须去问他

270
00:11:18,169 --> 00:11:18,830
问他诶

271
00:11:18,830 --> 00:11:19,850
你这个第几个逗号

272
00:11:19,850 --> 00:11:21,409
前面这个数值是什么

273
00:11:21,409 --> 00:11:22,909
然后我拿到这个数值

274
00:11:22,909 --> 00:11:25,009
我才知道它是代表的什么意思

275
00:11:25,009 --> 00:11:26,120
对不对啊

276
00:11:26,120 --> 00:11:27,320
所以这个就很麻烦了

277
00:11:27,320 --> 00:11:30,740
所以它对于数据描述而言不清晰啊

278
00:11:30,740 --> 00:11:32,840
csv就是一个简单的数据格式啊

279
00:11:32,840 --> 00:11:36,279
它就没有任何东西去描述这里面的数据啊

280
00:11:36,279 --> 00:11:39,159
但是它又是最省容量的啊

281
00:11:39,159 --> 00:11:41,080
这就是它的特点


1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,859 --> 00:00:12,640
ok这节课我们来说下动作系统啊

3
00:00:12,640 --> 00:00:20,019
那么这个动作系统其实呃算是cocos里面本身自己的一个系统啊

4
00:00:20,019 --> 00:00:21,339
它本身就有的系统

5
00:00:21,359 --> 00:00:25,859
而我们这边呢对它基本上可以说没有经过任何封装啊

6
00:00:25,859 --> 00:00:28,379
我们就可以直接拿来去使用呃

7
00:00:28,379 --> 00:00:30,719
但是他偶尔还是比较有用处的

8
00:00:30,719 --> 00:00:32,539
所以咱们来说一下啊

9
00:00:43,380 --> 00:00:46,890
那么其实所有的这个cocks呃

10
00:00:46,890 --> 00:00:47,759
内部啊

11
00:00:47,759 --> 00:00:51,659
cos 2 d内部的这些东西我们都可以拿来去用啊

12
00:00:51,659 --> 00:00:55,759
只是很多东西我们cos creator对它进行了封装啊

13
00:00:55,759 --> 00:00:58,259
所以我们不会直接使用啊

14
00:00:58,259 --> 00:00:59,939
不会像这个动作系统一样

15
00:00:59,939 --> 00:01:02,799
动作系统就是我们拿来直接去使用它

16
00:01:05,719 --> 00:01:09,439
那么在这里啊我们创建一个节点啊

17
00:01:09,439 --> 00:01:12,040
创建一个单色节点

18
00:01:12,159 --> 00:01:13,959
比如说我把它放到一个位置

19
00:01:13,959 --> 00:01:15,040
大家可以看一下

20
00:01:15,040 --> 00:01:18,019
我把它放到一个呃

21
00:01:18,019 --> 00:01:20,540
比如说大概300~300的一个位置吧

22
00:01:20,540 --> 00:01:22,219
当做我们的起始位置啊

23
00:01:22,219 --> 00:01:23,569
当我们起始位置

24
00:01:23,569 --> 00:01:28,239
然后我们给它添加一个脚本啊

25
00:01:28,239 --> 00:01:28,959
通过这个脚本

26
00:01:28,959 --> 00:01:32,290
我们给它很多动作脚本添加上来

27
00:01:32,290 --> 00:01:34,219
打开这个脚本

28
00:01:39,299 --> 00:01:40,719
打开脚本

29
00:01:42,840 --> 00:01:48,480
那么在这里啊我们脚本这里我们在start里面直接去写就好了啊

30
00:01:48,480 --> 00:01:52,939
首先动作他是一定要创建一个动作的

31
00:01:52,939 --> 00:01:53,629
对不对

32
00:01:53,629 --> 00:01:56,120
那么这个动作怎么去创建呢

33
00:01:56,120 --> 00:02:00,140
他在这里全都是用静态方法给我们创建的啊

34
00:02:00,140 --> 00:02:00,739
什么意思

35
00:02:00,739 --> 00:02:02,719
比如说我们要创建一个动作啊

36
00:02:02,719 --> 00:02:04,489
我们要创建一个动作

37
00:02:04,489 --> 00:02:12,879
let x2 就等于一个cc啊

38
00:02:12,879 --> 00:02:14,439
cc是他的这个名称空间

39
00:02:14,439 --> 00:02:15,419
对不对点

40
00:02:15,419 --> 00:02:18,120
然后后面跟你的这个动作名称就行了

41
00:02:18,120 --> 00:02:20,520
那我们在这里说一些常用的动作

42
00:02:20,520 --> 00:02:25,090
比如说木兔move to

43
00:02:25,090 --> 00:02:26,259
意思就是移动

44
00:02:26,259 --> 00:02:29,120
那么它的参数就是多长时间

45
00:02:29,120 --> 00:02:33,020
比如说两秒钟的时间移动到哪个位置位置

46
00:02:33,020 --> 00:02:34,879
我们可以用cc v2 啊

47
00:02:34,879 --> 00:02:36,099
然后给他一个位置

48
00:02:36,120 --> 00:02:38,159
也可以直接去写位置

49
00:02:38,159 --> 00:02:41,819
比如说移动到200x200也是可以的啊

50
00:02:41,819 --> 00:02:43,259
两种方式都可以

51
00:02:43,280 --> 00:02:45,240
比如说我们就用这种方式

52
00:02:45,259 --> 00:02:48,740
我们现在在300~300的坐标

53
00:02:48,740 --> 00:02:51,340
我让它移动到200~200啊

54
00:02:51,340 --> 00:02:52,900
那么这时候还没有动呢

55
00:02:52,900 --> 00:02:54,699
这只是一个动作还没有动起来

56
00:02:54,699 --> 00:02:56,719
怎样让这个动作动起来

57
00:02:57,419 --> 00:03:00,870
那么就是this.node是我们的这个节点

58
00:03:00,870 --> 00:03:03,080
一个run action

59
00:03:03,500 --> 00:03:06,020
把我们的这个action放到这儿就可以了

60
00:03:06,020 --> 00:03:09,379
这个就是执行动作

61
00:03:11,759 --> 00:03:16,409
哦嗯那么我们这边写完以后

62
00:03:16,409 --> 00:03:18,439
我们回到我们这边

63
00:03:18,639 --> 00:03:20,340
然后来运行一下

64
00:03:25,719 --> 00:03:27,219
我们看一下啊

65
00:03:27,219 --> 00:03:29,650
它的位置是从这个300~300

66
00:03:29,650 --> 00:03:31,120
花了两秒钟的时间

67
00:03:31,120 --> 00:03:31,780
做了个动画

68
00:03:31,780 --> 00:03:34,300
移到移动到这个200x200的

69
00:03:34,300 --> 00:03:35,080
对不对

70
00:03:36,699 --> 00:03:37,360
ok啊

71
00:03:37,360 --> 00:03:38,280
我们回来

72
00:03:41,020 --> 00:03:42,219
非常简单

73
00:03:42,219 --> 00:03:43,879
用起来非常简单

74
00:03:44,379 --> 00:03:44,949
呃

75
00:03:44,949 --> 00:03:46,000
那么这个动作

76
00:03:46,000 --> 00:03:47,979
比如说我如果运动到一半

77
00:03:47,979 --> 00:03:49,120
我想停怎么办

78
00:03:49,120 --> 00:03:50,319
注意要想停的时候

79
00:03:50,319 --> 00:03:52,819
你就直接掉这个停止的方法就好了

80
00:03:54,520 --> 00:03:56,500
这个嗓子有点不舒服啊

81
00:03:56,500 --> 00:04:00,400
可能这个声音都有点和之前的不太一样了

82
00:04:00,400 --> 00:04:02,949
是不是听起来

83
00:04:02,949 --> 00:04:09,800
嗯那么停止动作我们在这里会看到这样三个啊

84
00:04:09,800 --> 00:04:11,240
我们一个一个来说一下

85
00:04:11,240 --> 00:04:13,879
首先第一个是停止某个动作

86
00:04:13,879 --> 00:04:15,199
你要停止动作的话

87
00:04:15,199 --> 00:04:18,980
你就要把停止的动作放这只要一掉这个方法啊

88
00:04:18,980 --> 00:04:22,279
就是那么这个action动作就会立刻停下来啊

89
00:04:22,279 --> 00:04:24,379
不管当前action是什么样的动作

90
00:04:24,379 --> 00:04:25,680
都会立刻停下来

91
00:04:25,759 --> 00:04:31,620
还有一个就是this.note.stop all actions啊

92
00:04:31,620 --> 00:04:33,480
那么就是停止啊

93
00:04:33,480 --> 00:04:35,839
这个节点上面所有的一个动作

94
00:04:35,839 --> 00:04:38,000
所有的动作我都停止啊

95
00:04:38,000 --> 00:04:41,259
你比如说他我让他执行这个动作

96
00:04:41,259 --> 00:04:43,060
同时又让他执行另外一个动作

97
00:04:43,060 --> 00:04:44,709
他同时执行了两个动作

98
00:04:44,709 --> 00:04:47,050
我这个就可以停止所有的动作

99
00:04:47,050 --> 00:04:48,259
还有一个

100
00:04:51,240 --> 00:04:54,000
stop action by tag值啊

101
00:04:54,000 --> 00:05:00,160
那有这个tag大家就知道他这个是通过一个tag tag标签来进行停止的

102
00:05:00,160 --> 00:05:00,759
tag

103
00:05:00,759 --> 00:05:02,139
我们在做碰撞的时候

104
00:05:02,139 --> 00:05:03,879
我们就见识过他了

105
00:05:03,879 --> 00:05:04,360
对不对

106
00:05:04,360 --> 00:05:06,310
那么它我们可以给他一个标记

107
00:05:06,310 --> 00:05:09,620
通过标记来区分区分我们的碰撞体

108
00:05:09,620 --> 00:05:10,610
这边也一样

109
00:05:10,610 --> 00:05:13,730
我们可以给动作加上这个tag值

110
00:05:13,730 --> 00:05:15,560
然后在这里通过tag值

111
00:05:15,560 --> 00:05:18,639
比如说停止33号动作

112
00:05:18,639 --> 00:05:22,839
那么就证明我们其实这个动作是可以设置tag值的

113
00:05:22,839 --> 00:05:24,550
怎么样设置试试

114
00:05:24,550 --> 00:05:25,839
action.set

115
00:05:25,839 --> 00:05:28,660
你看设置tag值啊

116
00:05:28,660 --> 00:05:31,300
你可以给你的动作开始执行了

117
00:05:31,300 --> 00:05:33,399
给它设置一个tag值之后

118
00:05:33,399 --> 00:05:37,579
我们就可以通过这个tp值来进行停止了

119
00:05:41,379 --> 00:05:45,430
嗯那么当然有这个开始停止

120
00:05:45,430 --> 00:05:48,129
还有这个暂停

121
00:05:48,129 --> 00:05:49,600
对不对啊

122
00:05:49,600 --> 00:05:51,540
暂停嗯

123
00:05:51,540 --> 00:05:53,279
然后还有这个重新开始

124
00:05:53,279 --> 00:05:55,259
就是继续恢复啊

125
00:05:55,259 --> 00:06:00,300
当然这个暂停和恢复大家发现他只能对这个

126
00:06:00,300 --> 00:06:01,540
比如说暂停

127
00:06:01,879 --> 00:06:03,920
你看暂停和停止都一样

128
00:06:03,920 --> 00:06:05,269
它都是all actions

129
00:06:05,269 --> 00:06:07,160
就是所有运行的啊

130
00:06:07,160 --> 00:06:08,300
就是你不能暂停一个

131
00:06:08,300 --> 00:06:09,649
要不然就全部暂停

132
00:06:09,649 --> 00:06:12,629
要不然就全部呃恢复啊

133
00:06:12,629 --> 00:06:13,620
全部继续啊

134
00:06:13,620 --> 00:06:18,040
你不能说我对某一个节点暂停啊

135
00:06:18,399 --> 00:06:19,779
那不是不能对

136
00:06:19,779 --> 00:06:22,000
不能说对某一个动作暂停啊

137
00:06:22,000 --> 00:06:22,720
是这样

138
00:06:24,920 --> 00:06:26,839
你看暂停停止呃

139
00:06:26,839 --> 00:06:27,980
暂停恢复啊

140
00:06:27,980 --> 00:06:29,459
都是所有的动作

141
00:06:32,319 --> 00:06:34,600
那么这一块都是停止的啊

142
00:06:34,600 --> 00:06:37,360
停止的我们知道一下就ok了啊

143
00:06:37,360 --> 00:06:41,439
很多情况下我们甚至可能让他播放完了就不管了啊

144
00:06:41,540 --> 00:06:45,139
然后嗯偶尔我们会让它去停止

145
00:06:45,139 --> 00:06:48,379
能用的最多的就是直接去停止就行了啊

146
00:06:48,800 --> 00:06:52,480
那么这个动作的话我们注意啊

147
00:06:53,920 --> 00:06:55,779
每个很多动作啊

148
00:06:55,779 --> 00:06:58,300
他如果出现它是成对出现的

149
00:06:58,300 --> 00:06:59,579
比如说这个mo

150
00:06:59,600 --> 00:07:01,100
你看move除了一个to

151
00:07:01,100 --> 00:07:01,699
还有一个by

152
00:07:01,699 --> 00:07:02,240
对不对

153
00:07:02,240 --> 00:07:04,699
我们先要区分这一对儿的区别啊

154
00:07:04,699 --> 00:07:07,740
之后出现这个呃to和by

155
00:07:07,740 --> 00:07:09,029
大家就明白怎么回事

156
00:07:09,029 --> 00:07:11,160
首先move to我们已经知道了

157
00:07:11,160 --> 00:07:13,019
就是它移动到200~200的位置

158
00:07:13,019 --> 00:07:13,660
对不对

159
00:07:13,680 --> 00:07:15,120
那这里有个move by

160
00:07:15,120 --> 00:07:17,519
我们如果现在给他改成move by了

161
00:07:17,519 --> 00:07:19,579
我们看一下它的运动是什么样子

162
00:07:25,560 --> 00:07:28,379
诶它在300~300的位置

163
00:07:28,379 --> 00:07:30,899
它向右上方去移动了

164
00:07:30,899 --> 00:07:32,680
其实很清楚

165
00:07:33,220 --> 00:07:34,720
其实很清楚

166
00:07:34,740 --> 00:07:35,579
对不对

167
00:07:35,579 --> 00:07:36,660
什么意思

168
00:07:36,660 --> 00:07:39,839
这个move by就是以我自己为原点

169
00:07:39,839 --> 00:07:44,079
然后为零点向右上方移动200~200的位置

170
00:07:44,079 --> 00:07:46,810
也就是说这个是移动多少位置啊

171
00:07:46,810 --> 00:07:47,920
x移动200位置

172
00:07:47,920 --> 00:07:49,139
y移动200位置

173
00:07:49,160 --> 00:07:50,360
这个是什么呀

174
00:07:50,360 --> 00:07:52,220
移动到哪个位置啊

175
00:07:52,220 --> 00:07:54,860
移动到200逗号200这个位置

176
00:07:54,860 --> 00:07:56,209
这是移动200

177
00:07:56,209 --> 00:07:59,699
逗号200这样一个位置其实很容易区分

178
00:07:59,699 --> 00:08:01,079
这是一个绝对的一个位置

179
00:08:01,079 --> 00:08:02,339
这是一个相对的位置

180
00:08:02,339 --> 00:08:02,759
对不对

181
00:08:02,759 --> 00:08:05,360
相对的位置把这个一定要搞清楚啊

182
00:08:05,360 --> 00:08:07,160
to所有的to都是绝对的

183
00:08:07,160 --> 00:08:09,620
那比如说我们如果有旋转啊

184
00:08:09,620 --> 00:08:10,819
先不要考虑别的

185
00:08:10,819 --> 00:08:11,759
如果有旋转

186
00:08:11,759 --> 00:08:13,879
大家想如果有个旋转的

187
00:08:13,879 --> 00:08:16,040
然后rotate对不对

188
00:08:16,040 --> 00:08:17,180
rootto啊

189
00:08:17,180 --> 00:08:18,139
如果有这个的话

190
00:08:18,139 --> 00:08:19,759
那么它的意思是什么

191
00:08:20,339 --> 00:08:22,079
road to 90

192
00:08:22,079 --> 00:08:25,620
意思就是我不管当前我角度是多少

193
00:08:25,620 --> 00:08:27,240
我都要转到90度

194
00:08:27,240 --> 00:08:29,600
而move by 90是什么意思

195
00:08:30,079 --> 00:08:32,360
不管当前是多少度

196
00:08:32,360 --> 00:08:35,389
我只我都要转90度啊

197
00:08:35,389 --> 00:08:37,250
我就是自己还要转90度

198
00:08:37,250 --> 00:08:39,200
而第一个就是转到90度

199
00:08:39,200 --> 00:08:40,720
比如说我现在89度

200
00:08:40,740 --> 00:08:42,240
我move to到90度

201
00:08:42,240 --> 00:08:43,289
那我只转1度

202
00:08:43,289 --> 00:08:45,179
而move by就是现在89度

203
00:08:45,179 --> 00:08:47,129
我转590度

204
00:08:47,129 --> 00:08:49,559
我从89我还要转上90度

205
00:08:49,580 --> 00:08:50,120
对不对

206
00:08:50,120 --> 00:08:51,860
所以这就是一个绝对的一个相对的

207
00:08:51,860 --> 00:08:53,120
你不管是移动啊

208
00:08:53,120 --> 00:08:53,779
旋转啊

209
00:08:53,779 --> 00:08:56,059
甚至缩放都是这个啊

210
00:08:56,059 --> 00:08:58,220
那么大家先区分这两种啊

211
00:08:58,220 --> 00:08:59,179
区分这两种

212
00:08:59,860 --> 00:09:01,960
那么我们再说一些其他的啊

213
00:09:01,960 --> 00:09:07,360
首先我们就可以来看一下刚才我们想的这个rotate有没有啊

214
00:09:07,360 --> 00:09:07,840
果然有

215
00:09:07,840 --> 00:09:09,600
你看是一对啊

216
00:09:09,600 --> 00:09:11,480
我们是一个好啊

217
00:09:11,480 --> 00:09:14,179
大家知道他们俩的区别会用就可以了啊

218
00:09:14,179 --> 00:09:15,710
我们是就只剩一个了

219
00:09:15,710 --> 00:09:19,960
比如说root我们就用这个rotto两秒

220
00:09:19,980 --> 00:09:23,929
比如说赚100赚100度

221
00:09:23,929 --> 00:09:25,460
转到100度啊

222
00:09:25,460 --> 00:09:27,919
moon rotto是赚到100度

223
00:09:28,240 --> 00:09:32,080
我们看一下转转转90了

224
00:09:32,080 --> 00:09:34,179
你看这是不是刚好100啊

225
00:09:34,179 --> 00:09:34,899
100

226
00:09:38,159 --> 00:09:43,860
然后旋转有了啊移动啊

227
00:09:43,860 --> 00:09:45,960
这这这这这上面俩都是移动

228
00:09:49,299 --> 00:09:50,980
这是旋转啊

229
00:09:50,980 --> 00:09:53,039
这些是比较常用的动作

230
00:09:55,899 --> 00:09:57,659
呃说话

231
00:10:01,559 --> 00:10:04,139
kill也是一个to一个by

232
00:10:04,139 --> 00:10:05,519
对不对啊

233
00:10:05,519 --> 00:10:09,100
比如说我缩放缩放到多少倍

234
00:10:09,100 --> 00:10:12,799
我们就1.5缩放到1.5倍吧

235
00:10:15,399 --> 00:10:18,659
运行诶逐渐变大

236
00:10:18,879 --> 00:10:21,220
那么其实对于这个缩放来来说

237
00:10:21,220 --> 00:10:25,309
我们可以对它的x和y进行不同比例的缩放

238
00:10:25,309 --> 00:10:25,820
是不是

239
00:10:25,820 --> 00:10:27,559
那怎么样给它不同比例缩放

240
00:10:27,559 --> 00:10:29,299
你只要再加上一个值

241
00:10:29,299 --> 00:10:30,740
比如说0.5

242
00:10:30,740 --> 00:10:32,960
那么1.5就只指x了

243
00:10:32,960 --> 00:10:36,009
0.5就代表y啊

244
00:10:36,009 --> 00:10:38,059
那么我们这边运行一下

245
00:10:39,340 --> 00:10:41,440
你看x啊

246
00:10:41,440 --> 00:10:43,240
是变成变成1.5倍了

247
00:10:43,240 --> 00:10:44,960
y是变成0.5倍了

248
00:10:53,000 --> 00:10:57,879
然后突然后有个这个这个跳跃

249
00:11:04,279 --> 00:11:08,659
jump by跳跃跳跃

250
00:11:08,659 --> 00:11:12,529
比如说两秒时间嗯

251
00:11:12,529 --> 00:11:15,700
跳200逗号零

252
00:11:16,220 --> 00:11:18,659
那么这个是什么意思啊

253
00:11:18,700 --> 00:11:20,350
200逗号零

254
00:11:20,350 --> 00:11:21,519
他首先是一个位置

255
00:11:21,519 --> 00:11:23,799
我要跳到200~0啊

256
00:11:23,799 --> 00:11:25,240
我们用的是jambi是吧

257
00:11:25,240 --> 00:11:29,879
那就是从我的位置往x方向往右边跳200位置啊

258
00:11:29,879 --> 00:11:31,220
y不变啊

259
00:11:31,220 --> 00:11:32,299
就是y无变啊

260
00:11:32,299 --> 00:11:35,600
就相当于从我当前的位置往右跳了200的位置

261
00:11:35,600 --> 00:11:37,820
因为我们用的是这个b对不对

262
00:11:40,740 --> 00:11:47,039
然后呃逗号我这里大家可以看这个参数是个高度啊

263
00:11:47,039 --> 00:11:47,940
就是你跳多高

264
00:11:47,940 --> 00:11:48,539
对不对

265
00:11:48,539 --> 00:11:49,620
比如说跳100

266
00:11:49,620 --> 00:11:51,059
最后一个是跳几次

267
00:11:51,059 --> 00:11:52,519
js是跳的次数

268
00:11:52,519 --> 00:11:54,559
我们给他一个一一就行了

269
00:11:54,559 --> 00:11:55,610
我们就跳一次

270
00:11:55,610 --> 00:11:57,019
我们运行看一下效果

271
00:11:57,820 --> 00:12:02,700
向右你看是不是这样一个效果啊

272
00:12:07,700 --> 00:12:10,580
那么在这里嗯

273
00:12:12,779 --> 00:12:14,639
比如说我们次数再改一下

274
00:12:14,639 --> 00:12:15,659
改成跳三次

275
00:12:15,659 --> 00:12:16,620
看一下效果

276
00:12:20,179 --> 00:12:21,980
是不是很有意思啊

277
00:12:21,980 --> 00:12:23,840
这就是一个跳跃的动作啊

278
00:12:23,840 --> 00:12:27,620
跳跃的动作大家也可以试一下move to啊

279
00:12:27,620 --> 00:12:29,840
那是一个绝对的啊

280
00:12:29,840 --> 00:12:32,039
就跳到哪一个点就可以了

281
00:12:32,600 --> 00:12:35,450
我发现我可能不光是这个声音

282
00:12:35,450 --> 00:12:38,220
可能是有点着凉或者什么的

283
00:12:39,480 --> 00:12:42,740
那不舒服啊

284
00:12:44,340 --> 00:12:45,120
ok啊

285
00:12:45,120 --> 00:12:46,320
我们先继续啊

286
00:12:46,320 --> 00:12:48,000
先继续

287
00:12:48,000 --> 00:12:50,919
然后我们还有一个闪烁

288
00:12:53,259 --> 00:12:57,080
闪烁cc.link

289
00:12:58,779 --> 00:12:59,740
然后在这里

290
00:12:59,740 --> 00:13:08,360
比如说闪烁三三秒查的时间是不是几次五次啊

291
00:13:08,360 --> 00:13:09,500
三秒闪烁五次

292
00:13:09,500 --> 00:13:10,879
我们来看一下效果

293
00:13:14,320 --> 00:13:17,799
你看一共三秒钟时间闪了这么五次啊

294
00:13:17,799 --> 00:13:19,259
和信号灯似的是吧

295
00:13:21,899 --> 00:13:24,269
闪烁

296
00:13:24,269 --> 00:13:28,480
然后我们还有一个是淡入淡出

297
00:13:28,919 --> 00:13:30,659
比如说现在说弹出

298
00:13:30,659 --> 00:13:33,019
因为我们本来现在就显示了

299
00:13:33,480 --> 00:13:37,700
弹出的话就是fade out

300
00:13:38,460 --> 00:13:41,620
别的up弹出弹出

301
00:13:41,620 --> 00:13:43,000
那么就是多长时间

302
00:13:43,000 --> 00:13:44,320
比如说三秒啊

303
00:13:44,320 --> 00:13:45,789
就没别的参数了

304
00:13:45,789 --> 00:13:48,649
运行逐渐消失

305
00:13:48,649 --> 00:13:50,240
逐渐消失

306
00:13:50,240 --> 00:13:52,860
对不对啊

307
00:13:52,860 --> 00:13:55,320
这就是这个弹出

308
00:13:55,320 --> 00:13:58,960
那么有弹出当然就有弹幕了

309
00:14:00,200 --> 00:14:01,580
代入啊

310
00:14:01,580 --> 00:14:04,879
这个一般是在弹出以后会做淡入的啊

311
00:14:04,879 --> 00:14:06,080
我们现在做没有意义

312
00:14:06,080 --> 00:14:07,600
因为我们本来就看得见

313
00:14:07,620 --> 00:14:10,799
这个代入的意思就是从看看不见到看得见

314
00:14:10,799 --> 00:14:11,500
对不对

315
00:14:14,899 --> 00:14:20,799
然后我们指定一个呃这个这个渐变

316
00:14:24,080 --> 00:14:25,879
啊我指指另一个界面

317
00:14:25,879 --> 00:14:32,580
就是这个action等一个cc.fade to啊

318
00:14:32,580 --> 00:14:35,840
就是指定你这个

319
00:14:36,980 --> 00:14:39,799
现在啊到什么样的一个透明度

320
00:14:39,799 --> 00:14:41,840
因为它实际上修改的就是一个透明值

321
00:14:41,840 --> 00:14:42,440
阿尔法值

322
00:14:42,440 --> 00:14:43,019
对不对

323
00:14:43,039 --> 00:14:48,379
那这个就是花费多长时间到达一个什么样的透明度

324
00:14:48,379 --> 00:14:49,519
这是0~255

325
00:14:49,519 --> 00:14:50,360
我们给个100

326
00:14:50,360 --> 00:14:51,580
就是半透明

327
00:14:51,659 --> 00:14:54,899
我们让当前变成一个半透明的样子

328
00:14:56,100 --> 00:14:58,889
你看逐渐半透明

329
00:14:58,889 --> 00:15:00,840
然后到了大概100的数值

330
00:15:00,840 --> 00:15:02,799
它就停下来了啊

331
00:15:07,460 --> 00:15:09,559
然后再往下还有什么呀

332
00:15:09,559 --> 00:15:11,389
可以改变颜色啊

333
00:15:11,389 --> 00:15:14,200
可以对这个颜色做做一个改变

334
00:15:14,639 --> 00:15:19,320
tto啊

335
00:15:19,320 --> 00:15:22,070
我们直接用to就是到达某一个颜色

336
00:15:22,070 --> 00:15:25,980
比如说三秒钟我们要到达一个什么样的颜色

337
00:15:25,980 --> 00:15:26,940
它这是红绿蓝

338
00:15:26,940 --> 00:15:29,159
让你去填三个颜色的一个混合度

339
00:15:29,159 --> 00:15:30,799
每个都是0~25

340
00:15:30,799 --> 00:15:33,139
我们给了多少纯色吗

341
00:15:33,620 --> 00:15:36,950
100的红色100啊算了

342
00:15:36,950 --> 00:15:38,000
我喜欢紫色

343
00:15:38,000 --> 00:15:39,340
我们用紫色好了

344
00:15:39,620 --> 00:15:43,139
绿色可以加一点加加一点

345
00:15:44,159 --> 00:15:47,399
主要是红色跟蓝色混合

346
00:15:47,980 --> 00:15:52,750
ok我们这里做了一个130 100的这样的一个颜色

347
00:15:52,750 --> 00:15:54,440
我们来运行一下

348
00:15:54,899 --> 00:15:56,519
应该是个紫色

349
00:15:58,419 --> 00:16:00,429
那么没有问题啊

350
00:16:00,429 --> 00:16:04,539
那么它就是从你当前的颜色变到某一个颜色

351
00:16:04,539 --> 00:16:06,460
而且是逐渐变到某一个颜色

352
00:16:06,460 --> 00:16:07,200
对不对

353
00:16:09,960 --> 00:16:16,080
那么这些就是咱们嗯比较常用的一些动作啊

354
00:16:16,080 --> 00:16:18,840
那么这些动作有一个特点啊

355
00:16:19,379 --> 00:16:23,340
咱们说的目前啊说的都是延时动作啊

356
00:16:23,340 --> 00:16:27,019
也就是说他们全是和这个时间有关的啊

357
00:16:27,019 --> 00:16:30,409
那么实际上还有一大批动作

358
00:16:30,409 --> 00:16:33,019
它是和时间没关系的啊

359
00:16:33,019 --> 00:16:34,940
它是和时间没关系的

360
00:16:34,960 --> 00:16:36,519
那么咱们分开说

361
00:16:36,519 --> 00:16:39,639
大家这节课先把这些动作练习了

362
00:16:39,639 --> 00:16:43,320
然后我们下节课再说剩下的一组动作啊

363
00:16:43,320 --> 00:16:44,799
那我们这节课就这么多


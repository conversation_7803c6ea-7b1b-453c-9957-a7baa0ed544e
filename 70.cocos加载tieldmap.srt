1
00:00:09,019 --> 00:00:12,080
ok这节课我们来看一下生成的这个地图

2
00:00:12,080 --> 00:00:15,240
怎样在我们的这个cs里面去使用

3
00:00:15,999 --> 00:00:20,009
那么在这个地图啊绘制咱们就说这么多

4
00:00:20,009 --> 00:00:21,778
主要大家在这里啊

5
00:00:21,778 --> 00:00:22,798
就是要搞清楚的

6
00:00:22,798 --> 00:00:24,980
就是这个图层和这个对象层

7
00:00:24,980 --> 00:00:27,120
其实大家可以这样去想一想啊

8
00:00:28,160 --> 00:00:31,179
普通的图层绘制的都是死的啊

9
00:00:31,179 --> 00:00:33,219
也就是说如果这里有棵树啊

10
00:00:33,219 --> 00:00:35,079
那么这个树永远在这儿啊

11
00:00:35,079 --> 00:00:39,090
你在游戏里面它是不可能这个被我们改变的

12
00:00:39,090 --> 00:00:41,549
但是如果比如说一个地图上面有个金币

13
00:00:41,549 --> 00:00:42,990
我们一吃金币就没了

14
00:00:42,990 --> 00:00:46,119
那大家想如果你把金币挂到这个图块层上

15
00:00:46,119 --> 00:00:48,219
那么是不是就有问题了

16
00:00:48,219 --> 00:00:53,350
因为这样的话这个金币永远我们它都会显示在这个地图上啊

17
00:00:53,350 --> 00:00:54,850
你你这个作为玩家

18
00:00:54,850 --> 00:00:56,909
你想吃掉它是吃不掉的啊

19
00:00:56,909 --> 00:00:58,229
我们希望做到这一个金币

20
00:00:58,229 --> 00:01:00,030
我们玩家过来就能把它吃掉

21
00:01:00,030 --> 00:01:00,899
吃掉就没了

22
00:01:00,899 --> 00:01:05,609
那这个金币一定是我们自己手动生成的一个金币对象啊

23
00:01:05,609 --> 00:01:08,159
那这样的话怎么怎么做

24
00:01:08,159 --> 00:01:11,799
那只能把金币绘制成这个对象层啊

25
00:01:11,799 --> 00:01:12,909
绘制成对象层

26
00:01:12,909 --> 00:01:17,219
然后我们通过代码生成真正的金币啊

27
00:01:17,219 --> 00:01:18,599
就好比这这里啊

28
00:01:18,599 --> 00:01:20,069
这里就是我们举的一个例子

29
00:01:20,069 --> 00:01:21,870
这个代表的就是我们玩家

30
00:01:21,870 --> 00:01:26,269
那么这个玩家其实在这个地图上是不显示的啊

31
00:01:26,269 --> 00:01:30,859
当然我们是通过代码在这个位置生成一个真正的玩家啊

32
00:01:30,859 --> 00:01:34,219
然后这个玩家呢就可以在地图上面去进行移动啊

33
00:01:34,219 --> 00:01:36,310
就这样的一个意思呃

34
00:01:36,310 --> 00:01:38,680
总之就是有些地图啊就是会改变的

35
00:01:38,680 --> 00:01:43,500
这些东西一般都是在这个呃或者说产生交互的啊

36
00:01:43,500 --> 00:01:45,390
这些东西一般都要放到对象层

37
00:01:45,390 --> 00:01:47,159
然后死的啊

38
00:01:47,159 --> 00:01:49,069
不会产生交互的啊

39
00:01:49,069 --> 00:01:50,329
比如说这些树根呀

40
00:01:50,329 --> 00:01:51,650
花呀草啊这些的

41
00:01:51,650 --> 00:01:54,239
你就把它放到普通图层就可以啊

42
00:01:55,239 --> 00:01:57,540
ok啊那么回到这边

43
00:01:57,540 --> 00:01:59,010
我们创建一个新的工程

44
00:01:59,010 --> 00:02:01,430
然后新的工程窗口的话

45
00:02:01,430 --> 00:02:02,989
比如说让它稍微小一点

46
00:02:02,989 --> 00:02:05,780
比如说480乘以个320

47
00:02:11,938 --> 00:02:14,938
所以我们这个显示的这个窗口

48
00:02:14,938 --> 00:02:16,019
我们就让它小一点

49
00:02:16,019 --> 00:02:18,039
这样的话地图肯定会更大

50
00:02:18,879 --> 00:02:22,139
那因为很多游戏地图是大于我们的这个屏幕的

51
00:02:22,139 --> 00:02:22,900
对不对

52
00:02:23,778 --> 00:02:27,159
那在这里把我们的这个地图拖进来

53
00:02:28,139 --> 00:02:30,379
大家可以看这边就有我们的这个地图

54
00:02:30,379 --> 00:02:31,159
对不对

55
00:02:31,299 --> 00:02:34,778
这里有一个最后生成的这样的一个地图文件

56
00:02:34,778 --> 00:02:38,800
我们直接把这个地图文件拖拽到我们的这个场景里面

57
00:02:40,658 --> 00:02:42,688
那这时候大家就可以看到

58
00:02:42,688 --> 00:02:45,419
是不是我们刚才看到的那个地图就出来了

59
00:02:45,419 --> 00:02:49,000
然后对象层里面的那朵花是不是就看不见呀

60
00:02:50,099 --> 00:02:50,900
是不是

61
00:02:50,900 --> 00:02:55,819
所以对象层其实就是在真正显示的时候时候是不会显示的

62
00:02:58,860 --> 00:03:02,360
那么我们这时候大家可以看到这个窗口

63
00:03:04,580 --> 00:03:06,099
我们现在运行一下

64
00:03:06,399 --> 00:03:08,199
可以看到这样的一个位置

65
00:03:08,199 --> 00:03:08,679
对不对

66
00:03:08,679 --> 00:03:09,938
可以看到这个位置

67
00:03:09,938 --> 00:03:21,020
那么在这里比如说默认我把这个地图让它的左下角放到这个位置

68
00:03:22,599 --> 00:03:23,819
左下角啊

69
00:03:23,819 --> 00:03:26,240
左下角差不多放到这个位置就ok了

70
00:03:30,679 --> 00:03:33,519
也就是说我们屏幕现在运行起来以后

71
00:03:33,519 --> 00:03:35,498
默认是在左下角的啊

72
00:03:35,498 --> 00:03:37,599
默认是显示左下角的

73
00:03:39,218 --> 00:03:41,639
那么但是我们的玩家注意是在哪里

74
00:03:41,639 --> 00:03:43,240
玩家是在这里的

75
00:03:43,359 --> 00:03:46,899
我们看一下玩家是在这个位置的啊

76
00:03:46,899 --> 00:03:47,859
是在这个位置的

77
00:03:47,859 --> 00:03:49,658
大概是在这个位置啊

78
00:03:49,658 --> 00:03:51,150
大概是在这个位置

79
00:03:51,150 --> 00:03:53,969
那我们现在就希望当运行起来以后

80
00:03:53,969 --> 00:03:57,030
我们把这个玩家给他创建出来

81
00:03:57,030 --> 00:03:57,990
创建出来

82
00:03:57,990 --> 00:03:59,819
并且我们的这个

83
00:04:01,998 --> 00:04:06,169
摄像头就以玩家为中心来显示啊

84
00:04:06,169 --> 00:04:09,080
就摄像头以玩家为中心进行显示

85
00:04:09,080 --> 00:04:10,729
那这个东西怎么去做

86
00:04:10,729 --> 00:04:12,550
首先我们得有玩家吧

87
00:04:12,550 --> 00:04:13,960
首先得有玩家

88
00:04:13,960 --> 00:04:15,969
那玩家我们要放到哪

89
00:04:15,969 --> 00:04:22,019
那在这里玩家我们就要放到这个玩家层上啊

90
00:04:22,019 --> 00:04:23,720
就要放在这个玩家层上

91
00:04:23,720 --> 00:04:26,240
那比如说我在这儿创建一个空的节点吧

92
00:04:26,240 --> 00:04:28,759
我希望我所有的这个内容啊

93
00:04:28,759 --> 00:04:32,858
就是就所有的内容都放到这个空节点的这个里面

94
00:04:32,858 --> 00:04:34,899
当做它的子节点啊

95
00:04:36,218 --> 00:04:37,339
叫layer

96
00:04:39,079 --> 00:04:42,098
那么把这个空的节点放到左下角啊

97
00:04:42,098 --> 00:04:44,740
我希望以左下角为基准来做

98
00:04:45,000 --> 00:04:48,120
也就是说我现在希望啊在这一层里面

99
00:04:48,120 --> 00:04:49,879
比如说创建一个玩家

100
00:04:50,098 --> 00:04:51,059
玩家的话

101
00:04:51,059 --> 00:04:54,178
我们就用这个精灵来表示单色的精灵啊

102
00:04:54,178 --> 00:04:57,389
这个就是我们的玩家啊

103
00:04:57,389 --> 00:04:58,500
这个改个名字吧

104
00:04:58,500 --> 00:05:00,079
叫player layer

105
00:05:01,379 --> 00:05:04,519
然后这个精灵呢改个名字叫做ler

106
00:05:07,379 --> 00:05:10,120
我们把它啊可以放上来

107
00:05:10,259 --> 00:05:12,779
也就是说我们现在用它来代表一个玩家

108
00:05:12,779 --> 00:05:16,560
然后它的宽高给它设置成32x32啊

109
00:05:16,560 --> 00:05:19,019
就是一个格子的一个宽高啊

110
00:05:19,579 --> 00:05:21,800
呃在这里我们知道啊

111
00:05:21,800 --> 00:05:24,569
这个玩家上来肯定是不存在的啊

112
00:05:24,569 --> 00:05:27,990
我们现在是希望通过代码来创建这个玩家

113
00:05:27,990 --> 00:05:30,449
所以当把这个玩家创建出来以后

114
00:05:30,449 --> 00:05:33,720
我们现在把这个玩家做成一个预设体

115
00:05:33,720 --> 00:05:35,699
既然要做成预设题的话

116
00:05:35,699 --> 00:05:38,480
我们要动态加载一下这个预设体吧

117
00:05:40,259 --> 00:05:42,240
我们创建一个resource文件夹

118
00:05:42,240 --> 00:05:45,209
然后把普列拖拽到这个文件夹里

119
00:05:45,209 --> 00:05:47,220
这时候就可以把玩家删掉了

120
00:05:47,220 --> 00:05:49,769
我们希望通过代码的方式去创建玩家

121
00:05:49,769 --> 00:05:52,079
并且把玩家放在哪个位置

122
00:05:52,079 --> 00:05:54,459
就放在这个位置

123
00:05:58,220 --> 00:06:02,019
那么我们现在就需要去编写代码了

124
00:06:02,038 --> 00:06:02,939
代码的话

125
00:06:02,939 --> 00:06:05,699
我希望就放在地图啊这一层上面

126
00:06:05,699 --> 00:06:08,800
那我们在这里创建一个文件夹

127
00:06:08,800 --> 00:06:10,079
存放我的代码

128
00:06:12,980 --> 00:06:14,259
第一个脚本

129
00:06:15,339 --> 00:06:16,500
第一个脚本

130
00:06:24,199 --> 00:06:29,360
叫做直接叫map map control啊

131
00:06:29,360 --> 00:06:31,199
这就是map control好了

132
00:06:32,740 --> 00:06:34,500
整个一个地图的控制啊

133
00:06:34,500 --> 00:06:36,060
我们加到它的上面

134
00:06:37,600 --> 00:06:39,660
它可以改个名字叫map

135
00:06:43,060 --> 00:06:46,579
然后在这个脚本我们打开它

136
00:06:56,899 --> 00:06:57,939
叫map

137
00:07:01,300 --> 00:07:05,158
control那么在这里啊

138
00:07:05,158 --> 00:07:08,218
我们这个地图啊需要有什么操作啊

139
00:07:08,218 --> 00:07:09,658
地图需要有什么操作

140
00:07:09,658 --> 00:07:16,100
首先我要得到当前的这个整个这个瓦片地图的信息

141
00:07:16,478 --> 00:07:18,399
瓦片地图有一个类啊

142
00:07:18,399 --> 00:07:21,278
专门就是代表当前瓦片地图的这样的一个类

143
00:07:21,278 --> 00:07:23,869
那么这个类呢是什么类型

144
00:07:23,869 --> 00:07:27,959
我们给它写成属性是cc.tmap

145
00:07:30,920 --> 00:07:34,139
那么这个就是当前整个地图的信息

146
00:07:34,420 --> 00:07:37,678
那我们要拿到地图的信息要干嘛

147
00:07:37,678 --> 00:07:39,538
我们要拿到地图的信息

148
00:07:39,538 --> 00:07:42,610
然后要获取这个对象的信息

149
00:07:42,610 --> 00:07:43,870
拿到这个对象

150
00:07:43,870 --> 00:07:45,610
我们拿到他的xy

151
00:07:45,610 --> 00:07:50,139
是不是就证明我们的玩家要显示在这个x和y的位置上

152
00:07:50,139 --> 00:07:54,500
然后我们就可以创建这个呃玩家了啊

153
00:07:54,500 --> 00:07:58,129
所以我们实际上要获取那个玩家对象的信息

154
00:07:58,129 --> 00:08:04,220
那首先第一步我们要获取地图信息

155
00:08:04,220 --> 00:08:05,300
地图的信息

156
00:08:05,300 --> 00:08:09,649
这个tell map其实在这里它作为一个组件显示了出来

157
00:08:09,649 --> 00:08:15,389
大家可以看我们的地图这样一个组件叫tama啊

158
00:08:15,389 --> 00:08:18,000
那所以实际上我们就是要获取这个组件了

159
00:08:18,000 --> 00:08:19,500
那在这里就好获取了

160
00:08:19,500 --> 00:08:20,399
获取地图信息

161
00:08:20,399 --> 00:08:25,839
直接this.map就等于一个this点得到组件啊

162
00:08:25,839 --> 00:08:27,300
tell map的组件

163
00:08:28,740 --> 00:08:32,938
ti加map组件

164
00:08:35,159 --> 00:08:36,740
获取了地图信息以后

165
00:08:36,740 --> 00:08:38,029
地图有很多层

166
00:08:38,029 --> 00:08:40,580
我们知道有三个普通层和一个对象层

167
00:08:40,580 --> 00:08:41,240
对不对

168
00:08:41,240 --> 00:08:44,519
如果你要获取普通层啊

169
00:08:44,519 --> 00:08:46,139
我们这里不需要获取普通层

170
00:08:46,139 --> 00:08:46,919
但是我说一下

171
00:08:46,919 --> 00:08:48,389
如果要获取普通层

172
00:08:48,389 --> 00:08:49,500
你直接点map

173
00:08:49,500 --> 00:08:50,899
点get layer

174
00:08:50,960 --> 00:08:53,360
在这里填写层的名称就可以了

175
00:08:53,360 --> 00:08:56,198
但是我们这里是不需要获取的

176
00:08:56,419 --> 00:08:59,539
我们这里要获取的是什么对象层

177
00:09:01,720 --> 00:09:03,750
对象层的话啊

178
00:09:03,750 --> 00:09:10,429
其实就是我们那个flayer layer对象层的信息怎么获取

179
00:09:10,429 --> 00:09:11,600
this.map

180
00:09:11,600 --> 00:09:14,839
点燃get object group

181
00:09:15,600 --> 00:09:19,129
那么这个就是用来获取对象层的啊

182
00:09:19,129 --> 00:09:22,820
它获取某一层某一层里面所有的对象信息

183
00:09:22,820 --> 00:09:23,629
在这里

184
00:09:23,629 --> 00:09:26,879
这一层我们叫做普列

185
00:09:26,879 --> 00:09:29,600
是不是叫普列啊

186
00:09:29,600 --> 00:09:30,620
普利注意啊

187
00:09:30,620 --> 00:09:32,960
其实就是这一层的名称叫普列

188
00:09:32,960 --> 00:09:38,000
那么这样的话这一层里面所有的对象信息我都获取到了

189
00:09:38,000 --> 00:09:39,799
目前只有一个对象啊

190
00:09:39,799 --> 00:09:41,818
就是代表我们玩家的起始位置

191
00:09:41,818 --> 00:09:43,379
你还可以创建很多

192
00:09:43,379 --> 00:09:44,840
对不对啊

193
00:09:44,840 --> 00:09:46,460
比如说这个代表一个宝箱

194
00:09:46,460 --> 00:09:47,360
这个代表一个宝箱

195
00:09:47,360 --> 00:09:49,100
这个代表一个宝箱啊

196
00:09:49,100 --> 00:09:50,120
这些都是对象了

197
00:09:50,120 --> 00:09:51,539
在这啊

198
00:09:54,179 --> 00:09:56,480
那么在这儿我们现在只有一个啊

199
00:09:56,480 --> 00:09:57,839
就是我们这个玩家

200
00:10:00,379 --> 00:10:03,860
那么这个对象层信息啊获取到了

201
00:10:03,860 --> 00:10:09,120
我们现在要从这个对象层里面

202
00:10:11,399 --> 00:10:14,490
获取某个对象

203
00:10:14,490 --> 00:10:16,200
我们现在要获取的是什么

204
00:10:16,200 --> 00:10:17,250
玩家对象

205
00:10:17,250 --> 00:10:20,990
就是pyl那个呃o b g啊

206
00:10:20,990 --> 00:10:23,480
玩家这个对象怎么获取

207
00:10:23,480 --> 00:10:25,100
就是this点啊

208
00:10:25,100 --> 00:10:25,940
不是this了

209
00:10:25,940 --> 00:10:29,539
就直接用上面这个py layer

210
00:10:31,159 --> 00:10:31,879
打错了

211
00:10:31,879 --> 00:10:32,480
打个普雷

212
00:10:32,480 --> 00:10:33,339
普雷

213
00:10:37,419 --> 00:10:38,590
billier

214
00:10:38,590 --> 00:10:43,600
我们就直接点get object就得到一个对象

215
00:10:43,600 --> 00:10:45,419
得到什么对象呢

216
00:10:46,320 --> 00:10:49,259
普雷不叫普雷吧

217
00:10:50,940 --> 00:10:54,578
对象的名称就是它的名称叫start pos

218
00:10:57,120 --> 00:10:59,720
这样的话返回的这个内容啊

219
00:10:59,720 --> 00:11:04,570
返回的这个对象就是我们的这个花这样的一个对象啊

220
00:11:04,570 --> 00:11:07,509
其实就是我们的这个玩家对象拿到玩家对象了

221
00:11:07,509 --> 00:11:09,620
为了保证啊

222
00:11:09,620 --> 00:11:11,600
他是我是我们的玩家对象

223
00:11:11,600 --> 00:11:14,750
我们知道我们给玩家对象加了一个自定义的属性

224
00:11:14,750 --> 00:11:15,590
对不对

225
00:11:15,590 --> 00:11:17,090
加了一个自定义的属性

226
00:11:17,090 --> 00:11:19,078
自定义的属性叫is player

227
00:11:19,379 --> 00:11:27,389
那所以在这里我们就可以怎样来判断是否是玩家对象

228
00:11:27,389 --> 00:11:28,139
我们就可以

229
00:11:28,139 --> 00:11:28,679
如果

230
00:11:31,139 --> 00:11:34,440
这个对象点is player

231
00:11:34,440 --> 00:11:37,379
你看你直接就可以使用它的那个自定义的对象

232
00:11:37,379 --> 00:11:40,019
把自定义的属性为tru

233
00:11:40,139 --> 00:11:45,500
那么这里面才证明我们拿到了这样的一个对象啊

234
00:11:45,500 --> 00:11:48,099
就是他确定是这样的一个玩家对象的

235
00:11:49,120 --> 00:11:52,360
那么我们就可以拿到玩家对象的x和y了

236
00:11:52,360 --> 00:11:53,049
对不对

237
00:11:53,049 --> 00:11:54,159
拿到x和y了

238
00:11:54,159 --> 00:11:55,000
我们就可以怎样了

239
00:11:55,000 --> 00:11:57,700
在这个位置创建我们的玩家

240
00:11:57,700 --> 00:12:01,919
所以在这里我们要创建玩家了啊

241
00:12:01,919 --> 00:12:04,440
如果确定拿拿到我们玩家对象了啊

242
00:12:04,440 --> 00:12:05,820
我们能拿到玩家对象了

243
00:12:05,820 --> 00:12:08,039
也知道他的这个s y信息了

244
00:12:08,039 --> 00:12:09,600
我们就可以创建玩家了

245
00:12:09,600 --> 00:12:11,519
玩家的话现在放在哪里的

246
00:12:11,519 --> 00:12:12,120
玩家的话

247
00:12:12,120 --> 00:12:15,389
现在是预设体在这个资源文件夹里面的

248
00:12:15,389 --> 00:12:17,909
所以我们就要先加载资源

249
00:12:17,909 --> 00:12:18,929
对不对

250
00:12:18,929 --> 00:12:22,938
load.load加载资源

251
00:12:24,480 --> 00:12:25,818
不要忘啊

252
00:12:26,419 --> 00:12:29,480
一定要每一节课完了记录笔记啊

253
00:12:29,480 --> 00:12:32,539
你不要比如说到现在你一看这个哎呀忘了

254
00:12:32,539 --> 00:12:34,220
是不是哈哈啊

255
00:12:34,220 --> 00:12:35,120
千万不敢这样啊

256
00:12:35,120 --> 00:12:36,320
那你这样就会学完的话

257
00:12:36,320 --> 00:12:40,479
你会发现好像就记着最近几节课的前面全忘完了

258
00:12:41,740 --> 00:12:47,139
那首先在这里我们读一个加载的资源叫普列啊

259
00:12:47,139 --> 00:12:49,000
因为这里就是普列

260
00:12:49,480 --> 00:12:56,919
然后它的这个类型是预设体prefab加载完以后生成的资源

261
00:13:05,980 --> 00:13:08,230
是在这个回调里面的啊

262
00:13:08,230 --> 00:13:11,649
这个player pre就是我们最后加载完的啊

263
00:13:11,649 --> 00:13:13,539
读取出来的这个玩家预设体

264
00:13:13,539 --> 00:13:15,370
拿到预设题了就可以干嘛了

265
00:13:15,370 --> 00:13:16,599
创建玩家了

266
00:13:17,019 --> 00:13:19,539
这里才是创建玩家啊

267
00:13:20,299 --> 00:13:24,698
这里是加载玩家预设体

268
00:13:25,919 --> 00:13:27,690
那这个玩家的话

269
00:13:27,690 --> 00:13:30,339
我们也给他写成属性吧

270
00:13:32,460 --> 00:13:34,200
玩家就是一个节点

271
00:13:34,200 --> 00:13:34,950
对不对

272
00:13:34,950 --> 00:13:36,458
默认是空

273
00:13:38,379 --> 00:13:42,299
那么this点普player在这里就要创建了啊

274
00:13:42,299 --> 00:13:45,460
我们实例化实例化它啊

275
00:13:45,460 --> 00:13:49,120
然后实例化的话就直接是普利普瑞

276
00:13:49,519 --> 00:13:52,759
就是刚加载完成的这个预设体

277
00:13:52,759 --> 00:13:54,169
是不是直接放到这里

278
00:13:54,169 --> 00:13:58,190
通过它创建出来个玩家玩家的附体

279
00:13:58,190 --> 00:14:01,000
我希望是玩家

280
00:14:01,000 --> 00:14:02,500
我希望放到他的里面

281
00:14:02,500 --> 00:14:07,679
所以就是我的第一二第三个子物体里面的第一个子物体

282
00:14:07,679 --> 00:14:08,250
对不对

283
00:14:08,250 --> 00:14:10,529
第三个子物体里面的第一个子物体

284
00:14:10,529 --> 00:14:14,198
所以this点普雷点set parent

285
00:14:14,779 --> 00:14:16,460
那就是服务体

286
00:14:16,460 --> 00:14:18,200
就是我的子物体

287
00:14:18,200 --> 00:14:19,039
第几个子物体

288
00:14:19,039 --> 00:14:24,818
第三个就是二里面的第一个子物体就是零

289
00:14:25,419 --> 00:14:30,279
ok这样的话这个玩家创建出来就会被放到这个layer层下面

290
00:14:31,379 --> 00:14:40,210
然后玩家的初始位置x位置就是我们获取到这个对象信息的为呃

291
00:14:40,210 --> 00:14:44,759
x坐标就是pyl o b g的x

292
00:14:46,940 --> 00:14:51,559
2万普雷o b g的y

293
00:14:52,960 --> 00:14:57,460
那这样的话我们就是上来就创建了一个玩家

294
00:14:57,460 --> 00:15:00,659
玩家就在我们的那个对象的那个位置

295
00:15:00,740 --> 00:15:02,120
然后我希望怎样

296
00:15:02,120 --> 00:15:03,938
我希望我们的摄像头

297
00:15:05,879 --> 00:15:08,279
呃我希望咱们的摄像头注意啊

298
00:15:08,279 --> 00:15:09,679
这个摄像头

299
00:15:11,120 --> 00:15:16,059
咱们的屏幕其实显示的就是这个摄像头这个照到照到的内容

300
00:15:16,059 --> 00:15:18,850
摄像头照到的内容就是以它为中心

301
00:15:18,850 --> 00:15:21,759
然后范围就是屏幕这么大啊

302
00:15:21,759 --> 00:15:23,889
这就是屏摄像头照到的内容

303
00:15:23,889 --> 00:15:27,990
我要是希望永远看到这个玩家为中心的话

304
00:15:27,990 --> 00:15:32,190
我就想那我们就需要让这个摄像头永远和玩家的坐标保持

305
00:15:32,190 --> 00:15:33,450
怎样保持一致

306
00:15:33,450 --> 00:15:36,818
这样的话玩家永远会显示到我们的屏幕中心

307
00:15:37,179 --> 00:15:38,299
对不对

308
00:15:38,539 --> 00:15:42,200
那么这样的话我们就要做一件事儿啊

309
00:15:42,200 --> 00:15:44,159
就要让这个摄像头

310
00:15:46,519 --> 00:15:48,500
我们希望每一帧都做这样的事

311
00:15:48,500 --> 00:15:52,818
让摄像头跟随玩家

312
00:15:57,659 --> 00:16:01,039
那么这里我们注意一件事儿啊

313
00:16:01,620 --> 00:16:05,279
摄像头大家可以看摄像头的零零点在这个位置

314
00:16:05,279 --> 00:16:10,419
我要让他的这个中心点和我们玩家保持一致啊

315
00:16:10,419 --> 00:16:13,899
那么你其实我们看一下

316
00:16:13,899 --> 00:16:15,429
我们先写一下啊

317
00:16:15,429 --> 00:16:17,409
呃会有一点点问题

318
00:16:17,409 --> 00:16:18,220
问题存在

319
00:16:18,220 --> 00:16:19,419
我们马上再说啊

320
00:16:19,419 --> 00:16:20,649
我们先看出来啊

321
00:16:20,649 --> 00:16:27,120
就是如果这个玩家现在不等于空了

322
00:16:27,120 --> 00:16:28,200
就证明有玩家了

323
00:16:28,200 --> 00:16:29,340
我们再让他跟随

324
00:16:29,340 --> 00:16:30,870
跟随的话很简单

325
00:16:30,870 --> 00:16:32,759
cc.camera

326
00:16:34,519 --> 00:16:37,610
怎么样怎么样获取我们这个摄像头啊

327
00:16:37,610 --> 00:16:39,578
怎样获取我们这个摄像头

328
00:16:42,659 --> 00:16:44,690
cc.camera有点慢啊

329
00:16:44,690 --> 00:16:48,289
就可以获取到当前的这个正在使用的摄像头啊

330
00:16:48,289 --> 00:16:50,500
其实就是我们这个唯一的摄像头

331
00:16:50,580 --> 00:16:55,200
然后得到他的x就是我们玩家的x啊

332
00:16:55,200 --> 00:16:56,340
正常都是这样去想

333
00:16:56,340 --> 00:16:56,970
对不对

334
00:16:56,970 --> 00:16:58,438
然后camera

335
00:17:00,720 --> 00:17:07,759
点慢点node.y就等于this.fla

336
00:17:07,759 --> 00:17:09,039
点y

337
00:17:11,420 --> 00:17:14,569
那么但是我们现在运行一下

338
00:17:14,569 --> 00:17:15,740
看一下效果啊

339
00:17:15,740 --> 00:17:17,659
我们现在运行一下看看效果

340
00:17:19,980 --> 00:17:23,880
诶我们发现我们视角跑到了这样一个位置

341
00:17:23,880 --> 00:17:24,960
这个首先肯定是不对的

342
00:17:24,960 --> 00:17:26,279
因为玩家就不在这个位置

343
00:17:26,279 --> 00:17:27,029
对不对

344
00:17:27,869 --> 00:17:28,890
玩家在哪呢

345
00:17:28,890 --> 00:17:32,049
现在显示的是这个位置

346
00:17:32,049 --> 00:17:34,119
现在显示的这是这个位置

347
00:17:34,119 --> 00:17:35,319
实际上玩家在这个位置

348
00:17:35,319 --> 00:17:38,279
也就是说它的中心点应该是这样的啊

349
00:17:38,279 --> 00:17:39,900
摄像头应该显示这样的一个位置

350
00:17:39,900 --> 00:17:40,980
对不对啊

351
00:17:40,980 --> 00:17:42,519
那这是什么问题

352
00:17:45,220 --> 00:17:46,539
你一定要跟着去做啊

353
00:17:46,539 --> 00:17:47,259
你跟着去做

354
00:17:47,259 --> 00:17:48,759
你才能发现问题在哪

355
00:17:48,759 --> 00:17:49,599
你不跟着去做

356
00:17:49,599 --> 00:17:50,559
一会儿就晕了

357
00:17:50,559 --> 00:17:52,579
就搞不清楚问题所在了

358
00:17:53,539 --> 00:17:54,859
那就是玩家啊

359
00:17:54,859 --> 00:17:55,900
大家注意啊

360
00:17:55,900 --> 00:17:57,400
如果要让摄像头跟随外界

361
00:17:57,400 --> 00:17:59,380
因为摄像头这是屏幕

362
00:17:59,380 --> 00:18:01,509
这是摄像头摄像头的这个坐标

363
00:18:01,509 --> 00:18:04,279
零零点是这个屏幕的中心点

364
00:18:04,279 --> 00:18:07,099
实际上我们希望的是什么呀

365
00:18:07,099 --> 00:18:09,480
零零点在这个位置啊

366
00:18:09,480 --> 00:18:10,259
这是对的

367
00:18:10,259 --> 00:18:11,339
是在左下角这个位置

368
00:18:11,339 --> 00:18:13,650
但是它默认是在中心位置的啊

369
00:18:13,650 --> 00:18:16,349
所以它呢摄像头会有一个偏差

370
00:18:16,349 --> 00:18:20,119
偏差就是向右上方移动了半个屏幕的距离

371
00:18:21,279 --> 00:18:24,720
也就是说你现在如果要让摄像头和某个人对齐

372
00:18:24,720 --> 00:18:27,700
那么都会偏移半个屏幕的位置

373
00:18:27,700 --> 00:18:29,079
为了解决这个问题

374
00:18:29,079 --> 00:18:29,799
其实很简单

375
00:18:29,799 --> 00:18:31,460
现在半个屏幕位置

376
00:18:31,619 --> 00:18:34,559
一个屏幕是480x320啊

377
00:18:34,559 --> 00:18:35,940
偏移半个屏幕位置

378
00:18:35,940 --> 00:18:37,740
我们就让它减去半个屏幕位置

379
00:18:37,740 --> 00:18:39,150
就减去一个240

380
00:18:39,150 --> 00:18:41,138
减去一个160

381
00:18:43,819 --> 00:18:45,019
这时候运行一下

382
00:18:46,099 --> 00:18:52,779
大家可以看是不是这个就是我们最终的一个效果

383
00:18:53,099 --> 00:18:56,220
这个摄像头显示我们玩家啊

384
00:18:56,220 --> 00:18:57,690
中间就是我们的这个玩家

385
00:18:57,690 --> 00:18:58,799
他永远跟随玩家

386
00:18:58,799 --> 00:19:00,710
不管玩家到哪他都跟随

387
00:19:00,710 --> 00:19:03,799
然后玩家的话也在这里显示出来了啊

388
00:19:03,799 --> 00:19:05,180
玩家也显示出来了

389
00:19:05,180 --> 00:19:10,799
那么这个啊这个就是我们的通过代码去显示的这样的一个地图

390
00:19:11,180 --> 00:19:12,950
那么既然显示出来了

391
00:19:12,950 --> 00:19:19,609
我们肯定希望让这个玩家可以在地图上面去进行这个移动啊

392
00:19:19,609 --> 00:19:24,359
那么我们下节课我们就来去编写这个移动啊

393
00:19:24,359 --> 00:19:27,420
我们去来封装一个移动的这样的一个类啊

394
00:19:27,420 --> 00:19:28,980
以后大家去再写移动的话

395
00:19:28,980 --> 00:19:32,599
就可以用这个封装的这个类来进行这个操作了啊

396
00:19:32,599 --> 00:19:33,319
当然不管怎样

397
00:19:33,319 --> 00:19:35,240
这节课的内容大家先要搞清啊

398
00:19:35,240 --> 00:19:42,740
就是怎样通过代码的方式去获取我们地图上面的信息内容啊

399
00:19:42,740 --> 00:19:48,650
其实大家就发现了整个地图123层我们都不管它

400
00:19:48,650 --> 00:19:49,880
通过代码获取

401
00:19:49,880 --> 00:19:53,410
其实就是要获取对象层里面每个对象的信息啊

402
00:19:53,410 --> 00:19:54,849
比如说获取到这个对象以后

403
00:19:54,849 --> 00:19:58,069
我们要获取这个对象的位置信息啊

404
00:19:58,069 --> 00:20:01,039
然后我们就把我们真正的这个玩家放到这个位置上

405
00:20:01,039 --> 00:20:05,119
也就是说他现在好像一个替代对象一样啊

406
00:20:05,119 --> 00:20:06,319
就我们画地图的时候

407
00:20:06,319 --> 00:20:08,000
先把这个玩家起始点放这儿

408
00:20:08,000 --> 00:20:10,259
他就是一个替代替代物

409
00:20:10,779 --> 00:20:12,099
真正在代码里面

410
00:20:12,099 --> 00:20:14,019
实际上我们是会创建一个真正的玩家

411
00:20:14,019 --> 00:20:15,579
把他代替了的啊

412
00:20:15,579 --> 00:20:17,138
是这样的一个感觉

413
00:20:17,779 --> 00:20:18,400
ok啊

414
00:20:18,400 --> 00:20:20,349
那么我们这节课先这么多内容

415
00:20:20,349 --> 00:20:22,299
下节课我们继续


1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,080 --> 00:00:10,699
ok从这节课开始

3
00:00:10,699 --> 00:00:13,460
咱们来说一下咱们的ui系统啊

4
00:00:13,460 --> 00:00:14,960
咱们cbs的ui系统

5
00:00:14,960 --> 00:00:18,260
我们现在创建一个空的项目啊

6
00:00:18,260 --> 00:00:20,390
比如说叫ui

7
00:00:20,390 --> 00:00:23,059
我们新建这个项目

8
00:00:39,299 --> 00:00:41,039
那么在这边啊

9
00:00:41,039 --> 00:00:43,740
首先我们来看一下啊

10
00:00:43,740 --> 00:00:45,659
看一下这个紫色的框

11
00:00:45,659 --> 00:00:46,500
咱们最早说过

12
00:00:46,500 --> 00:00:47,880
这个就代表咱们的屏幕

13
00:00:47,880 --> 00:00:48,500
对不对

14
00:00:48,520 --> 00:00:50,950
那么我们就从这个框开始说起

15
00:00:50,950 --> 00:00:53,439
那这个框在这里我们叫做canvas

16
00:00:53,439 --> 00:00:55,780
我们从头到尾就没有没有管过它

17
00:00:55,780 --> 00:00:56,880
对不对啊

18
00:00:56,880 --> 00:01:00,060
我们中间对它做了几次操作啊

19
00:01:00,060 --> 00:01:02,009
只操作了一下分辨率啊

20
00:01:02,009 --> 00:01:05,560
分辨率一操作我就发现我们这个屏幕啊

21
00:01:05,560 --> 00:01:07,900
就是做成的游戏分辨率也会发生改变

22
00:01:07,900 --> 00:01:10,120
就会变成这个对应的分辨率

23
00:01:10,120 --> 00:01:10,750
对不对

24
00:01:10,750 --> 00:01:13,280
那我们这节课详细来说一下啊

25
00:01:18,000 --> 00:01:22,319
那么首先啊这个canvas这边它本身也是一个节点

26
00:01:22,319 --> 00:01:22,680
对不对

27
00:01:22,680 --> 00:01:24,900
所以节点相关的属性咱们不说了

28
00:01:24,900 --> 00:01:28,980
咱们就看这个canvas在这里就代表了咱们的屏幕

29
00:01:29,019 --> 00:01:30,640
那么这是咱们的屏幕

30
00:01:30,640 --> 00:01:34,579
然后屏幕里面所有的物体啊都会被渲染出来

31
00:01:34,579 --> 00:01:36,349
当然被谁渲染出来的

32
00:01:36,349 --> 00:01:38,659
是被这里的这个摄像机

33
00:01:38,680 --> 00:01:40,159
这里有个摄像机

34
00:01:40,180 --> 00:01:44,260
我们所有的物体都是通过这个摄像机渲染出来的啊

35
00:01:44,260 --> 00:01:45,879
都是通过它渲染出来的

36
00:01:45,879 --> 00:01:50,650
那么渲染出来最后最后的屏幕大小是谁规定的是这个canvas

37
00:01:50,650 --> 00:01:54,879
所以咱们这节课先来看一下咱们的canvas the colours

38
00:01:54,879 --> 00:01:58,379
这边我们一直知道在这里啊

39
00:01:58,379 --> 00:02:00,420
更改分辨率会影响我们的大小

40
00:02:00,420 --> 00:02:01,739
但是我们仔细看一下

41
00:02:01,739 --> 00:02:05,400
这边它的分辨率并不是说叫游戏分辨率

42
00:02:05,400 --> 00:02:07,359
它叫设计分辨率啊

43
00:02:07,359 --> 00:02:08,740
它叫设计分辨率

44
00:02:08,740 --> 00:02:09,460
什么意思

45
00:02:09,460 --> 00:02:10,960
它上面有很多介绍啊

46
00:02:10,960 --> 00:02:12,189
你仔细看一下

47
00:02:12,189 --> 00:02:14,259
你大概也就能明白啊

48
00:02:14,259 --> 00:02:16,479
就是说这个这个东西啊

49
00:02:16,479 --> 00:02:19,860
就是我们在设计的时候规定的一个分辨率

50
00:02:19,879 --> 00:02:22,620
那么当我们比如说设计了一个项目

51
00:02:22,659 --> 00:02:26,110
比如说我们按800x600做了一个项目

52
00:02:26,110 --> 00:02:26,979
做完以后

53
00:02:26,979 --> 00:02:29,680
最后我们跑到浏览器上的结果

54
00:02:29,979 --> 00:02:33,240
这个可能比如说我们是跑在华为p9 上的

55
00:02:33,460 --> 00:02:36,520
那么它的分辨率其实并不是800x900

56
00:02:36,520 --> 00:02:37,210
对不对啊

57
00:02:37,210 --> 00:02:38,439
你哪怕把它横过来

58
00:02:38,439 --> 00:02:39,240
也不是

59
00:02:39,439 --> 00:02:42,919
那么这时候怎么办啊

60
00:02:42,919 --> 00:02:43,909
怎么办

61
00:02:43,909 --> 00:02:46,719
或者是这里是苹果4

62
00:02:47,180 --> 00:02:48,139
对不对

63
00:02:48,139 --> 00:02:49,219
苹果4啊

64
00:02:49,219 --> 00:02:55,719
那么这时候如果我们我们的这个呃程序运行在这个手机上面又怎么办

65
00:02:55,719 --> 00:02:59,189
所以实际上就是每一台设备

66
00:02:59,189 --> 00:03:00,419
每一台不同的设备

67
00:03:00,419 --> 00:03:03,039
它的真正的分辨率是不同的

68
00:03:03,039 --> 00:03:08,800
但是我们不可能说去给每一个设备都去单独设计一下这个游戏

69
00:03:08,800 --> 00:03:09,819
安娜太费劲了

70
00:03:09,819 --> 00:03:10,659
我写个游戏

71
00:03:10,659 --> 00:03:12,400
我要给不同的设备去写

72
00:03:12,400 --> 00:03:13,449
那太麻烦了

73
00:03:13,449 --> 00:03:17,650
所以在这里我们这里啊就不管设备

74
00:03:17,650 --> 00:03:19,479
我们只管什么呀

75
00:03:20,199 --> 00:03:22,599
只关心这里的这个分辨率

76
00:03:22,599 --> 00:03:23,680
这个叫设计分辨率

77
00:03:23,680 --> 00:03:25,599
是我们设计的时候用的分辨率

78
00:03:25,620 --> 00:03:29,219
当我们通过这个分辨率设计完游戏以后

79
00:03:29,240 --> 00:03:32,030
那么跑在不同的分辨率上的时候

80
00:03:32,030 --> 00:03:37,770
那么它们会通过这个比例去进行缩放啊

81
00:03:37,770 --> 00:03:41,219
那在这里为了看的看清楚它是怎么缩放的

82
00:03:41,219 --> 00:03:43,210
我们在这里嗯

83
00:03:43,210 --> 00:03:45,460
比如说给一个方形的吧

84
00:03:45,460 --> 00:03:49,840
比如说是个800x800啊

85
00:03:49,840 --> 00:03:51,520
是一个方形的屏幕啊

86
00:03:51,520 --> 00:03:52,780
这个屏幕很很奇怪

87
00:03:52,780 --> 00:03:53,439
是个方形的

88
00:03:53,439 --> 00:03:54,400
是不是啊

89
00:03:54,400 --> 00:03:56,360
我们只是为了看得清楚一些

90
00:03:57,639 --> 00:04:01,479
那我这里拖上来两个资源

91
00:04:03,039 --> 00:04:05,710
啊我们先看这个这个是图片啊

92
00:04:05,710 --> 00:04:08,289
然后我们在这里嗯

93
00:04:08,289 --> 00:04:10,599
创建一个精灵

94
00:04:11,780 --> 00:04:13,969
创建一个精灵

95
00:04:13,969 --> 00:04:17,459
我把这个图片放上来

96
00:04:19,420 --> 00:04:21,579
这里这个这个这个图片很大

97
00:04:21,579 --> 00:04:22,990
是不是图片很大

98
00:04:22,990 --> 00:04:26,139
然后我们看一下这个屏幕是800x800

99
00:04:26,139 --> 00:04:29,639
所以我这个图片也改成800x800

100
00:04:29,639 --> 00:04:31,740
那这个图片是有边框的啊

101
00:04:31,740 --> 00:04:36,500
为的就是让它看起来更加的好看一些啊

102
00:04:36,500 --> 00:04:39,740
我把它填充满咱们的这个屏幕啊

103
00:04:39,740 --> 00:04:41,279
填充满咱们的屏幕

104
00:04:42,600 --> 00:04:47,779
那么这时候我们首先回到canvas上

105
00:04:48,079 --> 00:04:50,990
那么看一下它默认它是这样的

106
00:04:50,990 --> 00:04:52,519
这里我们设置一下宽高

107
00:04:52,519 --> 00:04:53,990
这里有两个勾

108
00:04:53,990 --> 00:04:55,920
分别是什么意思啊

109
00:04:58,459 --> 00:05:02,779
第一个啊第一个它是填充高

110
00:05:02,860 --> 00:05:03,759
填充高

111
00:05:03,759 --> 00:05:07,120
意思就是说我会把高给填充满

112
00:05:07,120 --> 00:05:08,410
第二个是填充宽

113
00:05:08,410 --> 00:05:11,500
意思是我会把这个屏幕的宽填充满

114
00:05:11,540 --> 00:05:13,819
我们现在先从什么都没有的

115
00:05:13,819 --> 00:05:14,720
我们来看啊

116
00:05:14,720 --> 00:05:15,319
什么都没有

117
00:05:15,319 --> 00:05:16,620
我们现在运行

118
00:05:17,980 --> 00:05:21,259
那么大家会看会发现到这样一个情况

119
00:05:21,459 --> 00:05:23,319
如果我们在一个屏幕上

120
00:05:23,319 --> 00:05:25,720
这个屏幕比如说它是小的

121
00:05:25,720 --> 00:05:27,699
那么我们会进行裁剪

122
00:05:28,180 --> 00:05:31,180
如果我们比如说放到苹果6上

123
00:05:31,180 --> 00:05:32,920
你看放到ipad上

124
00:05:33,019 --> 00:05:35,509
ipad上是把上下裁剪了

125
00:05:35,509 --> 00:05:36,980
然后放到这个手机上

126
00:05:36,980 --> 00:05:38,279
是把左右裁剪了

127
00:05:38,279 --> 00:05:41,100
也就是说如果我们这两个按钮什么都不勾

128
00:05:41,100 --> 00:05:43,769
结果就是它总有一边会填充满

129
00:05:43,769 --> 00:05:47,000
另外一边就会就会怎样给你裁剪掉

130
00:05:47,019 --> 00:05:49,660
任何一个屏幕都是这样的啊

131
00:05:49,660 --> 00:05:50,769
我们看到了

132
00:05:50,769 --> 00:05:54,300
那么在这里

133
00:05:54,300 --> 00:05:57,300
如果我们比如说设置把宽填充满

134
00:05:57,300 --> 00:05:58,199
把这个勾画上

135
00:05:58,199 --> 00:05:59,910
这是默认的一个状况啊

136
00:05:59,910 --> 00:06:04,240
那么再运行bug诶

137
00:06:05,720 --> 00:06:07,519
啊是把高填充的啊

138
00:06:07,519 --> 00:06:08,959
把高填充满没有问题

139
00:06:08,959 --> 00:06:10,199
那么大家可以看啊

140
00:06:10,220 --> 00:06:11,420
不管是什么屏幕

141
00:06:11,420 --> 00:06:14,240
它可能会裁剪我们的两边

142
00:06:14,240 --> 00:06:17,540
但是它的上下永远是填充满的

143
00:06:18,000 --> 00:06:21,329
你看如果是ipad

144
00:06:21,329 --> 00:06:22,980
那么ok上下填充版

145
00:06:22,980 --> 00:06:25,290
但是旁边左右多余了

146
00:06:25,290 --> 00:06:26,639
我就是黑屏

147
00:06:26,639 --> 00:06:28,079
如果是手机

148
00:06:28,079 --> 00:06:30,389
那左右那我就裁剪掉

149
00:06:30,389 --> 00:06:31,680
然后上下填充满

150
00:06:31,680 --> 00:06:34,040
这样的话就保证上下最起码是满的

151
00:06:34,040 --> 00:06:36,079
那么这是默认的一个状况

152
00:06:36,079 --> 00:06:38,209
如果我换第二个呢

153
00:06:38,209 --> 00:06:40,300
把这个框填充满

154
00:06:40,920 --> 00:06:42,600
那么我们再运行一下

155
00:06:42,600 --> 00:06:44,279
那这时候大家就可以看到

156
00:06:44,279 --> 00:06:45,540
如果对于手机而言

157
00:06:45,540 --> 00:06:46,500
我是宽填充满

158
00:06:46,500 --> 00:06:48,300
但是上下肯定填充不满了

159
00:06:48,300 --> 00:06:50,660
我就会以黑屏显示啊

160
00:06:50,660 --> 00:06:51,980
如果是pad的话

161
00:06:51,980 --> 00:06:53,240
宽填充满了

162
00:06:53,240 --> 00:06:55,139
那上下就会给你裁剪了

163
00:06:55,139 --> 00:07:00,959
default就是以正常的这个设计分辨率来显示了

164
00:07:01,540 --> 00:07:04,269
然后如果这两个都画上

165
00:07:04,269 --> 00:07:05,980
结果是怎样运行

166
00:07:07,800 --> 00:07:12,379
在pad上面我们看我们正常啊

167
00:07:12,379 --> 00:07:14,209
高给你填充满了

168
00:07:14,209 --> 00:07:17,360
那么宽的话虽然没有填充满

169
00:07:17,360 --> 00:07:19,040
但是最起码全部显示出来了

170
00:07:19,040 --> 00:07:19,800
对不对

171
00:07:19,819 --> 00:07:20,720
放到苹果

172
00:07:20,720 --> 00:07:22,319
放到苹果手机上呢

173
00:07:22,600 --> 00:07:24,459
唉宽给你填充满了

174
00:07:24,459 --> 00:07:25,269
高的话

175
00:07:25,269 --> 00:07:26,470
用黑边显示

176
00:07:26,470 --> 00:07:28,779
也就是说如果两个都选上了

177
00:07:28,779 --> 00:07:33,680
那么它能都会肯定会把你的这个游戏整个界面显示出来啊

178
00:07:33,680 --> 00:07:35,810
肯定会把你的界面整个显示出来

179
00:07:35,810 --> 00:07:39,959
那么多余的部分就这样用这个黑边给你填充上啊

180
00:07:39,959 --> 00:07:41,939
用这个黑边给你填充上啊

181
00:07:41,939 --> 00:07:46,170
仔细看一下这个这些效果啊

182
00:07:46,170 --> 00:07:50,800
那么首先呃大家自己先要把这个勾儿啊

183
00:07:50,800 --> 00:07:52,300
你自己打一打

184
00:07:52,319 --> 00:07:53,639
然后你去看一下不

185
00:07:53,639 --> 00:07:57,600
打勾的效果都打上的效果和打一个的效果一共就是四种

186
00:07:57,600 --> 00:07:58,279
对不对

187
00:07:58,300 --> 00:08:00,779
然后你把这四种搞清楚啊

188
00:08:00,779 --> 00:08:01,439
搞清楚

189
00:08:01,439 --> 00:08:04,019
看一下咱们的这个四种有什么区别

190
00:08:04,019 --> 00:08:05,850
这样的话比如说你做一个游戏

191
00:08:05,850 --> 00:08:08,879
你就知道哎我这个游戏是什么情况啊

192
00:08:08,879 --> 00:08:11,579
我就需要用哪一种啊

193
00:08:11,579 --> 00:08:13,560
哪一种是最合适的啊

194
00:08:13,560 --> 00:08:15,639
你可以自己在这里去选择了

195
00:08:16,360 --> 00:08:17,949
嗯ok啊

196
00:08:17,949 --> 00:08:20,079
那么这是canvas啊

197
00:08:20,079 --> 00:08:21,310
这是canvas

198
00:08:21,310 --> 00:08:23,139
那么咱们再说一个啊

199
00:08:23,139 --> 00:08:26,600
再说一个之前在这个渲染节点里面

200
00:08:26,600 --> 00:08:29,350
我们说了一个精灵

201
00:08:29,350 --> 00:08:31,240
是不是精灵

202
00:08:31,240 --> 00:08:35,059
那么我们这里再说一个文本啊

203
00:08:35,059 --> 00:08:36,350
再说一个文本

204
00:08:36,350 --> 00:08:38,899
首先我们创建一个文本

205
00:08:38,899 --> 00:08:40,759
创建一个文本

206
00:08:40,779 --> 00:08:43,840
那么这个labor呢就是用来显示文本的啊

207
00:08:43,840 --> 00:08:44,740
它跟精灵一样

208
00:08:44,740 --> 00:08:45,879
它都是一个节点

209
00:08:45,879 --> 00:08:47,259
带了一个labor组件

210
00:08:47,259 --> 00:08:48,940
在这里有个string属性

211
00:08:48,940 --> 00:08:51,919
就是你这个呃level啊

212
00:08:51,919 --> 00:08:53,940
就是这个标签去显示的内容

213
00:08:54,220 --> 00:08:56,019
那么最下面有个融合

214
00:08:56,019 --> 00:08:57,519
这个跟我们精灵一样

215
00:08:57,519 --> 00:08:58,419
我们不管它

216
00:08:58,419 --> 00:09:00,100
我们关心这一块的属性

217
00:09:00,100 --> 00:09:03,299
那么首先第二个是居中方式

218
00:09:03,320 --> 00:09:05,600
垂水平方向的居中啊

219
00:09:05,600 --> 00:09:07,070
我们是居中的

220
00:09:07,070 --> 00:09:08,419
你也可以选左对齐

221
00:09:08,419 --> 00:09:08,899
右对齐

222
00:09:08,899 --> 00:09:12,500
在这儿没有什么用啊

223
00:09:12,500 --> 00:09:19,799
那么这里大家发现我们想对它进行拉伸是拉不了的啊

224
00:09:19,799 --> 00:09:21,480
为什么我们等一下再说啊

225
00:09:21,480 --> 00:09:23,720
总之我们先调成这种方式啊

226
00:09:23,740 --> 00:09:26,649
然后在这里还有个垂直居中

227
00:09:26,649 --> 00:09:28,179
默认也是中间的

228
00:09:28,179 --> 00:09:29,440
还可以车上车下

229
00:09:29,440 --> 00:09:30,580
但是现在都没什么用

230
00:09:30,580 --> 00:09:32,379
因为现在它是紧贴着边框的

231
00:09:32,379 --> 00:09:33,120
对不对

232
00:09:33,460 --> 00:09:35,200
那么在这里有个字体大小

233
00:09:35,200 --> 00:09:36,279
你可以去进行修改

234
00:09:36,279 --> 00:09:37,480
默认是40啊

235
00:09:37,480 --> 00:09:39,399
你可以改成60啊

236
00:09:39,399 --> 00:09:41,049
这个大小可以自己去改

237
00:09:41,049 --> 00:09:44,679
然后行高如果有多行的话

238
00:09:44,679 --> 00:09:46,269
每一行行高是多少

239
00:09:46,269 --> 00:09:47,559
这个你可以去改啊

240
00:09:47,559 --> 00:09:49,299
一般你比如说字体60

241
00:09:49,299 --> 00:09:51,100
行高就是60啊

242
00:09:51,100 --> 00:09:57,860
在这里这个啊这个准确来说就叫做约束啊

243
00:09:57,860 --> 00:10:00,409
那么它这里是文字排版模式啊

244
00:10:00,409 --> 00:10:02,240
那么一共有这三种

245
00:10:02,240 --> 00:10:05,000
默认是那就是没有none的话是什么意思

246
00:10:05,000 --> 00:10:08,100
就是你输出多少内容

247
00:10:08,100 --> 00:10:11,519
那么它呢都是一行给你显示完啊

248
00:10:11,519 --> 00:10:12,600
一行显示完

249
00:10:12,600 --> 00:10:15,360
如果我们选择第二种

250
00:10:15,820 --> 00:10:20,230
那么这时候注意诶会会换行了

251
00:10:20,230 --> 00:10:21,639
大家可以看到会换行了

252
00:10:21,639 --> 00:10:23,620
但是有问题了啊

253
00:10:23,620 --> 00:10:27,009
那么它呢换到第二行以后

254
00:10:27,009 --> 00:10:31,460
哎我们需要手动的把它的高给拉高

255
00:10:31,460 --> 00:10:33,740
那这样的话它才能显示出来啊

256
00:10:33,740 --> 00:10:34,549
你要不拉的话

257
00:10:34,549 --> 00:10:36,740
那么它就是永远是一行显示

258
00:10:36,740 --> 00:10:39,139
但是它其实已经是多行了啊

259
00:10:39,139 --> 00:10:43,320
那么这样的话你比如说这个居中你就可以去修改了啊

260
00:10:43,320 --> 00:10:44,250
靠上显示

261
00:10:44,250 --> 00:10:45,120
靠下显示

262
00:10:45,120 --> 00:10:45,899
居中显示

263
00:10:45,899 --> 00:10:46,590
对不对

264
00:10:46,590 --> 00:10:48,559
那么还有第三种

265
00:10:48,840 --> 00:10:50,399
第三种是什么效果

266
00:10:50,399 --> 00:10:51,600
效果有时候不刷新

267
00:10:51,600 --> 00:10:53,460
你在这输点内容

268
00:10:53,460 --> 00:10:55,139
它就刷新了啊

269
00:10:55,139 --> 00:10:57,480
那么第三种是什么意思啊

270
00:10:57,480 --> 00:10:58,440
那么大家看啊

271
00:10:58,440 --> 00:11:01,700
比如说我给他这么这么大的一个大小

272
00:11:01,879 --> 00:11:03,019
我现在输内容

273
00:11:03,019 --> 00:11:04,340
说那个说内容

274
00:11:04,919 --> 00:11:09,639
大家发现它这个字体是越来越小的

275
00:11:10,240 --> 00:11:12,460
如果它折放不下了以后

276
00:11:12,460 --> 00:11:14,620
比如说我们超出这个范围了

277
00:11:14,620 --> 00:11:16,379
放不下更多内容怎么办

278
00:11:16,460 --> 00:11:20,379
他会把已有的这个字体全部给你变小

279
00:11:20,379 --> 00:11:23,440
这样的话保证让你就在这个范围的范围内

280
00:11:23,440 --> 00:11:27,529
能显示完我们这边的所有的这个字符啊

281
00:11:27,529 --> 00:11:28,740
就是这样

282
00:11:29,740 --> 00:11:31,960
你看本身是多大的字符对吧

283
00:11:31,960 --> 00:11:32,980
最后变成那么小了

284
00:11:32,980 --> 00:11:33,639
就是这个

285
00:11:33,639 --> 00:11:38,279
所以这个就是自动会给你进行一个字符的一个大小的一个缩减

286
00:11:38,679 --> 00:11:42,419
最后一个最后一个这个就是高的话

287
00:11:42,419 --> 00:11:44,220
就给就给你自动哎

288
00:11:44,220 --> 00:11:45,580
你看自动增加了

289
00:11:45,580 --> 00:11:46,480
本来是一行

290
00:11:46,480 --> 00:11:48,730
现在它给你自动增加成两行的高了

291
00:11:48,730 --> 00:11:50,240
如果你再写呢

292
00:11:50,740 --> 00:11:54,720
你看三行4号

293
00:11:54,720 --> 00:11:57,000
你只要去加这个字符

294
00:11:57,000 --> 00:12:00,059
那么它的这个高度就会给你自己增加啊

295
00:12:00,059 --> 00:12:04,000
那所以这其实就是它的一个嗯布局的一个方式啊

296
00:12:04,000 --> 00:12:05,919
就是他说的这样的一个布局的方式

297
00:12:05,919 --> 00:12:07,379
排版的方式啊

298
00:12:07,379 --> 00:12:12,679
那么再往下这里是是否使用系统字体

299
00:12:12,679 --> 00:12:13,519
默认是使用的

300
00:12:13,519 --> 00:12:14,360
如果使用的话

301
00:12:14,360 --> 00:12:15,889
这里就是字体的名称

302
00:12:15,889 --> 00:12:18,759
你使用的你当前系统的什么字体

303
00:12:18,759 --> 00:12:21,370
一般我们就用默认的这个都有是吧

304
00:12:21,370 --> 00:12:24,580
或者使用其他的我们系统里面都有的一些字体

305
00:12:24,580 --> 00:12:26,740
但是在这里还有一项选择

306
00:12:26,740 --> 00:12:30,220
就是可以给他一个字体

307
00:12:30,220 --> 00:12:33,580
那我们这里导入了一个字体是t t f格式的

308
00:12:33,580 --> 00:12:36,580
这个字体可以从网上去下t t f字体特别多

309
00:12:36,580 --> 00:12:38,159
比如说我们导进来

310
00:12:38,600 --> 00:12:44,720
那么这时候就会你看字体就会变成我们导进来的这样的一个字体了啊

311
00:12:46,320 --> 00:12:48,960
你好长款是吧

312
00:12:48,960 --> 00:12:49,919
嗯你好

313
00:12:50,659 --> 00:12:52,700
你看这个字体就变样了啊

314
00:12:52,700 --> 00:12:53,659
非常好看的字体

315
00:12:53,659 --> 00:12:55,279
对不对啊

316
00:12:55,279 --> 00:13:00,220
所以说这些字体大家可以从网上自己去下载啊

317
00:13:00,740 --> 00:13:03,169
嗯ok

318
00:13:03,169 --> 00:13:06,940
那么这个就是我们的label

319
00:13:06,940 --> 00:13:11,049
那么这节课我们主要说一下这个canvas和这个level啊

320
00:13:11,049 --> 00:13:14,840
那么我们下节课继续说其他组件啊

321
00:13:14,840 --> 00:13:16,759
那这个ui的组件还是比较多的

322
00:13:16,759 --> 00:13:19,679
我们一节课争取能说上2~3个

323
00:13:19,720 --> 00:13:21,500
ok那我们就先这么多


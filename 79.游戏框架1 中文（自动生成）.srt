1
00:00:09,640 --> 00:00:14,698
ok这节课咱们来讲一个编译游戏的一个思想啊

2
00:00:14,698 --> 00:00:15,929
就是游戏框架

3
00:00:15,929 --> 00:00:18,949
那么这个游戏框架整个而言呃

4
00:00:18,949 --> 00:00:22,280
它不能算是一个写代码的技术啊

5
00:00:22,280 --> 00:00:25,730
准确来说应该是一套写代码的这个思想

6
00:00:25,730 --> 00:00:30,050
那么这个思想也不是说每每个游戏都必须用啊

7
00:00:30,050 --> 00:00:33,619
这个得看你游戏的一个呃情况啊

8
00:00:33,619 --> 00:00:36,439
就如果你写的是一个很小型的游戏啊

9
00:00:36,439 --> 00:00:38,119
几个脚本就写完了啊

10
00:00:38,119 --> 00:00:39,450
那么就是没必要用

11
00:00:39,450 --> 00:00:42,509
如果你的你写的游戏是一个很大型的游戏

12
00:00:42,509 --> 00:00:45,630
比如说是一个rpg角色扮演类的游戏

13
00:00:45,630 --> 00:00:46,259
对不对

14
00:00:46,259 --> 00:00:49,920
那么你可能就需要用到这样的一个思想

15
00:00:49,920 --> 00:00:51,479
咱们cocos creator

16
00:00:51,479 --> 00:00:52,619
咱们之前说了

17
00:00:52,619 --> 00:00:54,780
他主要就是来编写小游戏的

18
00:00:54,780 --> 00:00:58,909
尤其是现在很多人都是拿它来去编写微信游戏的

19
00:00:58,909 --> 00:01:01,219
那么这个微信小游戏的话

20
00:01:01,340 --> 00:01:05,299
它本身也就并不是支持这种很大型的游戏了

21
00:01:05,299 --> 00:01:05,900
对不对

22
00:01:05,900 --> 00:01:09,079
那所以这个就看大家的一个情况了啊

23
00:01:09,079 --> 00:01:10,448
如果你写的游戏

24
00:01:10,448 --> 00:01:12,368
你主要是来写微信小游戏的

25
00:01:12,368 --> 00:01:15,079
而且呃游戏确实比较简单

26
00:01:15,079 --> 00:01:17,329
也没必要用得上这套思想啊

27
00:01:17,329 --> 00:01:20,640
但是如果你要做一个大型游戏啊

28
00:01:20,640 --> 00:01:24,390
就是这个游戏最后完事以后是要在电脑上啊

29
00:01:24,390 --> 00:01:27,359
或者说在手机端上去跑的啊

30
00:01:27,359 --> 00:01:29,719
那么你可以来看一下啊

31
00:01:29,719 --> 00:01:32,569
能不能用得上咱们这这个游戏呃

32
00:01:32,569 --> 00:01:35,180
用得上这个框架体系啊

33
00:01:35,180 --> 00:01:37,219
那么咱们在这里先说一下啊

34
00:01:38,280 --> 00:01:42,719
呃这个所谓的游戏框架是怎么回事啊

35
00:01:44,939 --> 00:01:46,700
我们知道在写一个游戏

36
00:01:46,700 --> 00:01:48,019
我们要写很多脚本

37
00:01:48,019 --> 00:01:48,439
对不对

38
00:01:48,439 --> 00:01:50,359
我们要写很多脚本啊

39
00:01:50,359 --> 00:01:52,459
那么咱们之前每一个游戏啊

40
00:01:52,459 --> 00:01:54,379
都是有几个脚本就完事了

41
00:01:54,379 --> 00:01:57,039
但是如果比如说你要写一个rpg游戏

42
00:01:57,039 --> 00:01:59,679
那它里面的脚本可能非常的多

43
00:01:59,679 --> 00:02:01,418
我们随便举几个例子啊

44
00:02:01,418 --> 00:02:04,140
比如说一个小方块就代表一个脚本啊

45
00:02:04,140 --> 00:02:05,519
我们可能要写一个脚本

46
00:02:05,519 --> 00:02:06,959
这个脚本是要干嘛的

47
00:02:06,959 --> 00:02:08,520
控制啊

48
00:02:08,520 --> 00:02:10,349
刷新血量的

49
00:02:10,349 --> 00:02:12,990
也就是说它是控制这个ui的一个脚本

50
00:02:12,990 --> 00:02:14,300
刷新血量的

51
00:02:14,780 --> 00:02:17,719
那么如果rpg游戏对于ui来说

52
00:02:17,719 --> 00:02:18,770
它还有什么呀

53
00:02:18,770 --> 00:02:23,300
有这个技能面板啊

54
00:02:23,300 --> 00:02:24,560
除了技能面板还有什么

55
00:02:24,560 --> 00:02:25,400
这个太多了

56
00:02:25,400 --> 00:02:28,759
个人属性面板对不对啊

57
00:02:28,759 --> 00:02:30,139
属性面板

58
00:02:39,879 --> 00:02:44,080
那么如果我们要写一个rpg类型游戏

59
00:02:44,080 --> 00:02:48,090
大家想光光是ui我们已经有很多脚本了

60
00:02:48,090 --> 00:02:49,650
我们在这儿如果真要列的话

61
00:02:49,650 --> 00:02:51,219
都会有很多脚本

62
00:02:52,240 --> 00:02:54,300
然后我们继续想再举例子

63
00:02:54,300 --> 00:02:59,340
比如说nbc对这种rpg游戏而言

64
00:02:59,340 --> 00:03:00,639
nbc就多了

65
00:03:01,000 --> 00:03:02,139
可能有什么呀

66
00:03:02,139 --> 00:03:03,479
有个铁匠

67
00:03:04,360 --> 00:03:05,280
铁匠啊

68
00:03:05,280 --> 00:03:08,219
你会挂写一个铁匠专门的一个脚本

69
00:03:08,219 --> 00:03:10,680
除了铁匠脚本还有什么脚本啊

70
00:03:10,680 --> 00:03:13,669
可能有这个卖防具的啊

71
00:03:13,669 --> 00:03:15,680
防具的一个npc

72
00:03:15,680 --> 00:03:16,099
对不对

73
00:03:16,099 --> 00:03:17,060
有铁匠npc

74
00:03:17,060 --> 00:03:19,139
有防具npc啊

75
00:03:19,139 --> 00:03:26,808
这个咱们把这个npc加上npc都加上了啊等等

76
00:03:26,808 --> 00:03:28,519
nbc特别多是吧

77
00:03:30,139 --> 00:03:31,818
或者说路人npc啊

78
00:03:31,818 --> 00:03:33,289
就是他不卖东西

79
00:03:33,289 --> 00:03:36,319
他就是跟你对话啊等等

80
00:03:40,129 --> 00:03:41,689
敌人可能有什么呀

81
00:03:41,689 --> 00:03:44,419
比如说我们可以去打这个狼

82
00:03:44,419 --> 00:03:44,900
狼

83
00:03:44,900 --> 00:03:45,620
就是个敌人

84
00:03:45,620 --> 00:03:46,960
对不对啊

85
00:03:46,960 --> 00:03:47,740
可能还有什么呀

86
00:03:47,740 --> 00:03:49,300
僵尸木乃伊

87
00:03:49,300 --> 00:03:50,259
对不对等等

88
00:03:50,259 --> 00:03:53,060
这些都可以作为你游戏里面的敌人产生的

89
00:03:53,060 --> 00:03:56,180
所以如果我们要写一个rpg类型的游戏

90
00:03:56,319 --> 00:03:58,060
那脚本种类啊

91
00:03:58,060 --> 00:03:59,560
脚本太多了

92
00:03:59,560 --> 00:04:01,180
脚本非常非常的多

93
00:04:01,180 --> 00:04:03,659
那么这时候就会有一个问题

94
00:04:04,278 --> 00:04:07,718
就是如果脚本多了的情况下

95
00:04:07,718 --> 00:04:10,538
我一个脚本要掉另外一个脚本里面的内容

96
00:04:10,538 --> 00:04:14,590
比如说刷新血量可能要掉这个铁匠脚本啊

97
00:04:18,029 --> 00:04:20,430
因为可能我们从这里买了防具

98
00:04:22,560 --> 00:04:27,619
或者说这个什么什么路人npc跟你对话啊

99
00:04:27,619 --> 00:04:28,759
你就学会了一个技能

100
00:04:28,759 --> 00:04:30,720
他就会掉这个技能面板

101
00:04:31,339 --> 00:04:32,860
然后我们打死一个狼

102
00:04:32,860 --> 00:04:34,360
这个狼会给我们一个东西

103
00:04:36,899 --> 00:04:38,699
我们打死一个僵尸呢

104
00:04:38,699 --> 00:04:40,798
然后诶我们可能升了级了

105
00:04:40,798 --> 00:04:42,038
那么就怎样了

106
00:04:42,038 --> 00:04:44,019
调我们的这个属性面板啊

107
00:04:44,019 --> 00:04:45,848
就会把我们的属性进行更改

108
00:04:45,848 --> 00:04:48,038
所以这时候大家就会仔细思考一下

109
00:04:48,038 --> 00:04:54,000
当前这个问题就在于我们的当我们脚本啊变多了起来的时候

110
00:04:54,000 --> 00:04:56,459
如果他们之间在相互调用

111
00:04:56,459 --> 00:04:58,399
那就太麻烦了啊

112
00:04:58,399 --> 00:04:59,629
当脚本越多的时候

113
00:04:59,629 --> 00:05:01,129
你会发现吊起来越麻烦

114
00:05:01,129 --> 00:05:02,680
最后就成了一个网状了

115
00:05:03,098 --> 00:05:04,418
写完这个游戏以后

116
00:05:04,418 --> 00:05:05,199
你别说写完了

117
00:05:05,199 --> 00:05:06,699
可能写一半你就写不下去了

118
00:05:06,699 --> 00:05:08,168
因为因为太乱了

119
00:05:08,168 --> 00:05:09,519
因为太乱了

120
00:05:09,658 --> 00:05:11,098
那这个东西怎么办

121
00:05:11,098 --> 00:05:14,009
你说有没有什么好的解决方法

122
00:05:14,009 --> 00:05:15,990
那就需要用到我们的游戏框架

123
00:05:15,990 --> 00:05:19,410
有所有的游戏框架其实就是一套消息机制

124
00:05:19,410 --> 00:05:21,240
这套机制

125
00:05:23,819 --> 00:05:28,519
啊当然框架里面框架这个概念很很广啊

126
00:05:33,550 --> 00:05:36,129
比如说做游戏常用的这个工具

127
00:05:36,129 --> 00:05:36,910
脚本啊等等

128
00:05:36,910 --> 00:05:37,449
其他的

129
00:05:37,449 --> 00:05:39,668
但是最核心的就是这个消息机制

130
00:05:39,668 --> 00:05:44,899
所有的消息机制就是我比如说在脚本之间相互调用啊

131
00:05:44,899 --> 00:05:50,240
我可以把它变得这个不是这么杂乱啊

132
00:05:50,240 --> 00:05:51,680
变得有序起来啊

133
00:05:51,680 --> 00:05:52,939
怎样去变啊

134
00:05:52,939 --> 00:05:53,660
怎样去变

135
00:05:53,660 --> 00:05:55,000
我们来看一下啊

136
00:05:55,158 --> 00:06:00,720
那么首先我们要做的第一件事就是现在的脚本太乱了啊

137
00:06:00,720 --> 00:06:03,029
所以我们要把脚本进行分类

138
00:06:03,029 --> 00:06:04,889
每一类我给他一个管理类

139
00:06:04,889 --> 00:06:07,779
这个管理类我们用单位去做啊

140
00:06:07,779 --> 00:06:10,300
管理类也就是说中间我们要加一层

141
00:06:10,300 --> 00:06:12,459
是是用来去去进行管理的

142
00:06:12,459 --> 00:06:14,120
比如说第一个是ui

143
00:06:14,980 --> 00:06:17,970
那么ui这个单位就是用来管理什么呀

144
00:06:17,970 --> 00:06:19,800
管理所有ui的啊

145
00:06:19,800 --> 00:06:21,759
比如说血量

146
00:06:24,038 --> 00:06:26,240
嗯把箭头画粗一些

147
00:06:30,100 --> 00:06:32,319
然后比如说技能面板

148
00:06:33,459 --> 00:06:35,519
比如说属性面板

149
00:06:41,670 --> 00:06:44,160
然后除了这个ui

150
00:06:44,160 --> 00:06:48,600
然后我们再来看这边是npc

151
00:06:48,600 --> 00:06:49,079
对不对

152
00:06:51,779 --> 00:06:52,740
nbc

153
00:06:54,800 --> 00:06:57,720
哎nbc铁匠

154
00:07:01,220 --> 00:07:03,420
然后防具

155
00:07:05,860 --> 00:07:08,040
路人他管理这几个脚本

156
00:07:08,040 --> 00:07:10,180
然后最后再来一个

157
00:07:11,560 --> 00:07:14,759
这个可能是个enemy

158
00:07:14,759 --> 00:07:16,559
就是敌人的一个控制器

159
00:07:16,559 --> 00:07:20,908
然后这个控制器呢它可能是管所有的敌人的

160
00:07:20,908 --> 00:07:22,279
比如说有狼

161
00:07:22,459 --> 00:07:23,569
有僵尸

162
00:07:23,569 --> 00:07:24,560
对不对

163
00:07:26,220 --> 00:07:29,360
也就是说我们中间加这么一层管理层啊

164
00:07:29,360 --> 00:07:30,879
加这么一层管理层

165
00:07:30,879 --> 00:07:32,860
那么加了管理层以后

166
00:07:32,860 --> 00:07:37,120
我们整个这个逻辑大家看起来是不是比刚才清晰了很多

167
00:07:37,860 --> 00:07:40,040
那么这时候大家去想啊

168
00:07:40,040 --> 00:07:41,420
我再和刚才一样

169
00:07:41,420 --> 00:07:45,139
比如说我刷新血量这个脚本要去掉铁匠这个脚本

170
00:07:45,139 --> 00:07:46,660
这时候就会怎样去掉

171
00:07:46,959 --> 00:07:49,300
这时候我就不会说直接调用了

172
00:07:49,300 --> 00:07:52,300
我会把这个消息发给我管理类

173
00:07:52,300 --> 00:07:55,779
我告诉我的管理类说我要去调哪个角度

174
00:07:55,779 --> 00:07:56,970
管理类呢

175
00:07:56,970 --> 00:07:59,300
去调另外一个管理类

176
00:08:00,660 --> 00:08:02,420
他把消息给了这个管理类

177
00:08:02,420 --> 00:08:04,879
然后这个管理类呢再把消息给铁匠

178
00:08:04,879 --> 00:08:08,680
这样的话大家看一下是不是比刚才结构清晰了很多

179
00:08:08,759 --> 00:08:11,579
那比如说如果我我没有跨类型的

180
00:08:11,579 --> 00:08:14,408
比如说我刷新血量想掉属性面板

181
00:08:14,408 --> 00:08:15,699
那这个更简单了

182
00:08:15,699 --> 00:08:18,908
我并不会直接调用这个这个脚本

183
00:08:18,908 --> 00:08:24,358
我会把诶我会把我想调用的这个事情告诉给ui管理器

184
00:08:24,358 --> 00:08:27,540
管理器呢再帮我去调用这个属性面板

185
00:08:28,060 --> 00:08:30,160
那也就是说他作为一个中介者了

186
00:08:30,160 --> 00:08:31,720
是不是作为一个中介者存在了

187
00:08:31,720 --> 00:08:32,710
这就是管理类

188
00:08:32,710 --> 00:08:34,940
但是即便如此

189
00:08:34,940 --> 00:08:36,080
我们还是觉得不太好

190
00:08:36,080 --> 00:08:36,440
为什么

191
00:08:36,440 --> 00:08:38,659
因为现在如果管

192
00:08:38,659 --> 00:08:40,399
如果是这种跨类型的

193
00:08:40,399 --> 00:08:43,980
比如说我ui里面的某一个脚本要掉nbc的脚本

194
00:08:43,980 --> 00:08:48,059
它其实在管理类之间还是存在于这种乱调用的

195
00:08:48,059 --> 00:08:51,870
比如说ui可能给npc调用nbc里面的

196
00:08:51,870 --> 00:08:53,490
也可能调用敌人里面的

197
00:08:53,490 --> 00:08:57,179
那nbc呢可能调用ui里面的脚本

198
00:08:57,179 --> 00:08:59,960
也可能调用敌人里面的脚本

199
00:08:59,960 --> 00:09:02,059
那敌人也可能调用它里面的脚本

200
00:09:02,059 --> 00:09:06,918
这样的话其实在管理层这一方面而言还是稍微有些乱的

201
00:09:06,918 --> 00:09:08,418
只是比刚才好一些

202
00:09:08,418 --> 00:09:12,840
那么我们就可以再给他们加一层啊

203
00:09:12,840 --> 00:09:14,399
我们可以再给他们加一层

204
00:09:14,399 --> 00:09:17,460
这一层是最上面一层就一个人啊

205
00:09:17,460 --> 00:09:20,279
因为管理类实际一个游戏它也没有几个管理类

206
00:09:20,279 --> 00:09:20,990
对不对

207
00:09:20,990 --> 00:09:23,269
那么所以我们再来一个人

208
00:09:23,269 --> 00:09:24,230
这个人是干嘛的

209
00:09:25,190 --> 00:09:28,240
这个是管理所有呃管理类的

210
00:09:28,240 --> 00:09:32,279
那这个我们就给它叫做消息中心啊

211
00:09:35,759 --> 00:09:40,279
那么这个消息中心里面他是给谁发消息的啊

212
00:09:40,279 --> 00:09:44,059
他是和哪些呃这个脚本进行消息交互的

213
00:09:44,059 --> 00:09:49,339
那么它呢就是和我们所有的管理类进行消息交互的啊

214
00:09:53,360 --> 00:09:54,909
大概就是这样一个结构

215
00:09:54,909 --> 00:09:56,379
大概就是这样一个结构

216
00:09:56,379 --> 00:10:02,019
也就是说现在如果我这个刷新血量这个脚本想调用防具npc这个脚本啊

217
00:10:02,019 --> 00:10:03,279
想调里面的一个方法

218
00:10:03,279 --> 00:10:04,720
或者想调里面的一个属性

219
00:10:04,720 --> 00:10:05,740
怎么办了啊

220
00:10:05,740 --> 00:10:07,328
它不能直接去调用

221
00:10:07,328 --> 00:10:10,778
他应该怎样把这个想调用的事情告诉给ui

222
00:10:10,778 --> 00:10:12,809
ui告诉给消息中心

223
00:10:12,809 --> 00:10:15,330
消息中心把这个事情告诉nbc

224
00:10:15,330 --> 00:10:18,029
nbc把这个消息再告诉防具啊

225
00:10:18,029 --> 00:10:19,519
是不是就变成这样了

226
00:10:20,440 --> 00:10:23,279
那么同样的别的脚本一样的啊

227
00:10:23,279 --> 00:10:24,840
如果比如说我们杀死一个狼

228
00:10:29,179 --> 00:10:32,610
那这个狼呢哎把这个消息给了敌人

229
00:10:32,610 --> 00:10:34,110
敌人给了消息中心

230
00:10:34,110 --> 00:10:35,250
消息中心给了ui

231
00:10:36,340 --> 00:10:40,059
这样的话是不是结构一下比刚才清晰了很多

232
00:10:40,200 --> 00:10:40,980
对不对

233
00:10:40,980 --> 00:10:44,820
所以当我们游戏如果做到这种rpg的这种情况啊

234
00:10:44,820 --> 00:10:47,370
就类似于rpg这种游戏呃

235
00:10:47,370 --> 00:10:49,230
这属于一种大型游戏了啊

236
00:10:49,230 --> 00:10:52,179
那么你就需要用到这样的一套消息机制了

237
00:10:52,720 --> 00:10:56,230
如果本身你的游戏写完以后也没几个脚本啊

238
00:10:56,230 --> 00:10:57,159
也没几个脚本

239
00:10:57,159 --> 00:10:59,320
那么你就可以不用这套机制啊

240
00:10:59,320 --> 00:11:04,559
所以今天这个东西啊呃准确而言就是也是看大家的一个需求啊

241
00:11:04,559 --> 00:11:07,240
你要是没必要呃

242
00:11:07,240 --> 00:11:08,799
不准备去做大型游戏啊

243
00:11:08,799 --> 00:11:11,379
你可以就是说把它看一看啊

244
00:11:11,379 --> 00:11:12,639
明白怎么回事啊

245
00:11:12,639 --> 00:11:14,879
大概明白怎么回事就可以了啊

246
00:11:16,820 --> 00:11:17,919
ok啊

247
00:11:17,919 --> 00:11:22,860
那么这时候我们要做到这样的一个情况怎么去做

248
00:11:22,860 --> 00:11:26,509
首先系统实际上帮我们实现了一个做到这样的一个东西

249
00:11:26,509 --> 00:11:28,730
那就是我们之前也讲过

250
00:11:28,730 --> 00:11:31,429
就是发送消息啊

251
00:11:31,429 --> 00:11:34,129
我们知道这个本身我们这个节点就有一个功能

252
00:11:34,129 --> 00:11:36,529
是监听和一个发送消息啊

253
00:11:36,529 --> 00:11:39,779
我们之前而是在某一节课学过啊

254
00:11:39,779 --> 00:11:41,110
哪一节课我忘了

255
00:11:41,110 --> 00:11:44,529
然后就是哎我们就可以说一个节点可以去监听一个消息

256
00:11:44,529 --> 00:11:47,019
然后另外一个节点就可以给他发消息

257
00:11:47,019 --> 00:11:48,139
但是

258
00:11:49,879 --> 00:11:52,740
这个系统实现并不是特别的好啊

259
00:11:52,740 --> 00:11:55,769
所以如果我们真要去做这个消息机制的话

260
00:11:55,769 --> 00:11:57,539
我们需要自己实现一套

261
00:11:57,539 --> 00:12:00,600
也就是说这套逻辑我们需要自己用代码实现一套

262
00:12:00,600 --> 00:12:01,980
那么怎么去做啊

263
00:12:01,980 --> 00:12:02,700
怎么去做

264
00:12:02,700 --> 00:12:04,879
那我们一层一层来想

265
00:12:04,960 --> 00:12:08,679
首先我们知道每一个基本的脚本啊

266
00:12:08,679 --> 00:12:10,179
如果我们要用游戏框架的话

267
00:12:10,179 --> 00:12:14,219
每一个基本的脚本它都有一个功能是接收消息

268
00:12:14,379 --> 00:12:17,350
而每一个基本的脚本默认是继承于谁的

269
00:12:17,350 --> 00:12:19,578
继承于cc.compet

270
00:12:21,960 --> 00:12:28,129
也就是说他们是继承于我们这个cos cos creator里面的这个组件的啊

271
00:12:28,129 --> 00:12:29,330
进行这个组件的

272
00:12:29,330 --> 00:12:32,960
但这是他并没有接收消息的功能啊

273
00:12:32,960 --> 00:12:35,440
所以我们在这里要创建第一个类

274
00:12:35,440 --> 00:12:38,259
第一类呢就是继承于他的啊

275
00:12:38,259 --> 00:12:41,599
可能这个类比如说我们取个名字叫做

276
00:12:42,320 --> 00:12:43,958
比如说叫做

277
00:12:46,659 --> 00:12:49,120
convenant base啊

278
00:12:49,120 --> 00:12:53,578
然后这个类啊我们创建一个新的类叫combent base

279
00:12:53,720 --> 00:12:55,940
然后这个类的话啊

280
00:12:55,940 --> 00:12:59,120
这个类我们让它继承于cc.confident

281
00:12:59,220 --> 00:13:00,600
那么从现在开始

282
00:13:00,600 --> 00:13:01,740
我们写这个类以后

283
00:13:01,740 --> 00:13:03,299
在这个类里面我们写一个方法

284
00:13:03,299 --> 00:13:04,950
就是接收消息的方法

285
00:13:04,950 --> 00:13:11,039
然后所有啊我们这边所有的这个基本的类啊

286
00:13:11,039 --> 00:13:12,990
我们这个游戏里面所写的基本的类

287
00:13:12,990 --> 00:13:14,279
正常我们创建完了

288
00:13:14,279 --> 00:13:16,019
它是继承一个cc.confident的

289
00:13:16,019 --> 00:13:16,649
对不对

290
00:13:16,649 --> 00:13:20,159
我们都给它改成继承于我们的这个continent base

291
00:13:20,279 --> 00:13:24,419
那么这样的话是不是他们所有的每个类里面都会有

292
00:13:24,419 --> 00:13:26,039
那个接收消息的方法

293
00:13:26,039 --> 00:13:26,850
对不对

294
00:13:26,850 --> 00:13:31,240
所以我们这里要创建的第一个类就是coment base啊

295
00:13:31,240 --> 00:13:32,740
就是一个基本的一个类

296
00:13:32,740 --> 00:13:33,940
基础的一个类

297
00:13:33,940 --> 00:13:36,639
这个类主要就是帮我们实现啊

298
00:13:36,639 --> 00:13:38,440
在它里面实现一个空的方法

299
00:13:38,440 --> 00:13:40,639
就是接收消息的这样的一个方法

300
00:13:40,639 --> 00:13:42,259
那么既然接收消息

301
00:13:42,259 --> 00:13:45,980
大家去想消息本身也是一个方法啊

302
00:13:45,980 --> 00:13:49,460
就是message本身也是一个对象啊

303
00:13:49,460 --> 00:13:50,720
不是不应该说是个方法

304
00:13:50,720 --> 00:13:52,719
message本身也是一个对象

305
00:13:53,899 --> 00:13:57,320
每一个消息我们都我们都给它封装为一个对象啊

306
00:13:57,320 --> 00:14:00,318
每一个消息都给它封装为一个消息对象

307
00:14:00,879 --> 00:14:02,958
然后除了这两个类呢

308
00:14:04,039 --> 00:14:06,860
管理类中间这一层我们说了有个管理类

309
00:14:06,860 --> 00:14:07,698
对不对

310
00:14:09,779 --> 00:14:10,919
manager

311
00:14:10,919 --> 00:14:13,409
我们在这里要写一个管理类

312
00:14:13,409 --> 00:14:14,580
管理类

313
00:14:14,580 --> 00:14:16,379
首先他也会接收消息

314
00:14:16,379 --> 00:14:18,519
所以管理类继承于他

315
00:14:18,679 --> 00:14:20,818
管理类是继承于他的

316
00:14:24,320 --> 00:14:25,580
管理类继承于他

317
00:14:25,580 --> 00:14:27,219
那么他呢继承于谁

318
00:14:27,419 --> 00:14:29,190
我们既然写到这儿了

319
00:14:29,190 --> 00:14:33,479
他能继承cc.component

320
00:14:39,600 --> 00:14:43,798
ok是不是就这样的管理类继承于这个鸡肋啊

321
00:14:43,798 --> 00:14:46,619
组件积累组件基类继承于我们的这个cc点

322
00:14:46,619 --> 00:14:47,339
coment啊

323
00:14:47,339 --> 00:14:49,159
这个类组件类

324
00:14:49,259 --> 00:14:52,139
也就是说这两个类是我们自己实现的啊

325
00:14:52,139 --> 00:14:53,340
这个也是我们自己实现的

326
00:14:53,340 --> 00:14:55,019
目前我们需要实现三个类了

327
00:14:55,019 --> 00:14:55,730
对不对

328
00:14:55,730 --> 00:14:57,529
然后把这个管理类实行完以后

329
00:14:57,529 --> 00:14:59,629
所有的这个管理这一层

330
00:14:59,629 --> 00:15:01,879
他们都是继承于这个管理类的

331
00:15:01,879 --> 00:15:03,740
都是继承于这个管理类的

332
00:15:04,299 --> 00:15:05,860
那么除了这个管理类

333
00:15:05,860 --> 00:15:07,000
我们有个最大的类

334
00:15:07,000 --> 00:15:10,269
这个类又是负责管理所有管理类的啊

335
00:15:10,269 --> 00:15:13,740
那么这个类就是我们这个消息中心对应的类

336
00:15:14,940 --> 00:15:19,919
这个类呢我们就叫它message center

337
00:15:19,919 --> 00:15:21,679
消息中心啊

338
00:15:21,679 --> 00:15:23,269
就是这样的一个类

339
00:15:23,269 --> 00:15:25,850
也就是说我们现在一定要自己写几个类

340
00:15:25,850 --> 00:15:26,690
我们看一下

341
00:15:26,690 --> 00:15:28,850
第一个就是组件类

342
00:15:28,850 --> 00:15:30,110
组件的积累啊

343
00:15:30,110 --> 00:15:32,440
组件的积累呢写好以后以后

344
00:15:32,440 --> 00:15:33,580
我们在这个游戏里面

345
00:15:33,580 --> 00:15:36,639
比如rpg游戏里面所有的小脚本啊

346
00:15:36,639 --> 00:15:39,529
基本的脚本全是继承于他的啊

347
00:15:39,529 --> 00:15:40,889
全是继承于他的

348
00:15:40,889 --> 00:15:42,509
然后我们要创建第二个类

349
00:15:42,509 --> 00:15:43,590
就是管理者类

350
00:15:43,590 --> 00:15:46,230
以后所有的单位的这种管理者

351
00:15:46,230 --> 00:15:51,139
管理者全是继承他的消息中心唉

352
00:15:51,139 --> 00:15:52,710
我们要单独写一个类

353
00:15:52,710 --> 00:15:57,080
然后我们其实中间每一次比如说传递传递的都是消息

354
00:15:57,080 --> 00:15:57,559
对不对

355
00:15:57,559 --> 00:16:01,250
在这路线中间路线里面传递的都是消息

356
00:16:01,250 --> 00:16:04,720
那消息本身我们也要给它一个类产生一个对象啊

357
00:16:04,720 --> 00:16:05,620
这样的话才能做到

358
00:16:05,620 --> 00:16:06,730
比如说刷新血量

359
00:16:06,730 --> 00:16:08,559
想给防具调个消息

360
00:16:08,559 --> 00:16:09,679
发个消息

361
00:16:09,679 --> 00:16:12,379
我们就要哎在它里面创建一个消息对象

362
00:16:12,379 --> 00:16:15,919
把这个对象呢经过传递传递给对应的这个脚本

363
00:16:15,919 --> 00:16:18,720
那么这个脚本就会收到你发的这个消息

364
00:16:18,720 --> 00:16:19,470
对不对

365
00:16:19,470 --> 00:16:21,480
所以目前就是这四个类

366
00:16:21,480 --> 00:16:22,500
那我们知道了以后

367
00:16:22,500 --> 00:16:26,460
我们就要通过这个代码去进行实现了啊

368
00:16:26,460 --> 00:16:27,818
去进行实现了

369
00:16:31,100 --> 00:16:32,000
逻辑啊

370
00:16:32,000 --> 00:16:33,139
就是这么一个逻辑

371
00:16:33,139 --> 00:16:35,720
大家尽量把这个东西搞清楚啊

372
00:16:35,720 --> 00:16:39,820
哪怕你说我们以后也不准备写大游戏啊

373
00:16:39,820 --> 00:16:44,990
因为这个cos creator很多情况下它是不需要写这种大型游戏的

374
00:16:44,990 --> 00:16:49,279
大型游戏我们基本上也不会用这个cos creator这个游戏情去做

375
00:16:49,820 --> 00:16:53,289
就是说你要是不管你做不做大型游戏啊

376
00:16:53,289 --> 00:16:55,509
但是这个框架尽量去理解啊

377
00:16:55,509 --> 00:16:57,730
因为你做游戏就离不开框架

378
00:16:57,730 --> 00:16:58,149
对不对

379
00:16:58,149 --> 00:17:01,000
以后可能你会接触一些比较复杂的游戏

380
00:17:01,000 --> 00:17:02,259
就会接触到框架

381
00:17:02,259 --> 00:17:05,019
那我们这个已经是最基础的一个框架了啊

382
00:17:05,019 --> 00:17:06,130
一套消息框架

383
00:17:06,130 --> 00:17:09,309
那么所以你尽量把它理解清楚

384
00:17:09,309 --> 00:17:10,569
而且理解清楚以后

385
00:17:10,569 --> 00:17:13,799
对你这个面试啊什么的也是有好处的

386
00:17:14,420 --> 00:17:14,990
嗯

387
00:17:14,990 --> 00:17:18,079
ok那么这节课我们就说这么多啊

388
00:17:18,079 --> 00:17:19,339
我们把这个理论说一下

389
00:17:19,339 --> 00:17:21,589
下节课我们开始去写代码

390
00:17:21,589 --> 00:17:23,950
ok那我们这节课就这么多

391
00:17:23,950 --> 00:17:25,039
略略略


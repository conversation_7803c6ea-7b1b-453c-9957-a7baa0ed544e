1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,320 --> 00:00:13,730
ok我们这节课来去写代码

3
00:00:13,730 --> 00:00:15,919
实现我们的这个游戏框架

4
00:00:15,919 --> 00:00:19,030
首先我们创建个文件夹哎

5
00:00:19,030 --> 00:00:21,219
把我们这个脚本文件夹创建好

6
00:00:21,219 --> 00:00:26,519
然后我们新建我们的四个脚本啊

7
00:00:26,519 --> 00:00:28,739
我们需要编写的这个四个对象

8
00:00:28,739 --> 00:00:30,600
我们先给它提前创建好

9
00:00:30,600 --> 00:00:35,450
第一个我们需要有一个message发消息的这样的一个类

10
00:00:35,450 --> 00:00:40,399
第二个是我们的这个呃消息中心

11
00:00:40,399 --> 00:00:41,219
对不对

12
00:00:41,740 --> 00:00:43,439
message center

13
00:00:45,640 --> 00:00:46,899
还有两个啊

14
00:00:46,899 --> 00:00:47,560
两个是什么

15
00:00:47,560 --> 00:00:49,380
一个是组件的鸡肋

16
00:00:50,460 --> 00:00:52,399
confident base

17
00:00:55,219 --> 00:01:00,359
再来一个是我们的管理类manager base

18
00:01:01,880 --> 00:01:04,480
那在这里我们都给它打开

19
00:01:09,439 --> 00:01:12,560
那打开以后我们一个一个来写啊

20
00:01:12,560 --> 00:01:13,519
一个一个来写

21
00:01:13,519 --> 00:01:15,500
首先在message这边啊

22
00:01:15,500 --> 00:01:18,540
我们改一下这个类的名字message

23
00:01:18,540 --> 00:01:22,439
然后这个类呢它是不继承于任何内容的啊

24
00:01:22,439 --> 00:01:23,819
就它不继承一个组件的

25
00:01:23,819 --> 00:01:26,340
它本身就是一个消息啊

26
00:01:26,340 --> 00:01:28,980
那所以对于这个类而言

27
00:01:28,980 --> 00:01:30,719
它就是一个单纯的class啊

28
00:01:30,719 --> 00:01:31,859
它就是一个单纯的class

29
00:01:31,859 --> 00:01:33,180
这个前面不要删啊

30
00:01:33,180 --> 00:01:37,120
不删它是为了呃其他文件可以找得到这个message

31
00:01:37,120 --> 00:01:38,859
你要是把前面的都删了

32
00:01:39,140 --> 00:01:44,560
那呃这个在别的文件里面就引用不到这个message这样的一个类啊

33
00:01:44,560 --> 00:01:47,079
所以这个留下来default可以去掉啊

34
00:01:47,079 --> 00:01:51,219
但是这个export必须留下嗯

35
00:01:51,959 --> 00:01:54,060
对于这样的一个类而言

36
00:01:54,060 --> 00:01:55,079
对于消息类而言

37
00:01:55,079 --> 00:01:56,819
我们里面需要有什么内容

38
00:01:56,819 --> 00:01:58,439
我们想一想啊

39
00:01:58,439 --> 00:01:59,939
每一个消息啊

40
00:01:59,939 --> 00:02:03,159
每一个消息我们

41
00:02:03,159 --> 00:02:06,900
比如说我这里我们大家想一想啊

42
00:02:07,359 --> 00:02:09,759
我现在刷新血量要给铁

43
00:02:09,759 --> 00:02:11,719
要给这个防具发一个消息

44
00:02:11,740 --> 00:02:14,349
那当我把消息给了消息中心的时候

45
00:02:14,349 --> 00:02:17,379
也就是说当消息中心拿到这个消息的时候

46
00:02:17,400 --> 00:02:20,610
他怎么知道这个消息就要哎走这条路

47
00:02:20,610 --> 00:02:22,469
先发给np c管理类

48
00:02:22,469 --> 00:02:23,699
nb c管理类

49
00:02:23,699 --> 00:02:27,960
再发给对应的这个呃这这样的一个对象呢

50
00:02:28,240 --> 00:02:29,680
啊这怎么知道

51
00:02:29,680 --> 00:02:32,379
那就证明这个message里面携带了一些信息

52
00:02:32,379 --> 00:02:37,590
首先第一个携带的信息就是你这个消息是哪个类型的啊

53
00:02:37,590 --> 00:02:38,879
是ui类型的

54
00:02:38,879 --> 00:02:41,199
比如说在这个里面就是ui类型的

55
00:02:41,199 --> 00:02:42,879
还是n b c类型的

56
00:02:42,879 --> 00:02:44,680
还是敌人类型的

57
00:02:44,680 --> 00:02:46,180
所以第一个是大类型

58
00:02:46,180 --> 00:02:48,520
这个我们一定要去携带

59
00:02:48,680 --> 00:02:55,379
第二个message里面携带的第二个信息就是什么小类型啊

60
00:02:55,379 --> 00:02:57,120
就是你到底要给谁发的

61
00:02:57,120 --> 00:02:59,789
是铁匠还是防具还是路人啊

62
00:02:59,789 --> 00:03:06,439
那也就是说第二个一般就是铁匠还是防具还是路人啊

63
00:03:06,439 --> 00:03:07,490
到底是哪一个

64
00:03:07,490 --> 00:03:10,139
那么你给他发的消息内容是什么

65
00:03:10,360 --> 00:03:12,280
三还有个内容

66
00:03:12,280 --> 00:03:14,319
所以对于每个消息而言

67
00:03:14,319 --> 00:03:16,580
里面必须有这三条内容啊

68
00:03:16,580 --> 00:03:19,340
这样的话当着一个消息中心接到这个消息的时候

69
00:03:19,340 --> 00:03:20,210
他才知道诶

70
00:03:20,210 --> 00:03:22,639
首先我通过第一第一条哎

71
00:03:22,639 --> 00:03:23,900
我知道是给谁发的

72
00:03:23,900 --> 00:03:25,259
给哪一大类发的

73
00:03:25,259 --> 00:03:28,919
然后第二条我这个每一个大类就是这个管理类

74
00:03:28,919 --> 00:03:31,199
我知道给哪一个小类去发的啊

75
00:03:31,199 --> 00:03:33,080
是给铁匠还是给防具了

76
00:03:33,080 --> 00:03:34,580
然后给他们以后

77
00:03:34,580 --> 00:03:36,020
他们的内容到底是什么

78
00:03:36,020 --> 00:03:37,009
这是第三条

79
00:03:37,009 --> 00:03:40,300
所以每个消息里面最起码应该有这三类存在

80
00:03:40,300 --> 00:03:44,699
那么这三类我们写代码里面第一个就是大的类型

81
00:03:44,840 --> 00:03:51,879
所以第一个我们叫它类型是number类型

82
00:03:51,879 --> 00:03:53,500
我们就用number来区分啊

83
00:03:53,500 --> 00:03:57,060
大类比如说呃每一类我们给它取个编号

84
00:03:57,060 --> 00:03:57,840
第一类叫一

85
00:03:57,840 --> 00:03:58,620
第二类叫二

86
00:03:58,620 --> 00:04:00,180
第第三类叫三啊

87
00:04:00,180 --> 00:04:02,520
我们就通过这个数字来进行分类

88
00:04:02,879 --> 00:04:04,349
第二个就是小类

89
00:04:04,349 --> 00:04:06,900
小类我们给它叫做命令啊

90
00:04:06,900 --> 00:04:09,080
就是你发的一个具体的命令了

91
00:04:09,340 --> 00:04:13,780
command的也是通过number

92
00:04:14,180 --> 00:04:16,519
也是通过这个数字来进行区分

93
00:04:16,519 --> 00:04:18,470
第三个具体的内容

94
00:04:18,470 --> 00:04:22,550
我们就给它叫做参数content

95
00:04:22,550 --> 00:04:25,939
因为具体的内容可能是各种类型啊

96
00:04:25,939 --> 00:04:27,199
可能是个字符串

97
00:04:27,660 --> 00:04:28,860
可能是个数字

98
00:04:28,860 --> 00:04:29,639
可能是个对象

99
00:04:29,639 --> 00:04:31,319
所以我们给他安利

100
00:04:31,319 --> 00:04:34,180
然后我们来一个构造方法

101
00:04:35,759 --> 00:04:37,079
构造方法

102
00:04:41,620 --> 00:04:42,939
构造方法的话

103
00:04:42,939 --> 00:04:46,600
tab command content

104
00:04:47,779 --> 00:04:51,589
我们让外界传进来这三个参数

105
00:04:51,589 --> 00:04:56,339
然后我们赋值给我们这三个属性就可以了

106
00:05:06,639 --> 00:05:10,180
那这样的话这个消息啊我们就算完成了

107
00:05:10,220 --> 00:05:13,160
那么在这里有一点就是这个类型和这个命令

108
00:05:13,160 --> 00:05:19,720
如果比如说我们拿真的拿123区分区分这个命令一般我们会大点

109
00:05:19,720 --> 00:05:22,759
比如说用100或者101

110
00:05:22,759 --> 00:05:24,259
200 202

111
00:05:24,259 --> 00:05:25,899
用这种数字区分的话

112
00:05:25,899 --> 00:05:28,839
我们看数字的话肯定会很容易看乱

113
00:05:28,839 --> 00:05:29,459
对不对

114
00:05:29,480 --> 00:05:32,000
到最后我们自己都忘了一代表哪一大类

115
00:05:32,000 --> 00:05:33,170
二代表哪一大类

116
00:05:33,170 --> 00:05:33,949
那怎么办

117
00:05:33,949 --> 00:05:37,319
所以我们在这里一般还要写一个类啊

118
00:05:37,319 --> 00:05:43,500
这个类呢去负责帮我们嗯做一些静态的一些变量啊

119
00:05:43,500 --> 00:05:45,519
那么这个类的话

120
00:05:45,639 --> 00:05:47,680
首先外界需要引用

121
00:05:47,680 --> 00:05:49,120
所以expert必须写上

122
00:05:49,120 --> 00:05:54,779
然后class message type就是消息的类型有哪些

123
00:05:54,779 --> 00:05:57,480
每一种类型我们都是静态的啊

124
00:05:57,480 --> 00:06:00,639
比如说我们的类型刚才说了有什么呀

125
00:06:00,639 --> 00:06:03,100
有type design啊

126
00:06:03,100 --> 00:06:04,800
这个这个ui对不对

127
00:06:04,899 --> 00:06:07,509
哎u i我们就让它变成一

128
00:06:07,509 --> 00:06:13,579
那这样的话以后我们比如说哎我们说要发发送的消息类型是ui类型

129
00:06:13,579 --> 00:06:15,019
你就不用直接写一

130
00:06:15,019 --> 00:06:16,220
你写一的话

131
00:06:16,480 --> 00:06:18,459
我们这也很容易搞乱啊

132
00:06:18,459 --> 00:06:19,420
你就怎样去写

133
00:06:19,420 --> 00:06:20,379
你哪里需要用到

134
00:06:20,379 --> 00:06:23,439
你就是哎比如说你这里用到这个消息了

135
00:06:23,439 --> 00:06:27,620
你就是message tp.tab ui

136
00:06:27,620 --> 00:06:31,370
哎你看这样用起来是不是看起来就会很清晰啊

137
00:06:31,370 --> 00:06:33,439
但实际上它本身还是一个什么呀

138
00:06:33,439 --> 00:06:34,220
还是一个数字

139
00:06:34,220 --> 00:06:34,980
对不对

140
00:06:36,800 --> 00:06:39,949
然后第二个比如说我们有个tap

141
00:06:39,949 --> 00:06:42,139
有个np c nbc

142
00:06:42,139 --> 00:06:43,220
比如说是二

143
00:06:43,220 --> 00:06:44,980
然后tp

144
00:06:47,279 --> 00:06:49,259
第三个呢可能是个敌人

145
00:06:49,259 --> 00:06:51,120
敌人就是第三类等等

146
00:06:51,120 --> 00:06:52,379
比如说可能还有很多

147
00:06:52,379 --> 00:06:57,199
比如说cp audio是声音管理器

148
00:06:57,199 --> 00:07:00,709
对不对啊等等非常多的管理器啊

149
00:07:00,709 --> 00:07:02,670
那么这是大类

150
00:07:02,670 --> 00:07:04,439
下面我们可以去写小类

151
00:07:04,439 --> 00:07:07,860
比如说具体的u i咱们之前说了

152
00:07:07,860 --> 00:07:11,240
ui里面可能有这个不同面板的

153
00:07:11,240 --> 00:07:11,959
对不对

154
00:07:11,959 --> 00:07:16,790
有这个比如说刷新血量的啊

155
00:07:16,790 --> 00:07:18,439
这样的一个命令

156
00:07:18,480 --> 00:07:21,120
也就是说这里是指类型啊

157
00:07:21,120 --> 00:07:25,240
从这里开始我们就去写命令的命令的话就和上面对应起来

158
00:07:25,240 --> 00:07:27,310
比如说这是uh 1

159
00:07:27,310 --> 00:07:31,899
那么我这个所有ui的命令我们都是100几啊

160
00:07:31,899 --> 00:07:33,579
比如说再来个ui的命令

161
00:07:33,579 --> 00:07:35,980
比如说是刷新分数的

162
00:07:37,180 --> 00:07:41,579
比如说给他的102啊

163
00:07:41,579 --> 00:07:45,180
当然像这个命令肯定是你编写游戏边去添加的啊

164
00:07:45,180 --> 00:07:46,139
我这里就是举例子

165
00:07:46,139 --> 00:07:48,660
我就先写上一些啊

166
00:07:48,720 --> 00:07:51,360
比如说还有很多内容是吧

167
00:07:52,740 --> 00:07:54,720
呃inventory啊

168
00:07:54,720 --> 00:07:56,819
比如说背包

169
00:07:56,819 --> 00:07:59,240
103

170
00:08:02,240 --> 00:08:03,920
呃很多啊

171
00:08:03,920 --> 00:08:05,509
我这里就不去想了

172
00:08:05,509 --> 00:08:10,079
那么接下来我们往下去写别的啊

173
00:08:10,079 --> 00:08:16,860
比如说第二个我们刚才写的是个这个这个呃n b c n b c

174
00:08:16,860 --> 00:08:21,420
那么我们就有这个比如说啊有这个铁匠啊什么的

175
00:08:21,420 --> 00:08:22,819
对不对啊

176
00:08:22,819 --> 00:08:26,660
那在这里我们就是nb c一号啊

177
00:08:26,660 --> 00:08:29,120
我这里就不去写那具体的了

178
00:08:29,120 --> 00:08:30,560
但是它的编码啊

179
00:08:30,560 --> 00:08:31,819
就从比如说201

180
00:08:31,819 --> 00:08:33,440
因为nbc对应的是二开头的

181
00:08:33,440 --> 00:08:34,419
对不对啊

182
00:08:34,419 --> 00:08:36,700
我们在这里就是举这样的一个例子啊

183
00:08:36,700 --> 00:08:38,889
比如说n b c2 号

184
00:08:38,889 --> 00:08:40,740
第二个n b c啊

185
00:08:40,740 --> 00:08:41,558
就这样

186
00:08:41,580 --> 00:08:45,299
然后比如说我们还有这个敌人的命令

187
00:08:45,299 --> 00:08:47,600
那就是enemy是吧

188
00:08:47,600 --> 00:08:50,419
有这个1号敌人能赢

189
00:08:50,419 --> 00:08:55,269
那就是301m

190
00:08:55,269 --> 00:08:58,480
2号那就是302啊

191
00:08:58,480 --> 00:08:59,559
就是举例子

192
00:08:59,639 --> 00:09:05,100
那么也就是说我们的不管是你的大类还是小类啊

193
00:09:05,100 --> 00:09:06,899
我们都要去给它写在这里

194
00:09:06,899 --> 00:09:09,600
然后用的时候你就不要去用数字了啊

195
00:09:09,600 --> 00:09:13,480
你就直接去message tap点对应的内容啊

196
00:09:13,480 --> 00:09:15,159
这样的话用起来是比较清晰的

197
00:09:15,159 --> 00:09:17,200
我们一眼就能看出来这个消息是干嘛的

198
00:09:17,200 --> 00:09:17,960
对不对

199
00:09:18,480 --> 00:09:19,110
嗯

200
00:09:19,110 --> 00:09:19,679
ok啊

201
00:09:19,679 --> 00:09:21,779
那么这个就是我们的消息类型啊

202
00:09:21,779 --> 00:09:22,740
这个就是我们消息

203
00:09:22,740 --> 00:09:25,799
整个这个消息我们就算是写完了啊

204
00:09:25,799 --> 00:09:27,669
消息就写完了呃

205
00:09:27,669 --> 00:09:31,419
这个得这个好好琢磨一下啊

206
00:09:31,419 --> 00:09:32,659
好好琢磨一下

207
00:09:33,080 --> 00:09:37,190
那么再往下我们在这里直接打开

208
00:09:37,190 --> 00:09:39,559
我们第二个去写这个组件

209
00:09:39,559 --> 00:09:40,600
的一个积累

210
00:09:40,620 --> 00:09:42,419
因为这个类型比较简单

211
00:09:42,419 --> 00:09:44,100
它的主要作用就是干嘛的

212
00:09:44,100 --> 00:09:46,259
就是来帮我们去接收消息的

213
00:09:46,440 --> 00:09:53,250
首先它的类我们要改一下confident base

214
00:09:53,250 --> 00:09:59,799
那么这个类呃它的主要目的是帮我们接收消息

215
00:09:59,799 --> 00:10:04,580
所以它的里面我们就去写一个方法接收消息就好了

216
00:10:05,720 --> 00:10:08,389
这样他所有的子类是不是都有这个消息

217
00:10:08,389 --> 00:10:09,320
都有这个方法了

218
00:10:09,320 --> 00:10:10,559
就是接收消息

219
00:10:10,659 --> 00:10:11,980
那么接收消息的话

220
00:10:11,980 --> 00:10:17,330
我们方法名字就叫receive message

221
00:10:17,330 --> 00:10:22,039
然后接收一个message什么类型的

222
00:10:22,279 --> 00:10:26,679
就是message类型的这样的一个方法

223
00:10:26,700 --> 00:10:28,950
那里面可以什么都不用写就可以了

224
00:10:28,950 --> 00:10:35,019
这样的话也就是说我所有继承于我这个convenant base的子类啊

225
00:10:35,019 --> 00:10:36,940
里面是不是都可以调用这个方法了

226
00:10:36,940 --> 00:10:40,419
也就是说他们都有这个接收消息的这样的一个功能了啊

227
00:10:40,419 --> 00:10:41,590
都有这样一个功能了

228
00:10:41,590 --> 00:10:44,000
那么这个类就算完成了

229
00:10:44,019 --> 00:10:46,240
这个是非常简单的一类了

230
00:10:46,240 --> 00:10:47,379
一下就完事了

231
00:10:47,379 --> 00:10:47,919
对不对

232
00:10:47,919 --> 00:10:50,000
然后接下来我们继续

233
00:10:51,360 --> 00:10:56,960
接下来我们来去写这个管理类啊

234
00:10:56,960 --> 00:10:57,740
管理类

235
00:10:57,740 --> 00:10:59,720
那我们就一层一层去写啊

236
00:10:59,720 --> 00:11:02,210
我们这个把这一层写完了

237
00:11:02,210 --> 00:11:03,500
我们去写管理类

238
00:11:03,500 --> 00:11:05,740
管理类的话就稍有些麻烦了

239
00:11:06,279 --> 00:11:07,779
嗯对管理类而言

240
00:11:07,779 --> 00:11:11,879
首先我们嗯改名字

241
00:11:14,299 --> 00:11:15,799
manager base

242
00:11:15,799 --> 00:11:17,330
首先把名字改好

243
00:11:17,330 --> 00:11:18,860
他能继承于谁

244
00:11:18,860 --> 00:11:23,919
继承于我们的component base啊

245
00:11:23,919 --> 00:11:25,539
因为它本身也会接收消息

246
00:11:25,539 --> 00:11:26,320
对不对

247
00:11:26,320 --> 00:11:28,960
所以它本身也是会接收消息的

248
00:11:28,960 --> 00:11:30,100
那我们一会再去写啊

249
00:11:30,100 --> 00:11:31,000
接收消息

250
00:11:31,360 --> 00:11:32,919
那么它既然是管理类

251
00:11:32,919 --> 00:11:34,860
他要管理所有的什么呀

252
00:11:35,840 --> 00:11:41,120
管理的消息接收者数组

253
00:11:42,159 --> 00:11:45,279
那么它既然会管理这些小类

254
00:11:45,279 --> 00:11:46,659
所以它一定有一个数组

255
00:11:46,659 --> 00:11:49,759
数组里面就是他管理的所有的小类啊

256
00:11:49,759 --> 00:11:52,279
所以在这里我们上来就要给大家写一个数组

257
00:11:52,279 --> 00:11:56,659
这个数组就是receive

258
00:11:56,659 --> 00:11:58,500
list

259
00:12:00,480 --> 00:12:04,590
这个数组是什么类型的呢

260
00:12:04,590 --> 00:12:09,299
come comb这个类型啊

261
00:12:09,299 --> 00:12:11,159
等一个空数组实例化

262
00:12:11,159 --> 00:12:12,600
默认是个空数组

263
00:12:13,159 --> 00:12:18,139
也因为我们所有的这个真正的这个啊

264
00:12:18,139 --> 00:12:19,700
就是你真正写完的脚本

265
00:12:19,700 --> 00:12:21,679
它都是继承于common base

266
00:12:21,679 --> 00:12:23,840
也就是说他们的父类都是康德的base

267
00:12:23,840 --> 00:12:24,419
对不对

268
00:12:24,419 --> 00:12:28,769
所以在这里我们就可以把这个company base数组写上

269
00:12:28,769 --> 00:12:31,379
这样的话它里面就可以去装什么呀

270
00:12:31,379 --> 00:12:33,419
装这些类型了

271
00:12:36,899 --> 00:12:40,299
那么数组有了呃

272
00:12:40,299 --> 00:12:41,379
有第二个问题

273
00:12:41,379 --> 00:12:45,120
第二个问题就是我们当前这个类啊

274
00:12:45,120 --> 00:12:47,759
当前这个管理类到底是什么类型

275
00:12:47,759 --> 00:12:49,019
接触什么样的消息

276
00:12:49,019 --> 00:12:51,210
就比如说我们有一个message到这儿了

277
00:12:51,210 --> 00:12:53,519
哎他这里是个nb c对不对

278
00:12:53,539 --> 00:12:57,259
他是不是就要去找对应的这个nbc啊

279
00:12:57,259 --> 00:12:58,639
这样的一个管理类

280
00:12:58,639 --> 00:13:02,220
但是你怎么知道这个类就是去负责nbc的

281
00:13:02,240 --> 00:13:04,220
所以在这里我们要一个属性

282
00:13:04,220 --> 00:13:12,320
属性就代表当前管理类接收的具体消息类型

283
00:13:12,320 --> 00:13:14,600
现在我们消息类型一共有几种

284
00:13:14,679 --> 00:13:16,360
一共有四种

285
00:13:16,360 --> 00:13:18,159
ui n p c敌人audio

286
00:13:18,159 --> 00:13:18,759
对不对

287
00:13:18,759 --> 00:13:22,960
所以在这里我们最终创建完啊

288
00:13:22,960 --> 00:13:25,899
最终如果你要真写这个对应的这个管理器的话

289
00:13:25,899 --> 00:13:27,620
应该会写四个管理器

290
00:13:27,639 --> 00:13:30,940
每一个管理器会对应一种类型啊

291
00:13:30,940 --> 00:13:33,220
每个管理器会有一种具体的这个类型

292
00:13:33,220 --> 00:13:33,879
对不对

293
00:13:33,879 --> 00:13:35,080
所以在这里啊

294
00:13:35,080 --> 00:13:36,580
我们第二个属性就是这个了

295
00:13:36,580 --> 00:13:39,570
message嗯

296
00:13:39,570 --> 00:13:43,059
tap number

297
00:13:45,460 --> 00:13:46,320
嗯

298
00:13:48,200 --> 00:13:51,529
message type number k啊

299
00:13:51,529 --> 00:13:52,759
那么

300
00:13:54,100 --> 00:13:56,980
这个啊我们默认就先给它为空好

301
00:13:56,980 --> 00:13:58,480
先给它为空啊

302
00:13:58,480 --> 00:14:00,340
就是这个最终我们要设定啊

303
00:14:00,340 --> 00:14:03,639
每一个管理类它是不同的接收类型啊

304
00:14:04,179 --> 00:14:08,230
然后在这里我们把unload写出来

305
00:14:08,230 --> 00:14:10,840
unload或者start实际上都可以

306
00:14:10,840 --> 00:14:14,210
但是为了确保我们这个管理类嗯

307
00:14:14,210 --> 00:14:18,379
他能在第一时间啊把他的初始化操作全完成

308
00:14:18,379 --> 00:14:22,470
所以我们尽早实例实例化它里面的内容啊

309
00:14:22,470 --> 00:14:24,179
那是越好的

310
00:14:24,200 --> 00:14:27,259
所以我们在unload里面去写代码

311
00:14:27,259 --> 00:14:28,899
第一件事就是

312
00:14:31,019 --> 00:14:35,759
设置一下当前管理类啊

313
00:14:35,759 --> 00:14:41,820
当前管理类接收的消息类型啊

314
00:14:41,820 --> 00:14:46,019
也就是说这个message message tp啊

315
00:14:46,019 --> 00:14:47,559
它到底是什么类型

316
00:14:47,679 --> 00:14:51,580
但是我们知道这个在这里设置是不太好的啊

317
00:14:51,580 --> 00:14:53,500
你在这里设置完就成死的了

318
00:14:53,500 --> 00:14:54,639
对不对啊

319
00:14:54,639 --> 00:14:55,179
怎么办

320
00:14:55,179 --> 00:14:56,700
那我们做这样一件事

321
00:14:57,440 --> 00:14:58,580
有一个方法

322
00:14:58,580 --> 00:15:06,740
这个方法叫做设置当前管理类的消息类型

323
00:15:08,940 --> 00:15:12,759
那么每一次啊每一次呃

324
00:15:12,759 --> 00:15:16,269
我们想设置这个这个消息类型的时候

325
00:15:16,269 --> 00:15:18,490
我们都在这里调一下这个方法

326
00:15:18,490 --> 00:15:22,789
这个方法比如说我们叫做set message top

327
00:15:22,789 --> 00:15:26,960
这个方法呢就会返回一个对应的消息类型

328
00:15:28,100 --> 00:15:31,490
比如说我们默认给它返回一个

329
00:15:31,490 --> 00:15:33,720
ui类型

330
00:15:33,980 --> 00:15:37,700
然后我们每次设置都在这里调一下这个方法

331
00:15:37,700 --> 00:15:39,519
set message type

332
00:15:39,759 --> 00:15:42,789
也就是说实际上我们创建的这个管理类啊

333
00:15:42,789 --> 00:15:44,259
不管是哪个管理类

334
00:15:44,259 --> 00:15:45,850
最后你写的各种管理类

335
00:15:45,850 --> 00:15:48,059
他们会自己调一下这个方法

336
00:15:48,059 --> 00:15:49,559
调一下这个方法以后

337
00:15:49,559 --> 00:15:54,830
然后来获取到当前的这个嗯管理类的一个类型啊

338
00:15:54,830 --> 00:15:55,700
那这样的话

339
00:15:55,700 --> 00:15:57,889
比如说如果啊我们下面举例子

340
00:15:57,889 --> 00:16:01,019
比如说大家要写一个具体的管理类了

341
00:16:01,019 --> 00:16:03,759
比如说是一个enemy敌人的一个管理类

342
00:16:05,200 --> 00:16:08,889
这个管理类呢首先它是要继承于谁的

343
00:16:08,889 --> 00:16:10,840
继承于我们的manager beast

344
00:16:10,840 --> 00:16:11,559
对不对

345
00:16:13,240 --> 00:16:14,649
继承于他以后

346
00:16:14,649 --> 00:16:18,500
这时候我们知道这个管理类一定要是什么呢

347
00:16:18,500 --> 00:16:20,899
他要接收的消息类型就是敌人的

348
00:16:20,899 --> 00:16:21,500
怎么办

349
00:16:21,500 --> 00:16:24,960
你就在这里重写一下set message a

350
00:16:25,379 --> 00:16:29,789
重写一下这个set message type这个方法就可以了

351
00:16:29,789 --> 00:16:35,500
在这里直接return一个message tp

352
00:16:35,500 --> 00:16:39,029
点tap adol就可以了啊

353
00:16:39,029 --> 00:16:42,659
你写不同的管理类就实现这个方法

354
00:16:42,659 --> 00:16:45,629
给它返回不同的类型就行了啊

355
00:16:45,629 --> 00:16:47,940
给它返回不同的类型就可以了啊

356
00:16:47,940 --> 00:16:48,779
注意啊

357
00:16:49,860 --> 00:16:51,240
那这样的话啊

358
00:16:51,240 --> 00:16:53,340
这个大概用就是这么一回事啊

359
00:16:53,340 --> 00:16:54,899
大概用就是这么一回事儿

360
00:16:56,580 --> 00:16:58,679
那么这个写完以后呃

361
00:16:58,679 --> 00:16:59,519
我们继续啊

362
00:16:59,519 --> 00:17:00,519
我们继续

363
00:17:01,799 --> 00:17:04,319
我们要有这样一个数组啊

364
00:17:04,319 --> 00:17:06,000
那接下来我们要一个方法

365
00:17:06,000 --> 00:17:07,279
方法是什么呀

366
00:17:08,240 --> 00:17:11,420
如果我们下面有很多的这样的一个小类

367
00:17:11,420 --> 00:17:14,420
我们需要让小类把小类加到我们这个数组里

368
00:17:14,420 --> 00:17:15,019
对不对

369
00:17:15,019 --> 00:17:16,819
那把小类加到数组里

370
00:17:16,819 --> 00:17:20,618
这个操作我们就叫做注册消息监听

371
00:17:26,900 --> 00:17:31,640
然后啊传进来的这个就是我们的这个小类了

372
00:17:31,640 --> 00:17:35,519
小类的父类就是我们的confident base

373
00:17:37,759 --> 00:17:41,029
也就是说我们如果有一个小类啊

374
00:17:41,029 --> 00:17:42,859
把小类直接调这个方法

375
00:17:42,859 --> 00:17:44,180
然后把小类传进来

376
00:17:44,180 --> 00:17:47,539
比如说把n p c一或者mc 2传进来啊

377
00:17:47,539 --> 00:17:49,640
或者把什么背包传进来

378
00:17:49,640 --> 00:17:50,869
传进来以后

379
00:17:50,869 --> 00:17:56,359
我们就在这里直接把它加到我们这个数组里

380
00:18:00,220 --> 00:18:02,529
加到我们的这个数组里就可以了

381
00:18:02,529 --> 00:18:06,240
这样的话就是我们每次调一下这个方法

382
00:18:06,240 --> 00:18:08,220
就往这个数组里面加一个成员

383
00:18:08,220 --> 00:18:09,869
第一个方法加一个成员

384
00:18:09,869 --> 00:18:12,700
你掉四次就加了四个成员啊

385
00:18:12,700 --> 00:18:16,180
也就是说他们之间之所以ui能管理这四个

386
00:18:16,180 --> 00:18:17,380
nbc能管理这三个

387
00:18:17,380 --> 00:18:21,079
就是因为ui里面掉了四次这个方法啊

388
00:18:21,079 --> 00:18:23,819
然后它里面加了四个子类啊

389
00:18:23,819 --> 00:18:26,759
nbc这个管理类里面调了三次

390
00:18:26,759 --> 00:18:28,319
这个方法加了三个子类

391
00:18:28,319 --> 00:18:32,259
是不是也就是这个就就是关于他们一个关系的啊方法

392
00:18:32,900 --> 00:18:35,359
那么作为管理类

393
00:18:35,359 --> 00:18:36,200
大家注意啊

394
00:18:36,200 --> 00:18:39,059
管理类也是会接收到消息的啊

395
00:18:39,180 --> 00:18:41,099
管理类也是会接受消息的

396
00:18:41,099 --> 00:18:41,940
大家注意啊

397
00:18:41,940 --> 00:18:43,799
message发送给消息中心

398
00:18:43,799 --> 00:18:46,299
消息中心第一次要把消息发给管理类

399
00:18:46,319 --> 00:18:49,289
管理类再发送给其他的类型的呃

400
00:18:49,289 --> 00:18:50,460
其他的这个小类

401
00:18:50,460 --> 00:18:52,920
也就是说管理类是作为一个中转站的存在

402
00:18:52,920 --> 00:18:55,859
那么它是有接收消息功能的

403
00:18:55,880 --> 00:18:58,819
那么所以我们让他继承了competent base在

404
00:18:58,819 --> 00:19:02,210
所以comment base是不是有接收消息这个功能

405
00:19:02,210 --> 00:19:08,359
那我们这个管理类同样就可以重写这个功能诶

406
00:19:08,359 --> 00:19:10,339
重写这个功能在这里

407
00:19:10,339 --> 00:19:11,500
message

408
00:19:13,220 --> 00:19:14,359
重启一下

409
00:19:15,059 --> 00:19:18,660
ok那么这样的话呃

410
00:19:18,660 --> 00:19:22,599
我们这个这个这个首先第一件事儿

411
00:19:22,599 --> 00:19:25,359
我们最好调一下父类的这个方法啊

412
00:19:25,359 --> 00:19:26,890
调一下父类的方法

413
00:19:26,890 --> 00:19:28,619
接收消息的方法

414
00:19:31,619 --> 00:19:32,880
这里一定要注意啊

415
00:19:32,880 --> 00:19:34,799
如果你不调负类的这个方法

416
00:19:34,799 --> 00:19:37,960
因为我们是继承于cant base

417
00:19:37,960 --> 00:19:38,619
对不对

418
00:19:38,619 --> 00:19:41,259
如果它里面写了方法了啊

419
00:19:41,259 --> 00:19:42,839
如果它里面写了代码了

420
00:19:42,839 --> 00:19:44,609
如果你没有这行代码

421
00:19:44,609 --> 00:19:47,819
那么相当于你把它整个方法全覆盖了

422
00:19:47,819 --> 00:19:49,799
它里面的这个方法是不会执行的

423
00:19:49,819 --> 00:19:52,640
如果你调了这句这句代码啊

424
00:19:52,640 --> 00:19:57,819
那么意思就是说现在啊就算我我是继承于我父类的啊

425
00:19:57,819 --> 00:19:59,599
我里我里面有我自己的方法

426
00:19:59,599 --> 00:20:01,970
但是在执行我自己的方法之前

427
00:20:01,970 --> 00:20:05,089
先把父类这个方法里面的内容执行一下

428
00:20:05,089 --> 00:20:08,019
所以这样的话它里面的方法是会执行的啊

429
00:20:08,019 --> 00:20:10,180
当然现在分类这个里面是空

430
00:20:10,180 --> 00:20:13,240
所以实际上现在写不写这一行都无所谓啊

431
00:20:13,240 --> 00:20:17,420
但是我们还是按规则就是给他去写上啊

432
00:20:18,759 --> 00:20:22,839
那么在这里首先我们判断一下啊

433
00:20:22,839 --> 00:20:24,019
判断一件事儿

434
00:20:24,259 --> 00:20:27,140
我们接收消息一定要接受对应的消息

435
00:20:27,140 --> 00:20:28,039
什么意思

436
00:20:28,079 --> 00:20:30,299
当消息中心拿到这个消息以后

437
00:20:30,299 --> 00:20:32,910
比如说如果现在消息是ui类型的

438
00:20:32,910 --> 00:20:36,099
然后他把这个消息给了n b c n b c是怎样的

439
00:20:36,119 --> 00:20:38,460
是不管这个消息的啊

440
00:20:38,460 --> 00:20:40,319
你不能说我一个ui类型的消息

441
00:20:40,319 --> 00:20:42,000
我我我发的nbc了

442
00:20:42,000 --> 00:20:43,740
nbc还会传给其他的

443
00:20:43,740 --> 00:20:44,700
这是不对的

444
00:20:44,700 --> 00:20:45,660
你只有对应的

445
00:20:45,660 --> 00:20:47,730
比如说诶是nb c类型的

446
00:20:47,730 --> 00:20:48,420
传过来

447
00:20:48,420 --> 00:20:49,440
我一看类型一样

448
00:20:49,440 --> 00:20:50,460
我才能给他转发

449
00:20:50,460 --> 00:20:51,059
对不对

450
00:20:51,059 --> 00:20:53,099
所以在这里判断一下类型

451
00:20:54,980 --> 00:20:57,920
判断需要写类型

452
00:20:57,920 --> 00:21:01,480
如果message啊

453
00:21:01,480 --> 00:21:06,380
就这个消息的类型不等于咱们这个this点

454
00:21:06,380 --> 00:21:09,259
message tab就是我当前的这个接收类型啊

455
00:21:09,259 --> 00:21:10,680
就我接收的这个类型

456
00:21:10,680 --> 00:21:13,299
就这就return啊

457
00:21:13,299 --> 00:21:14,799
这就到此结束了

458
00:21:14,799 --> 00:21:16,660
否则的话我就应该干嘛了

459
00:21:16,660 --> 00:21:17,500
转发

460
00:21:19,519 --> 00:21:21,839
否则的话我就要转发了

461
00:21:22,579 --> 00:21:27,619
那么我应该把对应的消息转发给对应的这个子呃

462
00:21:27,619 --> 00:21:28,700
最下面一层了

463
00:21:28,700 --> 00:21:29,210
对不对

464
00:21:29,210 --> 00:21:31,119
那这里我们再来开始说

465
00:21:31,119 --> 00:21:34,440
比如说我们现在消息是铁匠啊

466
00:21:34,440 --> 00:21:36,000
是nbc里面的铁匠

467
00:21:36,000 --> 00:21:38,940
那他能不能直接把这个消息给铁匠呢

468
00:21:38,940 --> 00:21:40,019
他其实没这个功能

469
00:21:40,019 --> 00:21:41,480
他不知道谁是铁匠

470
00:21:41,480 --> 00:21:43,160
所以他做的事只能是

471
00:21:43,160 --> 00:21:44,539
虽然这个消息是铁匠

472
00:21:44,539 --> 00:21:47,420
但他要把这个消息发送给所有的子类

473
00:21:47,519 --> 00:21:50,519
那么这些子类呢去判断啊

474
00:21:50,519 --> 00:21:55,230
判断你接收到的这个消息和当前的这个子类是否一样

475
00:21:55,230 --> 00:21:56,240
一样的话

476
00:21:56,240 --> 00:21:58,579
他就拿到这个消息不一样

477
00:21:58,579 --> 00:22:00,400
他就不做处理了啊

478
00:22:00,400 --> 00:22:01,180
所以其实是这样

479
00:22:01,180 --> 00:22:02,019
一件事

480
00:22:02,019 --> 00:22:04,900
那所以这样的话我们就知道了

481
00:22:04,900 --> 00:22:05,980
当接到一个消息

482
00:22:05,980 --> 00:22:07,119
如果大类一样

483
00:22:07,119 --> 00:22:08,440
比如说n b c一样

484
00:22:08,440 --> 00:22:13,250
那接下来我就要把这个消息转发给所有的这个这个呃

485
00:22:13,250 --> 00:22:14,720
下面的这一层了啊

486
00:22:14,720 --> 00:22:16,740
转发给下面这一层了啊

487
00:22:16,740 --> 00:22:18,119
这只能说一层两层

488
00:22:18,119 --> 00:22:19,619
不能说是父类子类啊

489
00:22:19,619 --> 00:22:21,859
有时候这个说法不小心就说错了

490
00:22:24,059 --> 00:22:25,920
啊向下分发啊

491
00:22:25,920 --> 00:22:27,240
这就是转发了

492
00:22:29,240 --> 00:22:34,700
这个应该是向下层分发消息

493
00:22:36,019 --> 00:22:37,849
那就直接for循环

494
00:22:37,849 --> 00:22:39,170
然后就可以了

495
00:22:39,170 --> 00:22:41,990
把这个数组里面所有的内容呃

496
00:22:41,990 --> 00:22:44,400
把这个数组里面遍历一下

497
00:22:45,220 --> 00:22:48,259
就是receive list

498
00:22:49,059 --> 00:22:50,950
然后前面let

499
00:22:50,950 --> 00:22:52,240
比如说cb

500
00:22:54,259 --> 00:22:58,759
然后在这里cb我们就让他叫receive message

501
00:22:58,759 --> 00:23:00,289
就是接收消息这个方法

502
00:23:00,289 --> 00:23:03,619
然后把我们这个消息传过去

503
00:23:03,640 --> 00:23:04,539
这样就ok了

504
00:23:04,539 --> 00:23:05,200
非常简单

505
00:23:05,200 --> 00:23:06,190
对不对啊

506
00:23:06,190 --> 00:23:12,960
这样的话就是便利一下这个呃数组里面的所有的这个呃所有的类啊

507
00:23:12,960 --> 00:23:14,279
所有的最下面的这一层

508
00:23:14,279 --> 00:23:18,660
然后每一每一个这个类我们都让它掉这个方法啊

509
00:23:18,660 --> 00:23:20,279
接收消息这个方法啊

510
00:23:20,279 --> 00:23:21,319
这样就ok了

511
00:23:22,920 --> 00:23:26,519
那么目前我们做完的是这两层

512
00:23:26,660 --> 00:23:28,759
但是我们还短一个消息中心

513
00:23:28,759 --> 00:23:29,390
对不对

514
00:23:29,390 --> 00:23:31,039
还短一个消息中心

515
00:23:31,039 --> 00:23:32,779
消息中心的话就简单了

516
00:23:32,779 --> 00:23:34,380
我们到消息中心这边

517
00:23:37,000 --> 00:23:40,740
嗯首先对于消息中心改一下名字

518
00:23:42,480 --> 00:23:44,039
message center

519
00:23:44,039 --> 00:23:47,519
其次消息中心它就是单独一个的存在啊

520
00:23:47,519 --> 00:23:50,279
他不需要嗯当做谁的组件

521
00:23:50,279 --> 00:23:52,460
所以他不需要继承于我们的组件

522
00:23:55,500 --> 00:24:00,390
那么这个消息中心类首先它一样的和那个管理类一样

523
00:24:00,390 --> 00:24:01,559
它也有个列表

524
00:24:01,559 --> 00:24:04,940
这个列表里面只不过放的是所有的管理类的一个列表

525
00:24:08,359 --> 00:24:10,259
我们直接用静态的好

526
00:24:15,940 --> 00:24:17,960
confident base

527
00:24:19,660 --> 00:24:21,099
也是一个数组

528
00:24:21,099 --> 00:24:23,589
这个数组里面放的就是所有的管理类

529
00:24:23,589 --> 00:24:26,200
但是我作为一个消息中心

530
00:24:26,200 --> 00:24:29,140
我的最重要的功能就是发送消息

531
00:24:30,359 --> 00:24:35,619
static就是send message

532
00:24:36,259 --> 00:24:38,539
send message mam

533
00:24:38,539 --> 00:24:39,410
sg

534
00:24:39,410 --> 00:24:42,539
什么类型的message类型

535
00:24:45,920 --> 00:24:48,470
然后在这里发消息的话

536
00:24:48,470 --> 00:24:52,880
就是给我这个数组里面所有的管理类转发这个消息就行了

537
00:24:52,880 --> 00:24:53,539
非常简单

538
00:24:53,539 --> 00:24:54,279
是不是

539
00:24:57,779 --> 00:24:59,140
light

540
00:25:02,140 --> 00:25:04,210
然后manager

541
00:25:04,210 --> 00:25:07,700
然后便利的就是我们这个manag

542
00:25:09,619 --> 00:25:13,680
在这里manager.receive message

543
00:25:14,559 --> 00:25:18,319
然后把这个放到这里就可以了啊

544
00:25:18,319 --> 00:25:20,240
只是因为只是这样发消息的话

545
00:25:20,240 --> 00:25:22,880
每次我们都发一个消息类比较麻烦

546
00:25:22,880 --> 00:25:24,539
我们可以再写一个方法

547
00:25:26,039 --> 00:25:28,299
简化一下这个发送消息

548
00:25:28,980 --> 00:25:32,640
就是比如说send的custom

549
00:25:34,599 --> 00:25:35,859
message

550
00:25:37,819 --> 00:25:41,779
然后在这里面我们直接tp number

551
00:25:43,599 --> 00:25:49,099
command number content

552
00:25:49,660 --> 00:25:51,160
看到的应该是你

553
00:25:51,160 --> 00:25:51,819
对不对

554
00:25:51,839 --> 00:25:54,390
因为消息就是由这些内容组成的

555
00:25:54,390 --> 00:26:00,299
然后在这个里面第一件事我们先把它组组装成为一个消息对象

556
00:26:00,980 --> 00:26:03,289
new message

557
00:26:03,289 --> 00:26:08,660
然后就是卡好man content

558
00:26:09,019 --> 00:26:10,759
然后有了消息对象了

559
00:26:10,759 --> 00:26:12,619
我们直接掉上面的这个方法

560
00:26:12,619 --> 00:26:14,200
send message就可以了

561
00:26:14,980 --> 00:26:18,740
ms sg是这样就可以了

562
00:26:19,279 --> 00:26:21,559
那这样的话消息中心就完了

563
00:26:21,559 --> 00:26:23,599
但是我们现在消息中心

564
00:26:23,599 --> 00:26:27,960
我们知道所有消息中心是要管理所有的管理类的啊

565
00:26:27,960 --> 00:26:30,779
所以在管理类这边我们要做一件事

566
00:26:30,779 --> 00:26:34,900
每次当他呃实例化的时候啊

567
00:26:34,900 --> 00:26:36,400
就是这个管理类创建的时候

568
00:26:36,400 --> 00:26:37,539
它会掉unload的方法

569
00:26:37,539 --> 00:26:37,900
对不对

570
00:26:37,900 --> 00:26:39,619
在这里面我们也做一件事

571
00:26:40,359 --> 00:26:48,079
把管理类添加到消息中心列表中

572
00:26:49,240 --> 00:26:55,039
就是message center点

573
00:26:55,039 --> 00:26:58,700
然后这个managers.push

574
00:26:58,700 --> 00:27:01,039
然后把自己加进去就可以了

575
00:27:01,039 --> 00:27:04,519
这样的话每一次只要我们写一个管理类的此类

576
00:27:04,519 --> 00:27:07,259
这个管理类的此类已经会怎样了

577
00:27:07,359 --> 00:27:11,200
已经会自动加到我们这个消息中心里了啊

578
00:27:11,200 --> 00:27:14,200
加到消息中心的这个管理列表里啊

579
00:27:14,200 --> 00:27:17,349
也就是说只要你是继承于manager的啊

580
00:27:17,349 --> 00:27:20,200
然后你就会自动被消息中心管理啊

581
00:27:20,200 --> 00:27:22,299
自动被消息中心管理嗯

582
00:27:22,299 --> 00:27:23,859
但是下面这个就不会自动了

583
00:27:23,859 --> 00:27:26,710
下面这个比如说你创建一个小的脚本

584
00:27:26,710 --> 00:27:28,299
继承于common base了

585
00:27:28,299 --> 00:27:30,160
这个就不会自动被这一层关系啊

586
00:27:30,160 --> 00:27:36,099
你就需要怎样第这个注册这个方法啊

587
00:27:36,099 --> 00:27:39,619
这样的话才能产生这个关系关联啊

588
00:27:40,000 --> 00:27:43,720
ok那么这四个脚本就算是完成了啊

589
00:27:43,720 --> 00:27:45,279
这四个就算是完成了

590
00:27:45,299 --> 00:27:47,579
所以如果是大型游戏的话啊

591
00:27:47,579 --> 00:27:48,720
一定要这种东西

592
00:27:48,720 --> 00:27:51,480
要这种消息框架才可以写得好一些

593
00:27:51,480 --> 00:27:53,039
咱们这个已经是最基础的了

594
00:27:53,039 --> 00:27:55,559
最简化的嗯

595
00:27:56,200 --> 00:27:58,119
有兴趣的话给大家研究透

596
00:27:58,119 --> 00:28:01,240
如果你只是想写一些小游戏的话啊

597
00:28:01,240 --> 00:28:04,859
或者想写一些微信很小的这个小游戏的话啊

598
00:28:04,859 --> 00:28:07,559
你也可以呃无视他啊

599
00:28:07,559 --> 00:28:10,359
但是尽量去看一看这些东西啊

600
00:28:10,859 --> 00:28:14,460
ok那咱们这节课就这么多


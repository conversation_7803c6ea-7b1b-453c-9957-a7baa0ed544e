1
00:00:09,419 --> 00:00:12,320
ok这节课我们来说一下粒子系统

2
00:00:12,339 --> 00:00:14,560
那么首先我们之前学过动画啊

3
00:00:14,560 --> 00:00:17,920
那我们之前做这个帧动画的时候

4
00:00:17,920 --> 00:00:20,679
我们用的是这个animation组件啊

5
00:00:20,679 --> 00:00:22,839
那么既然说到animation组件

6
00:00:22,839 --> 00:00:25,359
我再补充一下animation组件这个东西啊

7
00:00:25,359 --> 00:00:28,030
有时候用起来并不是很好用啊

8
00:00:28,030 --> 00:00:30,359
比如说他做一个人物的动画

9
00:00:30,359 --> 00:00:32,880
然后你如果让他做着这个人物的动画

10
00:00:32,880 --> 00:00:34,140
你再去移动这个人物

11
00:00:34,140 --> 00:00:36,119
你会发现他的动画可能就不做了

12
00:00:36,119 --> 00:00:37,039
就静止了

13
00:00:37,039 --> 00:00:39,950
所以他的这个动画系统有时候并不是很好用

14
00:00:39,950 --> 00:00:41,539
所以有时候你去有些公司

15
00:00:41,539 --> 00:00:43,850
可能他并不会直接用这个animation

16
00:00:43,850 --> 00:00:45,679
他甚至可能会怎样

17
00:00:45,719 --> 00:00:47,280
他自己啊

18
00:00:47,280 --> 00:00:48,869
他不用这个animation

19
00:00:48,869 --> 00:00:51,920
他自己去写写一个脚本

20
00:00:51,920 --> 00:00:57,960
用这个脚本来帮我们去切换这个帧图片啊

21
00:00:57,960 --> 00:00:58,679
切换帧图片

22
00:00:58,679 --> 00:01:01,728
比如说我们知道一个动画是有很多图片组成的

23
00:01:01,728 --> 00:01:02,298
对不对

24
00:01:02,298 --> 00:01:05,209
是由一针两针三针很多图片组成的

25
00:01:05,209 --> 00:01:07,950
那它呢就会去专门写一个脚本

26
00:01:07,950 --> 00:01:09,329
还专门写个脚本

27
00:01:09,329 --> 00:01:11,069
然后在update里面去干嘛

28
00:01:11,069 --> 00:01:16,230
去帮我们去切换这个当前精灵显示的帧

29
00:01:16,230 --> 00:01:19,560
那这样的话其实最后也会生成动画效果

30
00:01:19,560 --> 00:01:20,519
对不对啊

31
00:01:20,519 --> 00:01:25,439
但是这样的话就算是我们自己给他写的这样的一个呃动画播放器啊

32
00:01:25,439 --> 00:01:26,670
我们也好控制

33
00:01:26,670 --> 00:01:30,349
而系统的这个animation用它去做的这个真动画

34
00:01:30,349 --> 00:01:32,209
有时候就会有一些问题啊

35
00:01:32,209 --> 00:01:33,409
有时候就会有一些问题

36
00:01:33,409 --> 00:01:35,390
所以这个跟大家说一下

37
00:01:35,390 --> 00:01:36,409
就是让大家注意一下

38
00:01:36,409 --> 00:01:42,120
如果以后发现用这个系统的这个动画有问题了

39
00:01:42,120 --> 00:01:43,260
有bug了啊

40
00:01:43,260 --> 00:01:45,719
那么他这个确实就是这样的问

41
00:01:45,719 --> 00:01:46,920
有这样的问题存在

42
00:01:46,920 --> 00:01:48,280
你就可以怎样

43
00:01:48,459 --> 00:01:50,448
你就可以自己写个脚本

44
00:01:50,448 --> 00:01:52,489
在update里面自己

45
00:01:52,489 --> 00:01:56,388
比如说每隔上半秒钟更换更换一个图片

46
00:01:56,388 --> 00:01:58,280
每隔半秒钟更换一个图片

47
00:01:58,280 --> 00:02:01,640
这样的话实际上最终这个精灵是不是也在播放图片了

48
00:02:01,640 --> 00:02:04,260
当然这个就是通过逻辑来控制的

49
00:02:04,260 --> 00:02:08,280
那么这节课我们来说一下这个粒子效果啊

50
00:02:08,280 --> 00:02:11,759
当然乍一眼看上去它跟这个动画也是一样的

51
00:02:13,360 --> 00:02:14,219
把它拖过来

52
00:02:14,219 --> 00:02:15,120
我们可以看一下

53
00:02:15,120 --> 00:02:16,080
生成一个节点

54
00:02:16,080 --> 00:02:18,800
节点上面有一个组件是这个粒子系统

55
00:02:18,819 --> 00:02:21,460
然后在上面有几个属性啊

56
00:02:21,460 --> 00:02:22,419
有几个属性

57
00:02:22,419 --> 00:02:23,949
首先我们来说一下

58
00:02:23,949 --> 00:02:25,210
说几个啊

59
00:02:25,210 --> 00:02:32,959
第一个就是呃这个这这这个是否啊要这个算是一个预热播放啊

60
00:02:32,959 --> 00:02:33,939
什么意思啊

61
00:02:37,419 --> 00:02:39,300
如果我们不把它勾选

62
00:02:39,300 --> 00:02:40,479
大家可以看一下

63
00:02:41,379 --> 00:02:44,460
在这个就是默认的这个情况下啊

64
00:02:44,460 --> 00:02:45,900
就是默认的情况下啊

65
00:02:45,900 --> 00:02:47,400
就是我们不运行的情况下

66
00:02:47,400 --> 00:02:48,659
它这边就不管了

67
00:02:48,659 --> 00:02:49,469
不播放了

68
00:02:49,469 --> 00:02:51,210
我们把它勾起来啊

69
00:02:51,210 --> 00:02:52,349
它就正常播放啊

70
00:02:52,349 --> 00:02:53,610
其实就是这样一回事啊

71
00:02:53,610 --> 00:02:54,270
这个很简单

72
00:02:54,270 --> 00:02:54,949
说一下

73
00:02:54,949 --> 00:02:56,210
第二个就是韩式

74
00:02:56,210 --> 00:03:00,188
只要是当我们这个例子啊拖进来以后啊

75
00:03:00,188 --> 00:03:02,049
然后当我们这个点下运行

76
00:03:02,049 --> 00:03:03,879
只要这个游戏开始运行

77
00:03:03,879 --> 00:03:05,289
能看到例子以后

78
00:03:05,289 --> 00:03:06,939
他直接自己就播放了

79
00:03:06,939 --> 00:03:08,500
如果你把这个勾去掉

80
00:03:08,500 --> 00:03:11,939
你就要通过代码获取这个组件

81
00:03:11,939 --> 00:03:13,949
然后调用它的play方法啊

82
00:03:13,949 --> 00:03:15,270
就和那个动画组件一样

83
00:03:15,270 --> 00:03:16,530
它才会开始播放啊

84
00:03:16,530 --> 00:03:17,610
一般我们就把它勾上

85
00:03:17,610 --> 00:03:20,158
让它自动开始播放嗯

86
00:03:21,598 --> 00:03:24,658
在这里他用的粒子文件啊

87
00:03:24,658 --> 00:03:26,998
是默认的是这样的一个例子文件啊

88
00:03:26,998 --> 00:03:29,300
是这样一个例子文件我们不用管它

89
00:03:29,300 --> 00:03:32,360
但但是在这里有一个自定义

90
00:03:32,639 --> 00:03:34,770
把自定义勾选上以后

91
00:03:34,770 --> 00:03:36,240
它会有很多属性

92
00:03:36,240 --> 00:03:39,780
这些属性全是去更改粒子的属性的啊

93
00:03:39,780 --> 00:03:40,860
我们先来说一下

94
00:03:40,860 --> 00:03:43,460
大家可以看一下这边的属性

95
00:03:43,460 --> 00:03:45,919
有一栏的一栏就是属性

96
00:03:45,919 --> 00:03:48,899
就是很简单很好理解啊

97
00:03:48,899 --> 00:03:50,399
那么还有两栏呢

98
00:03:50,399 --> 00:03:51,479
你看很多都是两栏

99
00:03:51,479 --> 00:03:52,498
两栏的代表什么

100
00:03:52,498 --> 00:03:54,360
两男的代表一个范围啊

101
00:03:54,360 --> 00:03:56,099
代表一个范围是从多少到多少

102
00:03:56,099 --> 00:03:57,659
它代表的是一个范围啊

103
00:03:57,659 --> 00:04:02,020
也就是说结果是在这个范围之间去产生一个随机的呃

104
00:04:02,020 --> 00:04:04,300
那么我们一个一个来看一下啊

105
00:04:04,300 --> 00:04:05,379
把重要的来看一下

106
00:04:05,379 --> 00:04:07,400
首先这里有个时间

107
00:04:07,699 --> 00:04:08,780
我们先看这个吧

108
00:04:08,780 --> 00:04:09,560
这个是第一个

109
00:04:10,819 --> 00:04:12,900
这个是个发射的一个例子

110
00:04:13,199 --> 00:04:14,759
默认是这个圆的例子

111
00:04:14,759 --> 00:04:15,389
对不对

112
00:04:15,389 --> 00:04:17,759
如果你这里有个图片啊

113
00:04:17,759 --> 00:04:19,259
你可以把图片拖到这里

114
00:04:19,259 --> 00:04:24,639
那么它们发射发射出来的例子就不是这个圆形粒子了

115
00:04:24,639 --> 00:04:26,680
而是你拖进来的图片例子了

116
00:04:27,459 --> 00:04:34,680
那么这个就是时间就是指粒子啊存活多长时间就整体一波啊

117
00:04:34,680 --> 00:04:35,899
就是这个粒子

118
00:04:38,019 --> 00:04:39,180
存活多长时间

119
00:04:39,180 --> 00:04:41,879
默认-1的话就是它是一直存活的啊

120
00:04:41,879 --> 00:04:43,959
这个粒子发射器一直存活的

121
00:04:44,819 --> 00:04:46,339
如果我们给它个十秒

122
00:04:46,339 --> 00:04:49,639
就证明这个粒子发射器是会发射十秒的啊

123
00:04:49,639 --> 00:04:51,139
这个不应该叫粒子发射时间

124
00:04:51,139 --> 00:04:52,709
应该是整呃

125
00:04:52,709 --> 00:04:54,389
不应该叫粒子存活时间

126
00:04:54,389 --> 00:04:56,149
应该叫粒子发射时间

127
00:04:56,149 --> 00:04:58,279
它从中间这个点发射粒子

128
00:05:00,339 --> 00:05:01,660
比如说我们这儿给个一

129
00:05:01,660 --> 00:05:02,939
大家可以看一下

130
00:05:03,379 --> 00:05:07,009
可以可以很清楚看到发疫苗以后就完了

131
00:05:07,009 --> 00:05:08,540
完了以后他又继续开始了

132
00:05:08,540 --> 00:05:10,519
因为我们让它一直在预览状态

133
00:05:10,519 --> 00:05:11,129
对不对

134
00:05:19,600 --> 00:05:21,660
所以这里就是一个粒子发射时间

135
00:05:21,660 --> 00:05:22,860
我们如果给它-1的话

136
00:05:22,860 --> 00:05:24,420
就是这个粒子持续发射啊

137
00:05:24,420 --> 00:05:25,779
持续往外发射粒子

138
00:05:25,779 --> 00:05:28,540
而且当大家可以看到这个粒子效果很有意思啊

139
00:05:28,540 --> 00:05:30,339
它不是说往外一直发散

140
00:05:30,339 --> 00:05:33,879
他是a往外打出去以后好像又绕回来了啊

141
00:05:33,879 --> 00:05:35,800
这是因为它使用了重力的模式

142
00:05:35,800 --> 00:05:37,529
就是有引力啊

143
00:05:37,529 --> 00:05:39,029
这个在下面了

144
00:05:39,029 --> 00:05:40,019
咱们一会再说

145
00:05:40,019 --> 00:05:41,589
然后往下继续

146
00:05:41,589 --> 00:05:45,220
这里有个999是粒子每秒发射的数量

147
00:05:45,220 --> 00:05:46,750
是每秒发射的数量

148
00:05:46,750 --> 00:05:48,100
如果你给个一

149
00:05:48,598 --> 00:05:49,879
大家可以看

150
00:05:51,800 --> 00:05:54,100
每隔一秒他就发射一个啊

151
00:05:54,100 --> 00:05:55,449
每隔一秒发射一个

152
00:05:55,449 --> 00:05:56,699
主要给个100

153
00:05:57,480 --> 00:06:01,160
给个十啊

154
00:06:01,160 --> 00:06:04,420
就是按每秒发射十个粒子这样的一个速度

155
00:06:04,899 --> 00:06:08,889
那所以这个是个每秒发射粒子的一个呃数目

156
00:06:08,889 --> 00:06:11,980
然后有同学看会看出来100这么多

157
00:06:11,980 --> 00:06:16,500
比如说300这么多啊

158
00:06:16,879 --> 00:06:20,240
3000好像感觉也没有多太多

159
00:06:20,240 --> 00:06:24,199
是不是你看300好像这三天感觉没有多太多

160
00:06:24,199 --> 00:06:26,149
那这个原因是什么啊

161
00:06:27,589 --> 00:06:29,069
我们先给他一个300

162
00:06:29,069 --> 00:06:29,910
再往下

163
00:06:29,910 --> 00:06:32,310
这个才是一个粒子的一个运行时间

164
00:06:32,310 --> 00:06:33,959
这才是它的生命周期

165
00:06:33,959 --> 00:06:38,269
意思就是每一颗粒子从它诞生出来以后

166
00:06:38,269 --> 00:06:40,139
它的一个存活时间

167
00:06:40,279 --> 00:06:42,439
现在是0.2秒到0.5秒

168
00:06:42,439 --> 00:06:44,600
如果你给个0.2秒到五秒

169
00:06:44,600 --> 00:06:46,040
那有的粒子就会一直存活

170
00:06:46,040 --> 00:06:47,660
你看他就一直在这转

171
00:06:47,660 --> 00:06:48,740
因为他死不了

172
00:06:48,740 --> 00:06:49,639
对不对

173
00:06:49,639 --> 00:06:51,500
他就一直在这转啊

174
00:06:51,500 --> 00:06:54,420
有的粒子最长的可以活到五秒啊

175
00:06:59,120 --> 00:07:02,319
再往下这个就是粒子的最大数量啊

176
00:07:02,319 --> 00:07:04,480
就是说你不管粒子怎么整最大数量

177
00:07:04,480 --> 00:07:06,370
当前屏幕上就能出现多少个

178
00:07:06,370 --> 00:07:08,038
这是防止什么呀

179
00:07:08,038 --> 00:07:11,158
防止你这个如果比如说你写的代码有问题了

180
00:07:11,158 --> 00:07:17,540
你让你让一个屏幕同时出现了几千上万几10万几百万的例子

181
00:07:17,540 --> 00:07:20,060
那直接是不是你要是用手机的话

182
00:07:20,060 --> 00:07:21,259
手机就直接卡死了

183
00:07:21,259 --> 00:07:23,389
或者用电脑电脑估计也差不多了

184
00:07:23,389 --> 00:07:24,079
对不对

185
00:07:24,079 --> 00:07:25,250
所以为了性能着想

186
00:07:25,250 --> 00:07:27,519
这边我们一般会给一个上限的啊

187
00:07:27,519 --> 00:07:28,689
啊会给一个上限

188
00:07:28,689 --> 00:07:30,579
就最多200发对吧

189
00:07:30,579 --> 00:07:31,300
这是默认的

190
00:07:31,300 --> 00:07:32,459
你可以去修改

191
00:07:32,459 --> 00:07:34,379
这里有个颜色

192
00:07:34,379 --> 00:07:38,639
有一个开始的一个颜色和一个结束的颜色

193
00:07:38,639 --> 00:07:41,399
那么开始和结束野兽有个范围的

194
00:07:41,399 --> 00:07:42,329
大家可以看一下

195
00:07:42,329 --> 00:07:45,459
这比如说是开始的颜色是有两条

196
00:07:45,459 --> 00:07:46,000
对不对

197
00:07:46,000 --> 00:07:47,139
结束的也是两条

198
00:07:47,139 --> 00:07:50,740
那这就是开始的颜色范围和结束的颜色范围

199
00:07:50,740 --> 00:07:53,699
比如说开始的你可以选一个颜色

200
00:07:54,259 --> 00:07:56,240
哎你可以选一个颜色

201
00:07:56,240 --> 00:07:59,540
然后第二个颜色可以和它不一样点

202
00:07:59,540 --> 00:08:03,740
那结果就是在这两个之间去产生一个随机的一个颜色了

203
00:08:03,740 --> 00:08:05,730
结束的颜色也一样啊

204
00:08:05,730 --> 00:08:07,620
你也可以来上两个颜色

205
00:08:10,040 --> 00:08:14,319
最终就是在这些颜色里面去产生变化了

206
00:08:14,319 --> 00:08:17,918
那么再往下面啊

207
00:08:17,918 --> 00:08:20,108
这是一个粒子的一个角度啊

208
00:08:20,108 --> 00:08:21,218
那这个都是圆形

209
00:08:21,218 --> 00:08:21,759
看不出来

210
00:08:21,759 --> 00:08:22,778
我们就不管它了

211
00:08:22,778 --> 00:08:24,980
这里有个开始的一个大小

212
00:08:25,038 --> 00:08:27,738
大家可以看从3~50啊

213
00:08:27,738 --> 00:08:29,819
如果你比如说从3~5

214
00:08:29,978 --> 00:08:31,598
那大家可以看一下这个例子

215
00:08:31,598 --> 00:08:33,999
大小基本上都差不太多了啊

216
00:08:33,999 --> 00:08:36,360
因为如果有50的话

217
00:08:36,360 --> 00:08:39,000
你看很大旅游的例子就大的不行

218
00:08:39,000 --> 00:08:40,500
是不是就非常大

219
00:08:40,500 --> 00:08:42,538
因为它放大了这个50倍

220
00:08:44,519 --> 00:08:45,620
给个五的话

221
00:08:45,620 --> 00:08:46,940
你明显就小很多了

222
00:08:46,940 --> 00:08:47,299
对不对

223
00:08:47,299 --> 00:08:48,599
面就小很多了

224
00:08:50,919 --> 00:08:52,379
还有个结束的大小

225
00:08:52,379 --> 00:08:55,440
结束的大小是30~0啊

226
00:08:55,440 --> 00:08:57,919
也就是说有的例子是到最后就看不见了

227
00:08:58,720 --> 00:08:59,759
那我们可以怎样

228
00:08:59,759 --> 00:09:01,679
我们可以开始的时候都是零

229
00:09:01,679 --> 00:09:03,299
也就开始都看不见

230
00:09:03,299 --> 00:09:06,399
结束的时候都是五啊

231
00:09:06,399 --> 00:09:08,120
或者都是十

232
00:09:08,679 --> 00:09:09,600
大家可以看一下

233
00:09:09,600 --> 00:09:10,590
就这样一个效果

234
00:09:10,590 --> 00:09:11,909
开始都是一点点

235
00:09:11,909 --> 00:09:13,259
然后出来停一会儿

236
00:09:13,259 --> 00:09:15,690
然后就变成这个就会变大了啊

237
00:09:15,690 --> 00:09:16,979
石的话有点小

238
00:09:17,320 --> 00:09:18,850
50有点大

239
00:09:18,850 --> 00:09:19,979
30

240
00:09:22,059 --> 00:09:24,669
啊这就是从零到有

241
00:09:24,669 --> 00:09:25,179
对不对

242
00:09:25,179 --> 00:09:27,580
我们可以给稍微给一点吧

243
00:09:27,580 --> 00:09:29,070
从五开始啊

244
00:09:29,070 --> 00:09:30,600
这句例子出现的时候

245
00:09:30,600 --> 00:09:32,009
诞生的时候是五

246
00:09:32,009 --> 00:09:33,450
死亡的时候是30

247
00:09:33,450 --> 00:09:35,458
当然你可以给他一个范围啊

248
00:09:36,860 --> 00:09:39,460
再往下面是一个旋转啊

249
00:09:39,460 --> 00:09:41,259
这个就是它的一个自旋转

250
00:09:41,879 --> 00:09:44,720
开始的自旋转和结束时候的一个自旋转

251
00:09:44,720 --> 00:09:46,220
如果这有图形的话

252
00:09:46,220 --> 00:09:49,320
你就可以看到它本身自己一直去旋转啊

253
00:09:49,320 --> 00:09:50,970
自己其实一直去旋转

254
00:09:50,970 --> 00:09:54,359
旋转就是这里面去控制的啊

255
00:09:54,519 --> 00:09:58,169
那么一般我们做这种粒子效果的时候

256
00:09:58,169 --> 00:10:00,090
在这里大家可以看一下呃

257
00:10:00,090 --> 00:10:02,429
用圆形的话基本上旋转看不出来啊

258
00:10:02,429 --> 00:10:05,269
你还是可以拖个图片拖上来啊

259
00:10:05,269 --> 00:10:07,279
你就可以看到图片在这里旋转了

260
00:10:07,279 --> 00:10:10,559
你去调这里就可以看到图片的一个转速

261
00:10:11,000 --> 00:10:11,990
再往下面

262
00:10:11,990 --> 00:10:13,639
这是个发射器的一个位置

263
00:10:13,639 --> 00:10:15,899
这两个都是发射器的一个状态了

264
00:10:18,899 --> 00:10:20,700
那我们可以看一下啊

265
00:10:20,700 --> 00:10:21,659
我们可以看一下

266
00:10:21,659 --> 00:10:23,519
发射器默认是在零零位置

267
00:10:23,519 --> 00:10:25,200
也就是说在中心点发射的

268
00:10:29,179 --> 00:10:34,139
那么实际上大家可以看到现在就是大概在这一点去产生粒子了

269
00:10:35,620 --> 00:10:39,919
仔细看是可以看出来大概是在这一点给他一个50

270
00:10:40,960 --> 00:10:45,220
是不是能看出来在这一点开始发射粒子了啊

271
00:10:45,220 --> 00:10:47,830
x在50的位置发射粒子

272
00:10:47,830 --> 00:10:50,409
所以说这个就是你发射粒子的一个位置

273
00:10:50,409 --> 00:10:53,710
但是为什么粒子它不是说跟刚才一样散开就完了

274
00:10:53,710 --> 00:10:54,549
它都往这边走

275
00:10:54,549 --> 00:10:56,039
因为这边有引力啊

276
00:10:56,039 --> 00:10:57,659
这个引力咱们还是等一下说啊

277
00:10:59,100 --> 00:11:01,599
总之这就是一个发射器的一个位置

278
00:11:01,720 --> 00:11:04,089
下面是一个发射器的一个范围

279
00:11:04,089 --> 00:11:05,408
默认是七七

280
00:11:05,408 --> 00:11:06,788
也就是说x是七

281
00:11:06,788 --> 00:11:07,328
y是七

282
00:11:07,328 --> 00:11:08,599
这是不是一个句型

283
00:11:08,599 --> 00:11:12,438
也就是说默认是在这个句型里面随机产生粒子的

284
00:11:12,438 --> 00:11:15,639
如果你这比如说给一个70~70

285
00:11:17,139 --> 00:11:22,000
那么实际上大家可以看这就是一个70x70的一个句型

286
00:11:22,000 --> 00:11:24,940
在这个句型里面随机产生的粒子啊

287
00:11:24,940 --> 00:11:29,799
也就是说这是这个例子啊产生的一个范围啊

288
00:11:30,320 --> 00:11:32,509
我们给他七七

289
00:11:32,509 --> 00:11:34,039
你要给零零的话

290
00:11:34,039 --> 00:11:38,438
它最开始就是从一个点上去产生出来的啊

291
00:11:39,740 --> 00:11:43,080
再往下面这里大家注意一下啊

292
00:11:43,080 --> 00:11:45,360
这里有一个类型

293
00:11:45,360 --> 00:11:46,139
有三种类型

294
00:11:46,139 --> 00:11:47,100
第一个是free free

295
00:11:47,100 --> 00:11:47,909
什么意思

296
00:11:47,909 --> 00:11:50,799
我们拖住这个例子左右拖拽

297
00:11:51,259 --> 00:11:55,440
我们发现这个例子是不是有有种脱轨的感觉啊

298
00:11:55,440 --> 00:11:57,360
就是因为它产生在哪以后啊

299
00:11:57,360 --> 00:11:59,460
它就是在那个位置留下来了

300
00:11:59,460 --> 00:12:01,440
不会跟随我们坐标移动

301
00:12:01,440 --> 00:12:03,099
但是另外两种

302
00:12:04,679 --> 00:12:08,620
大家可以看一下另外两种

303
00:12:11,440 --> 00:12:13,029
是不是都跟随着走了

304
00:12:13,029 --> 00:12:14,919
所以实际上就是一个是相对的

305
00:12:14,919 --> 00:12:16,600
一个是绝对的啊

306
00:12:16,600 --> 00:12:18,610
你如果希望这个有个拖尾效果

307
00:12:18,610 --> 00:12:19,389
就选第一个

308
00:12:19,389 --> 00:12:23,340
一般如果你想你希望它跟着你这个坐标呃

309
00:12:23,340 --> 00:12:25,259
移动一般就选第三个就行了啊

310
00:12:25,259 --> 00:12:26,460
后面两个是差不多的

311
00:12:26,460 --> 00:12:27,938
就选第三个就行了

312
00:12:29,480 --> 00:12:30,799
那我再设置回来

313
00:12:30,799 --> 00:12:32,198
设置成这种效果

314
00:12:32,799 --> 00:12:35,500
再往下面这个就是发射器的类型了

315
00:12:35,500 --> 00:12:36,769
一般就两个

316
00:12:36,769 --> 00:12:37,909
第一个是个物理的

317
00:12:37,909 --> 00:12:39,889
一般没必要用到这个啊

318
00:12:39,889 --> 00:12:41,210
这就是因为它的存在

319
00:12:41,210 --> 00:12:44,379
所以我们粒子都是往中间就发射出去

320
00:12:44,379 --> 00:12:45,460
就往中间回来了

321
00:12:45,460 --> 00:12:46,000
对不对

322
00:12:46,000 --> 00:12:47,950
你还可以在下面去设置一下

323
00:12:47,950 --> 00:12:49,750
比如说引力的大小呀

324
00:12:49,750 --> 00:12:51,818
重力的大小速度等等

325
00:12:52,000 --> 00:12:56,779
但一般我们也会用这个这个选

326
00:12:56,779 --> 00:12:58,789
我们把它选择选择上

327
00:12:58,789 --> 00:13:01,070
那这个就是没有重力的啊

328
00:13:01,070 --> 00:13:01,850
没有中立的

329
00:13:04,850 --> 00:13:05,740
对不对

330
00:13:05,779 --> 00:13:08,870
那么大家可以看第一个就是什么呀

331
00:13:08,870 --> 00:13:12,759
初始的半径及变化范围是不是呃

332
00:13:12,759 --> 00:13:14,769
这就是最开始的一个半径

333
00:13:14,769 --> 00:13:16,120
这是结束的一个半径

334
00:13:16,120 --> 00:13:18,100
就是发射时候的半径

335
00:13:18,100 --> 00:13:19,899
这是发射结束时候的半径

336
00:13:19,899 --> 00:13:22,330
比如说如果我希望从这一点发射

337
00:13:22,330 --> 00:13:25,100
发射到结束的时候

338
00:13:25,100 --> 00:13:28,519
是在半径为50的圆的话

339
00:13:28,519 --> 00:13:29,679
你就可以这样

340
00:13:33,559 --> 00:13:36,799
结束的这个半径我们都给他50啊

341
00:13:36,799 --> 00:13:37,759
我们不给他范围

342
00:13:37,759 --> 00:13:38,419
都给它固定的

343
00:13:38,419 --> 00:13:39,279
都是50

344
00:13:39,279 --> 00:13:41,320
这样的话就是开始呃

345
00:13:41,320 --> 00:13:42,580
开始这个发射粒子的话

346
00:13:42,580 --> 00:13:44,149
我们都是从零零发射

347
00:13:44,149 --> 00:13:50,019
结束的时候都是在50x50半径的圆上结束

348
00:13:50,019 --> 00:13:52,419
这样的话是不是感觉有点像穿越时空

349
00:13:52,419 --> 00:13:54,549
这个感觉是吧

350
00:13:54,549 --> 00:13:55,840
就是穿越时空了

351
00:13:55,840 --> 00:13:56,620
当然也可以

352
00:13:56,620 --> 00:13:57,458
相反

353
00:13:57,899 --> 00:13:58,980
你看如果零零

354
00:13:58,980 --> 00:14:00,759
你前面这个给50

355
00:14:00,799 --> 00:14:05,659
就是从外面这个半径为50的这个大圆开始发射

356
00:14:05,659 --> 00:14:08,250
你看从半径为50的大圆开始发射

357
00:14:08,250 --> 00:14:10,299
发射的结果是哪里

358
00:14:10,299 --> 00:14:12,580
结果就是半径为零的这个圆

359
00:14:12,580 --> 00:14:14,500
其实就是中间这一点了啊

360
00:14:14,500 --> 00:14:17,350
所以就是很多例子从边缘发射

361
00:14:17,350 --> 00:14:18,580
发射到中间一点

362
00:14:18,580 --> 00:14:20,659
那这就是这个属性啊

363
00:14:23,480 --> 00:14:26,659
那么最后一个是旋转

364
00:14:26,659 --> 00:14:27,740
这个旋转也不说了

365
00:14:27,740 --> 00:14:29,570
大家自己去找个图

366
00:14:29,570 --> 00:14:32,519
然后拖过来自己可以测试一下啊

367
00:14:32,740 --> 00:14:37,120
那么这个东西其实说白了这套东西是相对而言

368
00:14:37,120 --> 00:14:42,120
还是比起普通的这个图片肯定要浪费性能

369
00:14:42,120 --> 00:14:42,840
浪费的多

370
00:14:42,840 --> 00:14:43,470
是不是

371
00:14:43,470 --> 00:14:47,419
所以说这套系统呃能不多用啊

372
00:14:47,419 --> 00:14:48,019
就不多用

373
00:14:48,019 --> 00:14:49,279
就能用别的方式解决

374
00:14:49,279 --> 00:14:50,240
就用别的方式解决

375
00:14:50,240 --> 00:14:51,019
解决不了的

376
00:14:51,019 --> 00:14:52,879
你再不来用这个粒子系统啊

377
00:14:52,879 --> 00:14:53,859
就可以了

378
00:14:55,139 --> 00:14:57,659
比如说有些同学希望用它来做一个火焰

379
00:14:57,659 --> 00:14:59,730
用它做出来的火焰啊

380
00:14:59,730 --> 00:15:01,590
如果你去调这边调的很真的话

381
00:15:01,590 --> 00:15:04,629
这个火焰效果就很就非常好啊

382
00:15:04,629 --> 00:15:07,250
但是相对而言它也是很浪费性能的

383
00:15:07,250 --> 00:15:08,870
而我们做2d游戏的话

384
00:15:08,870 --> 00:15:10,549
你完全可以整几张图

385
00:15:10,549 --> 00:15:11,509
就是火焰的图

386
00:15:11,509 --> 00:15:13,190
然后给他们去播放帧动画

387
00:15:13,190 --> 00:15:15,120
其实效果也是一个火焰

388
00:15:15,120 --> 00:15:16,029
对不对

389
00:15:16,029 --> 00:15:17,950
所以就看怎样做了

390
00:15:17,950 --> 00:15:20,110
你希望更真一点的有些特效啊

391
00:15:20,110 --> 00:15:21,578
爆炸效果火焰

392
00:15:21,578 --> 00:15:23,139
你这种华丽的效果

393
00:15:23,139 --> 00:15:24,879
你可以用它去做啊

394
00:15:24,879 --> 00:15:27,440
但是你要是考虑到性能的话

395
00:15:27,440 --> 00:15:29,779
就不要用它去做了啊

396
00:15:30,759 --> 00:15:33,000
ok那这就是这个东西啊

397
00:15:34,179 --> 00:15:36,519
我们这节课也就这一个内容

398
00:15:36,519 --> 00:15:37,539
也就这一个内容

399
00:15:37,539 --> 00:15:39,879
然后我们这边也就说完了呃

400
00:15:39,879 --> 00:15:42,309
那么大家下去把这个东西再看一看

401
00:15:42,309 --> 00:15:44,578
自己去拖一拖就搞定了啊

402
00:15:45,580 --> 00:15:48,360
ok那我们这节课就这么多


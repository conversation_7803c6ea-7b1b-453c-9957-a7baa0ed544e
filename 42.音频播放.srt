1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,880 --> 00:00:13,779
ok这节课咱们来说一下cocks creator里面的音频播放方式

3
00:00:13,839 --> 00:00:17,359
那么音频播放方式在这里有两种方式

4
00:00:17,379 --> 00:00:20,050
一种是基于组件的方式

5
00:00:20,050 --> 00:00:24,460
那还有一种就是coco原本的一个播放方式

6
00:00:24,460 --> 00:00:28,660
那么这种播放方式是不需要这个组件支持的啊

7
00:00:28,660 --> 00:00:29,679
就直接写代码

8
00:00:29,679 --> 00:00:31,730
它就可以播放音频了啊

9
00:00:31,730 --> 00:00:34,039
那么这两种方式咱们都来看一下

10
00:00:34,039 --> 00:00:35,689
咱们一个一个来说

11
00:00:35,689 --> 00:00:40,659
首先嗯这里啊我打开之前的一个项目

12
00:00:40,659 --> 00:00:43,030
大家大家新建一个项目也可以啊

13
00:00:43,030 --> 00:00:47,700
在这里呃resource下面我放了一个音频文件啊

14
00:00:47,700 --> 00:00:49,240
我放了一个音频文件

15
00:00:49,240 --> 00:00:51,549
然后我这里创建一个空的节点

16
00:00:51,549 --> 00:00:53,380
用它来播放咱们这个音乐

17
00:00:53,380 --> 00:00:55,060
咱们看一下怎么播放啊

18
00:00:58,159 --> 00:01:00,500
叫做audio source

19
00:01:03,000 --> 00:01:06,439
那么在这里嗯咱们先说第一种

20
00:01:08,659 --> 00:01:11,239
第一种就是用组件的方式来播放

21
00:01:11,239 --> 00:01:12,920
那我们在这里给添加组件

22
00:01:12,920 --> 00:01:14,780
有个其他组件啊

23
00:01:14,780 --> 00:01:16,459
我们之前碰撞组件说了

24
00:01:16,459 --> 00:01:18,469
那我们现在来看这个其他组件

25
00:01:18,469 --> 00:01:21,459
其他组件里面第二个就是一个audio source

26
00:01:21,480 --> 00:01:24,400
那么这个组件就是用来播放声音的

27
00:01:24,519 --> 00:01:28,120
那么在这里它有很多这个属性

28
00:01:28,120 --> 00:01:28,480
对不对

29
00:01:28,480 --> 00:01:29,640
咱们来说一下

30
00:01:29,719 --> 00:01:33,079
首先这个组件的名字叫audio audio source

31
00:01:33,079 --> 00:01:34,819
那我们知道属性七呃

32
00:01:34,819 --> 00:01:36,680
这个这个组件其实就是一个类

33
00:01:36,680 --> 00:01:37,359
对不对

34
00:01:37,359 --> 00:01:40,629
所以实际上它就对应了一个audio source这样的一个类

35
00:01:40,629 --> 00:01:43,060
这个类呢就帮咱们去播放

36
00:01:43,640 --> 00:01:46,810
那么知道这个以后

37
00:01:46,810 --> 00:01:49,959
然后还有一个类叫audio clip

38
00:01:49,959 --> 00:01:50,859
在这里写出来

39
00:01:50,859 --> 00:01:51,040
了

40
00:01:51,040 --> 00:01:51,760
大家可以看一下

41
00:01:51,760 --> 00:01:53,599
这里的名字叫audio clip

42
00:01:53,920 --> 00:01:56,230
意思是一个音频片段

43
00:01:56,230 --> 00:01:58,480
那么audio clip也是一个类

44
00:01:58,480 --> 00:02:00,159
那么audio clip啊

45
00:02:00,159 --> 00:02:02,140
咱们所有的音频文件

46
00:02:02,239 --> 00:02:03,739
所有的音频文件

47
00:02:03,739 --> 00:02:04,939
什么mp 3啊什么的

48
00:02:04,939 --> 00:02:07,040
拖到工程里面以后啊

49
00:02:07,040 --> 00:02:08,840
拖到这个文件夹里面以后

50
00:02:08,840 --> 00:02:11,759
它都是一个audio clip啊

51
00:02:11,759 --> 00:02:13,860
它都对应了一个audio clip

52
00:02:13,860 --> 00:02:18,659
那么在这里既然他们俩是同一种类型

53
00:02:18,659 --> 00:02:20,159
所以其实最简单的播放

54
00:02:20,159 --> 00:02:22,819
你可以直接把这个音频文件拖拽过来

55
00:02:23,199 --> 00:02:26,719
拖拽过来以后大家可以看就关联上了

56
00:02:26,719 --> 00:02:27,860
关联上了以后

57
00:02:27,860 --> 00:02:30,439
这里是一个音频声音啊

58
00:02:30,439 --> 00:02:31,699
零就不零

59
00:02:31,699 --> 00:02:32,599
就是声音最小

60
00:02:32,599 --> 00:02:34,159
因为声音最大这样的

61
00:02:34,159 --> 00:02:35,900
然后在这里是否静音

62
00:02:35,900 --> 00:02:37,860
是否开启循环播放

63
00:02:38,020 --> 00:02:42,310
然后这个是是否在最开始的时候就播放

64
00:02:42,310 --> 00:02:43,560
我们把它勾上

65
00:02:43,580 --> 00:02:44,419
勾上以后

66
00:02:44,419 --> 00:02:46,310
只要这个程序运行

67
00:02:46,310 --> 00:02:48,969
这个组件被激活了啊

68
00:02:48,969 --> 00:02:51,599
那么它就会自动播放啊

69
00:02:51,599 --> 00:02:52,740
那你不勾他的话

70
00:02:52,740 --> 00:02:55,379
默认它这边就只是加载了这个音频文件

71
00:02:55,379 --> 00:02:56,849
但是并没有给我们播放

72
00:02:56,849 --> 00:02:59,800
你要勾上它上来就会自己播放这个音乐

73
00:02:59,800 --> 00:03:02,780
这个是是否需要预加载啊

74
00:03:02,780 --> 00:03:06,740
预加载这个意思就是比如说如果你现在不需要播放啊

75
00:03:06,740 --> 00:03:08,099
不是上来就播放

76
00:03:08,360 --> 00:03:10,759
那你是不是上来要进行一个预加载

77
00:03:10,759 --> 00:03:12,560
如果你预加载过n

78
00:03:12,560 --> 00:03:13,879
等你点播放的时候

79
00:03:13,879 --> 00:03:15,080
它会立刻播放

80
00:03:15,080 --> 00:03:16,699
如果你没有这个预加载

81
00:03:16,699 --> 00:03:17,840
你点播放的时候

82
00:03:17,840 --> 00:03:22,069
他才在这个时刻给你去进行一个加载播放

83
00:03:22,069 --> 00:03:23,750
那么小音频无所谓

84
00:03:23,750 --> 00:03:27,319
大音频加载可能就会稍微卡顿一下啊

85
00:03:27,319 --> 00:03:28,699
所以是这个意思

86
00:03:29,780 --> 00:03:33,680
当然我们一般的游戏里面的音频都会都是很小的

87
00:03:33,680 --> 00:03:37,139
哪怕是一个很就是很长的一个背景循环音乐啊

88
00:03:37,139 --> 00:03:38,969
那基本上也没多大啊

89
00:03:38,969 --> 00:03:40,680
那我们在这里就播上这个

90
00:03:40,680 --> 00:03:41,759
让它上来就播放

91
00:03:41,759 --> 00:03:43,539
我们可以看一下这个效果

92
00:03:45,360 --> 00:03:47,460
可以看一下它的加载进度

93
00:03:47,460 --> 00:03:47,879
对不对

94
00:03:47,879 --> 00:03:49,120
加载这个资源

95
00:03:50,439 --> 00:03:52,120
然后加载完了以后

96
00:03:52,120 --> 00:03:55,139
现在就已经开始播放我们这个音乐

97
00:03:58,520 --> 00:03:59,360
ok啊

98
00:03:59,360 --> 00:04:01,280
那这个音乐就播放出来了

99
00:04:01,280 --> 00:04:04,939
但是有时候我们肯定不可能这么简单

100
00:04:04,939 --> 00:04:06,080
就播放一个音乐是吧

101
00:04:06,080 --> 00:04:10,539
我们可能呃是需要通过代码进行控制的啊

102
00:04:10,539 --> 00:04:11,620
通过代码怎么控制

103
00:04:11,620 --> 00:04:12,819
那我们在这里啊

104
00:04:12,819 --> 00:04:14,460
把这边该删的都删了

105
00:04:14,479 --> 00:04:17,519
我们通过代码的方式对它进行一个播放

106
00:04:17,699 --> 00:04:21,579
首先我这里创建一个新的脚本

107
00:04:22,040 --> 00:04:26,660
叫做audio audio manager

108
00:04:30,620 --> 00:04:32,120
随便取个名就行

109
00:04:32,120 --> 00:04:34,100
然后我们把它

110
00:04:34,100 --> 00:04:36,079
打开

111
00:04:41,720 --> 00:04:44,779
那么在这个里面首先我要到呃

112
00:04:44,779 --> 00:04:51,699
我要得到一个咱们的一个呃组件player啊

113
00:04:52,360 --> 00:04:54,430
类型是什么

114
00:04:54,430 --> 00:04:58,639
audio clip从哪里获取

115
00:04:58,639 --> 00:05:01,769
从我们自己的组件身上啊

116
00:05:01,769 --> 00:05:04,560
c c.audio audio source啊

117
00:05:04,560 --> 00:05:05,819
不是audio clip啊

118
00:05:05,819 --> 00:05:07,410
我要获取播放器呢

119
00:05:07,410 --> 00:05:09,379
audio sars

120
00:05:10,120 --> 00:05:14,319
这样的话这个player在这里就代表了播放器了啊

121
00:05:14,480 --> 00:05:18,500
那么就从我们的这个身上去找到这个播放器的这个组件

122
00:05:18,500 --> 00:05:20,000
然后把它拿到了

123
00:05:20,040 --> 00:05:22,470
拿到这个播放器的组件以后

124
00:05:22,470 --> 00:05:28,220
那接下来我们就可以加载这个这个这个音频了

125
00:05:28,220 --> 00:05:29,300
怎么加载

126
00:05:29,300 --> 00:05:32,720
比如说我们加载的音频是放在source里面

127
00:05:32,720 --> 00:05:34,040
也是放在resource里面

128
00:05:34,040 --> 00:05:34,879
叫做飞鱼

129
00:05:34,879 --> 00:05:35,660
对不对

130
00:05:36,019 --> 00:05:41,360
那在这里cc.load.load rs

131
00:05:42,959 --> 00:05:44,519
咱们说过这个是吧

132
00:05:44,519 --> 00:05:45,980
加载资源

133
00:05:46,120 --> 00:05:48,759
飞羽加载出来是什么类型

134
00:05:48,759 --> 00:05:52,660
cc.audio clip类型这个声音片段

135
00:05:52,660 --> 00:05:54,399
所以它是audio clip类型

136
00:05:54,480 --> 00:05:56,009
那么加载完了以后

137
00:05:56,009 --> 00:06:02,139
它会生成它会掉一个回调方法啊

138
00:06:02,139 --> 00:06:04,389
只要加载完成就会掉这个方法

139
00:06:04,389 --> 00:06:06,279
那么在这个方法里面

140
00:06:06,279 --> 00:06:09,980
咱们就可以来做很多操作了

141
00:06:09,980 --> 00:06:16,160
首先在这里这个clip clip就代表我们已经加载完的这个音频文件

142
00:06:16,160 --> 00:06:18,060
就是这个加载完的auto clip

143
00:06:18,100 --> 00:06:20,259
那么在这里既然它已经完了

144
00:06:20,259 --> 00:06:21,879
那我们来

145
00:06:21,879 --> 00:06:24,480
赋值音频

146
00:06:25,060 --> 00:06:32,379
那我们就可以普雷点clip就等于一个clip

147
00:06:32,740 --> 00:06:34,899
可以啊

148
00:06:34,899 --> 00:06:37,060
那么只要这句话复制完了以后

149
00:06:37,060 --> 00:06:40,939
就相当于我们从这个文件夹把这个加载出来

150
00:06:40,959 --> 00:06:43,420
然后并且赋值到了这个位置啊

151
00:06:43,420 --> 00:06:45,639
就是给这个clip进行一个复制啊

152
00:06:45,639 --> 00:06:47,519
就把这个音频文件复制上了

153
00:06:47,540 --> 00:06:50,720
复制上以后我们就可以做一些操作了啊

154
00:06:50,720 --> 00:06:53,600
比如说我们就可以播放了

155
00:06:53,600 --> 00:06:54,589
对不对

156
00:06:54,589 --> 00:06:57,740
回来点儿play

157
00:06:57,740 --> 00:07:00,319
那这时候我们来运行一下我们的这个程序

158
00:07:00,319 --> 00:07:01,279
我们看一下

159
00:07:01,279 --> 00:07:03,019
首先我们面板上什么也没有

160
00:07:03,019 --> 00:07:03,680
对不对

161
00:07:03,680 --> 00:07:04,639
那么运行一下

162
00:07:04,639 --> 00:07:06,160
看看它能不能播放

163
00:07:10,319 --> 00:07:11,879
没有问题是吧

164
00:07:11,879 --> 00:07:13,050
没有问题

165
00:07:13,050 --> 00:07:15,000
ok

166
00:07:17,060 --> 00:07:19,639
那么除了这个还有一些常用的

167
00:07:19,639 --> 00:07:24,079
比如说是否正在播放啊

168
00:07:24,079 --> 00:07:26,029
这是一个常用的一个属性图

169
00:07:26,029 --> 00:07:29,139
点is playing啊

170
00:07:29,139 --> 00:07:30,100
是否正在播放

171
00:07:30,100 --> 00:07:31,810
这些大家是需要知道的

172
00:07:31,810 --> 00:07:40,060
以及比如说暂停和恢复恢复播放

173
00:07:40,060 --> 00:07:42,860
还有停止就彻底停止播放

174
00:07:43,720 --> 00:07:45,939
那么暂停

175
00:07:45,939 --> 00:07:48,100
很简单

176
00:07:48,680 --> 00:07:50,180
这些三个啊

177
00:07:50,180 --> 00:07:51,860
每一个都是一个函数啊

178
00:07:51,860 --> 00:07:54,019
你调的这个函数就可以了

179
00:07:54,019 --> 00:07:58,240
resume停止是点stop

180
00:07:59,959 --> 00:08:04,040
那么他们就分别代表这个暂停恢复停止功能

181
00:08:04,040 --> 00:08:04,939
只要掉了他们

182
00:08:04,939 --> 00:08:06,019
比如说你播放了

183
00:08:06,019 --> 00:08:07,800
你在某个时刻啊

184
00:08:07,800 --> 00:08:09,839
要暂停或者要停止啊

185
00:08:09,839 --> 00:08:13,839
你就在某个地方去调对应的这个方法就可以了

186
00:08:14,759 --> 00:08:16,829
那么除此之外还有一些

187
00:08:16,829 --> 00:08:19,139
比如说我们再说两个可能会用到的

188
00:08:19,139 --> 00:08:23,339
一个是可能在这里我们需要去修改一下是否循环播放啊

189
00:08:23,339 --> 00:08:24,629
是不是要循环播放

190
00:08:24,629 --> 00:08:28,379
以及当前的这个声音大小啊

191
00:08:28,379 --> 00:08:31,240
这个可能可能还是会用到的啊

192
00:08:31,259 --> 00:08:36,360
那么这种方式就是我们用组件的方式来进行播放啊

193
00:08:36,360 --> 00:08:39,580
就这一片都是用组件的方式来进行播放

194
00:08:42,779 --> 00:08:45,360
组件的方式

195
00:08:45,360 --> 00:08:50,440
那大家可以看到其实一个组件就是一个audio source

196
00:08:50,440 --> 00:08:53,379
它其实就对应播放一个音频对吧

197
00:08:53,379 --> 00:08:54,340
你要播放哪个音频

198
00:08:54,340 --> 00:08:55,240
你就加在哪呢

199
00:08:55,240 --> 00:08:57,139
你要播放哪个音频就加在哪了

200
00:08:57,139 --> 00:08:59,120
它是这样的一种播放方式

201
00:08:59,879 --> 00:09:02,940
而cos原原本的这个音频播放

202
00:09:02,940 --> 00:09:04,500
它是不需要依赖组件的

203
00:09:04,500 --> 00:09:08,659
那么下下面我们说另外一种播放方式不需要依赖组件的

204
00:09:08,659 --> 00:09:12,639
我们同时我们同样还是要播放这个这个音频

205
00:09:12,639 --> 00:09:15,279
所以第一件事不管怎样

206
00:09:15,480 --> 00:09:17,309
你都要把它加载

207
00:09:17,309 --> 00:09:19,860
把这个音频加载加载进来啊

208
00:09:19,860 --> 00:09:21,720
加载进来都是audio clip

209
00:09:21,720 --> 00:09:23,340
都是audio clip

210
00:09:23,639 --> 00:09:26,179
那么接下来我们开始播放了啊

211
00:09:28,320 --> 00:09:30,840
首先这里面的播放啊

212
00:09:30,840 --> 00:09:33,059
这里面的播放就变了

213
00:09:33,059 --> 00:09:36,269
那么它呢会返回一个number类型

214
00:09:36,269 --> 00:09:40,200
audio i d number类型啊

215
00:09:40,200 --> 00:09:45,000
等于一个c c.audio n engine啊

216
00:09:45,000 --> 00:09:47,159
就是播放的变成一个引擎了

217
00:09:47,159 --> 00:09:47,519
对不对

218
00:09:47,519 --> 00:09:49,200
上面是一个播放的一个圆

219
00:09:49,200 --> 00:09:50,370
现在是一个播放引擎

220
00:09:50,370 --> 00:09:52,169
这是两个类

221
00:09:52,169 --> 00:09:56,500
两个类它可以直接普列play

222
00:09:56,500 --> 00:09:58,629
那么它播放它是分了

223
00:09:58,629 --> 00:10:00,159
他给你分的详细了一点

224
00:10:00,159 --> 00:10:01,179
大家仔细看啊

225
00:10:01,179 --> 00:10:02,409
第一个有个play

226
00:10:02,409 --> 00:10:04,480
就是你随便播放一个音频

227
00:10:04,500 --> 00:10:06,759
第二个就是播放音效

228
00:10:07,039 --> 00:10:08,690
第三个就是播放音乐

229
00:10:08,690 --> 00:10:09,799
那么这里注意啊

230
00:10:09,799 --> 00:10:11,360
音乐一般就是指背景音乐

231
00:10:11,360 --> 00:10:14,000
你游戏里面一般都是有背景音乐的对吧

232
00:10:14,000 --> 00:10:16,059
然后有些环境声音

233
00:10:16,059 --> 00:10:18,279
环境声音我们就用这个来进行播放

234
00:10:18,279 --> 00:10:20,419
比如说刮风的声音

235
00:10:20,419 --> 00:10:22,399
海水的声音啊等等

236
00:10:22,399 --> 00:10:23,240
这种声音

237
00:10:23,259 --> 00:10:24,340
还有一个play

238
00:10:24,340 --> 00:10:25,600
就是比如说一些音效

239
00:10:25,600 --> 00:10:28,000
我们一般会用这个去播放啊

240
00:10:28,000 --> 00:10:32,419
它是让就是其实就是我们给他分了这三种声音啊

241
00:10:32,419 --> 00:10:34,279
我们给他分了这三种声音

242
00:10:34,279 --> 00:10:38,240
那么他呢也也把这三种声音给我们分成了三个方法

243
00:10:38,279 --> 00:10:41,159
那比如说我们在这里就用那个播放音乐了

244
00:10:43,299 --> 00:10:44,440
播放音乐的话

245
00:10:44,440 --> 00:10:45,940
总播放里面两个参数

246
00:10:45,940 --> 00:10:49,600
第一个就是我们加载进来的clip音频片段

247
00:10:49,740 --> 00:10:53,960
第二个啊就是是否要循环啊

248
00:10:53,960 --> 00:10:55,730
这个就是一个正常播放

249
00:10:55,730 --> 00:10:58,639
那么它呢会返回一个i d啊

250
00:10:58,639 --> 00:10:59,870
他们会返回一个i d

251
00:10:59,870 --> 00:11:01,340
这个i d是干嘛的

252
00:11:01,340 --> 00:11:04,159
因为大家想比如说我们播放音效

253
00:11:04,159 --> 00:11:07,899
或者说播放环境音风可能刮着海水

254
00:11:07,899 --> 00:11:09,340
可能同时也在想着

255
00:11:09,340 --> 00:11:11,659
可能我们旁边还有人在打架

256
00:11:11,659 --> 00:11:13,490
还有打架的这个声音是吧

257
00:11:13,490 --> 00:11:15,720
再加上后面可能还有背景声音

258
00:11:15,740 --> 00:11:19,220
那么同一时间场景里面可能有多处在播放声音

259
00:11:19,240 --> 00:11:22,600
那这时候比如说我想对其中的某一个声音做操作

260
00:11:22,600 --> 00:11:25,299
比如说暂停这个声音啊

261
00:11:25,299 --> 00:11:26,860
比如说暂停风声

262
00:11:26,879 --> 00:11:29,789
你怎么知道哪个声音是风声

263
00:11:29,789 --> 00:11:31,590
所以在播放声音以后

264
00:11:31,590 --> 00:11:33,179
不管你播放什么声音

265
00:11:33,179 --> 00:11:33,840
哪种声音

266
00:11:33,840 --> 00:11:35,840
它都会返回一个i d之后

267
00:11:35,840 --> 00:11:38,179
比如说我们想对这个声音操作

268
00:11:38,179 --> 00:11:41,600
我们就对这个声音的操作就可以了啊

269
00:11:41,600 --> 00:11:42,799
是这样的一个意思

270
00:11:45,340 --> 00:11:50,019
啊就好比大家现在好多那个去点那个吃饭是吧

271
00:11:50,019 --> 00:11:51,039
你一点饭

272
00:11:51,039 --> 00:11:53,340
然后人家就给你个号码啊

273
00:11:53,340 --> 00:11:54,659
点给你个号码诶

274
00:11:54,659 --> 00:11:56,759
这个号码就对应着你啊

275
00:11:56,759 --> 00:11:58,200
就就是那个感觉啊

276
00:11:58,200 --> 00:12:00,340
这个一播放播放一个声音

277
00:12:00,340 --> 00:12:01,840
他就给你返回一个号码啊

278
00:12:01,840 --> 00:12:03,279
这个是唯一的号码

279
00:12:04,340 --> 00:12:09,559
那在这里我们同样有是否正在播放啊

280
00:12:09,559 --> 00:12:11,240
这这这些都是很常用的

281
00:12:11,240 --> 00:12:12,379
所以一定要知道

282
00:12:12,379 --> 00:12:17,120
那我们可以is music play啊

283
00:12:17,120 --> 00:12:18,379
是否正在播放

284
00:12:18,379 --> 00:12:19,639
只有个背景音乐

285
00:12:19,639 --> 00:12:20,120
有啊

286
00:12:20,120 --> 00:12:21,620
is music playing啊

287
00:12:21,620 --> 00:12:23,539
就是背景音乐是否正在播放

288
00:12:23,539 --> 00:12:24,779
那这个是个方法

289
00:12:24,799 --> 00:12:26,179
这个是个属性啊

290
00:12:26,179 --> 00:12:26,899
不一样

291
00:12:29,720 --> 00:12:39,759
那么除了这个还这就是剩下常用的就是我们的还是暂停恢复停止啊

292
00:12:39,759 --> 00:12:45,070
比如说上面的循环和这个声音大小啊

293
00:12:45,070 --> 00:12:47,830
其实这就是常用的暂停的话

294
00:12:47,830 --> 00:12:52,210
c c.audio engine点这个暂停

295
00:12:52,210 --> 00:12:54,820
那大家可以看到暂停的话

296
00:12:54,820 --> 00:12:56,919
这里就区分了

297
00:12:56,919 --> 00:13:02,039
暂停音频暂停所有的声音啊

298
00:13:02,039 --> 00:13:03,480
那就所有的都暂停了

299
00:13:03,480 --> 00:13:05,820
暂停所有的这个特效声音啊

300
00:13:05,820 --> 00:13:07,200
暂停某个特效

301
00:13:07,200 --> 00:13:09,830
暂停背景音乐啊

302
00:13:09,830 --> 00:13:14,899
那么这里我们这个不同的是不同的这个声音

303
00:13:14,899 --> 00:13:16,399
我们用不同的方法啊

304
00:13:16,399 --> 00:13:18,460
还有还有两个是所有的

305
00:13:18,460 --> 00:13:19,000
对不对

306
00:13:19,000 --> 00:13:21,309
比如说我们暂停一个音效

307
00:13:21,309 --> 00:13:23,799
这里它它一般是让你填个id的

308
00:13:23,799 --> 00:13:26,809
比如说你要有你就把id放到这儿

309
00:13:26,809 --> 00:13:31,000
它就会给你暂停对应的这个对应id的这个音效啊

310
00:13:31,000 --> 00:13:32,019
当然我们这儿不匹配

311
00:13:32,019 --> 00:13:34,480
我们这个i d是背景音乐的啊

312
00:13:34,480 --> 00:13:37,279
我们在这暂停的是这种音效的

313
00:13:37,279 --> 00:13:38,539
你要想暂停背景音乐

314
00:13:38,539 --> 00:13:40,940
你直接audio music就行了啊

315
00:13:40,940 --> 00:13:42,879
然后恢复的话

316
00:13:45,539 --> 00:13:47,370
恢复也一样啊

317
00:13:47,370 --> 00:13:49,799
比如说我们也用我们都用音效的做例子吧

318
00:13:49,799 --> 00:13:51,840
后面就是这个audio i d啊

319
00:13:51,840 --> 00:13:55,919
因为它是呃这个最短的啊

320
00:13:55,919 --> 00:13:58,440
然后最清晰一看就知道它是什么意思

321
00:14:00,620 --> 00:14:02,659
然后比如说停止就是stop

322
00:14:02,659 --> 00:14:04,639
你看全都是很多组啊

323
00:14:04,639 --> 00:14:05,539
全都是很多组

324
00:14:05,539 --> 00:14:07,419
每一个都是都有是吧

325
00:14:08,799 --> 00:14:10,480
然后你要暂停哪个声音

326
00:14:10,480 --> 00:14:14,179
你就把哪个声音的id放这儿循环

327
00:14:15,600 --> 00:14:23,279
循环audio这点这个这个set loop哎

328
00:14:23,279 --> 00:14:27,480
要哪一个声音是循环还是不循环

329
00:14:27,480 --> 00:14:28,860
你看对吧

330
00:14:28,860 --> 00:14:30,139
你都可以去设置

331
00:14:31,539 --> 00:14:35,919
然后声音的大小set value啊

332
00:14:35,919 --> 00:14:36,700
0~1

333
00:14:36,700 --> 00:14:37,480
对不对

334
00:14:37,879 --> 00:14:41,480
ok那么这个就是另外一种播放声音啊

335
00:14:41,480 --> 00:14:46,799
第一个没点那个audio i d o k

336
00:14:46,799 --> 00:14:49,559
大家就可以看到这是另外一种播放声音的方式

337
00:14:49,559 --> 00:14:53,779
那么我们下面是用音效做的例子

338
00:14:53,779 --> 00:14:55,250
所以都给他了一个i d

339
00:14:55,250 --> 00:14:59,740
如果比如说你看我要暂停我们之前播放的那个

340
00:14:59,740 --> 00:15:02,139
我们这里是播放的背景音乐

341
00:15:02,139 --> 00:15:03,519
如果要暂停背景音乐

342
00:15:03,519 --> 00:15:06,840
你就直接暂停背景音乐就行了

343
00:15:06,840 --> 00:15:08,940
那么背景音乐是不需要id的

344
00:15:08,940 --> 00:15:10,740
所有的背景相关的操作

345
00:15:10,740 --> 00:15:13,320
背景音乐相关的操作是不需要d的

346
00:15:13,320 --> 00:15:14,120
为什么

347
00:15:15,700 --> 00:15:22,820
因为背景音乐他的这个他说白了他同一时间只能有一个背景音乐

348
00:15:22,840 --> 00:15:26,110
一个游戏它的音效可能重叠啊

349
00:15:26,110 --> 00:15:27,700
这个这个这个比如说风声

350
00:15:27,700 --> 00:15:29,200
海声啊是吧

351
00:15:29,200 --> 00:15:31,080
水声啊什么

352
00:15:31,240 --> 00:15:33,580
这这这这个这个打架的声音啊

353
00:15:33,580 --> 00:15:34,659
这些是可以重叠的

354
00:15:34,659 --> 00:15:37,720
但是背景音乐同一时间只能播一个

355
00:15:37,720 --> 00:15:39,639
你没有看玩到一个游戏

356
00:15:39,639 --> 00:15:41,379
同时有两个人在唱两个歌

357
00:15:41,379 --> 00:15:41,980
那就乱了

358
00:15:41,980 --> 00:15:42,580
对不对

359
00:15:42,580 --> 00:15:45,100
所以如果我们操作背景音乐的话

360
00:15:45,100 --> 00:15:47,299
一般是不需要这个id的

361
00:15:47,299 --> 00:15:48,740
ok那这就是这些

362
00:15:48,740 --> 00:15:49,700
那我们可以试一下

363
00:15:49,700 --> 00:15:51,379
看看它能不能正常播放

364
00:15:51,379 --> 00:15:52,419
还没有试过

365
00:15:52,779 --> 00:15:54,460
我们现在来运行一下

366
00:15:54,460 --> 00:15:55,539
就一个这一行

367
00:15:55,539 --> 00:15:58,179
我们看一下能不能用这种方式进行播放

368
00:16:07,100 --> 00:16:08,539
ok没问题啊

369
00:16:08,539 --> 00:16:09,799
也正常播放了啊

370
00:16:09,799 --> 00:16:10,820
也正常播放了

371
00:16:10,820 --> 00:16:15,759
那么这个啊这个就是咱们这个播放啊

372
00:16:15,759 --> 00:16:18,100
播放这个音乐啊

373
00:16:18,100 --> 00:16:20,320
或者音效的这个方式啊

374
00:16:20,320 --> 00:16:23,440
大家在这个做做项目的时候用哪种都可以

375
00:16:23,440 --> 00:16:26,919
但是嗯怎么说啊

376
00:16:26,919 --> 00:16:30,460
像我个人相对而言比较喜欢用下面这种啊

377
00:16:30,460 --> 00:16:31,809
比较喜欢用下面这种

378
00:16:31,809 --> 00:16:32,980
也不需要组件

379
00:16:32,980 --> 00:16:33,519
省事儿

380
00:16:33,519 --> 00:16:35,529
是不是哈哈哈

381
00:16:35,529 --> 00:16:36,460
ok啊行

382
00:16:36,460 --> 00:16:39,879
那咱们就呃这节课就这么多啊

383
00:16:39,879 --> 00:16:41,440
这节课就是糊涂就这么多


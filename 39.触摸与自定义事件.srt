1
00:00:09,320 --> 00:00:13,630
ok那这节课我们来说一下触摸事件和自定义事件

2
00:00:13,630 --> 00:00:15,429
首先来说触摸事件啊

3
00:00:15,429 --> 00:00:16,910
首先触摸的话

4
00:00:16,910 --> 00:00:19,190
当然这个东西主要就是为手机准备的了

5
00:00:19,190 --> 00:00:19,670
对不对

6
00:00:19,670 --> 00:00:20,690
就是不用鼠标

7
00:00:20,690 --> 00:00:21,920
用这个手指触摸

8
00:00:21,920 --> 00:00:26,239
当然如果你在电脑上去运行它这个触摸事件啊

9
00:00:26,239 --> 00:00:28,048
也可以响应鼠标事件

10
00:00:28,048 --> 00:00:34,490
也就是说如果你现在比如说我只是我只要确定我只关注鼠标左键啊

11
00:00:34,490 --> 00:00:36,170
不关注鼠标右键和滚轮

12
00:00:36,170 --> 00:00:39,000
那么你用触摸都是完全可以的啊

13
00:00:39,320 --> 00:00:42,140
那么在这里比如说我还用这个节点吧

14
00:00:42,140 --> 00:00:45,020
我这个节点目前是监听的这个鼠标事件

15
00:00:45,020 --> 00:00:49,380
是不是我把鼠标事件我们在这里给它注释掉

16
00:00:49,380 --> 00:00:51,810
然后在下面鼠标键盘

17
00:00:51,810 --> 00:00:55,700
然后我们下面是个触摸触摸事件

18
00:00:57,439 --> 00:01:01,799
触摸事件的话我们在这里很简单

19
00:01:01,799 --> 00:01:03,479
我们还是要用这个图片

20
00:01:03,479 --> 00:01:05,819
我们还是在这个图片上去监听触摸时间

21
00:01:05,819 --> 00:01:08,769
那我们就this.note.on啊

22
00:01:08,769 --> 00:01:10,569
其实你看套路都是一样的

23
00:01:10,569 --> 00:01:11,359
对不对

24
00:01:11,980 --> 00:01:16,859
然后on这里面我们就是cc.node啊

25
00:01:16,859 --> 00:01:21,599
然后在这边点invent type事件类型

26
00:01:21,599 --> 00:01:24,239
我们之前上面写的是mouse

27
00:01:24,239 --> 00:01:28,260
那么这里面还有touch touch

28
00:01:28,260 --> 00:01:30,920
就是我们触摸事件啊

29
00:01:30,920 --> 00:01:35,060
比如说最基本的就是触摸到屏幕的时候的一个事件

30
00:01:35,060 --> 00:01:37,060
就是touch start啊

31
00:01:37,060 --> 00:01:40,599
我们来试一下能不能监听得到function

32
00:01:41,939 --> 00:01:44,840
这里面常用的属性大家记起来就好了啊

33
00:01:44,840 --> 00:01:45,980
一定要记下来

34
00:01:45,980 --> 00:01:47,930
比如说得到鼠标左右键呀

35
00:01:47,930 --> 00:01:50,359
得到键盘的这个编码啊什么的

36
00:01:51,540 --> 00:01:53,060
那么触摸一样

37
00:01:53,060 --> 00:01:54,140
触摸也有他自己的

38
00:01:54,140 --> 00:01:55,159
我们先不管啊

39
00:01:55,159 --> 00:01:58,980
我们先不管这个事件里面我们需要知道什么呃

40
00:01:58,980 --> 00:02:00,719
什么方法或者什么属性

41
00:02:00,719 --> 00:02:02,969
我们先在这儿随便打印一句

42
00:02:02,969 --> 00:02:05,760
看一下触摸能不能触发啊

43
00:02:05,760 --> 00:02:08,979
正常触发也叫触摸

44
00:02:11,319 --> 00:02:14,039
然后来这边运行

45
00:02:16,979 --> 00:02:22,819
我们在这边运行完以后就打开破绽了

46
00:02:23,639 --> 00:02:25,219
我们直接看一下啊

47
00:02:25,219 --> 00:02:26,960
鼠标按到这里没反应

48
00:02:26,960 --> 00:02:28,520
按这里大家可以看

49
00:02:28,520 --> 00:02:32,460
其实触摸是不是跟鼠标事件在这里感觉是一样的了

50
00:02:34,860 --> 00:02:35,930
ok啊

51
00:02:35,930 --> 00:02:37,219
那么

52
00:02:39,259 --> 00:02:41,740
再回来我们现在想知道

53
00:02:41,740 --> 00:02:42,939
比如说对于触摸来说

54
00:02:42,939 --> 00:02:45,710
他肯定呃不分左键右键

55
00:02:45,710 --> 00:02:50,110
但是在手机上面触摸有可能会做到多点触摸

56
00:02:50,110 --> 00:02:54,340
就是一个游戏可能同时要两个指头或者更多指头去控制

57
00:02:54,379 --> 00:02:55,280
对不对

58
00:02:55,280 --> 00:02:56,780
那么这个是有可能的

59
00:02:56,780 --> 00:03:01,590
仔细想一想自己玩的那些那些游戏有没有啊多指触摸的啊

60
00:03:01,590 --> 00:03:02,610
肯定是有的

61
00:03:02,610 --> 00:03:04,229
那么没有是没有

62
00:03:04,229 --> 00:03:05,209
如果有的话

63
00:03:05,209 --> 00:03:08,329
那么怎么判断你现在是多指触摸呢

64
00:03:08,329 --> 00:03:09,588
那首先大家想啊

65
00:03:09,588 --> 00:03:11,508
如果有一个手指按上去了

66
00:03:11,508 --> 00:03:13,310
那么我们会调一个这个方法

67
00:03:13,310 --> 00:03:15,289
如果有第二个手指按上去了

68
00:03:15,289 --> 00:03:18,289
那么就会有第二就会又掉一次

69
00:03:18,289 --> 00:03:21,409
这个方法也就是说这个方法如果有两个手指按的话

70
00:03:21,409 --> 00:03:22,460
它会掉两次

71
00:03:22,460 --> 00:03:27,680
你怎么区分哪一个手指是哪一个and的是哪一个点

72
00:03:28,079 --> 00:03:32,519
其实他这里给你每一个触摸点都做了一个编码啊

73
00:03:32,519 --> 00:03:39,000
这个编码的话你可以在这比如说触摸加上一个点

74
00:03:45,620 --> 00:03:47,000
他都给你分了一下这个id

75
00:03:47,000 --> 00:03:49,460
那这样的话就是你按一点的话

76
00:03:49,460 --> 00:03:50,419
我们在这儿运行一下

77
00:03:50,419 --> 00:03:51,319
看效果

78
00:03:59,080 --> 00:04:02,460
那么每次一多一打开多标签了

79
00:04:02,460 --> 00:04:04,620
它这个就快捷键就不管用了

80
00:04:04,620 --> 00:04:06,659
就很烦啊哈哈

81
00:04:06,659 --> 00:04:11,830
那么在这里大家可以看我一触摸000

82
00:04:11,830 --> 00:04:13,780
因为我这永远是第一个触摸

83
00:04:13,780 --> 00:04:15,400
如果这时候我在触摸当中

84
00:04:15,400 --> 00:04:17,370
我第二个手指按了

85
00:04:17,370 --> 00:04:19,889
那么这个编码就不是零了啊

86
00:04:19,889 --> 00:04:20,910
这个编码就不是零了

87
00:04:20,910 --> 00:04:26,199
那么通过这个编码我们就能区分开当前你按往屏幕上按了几个手指啊

88
00:04:26,199 --> 00:04:30,120
你当前是按按下去的是第几个手指啊

89
00:04:30,120 --> 00:04:31,620
一般是第一个按下去的是零

90
00:04:31,620 --> 00:04:32,160
对不对

91
00:04:32,160 --> 00:04:35,100
后面的是往后去增的这样的一个编码

92
00:04:36,459 --> 00:04:38,759
那么在这里除了触摸编码

93
00:04:38,759 --> 00:04:40,980
有时候我们还要得到一些

94
00:04:40,980 --> 00:04:45,759
比如说location依然是需要得到的啊

95
00:04:45,759 --> 00:04:49,879
或者说就得到一个x或者就得到一个y都可以

96
00:04:51,839 --> 00:04:53,959
那么我们可以看一下location

97
00:04:56,180 --> 00:04:57,199
location

98
00:04:59,819 --> 00:05:03,928
你看是不是这触摸的位置就出来了

99
00:05:03,928 --> 00:05:06,869
这两个还是必须知道的一个get id啊

100
00:05:06,869 --> 00:05:07,949
就是区分手指的

101
00:05:07,949 --> 00:05:08,879
对于触摸而言

102
00:05:08,879 --> 00:05:11,488
当然很多我们做的小题一般也就一个点啊

103
00:05:11,488 --> 00:05:12,399
一般就一个点

104
00:05:12,399 --> 00:05:14,980
如果有多个你那个get id就有用了

105
00:05:14,980 --> 00:05:18,620
然后这个这个location这个是必须知道的啊

106
00:05:20,060 --> 00:05:23,379
然后我们还是最起码先记住这两个啊

107
00:05:23,379 --> 00:05:25,089
触摸事件先记住这两个

108
00:05:25,089 --> 00:05:28,019
那么我们来说一下其他的这个触摸事件啊

109
00:05:29,100 --> 00:05:30,740
这是开始触摸

110
00:05:30,740 --> 00:05:32,480
那么当然还有结束触摸

111
00:05:32,480 --> 00:05:35,420
结束触摸就和你这个鼠标事件是一样的

112
00:05:35,420 --> 00:05:38,399
就是你鼠标松起来的那一下就是结束触摸

113
00:05:38,399 --> 00:05:39,240
对不对

114
00:05:39,279 --> 00:05:40,480
木也一样

115
00:05:40,480 --> 00:05:42,220
和鼠标木一样啊

116
00:05:44,379 --> 00:05:45,550
还没离开的时候

117
00:05:45,550 --> 00:05:48,519
只要移动就会掉木cancel

118
00:05:48,519 --> 00:05:51,339
你看提示的说的说明的很详细

119
00:05:51,339 --> 00:05:54,730
当手指在目标节点区域外离开屏幕时

120
00:05:54,730 --> 00:05:57,639
就是你这个你离开了你这个区域啊

121
00:05:57,639 --> 00:05:59,439
就是取消这个触摸时间了

122
00:05:59,439 --> 00:06:00,970
就是异常取消

123
00:06:00,970 --> 00:06:02,560
算是一个异常结束吧

124
00:06:02,560 --> 00:06:04,839
正常结束就是你有暗又抬起

125
00:06:04,839 --> 00:06:07,870
然后你按了以后却没有在我这个节点上抬起

126
00:06:07,870 --> 00:06:10,029
那这个就算是异常结束时间

127
00:06:10,029 --> 00:06:11,459
其实就是这个cancel

128
00:06:11,718 --> 00:06:14,860
那这就是触摸的四个时间

129
00:06:16,439 --> 00:06:20,040
那么自定义事件是怎么样去

130
00:06:21,100 --> 00:06:22,439
是怎么去用啊

131
00:06:22,439 --> 00:06:23,560
是怎么去用

132
00:06:24,000 --> 00:06:26,040
那我们就含在刚才这个里面吧

133
00:06:26,040 --> 00:06:27,600
我们还在这个脚本里面

134
00:06:27,600 --> 00:06:31,290
我们在这里面去监听一个自定义事件

135
00:06:31,290 --> 00:06:34,529
同时我们可以自己发送一下自定义事件

136
00:06:34,529 --> 00:06:36,959
那我们首先比如说在这里

137
00:06:37,680 --> 00:06:44,538
我们在这里监听自定义事件

138
00:06:44,538 --> 00:06:47,149
我们写一个自定义事件进行监听

139
00:06:47,149 --> 00:06:55,939
其实所谓的自呃自定义事件就是无非就是你监听的这个内容cc.node

140
00:06:55,939 --> 00:06:57,800
而且不是监听系统给你提供的了

141
00:06:57,800 --> 00:07:00,079
是你直接自己定义一个

142
00:07:00,079 --> 00:07:03,519
比如说叫my一啊

143
00:07:03,519 --> 00:07:05,199
这就是我监听的这个事件

144
00:07:05,199 --> 00:07:06,279
监听到了以后

145
00:07:06,279 --> 00:07:10,310
我就会掉我的这个回调方法啊

146
00:07:10,310 --> 00:07:12,420
然后我们可以打印一下

147
00:07:14,300 --> 00:07:18,040
debug自定义事件

148
00:07:18,339 --> 00:07:20,230
但是这个事件怎么触

149
00:07:20,230 --> 00:07:20,980
怎么触发

150
00:07:20,980 --> 00:07:23,470
我们知道这个系统的事件诶

151
00:07:23,470 --> 00:07:28,100
我这里我们知道这个系统的这些监听的事件

152
00:07:28,100 --> 00:07:29,899
它全是从底层给我们发送的

153
00:07:29,899 --> 00:07:31,689
比如鼠标按了键盘按了

154
00:07:31,689 --> 00:07:33,550
那他是给我们发消息的

155
00:07:33,550 --> 00:07:36,370
而这个自定义事件是系统是不会给你发的

156
00:07:36,370 --> 00:07:37,569
需要你自己去发

157
00:07:37,569 --> 00:07:39,839
那我们可以在触摸事件里面

158
00:07:39,839 --> 00:07:41,100
比如说我按下屏幕

159
00:07:41,100 --> 00:07:42,600
我给我自己发个消息

160
00:07:42,600 --> 00:07:43,019
怎么发

161
00:07:43,019 --> 00:07:43,800
其实很简单

162
00:07:43,800 --> 00:07:46,379
this.node点

163
00:07:47,379 --> 00:07:48,670
那么在这里

164
00:07:48,670 --> 00:07:52,000
由于我们是在这个这个这个匿名函数里面

165
00:07:52,000 --> 00:07:53,439
所以this啊

166
00:07:53,439 --> 00:07:56,779
我们就需要用let cf等于this

167
00:07:56,779 --> 00:08:00,079
这样才能正常去使用这个this

168
00:08:01,439 --> 00:08:09,298
cf.node点我们有两种发呃发送这个事件的方法啊

169
00:08:09,298 --> 00:08:10,699
这个是第一种

170
00:08:11,139 --> 00:08:17,079
我们在这里填写一下我们的事件名称my 1

171
00:08:18,339 --> 00:08:20,560
这时候我们运行一下

172
00:08:23,019 --> 00:08:24,699
看一下能不能发送啊

173
00:08:26,740 --> 00:08:29,209
按下自定义事件有了吧

174
00:08:29,209 --> 00:08:31,910
虽然我们按下以后调的是触摸事件

175
00:08:31,910 --> 00:08:36,899
但是触摸时间在这里给我自定义事件发了个消息

176
00:08:37,200 --> 00:08:40,440
那么这样的话我们就是一个发消息

177
00:08:40,440 --> 00:08:41,399
一个接收消息

178
00:08:41,399 --> 00:08:46,259
但实际上大家去想这种发消息在这里没有多大意义啊

179
00:08:46,259 --> 00:08:48,120
你自己脚本给自己脚本发

180
00:08:48,120 --> 00:08:48,750
是不是

181
00:08:48,750 --> 00:08:51,750
所以他这个这种发送没多大意义

182
00:08:51,750 --> 00:08:53,818
那么它还有一种发送

183
00:08:53,860 --> 00:08:55,149
还有一种发送

184
00:08:55,149 --> 00:08:56,860
还有一种发送是怎么发送

185
00:08:56,860 --> 00:08:58,438
是cf点

186
00:08:59,620 --> 00:09:06,659
第四node node.dispatch消息分发

187
00:09:07,000 --> 00:09:09,639
这个也是要去发消息

188
00:09:09,639 --> 00:09:12,389
我们依然是my 1

189
00:09:12,389 --> 00:09:14,519
我们不用上面那种去发消息了

190
00:09:14,519 --> 00:09:16,169
我们用下面这种发消息

191
00:09:16,169 --> 00:09:17,250
但是这里大家注意啊

192
00:09:17,250 --> 00:09:19,370
他这里让传的就不是个字符串了

193
00:09:19,370 --> 00:09:21,710
大家可以看我这里想传个字符串

194
00:09:21,710 --> 00:09:23,570
发现报错啊

195
00:09:23,570 --> 00:09:29,759
那么这个东西我们应该在这里给它创建一个英文的一个对象啊

196
00:09:29,759 --> 00:09:31,080
然后其实非常简单啊

197
00:09:31,080 --> 00:09:36,740
就是在这里我先把它就是直接在这里就创建对象

198
00:09:36,740 --> 00:09:42,938
new cc点点unit custom

199
00:09:44,980 --> 00:09:49,139
然后在这里把我们的世界名称写上搞定

200
00:09:55,100 --> 00:09:58,879
然后在这里他还是这个报错了

201
00:09:58,879 --> 00:09:59,659
对不对

202
00:09:59,659 --> 00:10:00,740
还是报错了

203
00:10:00,740 --> 00:10:01,700
我们看一下

204
00:10:01,700 --> 00:10:03,019
应该有两个参数

205
00:10:03,019 --> 00:10:04,159
但是只有一个

206
00:10:04,159 --> 00:10:06,470
那么后面有一个参数

207
00:10:06,470 --> 00:10:08,360
它让我们填一个布尔值

208
00:10:08,360 --> 00:10:10,730
叫做是否冒泡啊

209
00:10:10,730 --> 00:10:11,690
是否冒泡

210
00:10:11,690 --> 00:10:12,950
我们先给他一个处

211
00:10:12,950 --> 00:10:15,720
我们先不要管它什么意思啊

212
00:10:15,720 --> 00:10:17,789
也就是说你看这个功能比上面这个多

213
00:10:17,789 --> 00:10:19,889
前面第一个参数其实一样

214
00:10:19,889 --> 00:10:21,960
它主要就是多了这样一个参数

215
00:10:21,960 --> 00:10:23,220
这个参数是干嘛的

216
00:10:23,220 --> 00:10:25,019
我们先看一下能不能正常运行

217
00:10:25,019 --> 00:10:26,299
我们再解释啊

218
00:10:29,139 --> 00:10:30,340
运行完

219
00:10:30,340 --> 00:10:33,919
那么我们看一下

220
00:10:33,919 --> 00:10:36,649
点一下自定义事件是不是还是可以的

221
00:10:36,649 --> 00:10:40,289
也就是说目前情况下这个跟这个一样啊

222
00:10:40,289 --> 00:10:43,139
那么这个是否冒泡是什么意思啊

223
00:10:43,139 --> 00:10:46,019
我在这里咱们来说一下啊

224
00:10:46,019 --> 00:10:47,940
首先我们知道节点有父子关系

225
00:10:47,940 --> 00:10:48,839
是不是

226
00:10:49,259 --> 00:10:54,980
那么也就是说他这个正常用第一种方式我发就是给自己脚本发了

227
00:10:54,980 --> 00:10:55,940
第二种方式的话

228
00:10:55,940 --> 00:10:57,710
他不光可以给自己脚本发

229
00:10:57,710 --> 00:11:01,019
他可以给他的副节点去发送

230
00:11:01,980 --> 00:11:04,279
那么我们在这里打开个ppt好了

231
00:11:05,720 --> 00:11:07,279
打开个ppt

232
00:11:08,559 --> 00:11:10,339
我们来画一下

233
00:11:12,899 --> 00:11:17,210
实际上他所谓的方块就是比如说这是副节点

234
00:11:17,210 --> 00:11:19,159
这是子节点

235
00:11:19,860 --> 00:11:21,840
这是第三个子节点

236
00:11:21,840 --> 00:11:26,980
那么比如说我这个事件是从这里发送的啊

237
00:11:26,980 --> 00:11:30,120
发发送事件

238
00:11:32,659 --> 00:11:34,700
我事件是从这里发送的

239
00:11:34,700 --> 00:11:38,059
但是这个事件啊他是怎样发的

240
00:11:38,059 --> 00:11:40,590
他是通过冒泡的方法啊

241
00:11:40,590 --> 00:11:41,759
不光是给我自己

242
00:11:41,759 --> 00:11:43,759
他先把事件给了他

243
00:11:44,500 --> 00:11:47,419
再把事件给了他啊

244
00:11:47,419 --> 00:11:48,259
给到最顶层

245
00:11:48,259 --> 00:11:51,620
大家看这个感觉是不是从最底层一层层往上给

246
00:11:51,620 --> 00:11:53,419
所以这个就取了个名字

247
00:11:53,419 --> 00:11:57,000
就叫它冒泡冒泡

248
00:11:58,019 --> 00:12:00,019
冒泡传递啊

249
00:12:00,019 --> 00:12:01,570
冒泡传递消息

250
00:12:01,570 --> 00:12:02,889
那么注意啊

251
00:12:02,889 --> 00:12:06,009
冒泡传递并不意味着大家想一想

252
00:12:06,009 --> 00:12:07,450
谁先处理这个消息

253
00:12:07,450 --> 00:12:10,549
并不意味着谁先接收这个消息

254
00:12:10,549 --> 00:12:11,570
谁就先处理

255
00:12:11,570 --> 00:12:13,850
也就是说实际上你看他发送事件

256
00:12:13,850 --> 00:12:15,529
所以它本身应该最先收到

257
00:12:15,529 --> 00:12:18,169
也就是说我们很多人想是不是他先接受

258
00:12:18,169 --> 00:12:19,730
他先处理事件

259
00:12:19,730 --> 00:12:23,149
其实这只是他他的消息的一个传递流程

260
00:12:23,149 --> 00:12:24,740
当传递完以后

261
00:12:24,740 --> 00:12:27,139
它才会开始逐级的处理

262
00:12:30,360 --> 00:12:33,559
也就是说实际上它本身有一个来回了啊

263
00:12:33,559 --> 00:12:34,759
它本身是有一个来回的

264
00:12:34,759 --> 00:12:36,679
先通过冒泡把消息传递上去

265
00:12:36,679 --> 00:12:38,450
然后再一层一层处理消息

266
00:12:38,450 --> 00:12:39,740
但是我可以告诉大家

267
00:12:39,740 --> 00:12:41,269
他这个怎么说啊

268
00:12:41,269 --> 00:12:44,429
这个做的不是很好用啊

269
00:12:44,429 --> 00:12:46,649
不是不是特别好用啊

270
00:12:46,649 --> 00:12:48,419
但是他意思是这个意思啊

271
00:12:48,419 --> 00:12:51,589
就是说比如说我这个mc camera就可以发消息

272
00:12:51,589 --> 00:12:54,379
然后canvas canvas就可以收到这个消息

273
00:12:54,379 --> 00:12:56,089
这个消息就是冒泡上来的

274
00:12:56,089 --> 00:12:56,958
对不对

275
00:12:58,960 --> 00:13:02,169
如果我们要写大型项目啊

276
00:13:02,169 --> 00:13:04,059
如果我们要写大型项目

277
00:13:04,299 --> 00:13:07,840
可能如果需要用到这种就是消息机制的话

278
00:13:07,840 --> 00:13:11,230
可能我们就会自己实现一套自己的消息消息机制

279
00:13:11,230 --> 00:13:15,919
那么他提供的这两套机制准确来说都不是很好用啊

280
00:13:15,919 --> 00:13:18,590
那么这个就是你要是填force

281
00:13:18,590 --> 00:13:20,539
就是只在本脚本触发了

282
00:13:20,539 --> 00:13:21,470
你要填出的话

283
00:13:21,470 --> 00:13:23,149
就是可以冒泡给副节点

284
00:13:23,149 --> 00:13:24,659
就是这样的嗯

285
00:13:24,659 --> 00:13:27,539
但是整体而言啊都不是很好用

286
00:13:27,539 --> 00:13:31,779
所以如果大家要就正常编写小项目

287
00:13:31,779 --> 00:13:34,539
我们一般用不到发消息啊啊小游戏

288
00:13:34,539 --> 00:13:36,129
如果你编写的项目啊

289
00:13:36,129 --> 00:13:39,600
这个是嗯中大型项目了啊

290
00:13:39,600 --> 00:13:41,519
需要一个比较大的一个游戏框架

291
00:13:41,519 --> 00:13:42,590
游戏系统了

292
00:13:42,590 --> 00:13:46,340
那么推荐大家自己去写一套消息框架啊

293
00:13:46,340 --> 00:13:47,990
实现自己的一套消息框架

294
00:13:47,990 --> 00:13:51,019
用起来应该是最顺手最顺手的啊

295
00:13:51,259 --> 00:13:56,110
嗯ok框架的话我们也是后面我们会说一下

296
00:13:56,110 --> 00:14:00,820
那么现在我们先把这个系统的这两种消息模式说完就ok了

297
00:14:00,820 --> 00:14:03,610
那么这节课我们就先说这么多啊

298
00:14:03,610 --> 00:14:07,129
其实重点内容主要就是三种啊

299
00:14:07,129 --> 00:14:09,320
鼠标键盘触摸事件啊

300
00:14:09,320 --> 00:14:11,570
把这三种搞定就ok了

301
00:14:11,570 --> 00:14:13,899
ok那我们这节课就这么多

302
00:14:14,320 --> 00:14:15,320
略略略


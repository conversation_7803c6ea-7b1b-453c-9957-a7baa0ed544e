1
00:00:08,800 --> 00:00:11,679
ok那这节课我们来说一下微信小游戏的发布

2
00:00:11,679 --> 00:00:15,189
那实际上微信小游戏就可以算是一个事例啊

3
00:00:15,189 --> 00:00:18,370
如果你会在这个微信上面发布消息

4
00:00:18,370 --> 00:00:19,510
那么其他平台

5
00:00:19,510 --> 00:00:22,629
比如说我们那会儿看到的有什么oppo vivo的啊

6
00:00:22,629 --> 00:00:24,489
基本上也都大同小异啊

7
00:00:24,489 --> 00:00:25,368
大同小异

8
00:00:25,368 --> 00:00:27,228
那么在他们的公众平台上面

9
00:00:27,228 --> 00:00:29,329
按照我们这个微信这个操作流程

10
00:00:29,329 --> 00:00:31,010
基本上也就差不多了

11
00:00:31,010 --> 00:00:33,518
那么在这个微信公众平台上面

12
00:00:34,899 --> 00:00:40,899
大家首先做的第一件事就是要申请一个微信公众平台的账号啊

13
00:00:40,899 --> 00:00:43,609
要注册一个微信公众平台的账号

14
00:00:43,609 --> 00:00:45,950
那么注册的类型就是小程序类型的

15
00:00:45,950 --> 00:00:48,950
因为我们小游戏也属于小程序啊

16
00:00:48,950 --> 00:00:52,549
然后注册一个微信公众平台小程序啊

17
00:00:52,549 --> 00:00:55,609
这样的一个账号注册完以后登录登录了以后

18
00:00:55,609 --> 00:00:57,698
你就可以进到这样的一个界面了

19
00:00:57,899 --> 00:00:59,880
那么注册的话非常简单啊

20
00:00:59,880 --> 00:01:01,740
你就输入一个邮箱就可以了啊

21
00:01:01,740 --> 00:01:02,460
输入一个邮箱

22
00:01:02,460 --> 00:01:03,299
然后输嗯

23
00:01:03,299 --> 00:01:06,939
然后嗯他给你邮箱发个这个链接啊

24
00:01:06,939 --> 00:01:09,159
点击链接在邮箱里面点击链接

25
00:01:09,159 --> 00:01:10,989
然后就注册完成了

26
00:01:14,060 --> 00:01:16,540
那么首先第一件事啊

27
00:01:16,540 --> 00:01:17,920
在这里大家发现有两步

28
00:01:17,920 --> 00:01:18,549
对不对

29
00:01:18,549 --> 00:01:20,980
如果你是第一次呃

30
00:01:20,980 --> 00:01:21,640
进来的话

31
00:01:21,640 --> 00:01:23,290
在这里有个小程序信息

32
00:01:23,290 --> 00:01:24,579
我这里是已完成

33
00:01:24,579 --> 00:01:26,260
大家应该在这里还是未完成

34
00:01:26,260 --> 00:01:31,090
你在这里需要点击这个按钮去进行小程序信息的这个补充

35
00:01:31,090 --> 00:01:32,200
其实很简单

36
00:01:32,200 --> 00:01:34,890
在里面就是他这里面说的

37
00:01:34,890 --> 00:01:37,829
你需要准备一个小程序的名称啊

38
00:01:37,829 --> 00:01:40,810
这个就是其实就是我们的游戏名称

39
00:01:40,810 --> 00:01:44,409
游戏的这个小小程序的这样的一个图标啊

40
00:01:44,409 --> 00:01:45,700
你需要准备一个图标

41
00:01:45,700 --> 00:01:47,590
再准备一个描述啊

42
00:01:47,590 --> 00:01:50,179
对当前这个小程序的一个描述

43
00:01:50,459 --> 00:01:54,920
那么写完以后点击完成就变成已完成了啊

44
00:01:54,920 --> 00:01:56,329
就变成已完成了

45
00:01:56,329 --> 00:01:59,120
那么在这里大家可以看到左边有一六信息

46
00:01:59,120 --> 00:01:59,859
对不对

47
00:02:01,180 --> 00:02:05,640
那么当你这个小程序信息补充完成的时候

48
00:02:05,640 --> 00:02:09,360
我们最重要的就是要在这边啊

49
00:02:09,360 --> 00:02:10,620
点击设置

50
00:02:10,620 --> 00:02:13,590
那么在设置这边我们选择开发设置

51
00:02:13,590 --> 00:02:17,120
在开发设置这边大家就可以看到一个开发者

52
00:02:17,139 --> 00:02:19,659
然后这个是我们要的最重要的东西

53
00:02:19,659 --> 00:02:21,520
就是说我们创建完我们这个账号

54
00:02:21,520 --> 00:02:23,829
最重要的就是要获取到这样一个id

55
00:02:23,829 --> 00:02:27,009
通过这个id我们就可以啊

56
00:02:27,009 --> 00:02:28,509
就相当于身份证号啊

57
00:02:28,509 --> 00:02:30,849
就每一个程序有一个身份证号啊

58
00:02:30,849 --> 00:02:32,800
这个就是我们小程序的身份证号

59
00:02:32,800 --> 00:02:34,120
通过这个身份证号

60
00:02:37,889 --> 00:02:42,000
并且可以传到我们这个微信的这样的一个平台上面

61
00:02:42,000 --> 00:02:47,759
那么这个账号一定不要让其他人知道啊

62
00:02:47,759 --> 00:02:49,800
就是这个账号是自己保存的啊

63
00:02:49,800 --> 00:02:51,099
是自己保存的

64
00:02:53,438 --> 00:02:54,959
就是怎么说啊

65
00:02:54,959 --> 00:02:58,229
就是说相当于你要把身份证告诉给其他人

66
00:02:58,229 --> 00:03:01,969
那其他人可能就会啊利用你这个身份证啊

67
00:03:01,969 --> 00:03:06,610
然后他就会给你这个呃就会可能会去做一些不好的事啊

68
00:03:06,610 --> 00:03:07,090
对不对

69
00:03:07,090 --> 00:03:11,520
所以在这里对这个i p a p p i d而言啊

70
00:03:12,778 --> 00:03:16,219
尽量啊就是自己保存就可以了啊

71
00:03:16,259 --> 00:03:18,780
呃那么在这里拿到这个以后

72
00:03:18,780 --> 00:03:20,580
我们接下来就要做什么操作了

73
00:03:20,580 --> 00:03:22,900
接下来我们选择首页

74
00:03:23,658 --> 00:03:26,498
在这里有个小游戏开发者工具

75
00:03:26,498 --> 00:03:27,659
点击它

76
00:03:32,439 --> 00:03:38,019
那么到了这里我们要下载一下它的这样的一个开发者工具啊

77
00:03:38,019 --> 00:03:38,739
有64位

78
00:03:38,739 --> 00:03:39,870
32位啊

79
00:03:39,870 --> 00:03:41,370
这个是看你的系统

80
00:03:41,370 --> 00:03:42,360
还有苹果系统

81
00:03:42,360 --> 00:03:43,530
我们在这里啊

82
00:03:43,530 --> 00:03:45,780
我下载的是windows 64位的

83
00:03:45,780 --> 00:03:47,259
下载完以后安装

84
00:03:47,259 --> 00:03:48,460
安装完以后

85
00:03:48,460 --> 00:03:48,879
我们

86
00:03:51,400 --> 00:03:52,090
打开它

87
00:03:52,090 --> 00:03:53,319
我们打开它

88
00:03:59,558 --> 00:04:00,419
打开它

89
00:04:00,419 --> 00:04:01,348
打开它以后

90
00:04:01,348 --> 00:04:04,438
它让你扫扫描一下二维码啊

91
00:04:04,438 --> 00:04:05,338
扫描一下二维码

92
00:04:05,338 --> 00:04:06,598
你扫描二维码

93
00:04:06,598 --> 00:04:09,919
然后扫描完以后就可以登录成功了

94
00:04:09,919 --> 00:04:11,259
登录成功以后

95
00:04:11,259 --> 00:04:14,080
在这里大家就可以看到有两个选项啊

96
00:04:14,080 --> 00:04:16,000
一个是小程序项目

97
00:04:16,000 --> 00:04:17,560
一个是公众号网页项目

98
00:04:17,560 --> 00:04:18,879
对不对啊

99
00:04:18,879 --> 00:04:20,439
在这里你都可以点击

100
00:04:20,439 --> 00:04:22,560
然后就可以打开一个页面啊

101
00:04:24,879 --> 00:04:27,000
但是啊只要你把它安装好

102
00:04:27,000 --> 00:04:28,500
一定要登录一次啊

103
00:04:28,500 --> 00:04:29,970
就是扫描二维码

104
00:04:29,970 --> 00:04:31,079
一定要登录一次

105
00:04:31,079 --> 00:04:34,899
最起码你要进入一次到这个界面

106
00:04:34,899 --> 00:04:37,600
这样的话他才知道你这个是登录成功的啊

107
00:04:37,600 --> 00:04:38,600
是有权限的

108
00:04:38,639 --> 00:04:41,160
登录完以后你就可以把它关掉了

109
00:04:41,160 --> 00:04:42,540
接下来做什么操作

110
00:04:42,540 --> 00:04:46,220
在这边项目这边选择构建发布

111
00:04:46,220 --> 00:04:47,459
选择微信

112
00:04:49,939 --> 00:04:51,639
在微信这边啊

113
00:04:51,639 --> 00:04:56,019
在微信这边我们往下去看啊

114
00:04:56,019 --> 00:04:58,300
往下去看前面的内容是一样的

115
00:04:58,300 --> 00:05:01,480
在这里有个设备方向有一个竖屏横屏的啊

116
00:05:01,480 --> 00:05:03,819
在这里你可以去选择portrait

117
00:05:03,819 --> 00:05:04,569
就是竖屏

118
00:05:04,569 --> 00:05:06,259
lscape就是横屏

119
00:05:06,259 --> 00:05:07,939
然后在这里有个a p p i d

120
00:05:07,939 --> 00:05:11,209
这个p i d默认它会给你一个测试用的啊

121
00:05:11,209 --> 00:05:14,819
但是你在这里就要去选择你的a p i d了啊

122
00:05:14,819 --> 00:05:17,040
比如说我这里就用它测试用的

123
00:05:17,740 --> 00:05:21,959
那么然后接下来有两个内容可不填啊

124
00:05:21,959 --> 00:05:24,439
这个是个远程的服务器地址

125
00:05:25,100 --> 00:05:29,139
那么我们在这里啊默认就不提它了

126
00:05:29,139 --> 00:05:30,480
我们选择构建

127
00:05:36,899 --> 00:05:38,060
构建完成以后

128
00:05:38,060 --> 00:05:40,939
我们仍然是我们在这里可以打开运行啊

129
00:05:40,939 --> 00:05:42,300
我们可以打开运行

130
00:05:42,560 --> 00:05:44,139
点击一下运行

131
00:05:46,660 --> 00:05:50,490
大家可以看它的这个开发者工具自动就打开了

132
00:05:50,490 --> 00:05:52,019
打开了以后

133
00:05:52,579 --> 00:05:55,119
你看这就到了一个预览界面了

134
00:05:55,119 --> 00:05:56,468
当前是iphone 5

135
00:05:56,468 --> 00:06:01,220
然后在iphone 5手机上面打开咱们这个微信小程序是个什么样的情况

136
00:06:01,220 --> 00:06:04,579
唉你可以在这里去进行这个游玩了

137
00:06:04,579 --> 00:06:05,379
对不对

138
00:06:07,779 --> 00:06:10,139
那么在这里啊你可以去选择

139
00:06:10,139 --> 00:06:11,819
就是其他的这个不同设备

140
00:06:11,819 --> 00:06:13,829
在其他设备上上面看一下

141
00:06:13,829 --> 00:06:16,439
打开我们这个小游戏会是什么样的一个情况

142
00:06:16,439 --> 00:06:19,740
那在这里上面就会有很多这个按钮啊

143
00:06:19,740 --> 00:06:21,149
又会就会有很多按钮

144
00:06:21,149 --> 00:06:23,519
那么在这里你就可以去进行最终的

145
00:06:23,519 --> 00:06:26,250
比如说上传啊什么的啊

146
00:06:26,250 --> 00:06:28,470
但是这里上传我这里就不能上传了

147
00:06:28,470 --> 00:06:29,199
因为

148
00:06:31,420 --> 00:06:33,600
咱们的这个app id啊

149
00:06:33,600 --> 00:06:36,588
选择的是呃测试用的啊

150
00:06:36,588 --> 00:06:37,728
如果你要上传的话

151
00:06:37,728 --> 00:06:41,740
你要把那个a p a i d设置为你自己的a p p i d

152
00:06:43,959 --> 00:06:47,730
ok那么其实大概啊我们就是这样一回事啊

153
00:06:47,730 --> 00:06:51,120
只是我们现在没有去最终的进行一个上传

154
00:06:51,120 --> 00:06:55,288
那么大家把这个按照这一系列操作啊

155
00:06:55,288 --> 00:06:56,369
设置完成以后

156
00:06:56,369 --> 00:07:00,028
你在这边就可以进行真正的这个上传了啊

157
00:07:00,028 --> 00:07:02,548
但是a p p i d啊一定要填对啊

158
00:07:05,528 --> 00:07:07,658
a p i d一定要填你自己的

159
00:07:07,658 --> 00:07:12,819
这样的话上传以后他就会把这个游戏上传到你的这个账号下面啊

160
00:07:12,819 --> 00:07:14,389
上传到你的账号下面

161
00:07:14,389 --> 00:07:16,160
那么大概就是这么一回事

162
00:07:16,160 --> 00:07:20,449
然后剩下的大家就可以对这个工具其他功能去做一些了解啊

163
00:07:20,449 --> 00:07:25,829
以及如果你要真正去做这个微信开发的话啊

164
00:07:25,829 --> 00:07:29,310
你需要在这里把整个微信公众平台啊

165
00:07:29,310 --> 00:07:31,350
它的左边这一溜内容啊

166
00:07:31,350 --> 00:07:32,670
要把它搞清楚啊

167
00:07:32,670 --> 00:07:34,329
要把它搞清楚啊

168
00:07:34,329 --> 00:07:35,528
是这么回事

169
00:07:35,528 --> 00:07:37,928
那我最后在这里上传一下好了

170
00:07:37,928 --> 00:07:39,250
让大家看一下

171
00:07:41,350 --> 00:07:43,149
用我的这个a p p i d啊

172
00:07:44,350 --> 00:07:45,610
那这样的话啊

173
00:07:45,610 --> 00:07:47,410
这个就是身份证号算是对上了

174
00:07:47,410 --> 00:07:48,000
对不对

175
00:07:48,000 --> 00:07:51,220
然后在这里我们点击上传

176
00:07:51,899 --> 00:07:57,038
点确定它会在这里让我们输入一个版本号和一个项目备注

177
00:07:57,038 --> 00:07:59,499
这个版本号就看你怎样输了

178
00:07:59,499 --> 00:08:01,298
一般最开始就是1.1几

179
00:08:01,298 --> 00:08:02,079
对不对

180
00:08:02,079 --> 00:08:03,759
那么项目备注啊

181
00:08:03,759 --> 00:08:04,540
你就可以写歌

182
00:08:04,540 --> 00:08:06,009
比如说我这里是测试

183
00:08:06,009 --> 00:08:07,699
然后点击上传

184
00:08:09,279 --> 00:08:11,439
那么在这里注意一点啊

185
00:08:11,819 --> 00:08:15,420
呃咱们之前讲过很多很多技术

186
00:08:15,420 --> 00:08:17,339
比如说用这个小地图啊

187
00:08:17,339 --> 00:08:21,879
就瓦片地图那一个地图其实容量已经非常大了

188
00:08:21,879 --> 00:08:22,300
对不对

189
00:08:22,300 --> 00:08:25,990
所以如果是呃做的瓦片地图那一类的游戏

190
00:08:25,990 --> 00:08:29,350
那这个游戏下来整个容量就已经非常大了

191
00:08:29,350 --> 00:08:33,250
而我们微信游戏它是有容量限制的啊

192
00:08:33,250 --> 00:08:38,200
它的这个容量限制就是基本上大概在十兆左右啊

193
00:08:38,200 --> 00:08:40,600
所以说如果你要做微信游戏

194
00:08:40,600 --> 00:08:43,000
一般都是非常小型的游戏啊

195
00:08:43,000 --> 00:08:45,169
一般都是非常小型的游戏啊

196
00:08:45,169 --> 00:08:46,669
咱们之前讲过很多技术

197
00:08:46,669 --> 00:08:48,620
只是在技术层面去用到它

198
00:08:48,620 --> 00:08:51,470
如果你要真正的去做微信小游戏的话

199
00:08:51,470 --> 00:08:53,600
可能你就压根用不上那些东西

200
00:08:53,600 --> 00:08:56,179
因为微信小游戏都是比较简单的

201
00:08:56,179 --> 00:08:58,279
容量比较小的这样的游戏

202
00:08:58,279 --> 00:09:02,198
然后在这边的话我们上传上传完成以后

203
00:09:02,940 --> 00:09:04,730
我们打开公众平台

204
00:09:04,730 --> 00:09:06,919
在这边有个首页开发管理

205
00:09:06,919 --> 00:09:08,419
你点击开发管理

206
00:09:08,419 --> 00:09:11,100
在这里就有一个开发版本啊

207
00:09:11,100 --> 00:09:13,409
开发版本就是我们上传的版本

208
00:09:13,409 --> 00:09:15,698
然后在这里有个提交审核

209
00:09:16,080 --> 00:09:17,399
提交审核的话

210
00:09:17,399 --> 00:09:18,960
你可以继续提交啊

211
00:09:18,960 --> 00:09:20,019
继续提交

212
00:09:20,019 --> 00:09:22,899
然后在这里下一步提交完了以后

213
00:09:22,899 --> 00:09:26,379
你就这个版本就就开始审核了

214
00:09:26,379 --> 00:09:28,539
如果审核完成以后

215
00:09:28,799 --> 00:09:33,000
那么它就会变成上线版本变成线上版本

216
00:09:33,000 --> 00:09:35,460
然后你就可以在在这个微信里面

217
00:09:35,460 --> 00:09:38,879
小游戏这边去看到你的这样的一个游戏了啊

218
00:09:38,879 --> 00:09:42,759
ok那么大概上传就是这样的一回事

219
00:09:42,759 --> 00:09:45,460
那么其他功能大家可以自己去研究啊

220
00:09:45,460 --> 00:09:46,509
我们就说了这么多

221
00:09:46,509 --> 00:09:47,799
那么这节课就这么多

222
00:09:47,799 --> 00:09:49,840
内容略略略


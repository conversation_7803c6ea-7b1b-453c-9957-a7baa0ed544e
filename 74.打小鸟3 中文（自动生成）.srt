1
00:00:09,140 --> 00:00:11,910
ok这节课我们来写这个管理类

2
00:00:11,910 --> 00:00:14,130
那首先这个小鸟已经写完了

3
00:00:14,130 --> 00:00:16,829
那我们把这个小鸟拖成一个预设体

4
00:00:16,829 --> 00:00:18,329
然后把它删除掉啊

5
00:00:18,329 --> 00:00:19,410
就默认它是没有的

6
00:00:19,410 --> 00:00:21,940
我们通过代码去创建很多小鸟

7
00:00:22,000 --> 00:00:26,920
那么在这边我们到管理类这边来啊

8
00:00:29,719 --> 00:00:31,269
呃在他的里面

9
00:00:31,269 --> 00:00:32,679
我们来写一下

10
00:00:32,679 --> 00:00:33,518
首先

11
00:00:35,399 --> 00:00:38,000
把我们必须写出来的属性啊

12
00:00:38,000 --> 00:00:41,579
就是写出来第一个就是小鸟的一个预设体

13
00:00:41,899 --> 00:00:44,539
通过预设体我们可以创建出很多的小鸟

14
00:00:44,539 --> 00:00:45,378
对不对

15
00:00:55,000 --> 00:00:55,979
prefab

16
00:00:57,119 --> 00:00:59,280
那么小鸟的预设题

17
00:00:59,280 --> 00:01:05,379
有了小鸟的这个多长时间

18
00:01:05,379 --> 00:01:07,060
我们让它产生一只小鸟

19
00:01:07,060 --> 00:01:10,399
我们就一秒出现一只小鸟

20
00:01:11,659 --> 00:01:13,760
看啊

21
00:01:13,760 --> 00:01:14,840
这是我们默认的啊

22
00:01:14,840 --> 00:01:16,010
当然你可以去修改

23
00:01:16,010 --> 00:01:17,659
我们再加个分数啊

24
00:01:17,659 --> 00:01:19,069
我们这个是有分值的

25
00:01:19,069 --> 00:01:20,959
打死一个小鸟啊

26
00:01:20,959 --> 00:01:22,698
比如说我们加上多少分

27
00:01:22,698 --> 00:01:23,459
对不对

28
00:01:24,739 --> 00:01:26,560
我们先给这些东西啊

29
00:01:26,560 --> 00:01:28,090
我们先给这些东西

30
00:01:28,090 --> 00:01:30,219
ok

31
00:01:33,219 --> 00:01:36,540
在这里start这边我们就可以先开始去写了啊

32
00:01:36,540 --> 00:01:37,920
一会儿我们缺个什么属性

33
00:01:37,920 --> 00:01:39,840
我们再回来去补啊

34
00:01:39,939 --> 00:01:42,459
嗯start这边我们就可以啊

35
00:01:42,459 --> 00:01:47,039
每隔一秒创建一只小鸟

36
00:01:48,739 --> 00:01:51,459
this.node.run action

37
00:01:51,459 --> 00:01:53,560
我们也要执行一个动画啊

38
00:01:53,560 --> 00:01:55,219
执行一个动作什么动作

39
00:01:55,219 --> 00:01:55,939
大家注意啊

40
00:01:55,939 --> 00:01:57,200
现在我们要做一个定时器

41
00:01:57,200 --> 00:01:57,739
对不对

42
00:01:57,739 --> 00:01:59,750
每隔一秒做一件事啊

43
00:01:59,750 --> 00:02:02,420
其实我们用这个呃

44
00:02:03,079 --> 00:02:05,260
除了我们之前讲过的那个定时器啊

45
00:02:05,260 --> 00:02:07,420
我们用这个动作啊也是可以执行的

46
00:02:07,420 --> 00:02:08,710
大家来看一下啊

47
00:02:08,710 --> 00:02:13,778
我们可以先让他最外层放一个repeat forever啊

48
00:02:13,778 --> 00:02:18,399
这是我们之前说的这个呃永远执行一个动作啊

49
00:02:18,399 --> 00:02:18,818
对不对

50
00:02:18,818 --> 00:02:20,438
只要里面填一个动作啊

51
00:02:20,438 --> 00:02:24,150
那么它呢就会永远去执行这里面的这个动作

52
00:02:24,150 --> 00:02:26,610
那么他永远执行的动作是什么

53
00:02:26,610 --> 00:02:28,439
cc.s quence

54
00:02:28,639 --> 00:02:29,180
哎

55
00:02:29,180 --> 00:02:30,680
又是一个队列动作啊

56
00:02:30,680 --> 00:02:32,599
就是他永远执行一个队列动作

57
00:02:35,509 --> 00:02:38,348
那我们就可以在这里永远是这些动作

58
00:02:38,348 --> 00:02:39,068
一个执行完

59
00:02:39,068 --> 00:02:40,808
一个执行完一个执行完啊

60
00:02:40,808 --> 00:02:41,949
按顺序执行完一遍

61
00:02:41,949 --> 00:02:42,788
然后再来一遍

62
00:02:42,788 --> 00:02:43,389
再来一遍

63
00:02:43,389 --> 00:02:44,769
就变成一个循环执行了

64
00:02:44,769 --> 00:02:45,460
对不对

65
00:02:45,460 --> 00:02:48,580
然后在这里面这个队列里面我们就给他两个动作

66
00:02:48,580 --> 00:02:52,169
第一个是个延时动作啊

67
00:02:52,169 --> 00:02:53,280
就是延时动作

68
00:02:53,280 --> 00:02:55,409
比如说我们给他一个this.time

69
00:02:55,409 --> 00:02:56,819
就是多长时间

70
00:02:57,019 --> 00:02:58,278
没过多长时间

71
00:02:58,278 --> 00:02:59,899
我们再让他执行第二个动作

72
00:02:59,899 --> 00:03:01,788
第二个动作就是一个回调

73
00:03:01,788 --> 00:03:04,280
就是一个call funk

74
00:03:05,718 --> 00:03:07,139
就是一个回调

75
00:03:08,338 --> 00:03:12,498
那这样的话我们就做成了一个循环的一个操作啊

76
00:03:12,498 --> 00:03:15,199
就是每隔一秒会进来执行这里面的方法

77
00:03:15,199 --> 00:03:17,009
一次每隔一秒执行一次

78
00:03:17,009 --> 00:03:18,629
每隔一秒执行一次啊

79
00:03:18,629 --> 00:03:20,479
就变成这样啊

80
00:03:21,278 --> 00:03:25,079
那当然大家如果用之前咱们说的那个定时器的话

81
00:03:25,079 --> 00:03:26,038
也是可以的

82
00:03:26,038 --> 00:03:27,269
计时器也是可以的

83
00:03:27,269 --> 00:03:30,000
在这里我们来创建小鸟

84
00:03:33,118 --> 00:03:38,849
我们在这里意思就是每一种方式我们都可以去用一用

85
00:03:38,849 --> 00:03:42,329
然后大家不要去问哪个方式好啊

86
00:03:42,329 --> 00:03:43,889
这些方式都是存在的

87
00:03:43,889 --> 00:03:45,209
证明你都可以用啊

88
00:03:45,209 --> 00:03:48,530
还是我之前说的代码只要做出来都是好的啊

89
00:03:48,530 --> 00:03:50,030
你如果用这种方法

90
00:03:50,030 --> 00:03:50,750
你觉得不行

91
00:03:50,750 --> 00:03:52,870
你可以换另外一种方法再去用

92
00:03:52,870 --> 00:03:53,650
对不对

93
00:03:53,650 --> 00:03:55,960
但是没有说啊

94
00:03:55,960 --> 00:03:58,569
你直接就是说诶哪种方法好

95
00:03:58,569 --> 00:04:00,430
我以后就用哪种方法啊

96
00:04:00,430 --> 00:04:01,030
没必要啊

97
00:04:01,030 --> 00:04:05,110
比如说用定时器那几种和这种我到底用哪种啊

98
00:04:05,110 --> 00:04:06,849
这个就是最开始你愿意用哪种

99
00:04:06,849 --> 00:04:07,659
用哪种

100
00:04:07,659 --> 00:04:09,129
当你用

101
00:04:09,129 --> 00:04:10,780
比如说你用这种方法诶

102
00:04:10,780 --> 00:04:12,280
有时候会有问题了

103
00:04:12,280 --> 00:04:13,120
怎么办

104
00:04:13,120 --> 00:04:15,039
你就可以换另外一种方法试一下

105
00:04:15,039 --> 00:04:16,490
对不对啊

106
00:04:16,490 --> 00:04:17,569
程序员都是这样的

107
00:04:19,310 --> 00:04:20,329
创建好小点以后

108
00:04:22,740 --> 00:04:24,079
小鸟的附体

109
00:04:24,079 --> 00:04:26,600
我们应该设置给这个manager啊

110
00:04:26,600 --> 00:04:28,339
就是自身这个节点

111
00:04:28,879 --> 00:04:30,500
所以在这里

112
00:04:34,360 --> 00:04:36,420
我们设置副题

113
00:04:36,420 --> 00:04:41,800
我们就可以bird.set parent就是this.node

114
00:04:41,800 --> 00:04:44,079
this.node就是当前这个管理器了啊

115
00:04:44,079 --> 00:04:44,860
对不对

116
00:04:44,920 --> 00:04:47,500
然后这个小鸟的起始点

117
00:04:47,500 --> 00:04:51,279
它的y轴坐标和我们目前的y轴坐标一样啊

118
00:04:51,279 --> 00:04:55,699
因为我们这个管理器这个空节点就是在这个位置啊

119
00:04:56,399 --> 00:04:59,120
所以y轴啊我们设置一下

120
00:04:59,120 --> 00:05:00,319
就和当年管理类

121
00:05:00,319 --> 00:05:03,439
我们给它放放到同一个y轴上

122
00:05:03,439 --> 00:05:04,730
x轴的话

123
00:05:04,730 --> 00:05:06,500
我们要让它产生一个随机

124
00:05:06,500 --> 00:05:07,639
从这边到这边

125
00:05:07,639 --> 00:05:09,839
和上面的那个随机一样

126
00:05:09,939 --> 00:05:11,319
和上面的随机一样

127
00:05:11,319 --> 00:05:15,459
所以它的这个随机数值也是一个mass点

128
00:05:15,459 --> 00:05:20,370
random乘以个220减去个110

129
00:05:20,370 --> 00:05:20,939
对不对

130
00:05:20,939 --> 00:05:24,060
也是从-一到正一零之间有个随机

131
00:05:24,319 --> 00:05:28,920
然后这样的话这两个就是设置小鸟的这个位置

132
00:05:31,079 --> 00:05:34,220
然后我们继续啊继续

133
00:05:36,120 --> 00:05:37,100
这边完了以后

134
00:05:37,100 --> 00:05:37,879
我们想想

135
00:05:37,879 --> 00:05:40,639
我们让小鸟就进行运动啊

136
00:05:40,639 --> 00:05:42,290
让小鸟去这个飞

137
00:05:42,290 --> 00:05:46,959
我们就可以怎样做的点fly啊

138
00:05:46,959 --> 00:05:48,579
我们要得到小鸟身上

139
00:05:48,579 --> 00:05:53,240
我们的这个脚本bd control这个脚本get component这个脚本

140
00:05:54,879 --> 00:05:55,920
也不是cc了

141
00:05:55,920 --> 00:05:58,980
直接就是bd control找到这个脚本

142
00:05:58,980 --> 00:06:00,680
然后就可以fly

143
00:06:00,759 --> 00:06:03,939
那这时候大家就可以看一下是不是咱们上节课说的

144
00:06:03,939 --> 00:06:07,459
因为我们这个管理类里面创建了小鸟

145
00:06:07,459 --> 00:06:09,860
所以我们调小鸟里面的内容很好调用

146
00:06:09,860 --> 00:06:11,899
直接用点语法调用就可以了啊

147
00:06:11,899 --> 00:06:13,629
就可以调到它里面的方法

148
00:06:13,629 --> 00:06:17,389
但是我如果是父类里面的方法呃

149
00:06:17,389 --> 00:06:19,490
这个这个不应该叫副副词类啊

150
00:06:19,490 --> 00:06:22,730
应该是比如说我这个管理类里面啊

151
00:06:22,730 --> 00:06:23,810
我创建一个方法

152
00:06:23,810 --> 00:06:26,060
然后在它里面想调用

153
00:06:26,180 --> 00:06:28,660
那就必须通过这个回调方式了啊

154
00:06:32,740 --> 00:06:33,600
嗯

155
00:06:36,199 --> 00:06:37,178
我们看一下啊

156
00:06:37,178 --> 00:06:38,588
让小鸟飞

157
00:06:38,588 --> 00:06:40,119
现在先试一下啊

158
00:06:40,119 --> 00:06:41,319
现在先试一下

159
00:06:41,319 --> 00:06:44,120
应该就已经能正常执行了

160
00:06:49,600 --> 00:06:51,149
这是卡住了吗

161
00:06:51,149 --> 00:06:52,720
我们哪里有问题

162
00:06:54,899 --> 00:06:57,920
set parent不能读取属性

163
00:06:57,920 --> 00:06:59,420
我们看一下它报错了

164
00:06:59,420 --> 00:07:00,920
然后我们可以打开输出

165
00:07:00,920 --> 00:07:03,069
大家可以看到有错误啊

166
00:07:03,069 --> 00:07:04,360
不能读取属性

167
00:07:04,360 --> 00:07:05,379
set parent

168
00:07:05,379 --> 00:07:06,579
我们来看一下啊

169
00:07:06,579 --> 00:07:10,579
set parent也就是说啊

170
00:07:10,579 --> 00:07:11,538
这里有错啊

171
00:07:11,538 --> 00:07:12,499
不能读取这个属性

172
00:07:12,499 --> 00:07:13,459
就证明他有错

173
00:07:13,459 --> 00:07:13,879
他有错

174
00:07:13,879 --> 00:07:17,089
就证明我们在这里没有创建出来

175
00:07:17,089 --> 00:07:19,189
这个小鸟为什么没创建出来

176
00:07:19,189 --> 00:07:23,478
因为我们这边没有关联这个预设体啊

177
00:07:23,478 --> 00:07:25,038
所以找错误就是这样去找的

178
00:07:25,038 --> 00:07:25,839
对不对

179
00:07:27,639 --> 00:07:28,980
再运行一下

180
00:07:32,259 --> 00:07:34,079
小鸟是不是就出来了

181
00:07:34,079 --> 00:07:36,329
我们就可以去点小鸟了啊

182
00:07:36,329 --> 00:07:38,819
那么这时候停了

183
00:07:42,600 --> 00:07:44,259
我们看一下

184
00:07:47,360 --> 00:07:48,620
又报错了啊

185
00:07:48,620 --> 00:07:50,360
这是回调没有设置啊

186
00:07:50,360 --> 00:07:51,740
这个我们不用管啊

187
00:07:51,740 --> 00:07:54,019
总之就是现在已经可以飞出来了

188
00:07:54,019 --> 00:07:54,819
对不对

189
00:07:54,920 --> 00:07:56,240
回调没设置

190
00:07:56,240 --> 00:07:59,569
是因为我们这边还有两个属性没有去写

191
00:07:59,569 --> 00:08:00,680
一个就是小鸟

192
00:08:00,680 --> 00:08:01,819
我们知道有两个回调

193
00:08:01,819 --> 00:08:02,120
对不对

194
00:08:02,120 --> 00:08:03,500
这是我们去写的

195
00:08:03,500 --> 00:08:04,759
一个是加分回调

196
00:08:04,759 --> 00:08:05,959
一个是游戏结束回调

197
00:08:05,959 --> 00:08:06,500
在这边

198
00:08:06,500 --> 00:08:07,620
首先是格

199
00:08:09,259 --> 00:08:10,500
加分吧

200
00:08:12,560 --> 00:08:17,740
一个是个游戏结束丢掉加分

201
00:08:17,740 --> 00:08:20,649
回调的话就是bird.get

202
00:08:20,649 --> 00:08:22,720
首先拿到我们这个脚本

203
00:08:25,240 --> 00:08:30,120
脚本里面有一个加分的一个回调啊

204
00:08:30,120 --> 00:08:31,740
我们就可以直接

205
00:08:34,360 --> 00:08:35,909
给他写一个回调

206
00:08:35,909 --> 00:08:38,918
那么游戏结束回调是一样的

207
00:08:44,659 --> 00:08:49,259
游戏结束回调就是我们的死亡回调

208
00:08:51,919 --> 00:08:54,219
我们也给他写一个回调方法

209
00:08:55,200 --> 00:09:00,559
ok那么在这里面我们就就可以去写加分和结束的这个操作了

210
00:09:00,559 --> 00:09:02,779
那这样的话逻辑才是对的啊

211
00:09:02,779 --> 00:09:04,409
因为就是说这样的话

212
00:09:04,409 --> 00:09:06,870
我们的加分不管是哪个小鸟的加分

213
00:09:06,870 --> 00:09:09,299
最后都会到跳到这个方法里

214
00:09:10,360 --> 00:09:14,220
然后不管是因为哪个小鸟造成的

215
00:09:14,220 --> 00:09:16,200
游戏结束都会到这个方法里

216
00:09:16,200 --> 00:09:17,669
这个便于我们管理

217
00:09:17,669 --> 00:09:18,960
而边缘我们管理

218
00:09:18,960 --> 00:09:21,149
而放在这个方法里面就太散了

219
00:09:21,149 --> 00:09:22,950
每一个小鸟都有这样一个脚本

220
00:09:22,950 --> 00:09:23,850
就不好管理了

221
00:09:23,850 --> 00:09:24,990
而这个脚本只有一个

222
00:09:24,990 --> 00:09:25,779
对不对

223
00:09:26,299 --> 00:09:29,059
那所以我们在这儿就可以直接去写加分的操作了

224
00:09:29,059 --> 00:09:31,578
比如说this.call

225
00:09:32,340 --> 00:09:34,879
每一次我们都让他加100分

226
00:09:34,879 --> 00:09:35,779
再加100分

227
00:09:35,779 --> 00:09:36,578
对不对

228
00:09:36,860 --> 00:09:38,840
但是我们现在还没有ui啊

229
00:09:38,840 --> 00:09:39,740
先不用管ui

230
00:09:39,740 --> 00:09:41,839
总之现在就是这个分数是有的

231
00:09:42,159 --> 00:09:43,629
然后死亡的话

232
00:09:43,629 --> 00:09:45,100
每次游戏结束

233
00:09:45,100 --> 00:09:47,019
游戏结束我都怎样

234
00:09:47,019 --> 00:09:51,669
我觉得这点note删除当前所有的小鸟

235
00:09:51,669 --> 00:09:54,519
当前所有的小鸟是不是都是我的子节点

236
00:09:54,519 --> 00:09:58,958
所以我可以直接与一个删除所有的子节点

237
00:09:59,820 --> 00:10:01,620
删除所有的子节点

238
00:10:01,620 --> 00:10:04,919
并且我要停止这个循环

239
00:10:05,279 --> 00:10:07,080
我不能再创建小鸟了啊

240
00:10:07,080 --> 00:10:08,730
就让这个循环停止下来

241
00:10:08,730 --> 00:10:12,620
所以就是this.node.stop or action

242
00:10:13,519 --> 00:10:14,840
就是停止所有动作

243
00:10:14,840 --> 00:10:16,700
那这个循环就停了啊

244
00:10:16,700 --> 00:10:21,399
那这样的话目前我们这个就算是完事了

245
00:10:21,399 --> 00:10:22,360
我们可以试一下

246
00:10:22,360 --> 00:10:24,309
先可以先输出试一下

247
00:10:24,309 --> 00:10:25,179
consolute

248
00:10:25,179 --> 00:10:27,360
点debug this

249
00:10:27,360 --> 00:10:28,080
点score

250
00:10:28,080 --> 00:10:31,440
把这个分数给它输出出来

251
00:10:31,440 --> 00:10:34,719
在这儿写一下分数

252
00:10:37,720 --> 00:10:40,839
然后这边呢我们可以输出一个死亡

253
00:10:42,759 --> 00:10:43,419
死亡

254
00:10:45,700 --> 00:10:47,458
我们来运行一下

255
00:10:51,940 --> 00:10:56,200
点一下点一下点一下点一下点一下

256
00:10:56,200 --> 00:10:59,458
大家可以看分数是不是正常去增加了

257
00:11:01,500 --> 00:11:04,120
上来上来上来

258
00:11:04,480 --> 00:11:05,980
然后如果我拿一下

259
00:11:05,980 --> 00:11:08,179
没有去点中那个小鸟的话

260
00:11:12,000 --> 00:11:14,940
啊这个上面我们给的是距离太高了是吧

261
00:11:14,940 --> 00:11:15,600
嗯

262
00:11:15,600 --> 00:11:17,100
应该是给的距离太高了

263
00:11:17,100 --> 00:11:19,379
然后他这边就会爆出这个死亡了啊

264
00:11:19,379 --> 00:11:20,279
就会爆出死亡了

265
00:11:20,279 --> 00:11:21,578
掉死亡方法

266
00:11:23,980 --> 00:11:25,299
我们来看一下啊

267
00:11:27,039 --> 00:11:28,899
这个小鸟

268
00:11:32,340 --> 00:11:34,799
我们死亡上他多会掉死亡

269
00:11:34,799 --> 00:11:36,818
怎么跑那么高才死亡

270
00:11:41,419 --> 00:11:43,620
与结束是在这里

271
00:11:49,320 --> 00:11:51,889
目的地是个185

272
00:11:51,889 --> 00:11:53,659
185太高了吗

273
00:11:54,059 --> 00:11:55,799
啊这个大家自己去调吧

274
00:11:55,799 --> 00:11:58,019
反正就是我现在高度就是185

275
00:11:58,019 --> 00:12:00,139
就是屏幕向上185啊

276
00:12:00,139 --> 00:12:02,208
你向下给它一个小数值的话

277
00:12:02,208 --> 00:12:04,919
那么它就会死亡的更快一点啊

278
00:12:08,720 --> 00:12:10,039
那么在这边啊

279
00:12:10,039 --> 00:12:10,940
我们来继续啊

280
00:12:10,940 --> 00:12:12,139
我们先继续

281
00:12:13,519 --> 00:12:15,500
现在可以死亡啊

282
00:12:15,500 --> 00:12:17,000
然后也可以去加分

283
00:12:17,000 --> 00:12:18,500
也可以去加分

284
00:12:18,600 --> 00:12:21,480
但是我们现在有一个问题啊

285
00:12:21,480 --> 00:12:24,720
就是说我们是通过打印打印出来的不太好

286
00:12:24,720 --> 00:12:28,179
我们希望给它加上一个ui界面啊

287
00:12:28,179 --> 00:12:29,500
加上一个ui界面

288
00:12:29,500 --> 00:12:32,259
那么这个ui界面怎么去加啊

289
00:12:32,259 --> 00:12:33,820
怎么去加呃

290
00:12:33,820 --> 00:12:36,159
其实很简单非常简单

291
00:12:37,179 --> 00:12:39,820
我们在这里死亡的话

292
00:12:39,820 --> 00:12:42,039
给它添加一个ui界面

293
00:12:42,039 --> 00:12:43,779
什么样的ui界面呢

294
00:12:49,059 --> 00:12:50,580
先来这个加分的吧

295
00:12:50,580 --> 00:12:51,539
先来加分的

296
00:12:51,539 --> 00:12:53,179
我们应该是来个label

297
00:12:54,039 --> 00:12:57,519
我们在这边来一个加分的这样的一个标签啊

298
00:12:57,519 --> 00:13:00,559
在这里我们让它一直去进行一个加分

299
00:13:01,539 --> 00:13:02,679
字体太大了

300
00:13:02,679 --> 00:13:06,859
比如说给个15 13

301
00:13:09,440 --> 00:13:12,019
在这里比如说叫分数

302
00:13:15,919 --> 00:13:18,080
然后在它下面创建的子节点

303
00:13:21,019 --> 00:13:22,419
往右边放

304
00:13:25,960 --> 00:13:27,318
是个零

305
00:13:28,480 --> 00:13:30,190
这也是13吧

306
00:13:30,190 --> 00:13:34,120
那在这边呢我们就可以给它显示当前的分数了啊

307
00:13:34,120 --> 00:13:35,599
显示当前的分数了

308
00:13:36,240 --> 00:13:38,570
那我们这节课把分数也给做完

309
00:13:38,570 --> 00:13:41,659
下节课我们来做一个这个开始的场景

310
00:13:41,659 --> 00:13:43,938
我们做一个场景之间的一个跳转

311
00:13:45,519 --> 00:13:47,259
那么分数这个标签有来

312
00:13:47,259 --> 00:13:49,600
我们怎样让这个标签产生关联

313
00:13:49,600 --> 00:13:51,659
那首先我们在这边

314
00:13:53,220 --> 00:13:54,929
回到这个管理类这边

315
00:13:54,929 --> 00:13:55,740
除了分数

316
00:13:55,740 --> 00:13:58,159
我们再来一个分数标签

317
00:14:04,100 --> 00:14:04,899
label

318
00:14:08,379 --> 00:14:09,740
scar label

319
00:14:12,200 --> 00:14:13,940
就是分数的一个标签

320
00:14:13,940 --> 00:14:14,720
写完以后

321
00:14:14,720 --> 00:14:16,179
我们来关联一下

322
00:14:18,159 --> 00:14:22,320
这里就有了我们关联一下这个分数的标签

323
00:14:22,320 --> 00:14:24,938
关联上分数的标签以后

324
00:14:25,200 --> 00:14:29,480
然后我们就可以去对他操作了

325
00:14:29,480 --> 00:14:30,559
就非常简单了

326
00:14:30,559 --> 00:14:36,000
我们就直接在这里把这个输出的这个分数就可以不让他输出了

327
00:14:36,000 --> 00:14:41,029
直接在这roll label点文本

328
00:14:41,029 --> 00:14:42,889
文本是string

329
00:14:42,889 --> 00:14:48,340
就等一个this点分数当前的分数啊

330
00:14:48,340 --> 00:14:50,559
我们可以加上一个空的字符串啊

331
00:14:50,559 --> 00:14:53,490
就把number转成字符串就可以了

332
00:14:53,490 --> 00:14:56,340
这样的话我们就算完成了啊

333
00:14:56,340 --> 00:14:57,578
就算完成了

334
00:14:58,460 --> 00:14:59,179
运行一下

335
00:14:59,179 --> 00:15:00,359
看一下效果

336
00:15:03,659 --> 00:15:05,190
点一下100分

337
00:15:05,190 --> 00:15:06,059
200分

338
00:15:06,059 --> 00:15:07,110
300分

339
00:15:07,110 --> 00:15:08,100
500分

340
00:15:08,100 --> 00:15:08,700
500分

341
00:15:08,700 --> 00:15:11,419
600分700 800

342
00:15:11,419 --> 00:15:13,340
如果大家去找件背景音乐的话

343
00:15:13,340 --> 00:15:14,720
还可以给它加上背景音乐

344
00:15:14,720 --> 00:15:15,719
哈哈

345
00:15:17,779 --> 00:15:18,340
ok啊

346
00:15:18,340 --> 00:15:20,049
那这个分数已经没问题了

347
00:15:20,049 --> 00:15:22,600
那么我们这节课就这么多内容啊

348
00:15:22,600 --> 00:15:24,259
这节课就这么多内容

349
00:15:30,460 --> 00:15:31,480
略略略


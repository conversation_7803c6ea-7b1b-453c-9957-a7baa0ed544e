1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,320 --> 00:00:14,380
ok这节课我们开始来编写客户端啊

3
00:00:14,380 --> 00:00:16,719
那我们服务端我们已经编写完了

4
00:00:16,719 --> 00:00:18,039
服务端现在会做什么事

5
00:00:18,039 --> 00:00:19,199
首先运行起来

6
00:00:19,219 --> 00:00:21,079
只要这个端口能正常监听

7
00:00:21,079 --> 00:00:23,320
它就会输出这一行信息

8
00:00:23,339 --> 00:00:25,920
其次只要有客户端连接过来

9
00:00:25,920 --> 00:00:27,719
那么连接过来以后

10
00:00:27,719 --> 00:00:29,039
我就会怎样

11
00:00:29,039 --> 00:00:31,739
我就会我们输出一个东西吧

12
00:00:31,739 --> 00:00:32,259
再

13
00:00:34,659 --> 00:00:37,090
debug一个呃

14
00:00:37,090 --> 00:00:38,640
有客户端连接

15
00:00:40,539 --> 00:00:42,619
啊比如说有客户端连接

16
00:00:42,619 --> 00:00:44,479
只要有客户端过来连接我了

17
00:00:44,479 --> 00:00:46,939
我这边服务端就会显示有客户端连接

18
00:00:46,939 --> 00:00:48,740
同时我开始去监听

19
00:00:48,740 --> 00:00:50,479
如果客户端发来一个消息

20
00:00:50,479 --> 00:00:52,280
我这边就会有客户端发来消息

21
00:00:52,280 --> 00:00:52,700
冒号

22
00:00:52,700 --> 00:00:54,619
后面就是发的消息内容

23
00:00:55,140 --> 00:00:59,299
那么这个这个东西怎么启动呢

24
00:00:59,320 --> 00:01:03,280
啊那么就比如说我现在是把当前这个文件存在了桌面

25
00:01:03,280 --> 00:01:09,010
那首先你要在你的这个命令行进入到桌面啊

26
00:01:09,010 --> 00:01:10,540
就是cd到你的桌面

27
00:01:10,540 --> 00:01:11,920
cd到桌面以后

28
00:01:11,920 --> 00:01:13,780
这时候我们就可以去执行了

29
00:01:13,780 --> 00:01:15,579
怎样去执行嗯

30
00:01:15,579 --> 00:01:16,000
很简单

31
00:01:16,000 --> 00:01:17,769
首先note空格

32
00:01:17,769 --> 00:01:20,859
然后你的这个脚本名称

33
00:01:21,640 --> 00:01:22,840
然后回车

34
00:01:22,840 --> 00:01:26,079
这时候大家可以看服务监听在餐厅端口

35
00:01:26,079 --> 00:01:27,939
就是这个是不是已经开始监听了

36
00:01:27,959 --> 00:01:30,209
这个脚本已经开始执行了啊

37
00:01:30,209 --> 00:01:31,680
那这样的话就不用管了

38
00:01:31,680 --> 00:01:33,629
就服务端就已经开始监听了

39
00:01:33,629 --> 00:01:35,549
那我们现在编写客户端

40
00:01:35,549 --> 00:01:37,180
创建一个空的项目

41
00:01:46,060 --> 00:01:49,840
我们知道我们现在用的是soft.i o这个框架

42
00:01:49,840 --> 00:01:50,200
对不对

43
00:01:50,200 --> 00:01:51,700
然后这个框架的话

44
00:01:51,719 --> 00:01:53,819
咱们上节课做服务端的时候

45
00:01:53,819 --> 00:01:58,019
是通过命令行让这个服务端支持了这个soft的i

46
00:01:58,019 --> 00:02:01,939
那么如果我们客户端也要用socket io的话

47
00:02:01,939 --> 00:02:07,980
那么客户端也必须导入socl的这个文件文件是什么

48
00:02:09,240 --> 00:02:12,930
那么就是这两个文件我在这导进来啊

49
00:02:12,930 --> 00:02:14,039
然后我共享了

50
00:02:14,039 --> 00:02:16,379
然后大家去直接下载就行啊

51
00:02:16,379 --> 00:02:17,979
直接下载呃

52
00:02:17,979 --> 00:02:19,539
这里有两个文件啊

53
00:02:19,539 --> 00:02:21,460
一个是type script的

54
00:02:21,460 --> 00:02:22,719
一个是g s的啊

55
00:02:22,719 --> 00:02:25,460
我们给它放到呃这里就行了

56
00:02:25,460 --> 00:02:27,680
或者你给它放到脚本里面啊

57
00:02:27,680 --> 00:02:30,240
脚本文件夹里都行啊

58
00:02:30,240 --> 00:02:32,300
或者我们创建个脚本文件夹吧

59
00:02:35,639 --> 00:02:37,000
把它放里面

60
00:02:38,020 --> 00:02:42,400
然后我们在这边创建一个新的脚本

61
00:02:42,400 --> 00:02:44,710
创建一个新的脚本呃

62
00:02:44,710 --> 00:02:47,259
新的脚本我们给它

63
00:02:47,840 --> 00:02:53,259
叫做socket test

64
00:02:53,719 --> 00:02:57,500
然后把它挂到我们一个节点上就行了啊

65
00:02:57,500 --> 00:03:00,000
只是为了让它能正常运行起来就行了

66
00:03:00,020 --> 00:03:02,840
然后打开它来编写客户端的脚本

67
00:03:02,840 --> 00:03:06,439
我们看一下它能不能连接上我们的服务端啊

68
00:03:06,439 --> 00:03:07,639
能不能连接上我的服务端

69
00:03:07,639 --> 00:03:09,560
其实就非常简单非常简单

70
00:03:09,560 --> 00:03:10,639
也是一句话

71
00:03:11,020 --> 00:03:18,250
在start里面我们直接首先我们先创建一个变量

72
00:03:18,250 --> 00:03:21,280
这个变量呢叫做socket

73
00:03:21,280 --> 00:03:22,689
什么类型呢

74
00:03:22,689 --> 00:03:24,900
就是soft的类型啊

75
00:03:24,900 --> 00:03:26,539
然后它默认是个空

76
00:03:27,039 --> 00:03:28,000
这是一个变量

77
00:03:28,000 --> 00:03:28,750
对不对

78
00:03:28,750 --> 00:03:36,259
然后在start里面我们this.socket就等于一个io.connect

79
00:03:38,159 --> 00:03:44,129
然后在connect里面哎我们就输入我们的这个服务端监听的地址

80
00:03:44,129 --> 00:03:46,439
因为我现在是在本地注意啊

81
00:03:46,439 --> 00:03:48,500
我现在是在本机上呃

82
00:03:48,500 --> 00:03:54,219
本机的ip地址我们就叫local host或者127点点0.1也行啊

83
00:03:54,219 --> 00:03:55,629
这个代表就是本机

84
00:03:55,629 --> 00:03:58,330
如果你把它发布到网站网站上啊

85
00:03:58,330 --> 00:04:00,740
这就是一个域名或者是一个ip地址

86
00:04:01,360 --> 00:04:03,069
端口是多少3000

87
00:04:03,069 --> 00:04:06,219
这是不是服务器在本地的3000端口上监听的

88
00:04:06,219 --> 00:04:08,659
所以我们去连它连城以后

89
00:04:08,659 --> 00:04:12,680
这个this.socket就是我们客户端上的这个socket

90
00:04:12,680 --> 00:04:14,780
也就是这个socket啊

91
00:04:14,780 --> 00:04:15,900
就是这个位置

92
00:04:17,459 --> 00:04:23,480
那所以当我们只要这一句话就已经连接了啊

93
00:04:23,480 --> 00:04:24,860
但是我作为客户端

94
00:04:24,860 --> 00:04:25,699
我连接完以后

95
00:04:25,699 --> 00:04:27,439
我都知道我有没有连接成功

96
00:04:27,439 --> 00:04:28,160
对不对

97
00:04:29,060 --> 00:04:32,339
连接服务端啊

98
00:04:32,339 --> 00:04:35,639
那么我连接服务端知道成没成功怎么办

99
00:04:35,639 --> 00:04:36,779
那就是回调了

100
00:04:36,779 --> 00:04:37,620
对不对

101
00:04:37,620 --> 00:04:41,379
判断是否连接成功

102
00:04:42,000 --> 00:04:46,079
this.socket.二一样的

103
00:04:46,079 --> 00:04:48,420
你看两边都基本上写法是完全一样的

104
00:04:48,420 --> 00:04:49,060
对不对

105
00:04:50,180 --> 00:04:53,180
connect啊

106
00:04:53,180 --> 00:04:54,319
注意啊

107
00:04:54,319 --> 00:04:55,579
这就是不一样的了啊

108
00:04:55,579 --> 00:05:00,620
那边你看作为服作为这个这个啊

109
00:05:00,620 --> 00:05:01,879
刚才那个脚本

110
00:05:11,360 --> 00:05:13,069
啊这是服务端脚本

111
00:05:13,069 --> 00:05:15,459
作为服务端脚本在这边

112
00:05:15,480 --> 00:05:18,180
他如果有客户端连接是connection

113
00:05:18,180 --> 00:05:18,660
注意啊

114
00:05:18,660 --> 00:05:19,920
是不一样的啊

115
00:05:19,920 --> 00:05:21,240
然后咱们作为客户端而言

116
00:05:21,240 --> 00:05:22,899
就是connected啊

117
00:05:25,259 --> 00:05:27,300
这就是判断是否连接成功了

118
00:05:27,300 --> 00:05:28,889
如果连接成功以后

119
00:05:28,889 --> 00:05:30,920
然后这里注意啊

120
00:05:31,060 --> 00:05:36,500
嗯我们就会来一个回调来回调data

121
00:05:38,879 --> 00:05:42,420
在这边在这边你就可以可以直接打印console

122
00:05:42,420 --> 00:05:48,040
点bug就是连接成功啊

123
00:05:48,040 --> 00:05:50,439
所以你只要能进入到这个里面

124
00:05:50,439 --> 00:05:53,480
就证明你的连接是成功了啊

125
00:05:53,480 --> 00:05:54,620
就证明你连接成功了

126
00:05:54,620 --> 00:05:57,740
那我们现在就可以来试一下啊

127
00:05:57,740 --> 00:05:58,459
我们来试一下

128
00:05:58,459 --> 00:06:03,360
看看是不是连接真的这个嗯成功了呃

129
00:06:03,360 --> 00:06:04,620
我们来运行

130
00:06:07,160 --> 00:06:09,680
运行完以后先看服务端

131
00:06:10,139 --> 00:06:12,300
大家可以看服务端这边输出出来了

132
00:06:12,300 --> 00:06:13,500
有客户端连接了

133
00:06:13,500 --> 00:06:15,040
有没有啊

134
00:06:15,040 --> 00:06:16,060
有客户端连接了

135
00:06:16,060 --> 00:06:20,180
证明服务端进入到这个回调里了啊

136
00:06:20,180 --> 00:06:21,139
有客户端连接了

137
00:06:21,139 --> 00:06:24,620
并且已经开始监听有客户端有没有发来消息了

138
00:06:24,620 --> 00:06:28,180
如果有发来消息就会进到这个回调函数里面

139
00:06:29,360 --> 00:06:31,040
那么在这里有客端连接了

140
00:06:31,040 --> 00:06:35,920
那我再看一下咱们客户端的打印客户端

141
00:06:35,920 --> 00:06:40,310
这边我们发现没有打印任何东西哦

142
00:06:40,310 --> 00:06:43,639
这边客户端云我应该是搞混了

143
00:06:43,639 --> 00:06:45,199
我跟另外一个搞混了

144
00:06:45,199 --> 00:06:50,699
咱们这个应该是connect啊

145
00:06:51,620 --> 00:06:54,110
哎呀不同的这个服务器语言

146
00:06:54,110 --> 00:06:58,730
然后他这个很多这些这些关键词啊什么的不一样

147
00:06:58,730 --> 00:07:00,199
这个应该是connect啊

148
00:07:00,199 --> 00:07:01,519
我们来重新运行一下

149
00:07:01,519 --> 00:07:02,439
保存一下

150
00:07:04,019 --> 00:07:05,519
但是这里有一个问题啊

151
00:07:05,519 --> 00:07:07,860
可能会有哎这里已经成功了

152
00:07:07,860 --> 00:07:08,579
大家看一下啊

153
00:07:08,579 --> 00:07:09,449
已经成功了

154
00:07:09,449 --> 00:07:10,920
但是问题虽然没出来

155
00:07:10,920 --> 00:07:12,060
我还是要说一下啊

156
00:07:12,060 --> 00:07:13,000
什么问题

157
00:07:14,019 --> 00:07:16,500
咱们这个cos creator这个引擎啊

158
00:07:16,680 --> 00:07:19,379
他这个处理网络连接并不是特别好

159
00:07:19,379 --> 00:07:20,220
什么意思啊

160
00:07:20,220 --> 00:07:22,829
比如说你第一次做一个网络连接

161
00:07:22,829 --> 00:07:24,600
如果比如说成功了

162
00:07:24,600 --> 00:07:28,199
有时候你第二次比如说修改了几行代码再运行

163
00:07:28,199 --> 00:07:32,639
你会发现诶好像你刚才去编写的这个内容

164
00:07:32,639 --> 00:07:34,139
就是修改的内容

165
00:07:34,139 --> 00:07:35,339
你不管改了什么

166
00:07:35,339 --> 00:07:38,649
好像没有没有成功啊

167
00:07:38,649 --> 00:07:39,819
偶尔会有这种情况

168
00:07:39,819 --> 00:07:40,720
如果有这种情况

169
00:07:40,720 --> 00:07:43,339
就是你比如说你这个代码

170
00:07:43,339 --> 00:07:44,540
你现在发现有错误了

171
00:07:44,540 --> 00:07:45,379
然后你去修改

172
00:07:45,379 --> 00:07:46,040
修改完以后

173
00:07:46,040 --> 00:07:49,819
你发现你修改的代码好像没执行啊

174
00:07:49,819 --> 00:07:51,879
还是之前执行的之前的代码

175
00:07:51,879 --> 00:07:53,920
那你直接把浏览器也关了

176
00:07:53,920 --> 00:07:56,019
然后这个cos creator也关了

177
00:07:56,019 --> 00:07:57,600
全部重新启动就好了

178
00:07:57,879 --> 00:08:02,540
嗯这个在正常情况下其实很少见

179
00:08:02,540 --> 00:08:04,040
但是一一碰上网络

180
00:08:04,040 --> 00:08:05,149
尤其是套接字

181
00:08:05,149 --> 00:08:06,860
有时候就会有这样的问题

182
00:08:06,860 --> 00:08:09,040
所以遇到这样的问题一定要注意啊

183
00:08:09,699 --> 00:08:11,800
我刚才修改大家可以看啊

184
00:08:11,800 --> 00:08:12,759
他是没问题啊

185
00:08:12,759 --> 00:08:13,839
确实修改完了

186
00:08:13,839 --> 00:08:16,120
而且这个连接成功也打印出来了啊

187
00:08:16,120 --> 00:08:17,500
就证明没问题

188
00:08:17,500 --> 00:08:19,079
connect连接成功

189
00:08:19,100 --> 00:08:21,079
这样的话最起码我们就知道

190
00:08:21,100 --> 00:08:23,860
你看我们刚才重新启动了两次

191
00:08:23,860 --> 00:08:26,709
现在一共有三个客户端连接过啊

192
00:08:26,709 --> 00:08:29,709
并且三个客户端在这里连接了

193
00:08:29,709 --> 00:08:32,559
现在就属于客户端跟服务器有连接

194
00:08:32,559 --> 00:08:34,600
但是谁都没给谁发消息

195
00:08:34,600 --> 00:08:35,320
对不对

196
00:08:35,639 --> 00:08:38,250
那我们在这里做一个操作

197
00:08:38,250 --> 00:08:39,570
如果连接成功了

198
00:08:39,570 --> 00:08:42,600
我们就让客户端给服务端发条消息

199
00:08:44,399 --> 00:08:45,330
很简单

200
00:08:45,330 --> 00:08:46,259
我们来试一下

201
00:08:46,259 --> 00:08:47,759
比如说你看现在已经连接成功了

202
00:08:47,759 --> 00:08:49,580
那我我要发消息怎么办

203
00:08:50,720 --> 00:08:55,340
给服务端发消息

204
00:08:55,340 --> 00:08:59,779
那就这点儿socket点

205
00:08:59,799 --> 00:09:01,419
你看他就俩方法

206
00:09:01,419 --> 00:09:02,320
on就是监听

207
00:09:02,320 --> 00:09:04,120
第一个就是发消息

208
00:09:04,120 --> 00:09:06,340
然后在这里发的消息

209
00:09:06,340 --> 00:09:08,860
比如说我们定义一个k消息的名称

210
00:09:08,860 --> 00:09:11,460
就是message这个名字你可以改啊

211
00:09:11,460 --> 00:09:12,419
你叫别的也行

212
00:09:12,419 --> 00:09:13,899
我们这儿就叫message

213
00:09:14,139 --> 00:09:15,940
这些名字是不能改的啊

214
00:09:15,940 --> 00:09:17,200
connection和connect

215
00:09:17,200 --> 00:09:17,799
这是不能改的

216
00:09:17,799 --> 00:09:20,139
这是固定的这个消息的名称啊

217
00:09:20,139 --> 00:09:22,659
只要是消息这个这个啊

218
00:09:22,659 --> 00:09:23,379
第一个参数

219
00:09:23,379 --> 00:09:25,330
这个字符串你都是可以修改的

220
00:09:25,330 --> 00:09:26,980
发的消息是什么

221
00:09:26,980 --> 00:09:28,000
第二个是个参数

222
00:09:28,000 --> 00:09:29,200
我们参数直接给个字符

223
00:09:29,200 --> 00:09:29,980
串好了

224
00:09:30,259 --> 00:09:31,129
您好

225
00:09:31,129 --> 00:09:33,379
我是一个客户端

226
00:09:34,539 --> 00:09:37,340
比如说我们给它一个字符串发过去

227
00:09:37,580 --> 00:09:39,019
然后作为服务端呢

228
00:09:39,019 --> 00:09:41,149
大家可以看一下服务端

229
00:09:41,149 --> 00:09:42,559
它就已经监听了

230
00:09:42,559 --> 00:09:42,980
对不对

231
00:09:42,980 --> 00:09:46,080
他已经监听这个message了啊

232
00:09:46,080 --> 00:09:47,460
这两个一定是一对啊

233
00:09:47,460 --> 00:09:49,620
你客户端发过来一个message

234
00:09:49,620 --> 00:09:52,200
那么服务端在这边监听的就是message

235
00:09:52,200 --> 00:09:53,039
监听完了以后

236
00:09:53,039 --> 00:09:56,159
他会把这个既然听到的这个数据打印出来

237
00:09:56,159 --> 00:10:01,960
这个data实际上就是我们的这个啊就是这个信息啊

238
00:10:01,960 --> 00:10:03,100
我们现在来试一下啊

239
00:10:03,100 --> 00:10:03,940
行不行

240
00:10:04,980 --> 00:10:07,559
重新运行一下脚本保存了

241
00:10:07,559 --> 00:10:08,399
保存了

242
00:10:08,399 --> 00:10:10,120
我们来运行一下

243
00:10:10,759 --> 00:10:12,679
运行完以后看一下

244
00:10:13,519 --> 00:10:15,019
ok我们可以看一下啊

245
00:10:15,019 --> 00:10:16,820
这边客户端发来消息

246
00:10:16,820 --> 00:10:17,600
我们不用管啊

247
00:10:17,600 --> 00:10:20,360
这个是因为命令提示符啊

248
00:10:20,360 --> 00:10:23,639
咱们这个呃对于这个中文啊

249
00:10:23,639 --> 00:10:27,059
中文的这个支持不是很好啊

250
00:10:27,059 --> 00:10:27,779
不是很好

251
00:10:27,779 --> 00:10:31,019
我们这里测试一般最好还是用英文啊

252
00:10:31,019 --> 00:10:33,820
测试还是要在英文里面啊

253
00:10:34,179 --> 00:10:37,120
等我们做成这个真正终端之间的通信啊

254
00:10:37,120 --> 00:10:38,299
就没这些问题

255
00:10:39,679 --> 00:10:45,080
所以这里就是说hello中文编码问题啊

256
00:10:45,080 --> 00:10:45,950
不用管它

257
00:10:45,950 --> 00:10:47,580
换成hello试一试

258
00:10:50,039 --> 00:10:52,799
看这边大家可以看

259
00:10:52,799 --> 00:10:53,399
hello

260
00:10:53,399 --> 00:10:54,419
是不是啊

261
00:10:54,419 --> 00:10:55,320
就没有问题了啊

262
00:10:55,320 --> 00:10:56,360
就没有问题了

263
00:10:56,600 --> 00:10:58,179
一定要注意啊

264
00:11:01,559 --> 00:11:06,570
那么现在我们客户端知道诶什么时候连接了

265
00:11:06,570 --> 00:11:08,639
那么什么时候断开呢

266
00:11:08,639 --> 00:11:11,100
最起码我们要知道是否连接成功

267
00:11:11,100 --> 00:11:14,519
然后如果你断开服务器啊

268
00:11:14,519 --> 00:11:17,620
判断是否断开

269
00:11:18,059 --> 00:11:19,379
这两个一定要知道

270
00:11:19,379 --> 00:11:21,029
因为有时候比如说连接成功

271
00:11:21,029 --> 00:11:22,259
我们要做些事

272
00:11:22,259 --> 00:11:23,460
断开连接也要做些事

273
00:11:23,460 --> 00:11:23,940
对不对

274
00:11:23,940 --> 00:11:24,840
所以断开连接

275
00:11:24,840 --> 00:11:26,139
我们今天听的就是

276
00:11:29,019 --> 00:11:31,720
disconnect

277
00:11:31,720 --> 00:11:36,389
然后在这边同样也是啊

278
00:11:36,389 --> 00:11:38,250
有一个回调

279
00:11:38,250 --> 00:11:41,360
回调里面你就可以去写

280
00:11:41,360 --> 00:11:42,440
比如说断开以后

281
00:11:42,440 --> 00:11:45,039
你就让这个程序跳到哪个页面呀

282
00:11:45,039 --> 00:11:47,379
或者怎样的啊等等都行啊

283
00:11:47,379 --> 00:11:49,179
就是万一你和服务器断线了是吧

284
00:11:49,179 --> 00:11:50,600
这就叫断线啊

285
00:11:50,600 --> 00:11:51,860
断开了你要做什么事儿

286
00:11:51,860 --> 00:11:54,549
你就在这儿去处理就行了啊

287
00:11:54,549 --> 00:12:01,259
那么现在啊我们服务器已经可以接收到客户端发来的消息了

288
00:12:01,259 --> 00:12:03,659
比如说如果他收到消息

289
00:12:03,659 --> 00:12:06,019
同样他也要给客户端回个消息

290
00:12:06,019 --> 00:12:07,639
也就是说只要客户端发的消息

291
00:12:07,639 --> 00:12:10,259
我我再回一个消息怎么办

292
00:12:11,519 --> 00:12:15,120
给客户端发消息

293
00:12:15,940 --> 00:12:18,519
那么如果我要给这个客户端发消息也是一样的

294
00:12:18,519 --> 00:12:19,600
很简单是吧

295
00:12:19,600 --> 00:12:22,299
就是socket点

296
00:12:22,740 --> 00:12:26,899
比如说给他发个message

297
00:12:28,559 --> 00:12:29,159
是吧

298
00:12:29,159 --> 00:12:30,570
消息的名称

299
00:12:30,570 --> 00:12:32,039
消息的字符串

300
00:12:32,039 --> 00:12:34,139
这个你你随便去写是吧

301
00:12:34,519 --> 00:12:37,070
hello

302
00:12:37,070 --> 00:12:38,240
那这边是啥

303
00:12:38,240 --> 00:12:38,779
hello

304
00:12:38,779 --> 00:12:42,480
word代码都是这个是吧

305
00:12:42,480 --> 00:12:44,820
咱们就分开客户端发过来哈喽

306
00:12:44,820 --> 00:12:46,679
然后服务端回个word

307
00:12:47,360 --> 00:12:49,879
然后这边我发过去这个啊

308
00:12:49,879 --> 00:12:53,120
我们客户端还没有去接收呢

309
00:12:53,120 --> 00:12:53,600
对不对

310
00:12:53,600 --> 00:12:55,580
那么客户端怎么去接收

311
00:12:55,720 --> 00:12:56,860
大家可以看客户端

312
00:12:56,860 --> 00:12:59,080
现在还没有做任何接收的事儿

313
00:12:59,080 --> 00:13:01,919
那客户端就应该去这样去做了

314
00:13:03,519 --> 00:13:06,700
客户端接收消息

315
00:13:08,200 --> 00:13:11,419
this.socket.二

316
00:13:12,820 --> 00:13:15,159
然后我们就是message

317
00:13:17,940 --> 00:13:22,519
这个就是接收服务器给我发来的消息了啊

318
00:13:22,519 --> 00:13:24,559
就接收服务器给我发来的消息

319
00:13:24,559 --> 00:13:25,639
如果接收到了

320
00:13:25,639 --> 00:13:27,820
我们再给它打印出来

321
00:13:30,360 --> 00:13:33,279
ok那么就这样

322
00:13:35,139 --> 00:13:36,940
那么我们服务器得重启一下

323
00:13:36,940 --> 00:13:37,899
怎么重启服务器

324
00:13:37,899 --> 00:13:41,039
ctrl加c按两下就行了

325
00:13:41,039 --> 00:13:43,299
然后再重新执行

326
00:13:43,460 --> 00:13:45,919
然后客户端重新执行

327
00:13:45,919 --> 00:13:46,700
执行完了

328
00:13:46,700 --> 00:13:49,710
看一下这边有客户端连接

329
00:13:49,710 --> 00:13:50,639
并且发来消息

330
00:13:50,639 --> 00:13:51,240
hello

331
00:13:51,240 --> 00:13:55,340
然后我们再看一下客户端的打印连接

332
00:13:55,340 --> 00:13:56,990
成功收到一个word

333
00:13:56,990 --> 00:13:58,399
并且把word打印出来了

334
00:13:58,399 --> 00:14:01,100
word是不是就是服务器回复的消息

335
00:14:01,519 --> 00:14:02,509
ok啊

336
00:14:02,509 --> 00:14:03,919
那么这个东西啊

337
00:14:03,919 --> 00:14:08,179
就是客户端跟服务器之间的一个连接啊

338
00:14:08,179 --> 00:14:11,399
非常简单非常简单呃

339
00:14:13,000 --> 00:14:15,100
我们这节课就先说这么多吧

340
00:14:15,100 --> 00:14:16,539
先说这么多啊

341
00:14:16,539 --> 00:14:17,379
通过这个啊

342
00:14:17,379 --> 00:14:18,340
就这几行代码

343
00:14:18,340 --> 00:14:19,899
大家练一练啊

344
00:14:19,899 --> 00:14:23,679
就就呃就这个发消息最起码就没问题了啊

345
00:14:23,679 --> 00:14:26,620
其实两边的两边的代码都一样哈

346
00:14:26,620 --> 00:14:28,620
其实两边代码都一样啊

347
00:14:28,620 --> 00:14:30,899
发送跟接收完全是一样的

348
00:14:30,899 --> 00:14:32,879
你只需要记住的是什么

349
00:14:32,879 --> 00:14:35,720
只需要记住的是这个关键词是吧

350
00:14:35,720 --> 00:14:37,399
比如说connect disconnect

351
00:14:37,399 --> 00:14:39,820
这边是个connection啊

352
00:14:39,820 --> 00:14:41,649
记住这些就行了

353
00:14:41,649 --> 00:14:43,720
然后别的就没什么了

354
00:14:43,720 --> 00:14:46,370
嗯嗯ok啊

355
00:14:46,370 --> 00:14:48,600
那么这节课咱们就这么多内容


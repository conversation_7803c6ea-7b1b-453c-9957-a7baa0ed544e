1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,619 --> 00:00:11,619
这节课我们来说一下节点啊

3
00:00:11,619 --> 00:00:14,839
它这个类型的常用的属性和方法

4
00:00:14,839 --> 00:00:18,109
那比如说我们拿到一个脚本啊

5
00:00:18,109 --> 00:00:20,719
我们知道这个脚本就代表了一个组件

6
00:00:20,719 --> 00:00:21,140
对不对

7
00:00:21,140 --> 00:00:24,739
就代表我们这个比如说就是这个test组件

8
00:00:25,219 --> 00:00:30,679
我这个test的组件我怎样去拿到我这个节点啊

9
00:00:30,679 --> 00:00:31,460
为了方便

10
00:00:31,460 --> 00:00:32,780
因为节点我们知道啊

11
00:00:32,780 --> 00:00:36,890
这个node每一个添加上来的节点都有这个组件

12
00:00:36,890 --> 00:00:39,600
所以唉为了方便

13
00:00:39,600 --> 00:00:46,240
那么他这里直接有一个this this调属性

14
00:00:46,240 --> 00:00:47,890
直接一个属性就叫node

15
00:00:47,890 --> 00:00:53,880
这个node就是我当前这个组件所挂载的那个物体身上的node节点

16
00:00:53,920 --> 00:00:58,560
然后在它里面就有很多属性和方法可以让我们去用了

17
00:00:58,560 --> 00:01:02,759
那首先我们第一个先来说一下这个父子集的关系啊

18
00:01:02,759 --> 00:01:04,379
先来说父子集的关系

19
00:01:05,640 --> 00:01:07,719
那么我们之前说过

20
00:01:07,739 --> 00:01:09,540
如果有多个节点

21
00:01:09,540 --> 00:01:12,510
那么这些节点是可以有父子关系的啊

22
00:01:12,510 --> 00:01:17,439
你比如说现在我脚本是在节点那个一上面啊

23
00:01:17,439 --> 00:01:19,900
那么节点二呢是我的子物体

24
00:01:19,900 --> 00:01:20,530
对不对

25
00:01:20,530 --> 00:01:22,900
我想获取我的子物体怎么获取

26
00:01:22,900 --> 00:01:24,519
那么子物体可能有很多个

27
00:01:24,519 --> 00:01:25,260
对不对

28
00:01:25,920 --> 00:01:28,000
比如说可能还有子无敌三

29
00:01:29,079 --> 00:01:30,760
那么可能有很多子物体

30
00:01:30,760 --> 00:01:33,640
我想获得我其中的子物体怎么获取

31
00:01:34,219 --> 00:01:36,019
那么这里注意啊

32
00:01:36,719 --> 00:01:39,239
获得子节点啊

33
00:01:39,239 --> 00:01:40,769
其实这个就是子节点

34
00:01:40,769 --> 00:01:43,859
因为它的父子关系都是靠节点来维持的

35
00:01:43,859 --> 00:01:45,060
你可以这样做

36
00:01:45,060 --> 00:01:45,659
很简单

37
00:01:45,659 --> 00:01:48,319
this.node.children

38
00:01:48,439 --> 00:01:53,519
那么在这里children count的意思就是我子节点有几个啊

39
00:01:53,519 --> 00:01:55,019
这个是子节点有几个

40
00:01:55,019 --> 00:01:56,700
那children就是所有的子节点

41
00:01:56,700 --> 00:01:57,629
这是一个数组

42
00:01:57,629 --> 00:01:59,340
那么你拿到数据以后

43
00:01:59,340 --> 00:02:01,260
零就是第一个子节点

44
00:02:01,260 --> 00:02:02,760
一就是第二个子节点啊

45
00:02:02,760 --> 00:02:05,340
那这就是拿到第几个子节点啊

46
00:02:05,340 --> 00:02:06,799
这是一种方式

47
00:02:06,799 --> 00:02:09,110
还有一种方式

48
00:02:09,110 --> 00:02:16,810
this.node di get child by name啊

49
00:02:16,810 --> 00:02:19,360
我们就是用这个by name这种方式

50
00:02:19,360 --> 00:02:22,460
这里面可以填个节点的名称啊

51
00:02:22,460 --> 00:02:23,719
就你子节点哪一个

52
00:02:23,719 --> 00:02:25,009
比如说叫abc

53
00:02:25,009 --> 00:02:29,300
那么他们就把这个叫abc的子节点给你返回回来了啊

54
00:02:29,300 --> 00:02:31,180
这是通过名称去查找

55
00:02:31,879 --> 00:02:33,830
那么还有一种方式

56
00:02:33,830 --> 00:02:35,419
还有一种方式

57
00:02:35,419 --> 00:02:36,680
这种方式是什么

58
00:02:36,680 --> 00:02:39,259
有时候就是虽然不太常见啊

59
00:02:39,259 --> 00:02:43,389
比如说我现在我要查找

60
00:02:43,389 --> 00:02:45,860
我要拿到这个man camera这个节点

61
00:02:45,879 --> 00:02:48,939
但是我脚本在这个new node上面挂的啊

62
00:02:48,939 --> 00:02:50,080
就是我脚本在这儿

63
00:02:50,080 --> 00:02:53,000
但是我想获得这个节点怎么获取

64
00:02:53,020 --> 00:02:55,719
那你就不能通过你获取你的子节点了

65
00:02:55,719 --> 00:02:57,879
因为这个man camera和你没有半毛钱关系

66
00:02:57,879 --> 00:02:58,439
对不对

67
00:02:58,460 --> 00:03:00,500
它的父节点是canvas convers

68
00:03:00,500 --> 00:03:01,759
已经是最外层节点了

69
00:03:01,759 --> 00:03:02,639
那怎么办

70
00:03:03,340 --> 00:03:05,530
你就可以通过这种方式

71
00:03:05,530 --> 00:03:08,020
它有一种路径获得的方式啊

72
00:03:08,020 --> 00:03:10,569
就通过路径获取这个节点的方式

73
00:03:10,569 --> 00:03:12,379
你直接就是啊

74
00:03:12,379 --> 00:03:13,819
这不是this.node啊

75
00:03:13,819 --> 00:03:17,400
你就可以直接这样用cc.fund啊

76
00:03:17,400 --> 00:03:19,300
cc我们知道就是名称空间

77
00:03:19,319 --> 00:03:24,960
所以cc点后面的内容都是这个cocos 2 d给我们提供的方法

78
00:03:24,960 --> 00:03:27,250
里面有个f find

79
00:03:27,250 --> 00:03:28,539
就是填写一个路径

80
00:03:28,539 --> 00:03:31,629
这个路径你就可以比如说cos

81
00:03:31,629 --> 00:03:35,580
然后比如说把camera啊

82
00:03:35,580 --> 00:03:36,840
通过这种方式

83
00:03:36,840 --> 00:03:39,900
它是从最外层开始找第一个就是最外层

84
00:03:39,900 --> 00:03:42,300
然后每每找一层来个斜杠

85
00:03:42,300 --> 00:03:43,599
没找一层来个斜杠

86
00:03:43,620 --> 00:03:45,539
那么通过这种方式啊

87
00:03:45,539 --> 00:03:46,979
你查找字节点

88
00:03:47,180 --> 00:03:53,120
那基本上这三种方式就是我们查找字节点的方式啊

89
00:03:53,120 --> 00:03:56,000
那比如说比如说啊

90
00:03:56,000 --> 00:03:58,699
我们呃

91
00:04:00,460 --> 00:04:03,340
我们目前比如说我们这个节点

92
00:04:03,340 --> 00:04:04,870
我的父节点啊

93
00:04:04,870 --> 00:04:06,919
怎么样获得副节点

94
00:04:07,680 --> 00:04:08,879
有了子节点了

95
00:04:08,879 --> 00:04:10,560
我们要关心怎么获取复节点

96
00:04:10,560 --> 00:04:12,120
当拿到一个节点以后

97
00:04:12,120 --> 00:04:16,120
他的副节点就叫get parent

98
00:04:16,699 --> 00:04:20,540
get parent就是用来获取父节点的

99
00:04:20,540 --> 00:04:25,199
在这个括号这个方法它返回的就是一个节点

100
00:04:25,240 --> 00:04:28,040
那么同时它含一个方法

101
00:04:28,120 --> 00:04:30,790
this.node点

102
00:04:30,790 --> 00:04:32,579
set parent

103
00:04:32,579 --> 00:04:34,980
这个意思就是设置一个副节点

104
00:04:34,980 --> 00:04:36,839
就本来比如说我没复节点

105
00:04:36,839 --> 00:04:41,430
或者说啊我现在的副节点我要变一个

106
00:04:41,430 --> 00:04:43,350
你就把新的副节点啊

107
00:04:43,350 --> 00:04:46,689
填一个新的note放到这个里面唉

108
00:04:46,689 --> 00:04:47,920
放到里面以后

109
00:04:47,920 --> 00:04:53,139
那么它这个this.note就会变为这个参数的子节点啊

110
00:04:53,139 --> 00:04:54,839
就会变为它的子节点啊

111
00:04:54,839 --> 00:04:56,759
就这样的一个意思

112
00:04:56,759 --> 00:05:00,639
当然你要呃设置空

113
00:05:00,639 --> 00:05:02,079
那就没有负节点了

114
00:05:02,079 --> 00:05:03,279
你就放到最外层了

115
00:05:03,279 --> 00:05:03,980
对不对

116
00:05:04,920 --> 00:05:06,449
ok啊

117
00:05:06,449 --> 00:05:12,920
父子节点啊其实还是蛮简单的

118
00:05:12,920 --> 00:05:14,040
父子关系

119
00:05:15,680 --> 00:05:19,319
那么比如说啊比如说我们想想一下啊

120
00:05:19,579 --> 00:05:22,939
其实它还有一个移除复节点的方法啊

121
00:05:22,939 --> 00:05:24,920
就是不光是用set它可以

122
00:05:24,920 --> 00:05:29,629
比如说我想从嗯remove诶

123
00:05:29,629 --> 00:05:31,379
点remove

124
00:05:31,819 --> 00:05:34,850
你看它有一些移除其中啊

125
00:05:34,850 --> 00:05:36,980
他的这个节点有一些移除的方法

126
00:05:36,980 --> 00:05:40,189
有一个remove or啊

127
00:05:40,189 --> 00:05:47,759
这是移除所有的子节点

128
00:05:48,839 --> 00:05:49,800
对不对

129
00:05:49,800 --> 00:05:51,660
移除所有的子节点

130
00:05:51,680 --> 00:05:54,379
那么他的这个移除节点还有什么呀

131
00:05:54,379 --> 00:05:55,660
remove child

132
00:05:55,660 --> 00:05:58,209
就是移除哪一个节点

133
00:05:58,209 --> 00:05:59,589
remove child

134
00:05:59,589 --> 00:06:03,339
他这个参数里面需要你去填写一个节点啊

135
00:06:03,339 --> 00:06:06,379
就是移除哪一个节点啊

136
00:06:06,379 --> 00:06:08,360
你这里要放一个节点的啊

137
00:06:08,360 --> 00:06:09,500
哪一个节点

138
00:06:11,399 --> 00:06:15,220
以及remove啊

139
00:06:15,220 --> 00:06:18,579
那这里有一个remove from parent啊

140
00:06:18,579 --> 00:06:20,439
就是把我这个节点移除掉

141
00:06:20,439 --> 00:06:21,699
从哪里移除掉

142
00:06:21,720 --> 00:06:24,300
从复节点里面移除掉啊

143
00:06:24,300 --> 00:06:25,920
就比如说我这个this.note

144
00:06:25,920 --> 00:06:27,040
如果是个子节点

145
00:06:27,060 --> 00:06:31,139
我一掉这个方法我就从我的父节点里面脱离出来了啊

146
00:06:31,139 --> 00:06:32,180
脱离出来了

147
00:06:37,360 --> 00:06:38,949
从

148
00:06:38,949 --> 00:06:41,660
中移除掉

149
00:06:42,279 --> 00:06:47,199
然后这个就是移除某个子节点

150
00:06:47,199 --> 00:06:47,829
对不对

151
00:06:47,829 --> 00:06:49,000
然后就这样的

152
00:06:52,980 --> 00:06:55,259
那么节点的操作啊

153
00:06:55,259 --> 00:06:56,699
知道这些足够用啊

154
00:06:56,699 --> 00:06:58,019
足够用啊

155
00:06:58,019 --> 00:06:58,939
足够用

156
00:06:58,959 --> 00:07:00,639
那么除了节点的操作

157
00:07:00,639 --> 00:07:03,339
剩下的操作我们拿到this.node

158
00:07:03,339 --> 00:07:07,160
因为我们知道this.node就是什么呀

159
00:07:07,980 --> 00:07:10,740
就是我们右边这一块的内容啊

160
00:07:10,740 --> 00:07:12,149
就是我们这一块的内容

161
00:07:12,149 --> 00:07:14,370
所以在上面看的所有的属性

162
00:07:14,370 --> 00:07:20,279
你通通可以通过呃我们这个代码来进行使用

163
00:07:20,279 --> 00:07:21,329
进行操作

164
00:07:21,329 --> 00:07:25,180
比如说this.note

165
00:07:25,180 --> 00:07:29,060
我们可以更改它的x和更改它的y啊

166
00:07:29,060 --> 00:07:30,860
this.note.x

167
00:07:30,860 --> 00:07:32,360
this.note.y

168
00:07:32,360 --> 00:07:34,519
那么这这些都是没有问题啊

169
00:07:34,519 --> 00:07:36,259
我们可以通过这些方式

170
00:07:36,279 --> 00:07:39,300
然后来去a公子写到这里了

171
00:07:40,779 --> 00:07:47,610
那么我们可以通过这种方式来访问我的x和y它的一个位置啊

172
00:07:47,610 --> 00:07:50,779
当然这个访问位置的话

173
00:07:52,660 --> 00:07:57,009
位置这个就和上面完全不是一个意思了

174
00:07:57,009 --> 00:08:01,000
我们空1号访问位置

175
00:08:01,000 --> 00:08:03,189
除了单独访问xy

176
00:08:03,189 --> 00:08:09,339
你还可以z.node.set position

177
00:08:09,819 --> 00:08:11,079
set position

178
00:08:11,079 --> 00:08:14,259
就是直接你这里面可以填一个xy

179
00:08:14,259 --> 00:08:16,120
比如说三四啊

180
00:08:16,120 --> 00:08:18,970
这样的话你就直接把你的位置设置到34了

181
00:08:18,970 --> 00:08:21,220
还或者还有一种方式

182
00:08:21,220 --> 00:08:23,699
this.node.set parent

183
00:08:24,399 --> 00:08:28,459
你可以在这里面去给他一个结构体

184
00:08:28,459 --> 00:08:30,139
这个结构体就是两个参数

185
00:08:30,139 --> 00:08:34,899
那么它也是这个cc名称空间提供的叫v2 v2 

186
00:08:34,899 --> 00:08:37,720
这个呃其实说白了就是v2 

187
00:08:37,720 --> 00:08:39,220
你就可以把它当做一个对象

188
00:08:39,220 --> 00:08:41,500
这个对象里面只有两个数

189
00:08:41,500 --> 00:08:43,120
一个是x

190
00:08:43,120 --> 00:08:45,240
一个是y啊

191
00:08:47,840 --> 00:08:49,519
v2 啊

192
00:08:49,519 --> 00:08:53,519
应该是vector 2

193
00:08:56,139 --> 00:08:58,299
v2 v2 啊

194
00:08:58,299 --> 00:08:59,080
这写错了

195
00:08:59,080 --> 00:09:02,139
reset position啊

196
00:09:02,139 --> 00:09:04,000
啊这样就没问题了啊

197
00:09:04,019 --> 00:09:05,639
这里注意啊

198
00:09:05,639 --> 00:09:07,500
就是你设置这个位置的时候

199
00:09:07,500 --> 00:09:12,039
你可以直接给他xy或者给他一个cc.v2 啊

200
00:09:12,039 --> 00:09:15,639
然后通过这个方法啊

201
00:09:15,639 --> 00:09:17,440
就是cos给我们提供的这个方法

202
00:09:17,440 --> 00:09:21,200
它会创建一个v2 的这样的一个对象啊

203
00:09:21,200 --> 00:09:22,940
就是这个对象名字就叫v2 

204
00:09:22,940 --> 00:09:23,480
然后为什么

205
00:09:23,480 --> 00:09:26,070
因为它里面就保存了一个x一个y啊

206
00:09:26,070 --> 00:09:27,299
就是这样的一个东西

207
00:09:27,299 --> 00:09:29,039
你也可以直接这样给它

208
00:09:29,039 --> 00:09:30,299
两种方式都行

209
00:09:33,120 --> 00:09:36,259
呃当然这个可能简单些

210
00:09:36,259 --> 00:09:37,820
我一般就用这个是吧啊

211
00:09:37,820 --> 00:09:39,259
一般没没什么特殊情况

212
00:09:39,259 --> 00:09:41,740
直接就给他xy就完了啊

213
00:09:41,740 --> 00:09:45,700
那比如说我们你看这个组件上面其他的什么旋转呀

214
00:09:45,700 --> 00:09:47,080
这些东西啊

215
00:09:47,080 --> 00:09:53,769
我们在这里都可以找到this.node.rotation

216
00:09:53,769 --> 00:09:54,639
对不对

217
00:09:54,639 --> 00:09:55,629
旋转啊

218
00:09:55,629 --> 00:09:56,500
旋转多少

219
00:09:56,500 --> 00:09:58,919
你直接给他角度就ok了啊

220
00:10:00,600 --> 00:10:02,279
啊旋转还有什么

221
00:10:02,279 --> 00:10:05,649
我们看啊skill缩放呀

222
00:10:05,649 --> 00:10:06,759
这个锚点呀

223
00:10:06,759 --> 00:10:07,679
对不对

224
00:10:08,759 --> 00:10:12,950
播放类似点node.skill

225
00:10:12,950 --> 00:10:15,409
你看scare有缩放

226
00:10:15,409 --> 00:10:21,330
也可以直接只对x的缩放或者y的缩放去进行设置啊

227
00:10:21,330 --> 00:10:22,559
都是可以的

228
00:10:24,080 --> 00:10:25,759
啊除了这个缩放

229
00:10:25,759 --> 00:10:26,539
还有锚点

230
00:10:26,539 --> 00:10:27,350
对不对

231
00:10:27,350 --> 00:10:30,120
锚点也可以通过代码去改

232
00:10:30,500 --> 00:10:32,870
你看锚点x的锚点

233
00:10:32,870 --> 00:10:36,440
y的锚点在哪里都是可以去修改的

234
00:10:36,600 --> 00:10:39,480
所以我们实际上在这里就是给了几个例子啊

235
00:10:39,480 --> 00:10:40,679
在这里可以看到的

236
00:10:40,679 --> 00:10:41,820
你都可以修改

237
00:10:41,820 --> 00:10:44,019
那么在这里再说一个

238
00:10:45,960 --> 00:10:46,799
说个什么

239
00:10:46,799 --> 00:10:47,759
我们看看

240
00:10:50,799 --> 00:10:52,360
我们说一个特殊的啊

241
00:10:52,360 --> 00:10:53,799
就是这里面所有的数值啊

242
00:10:53,799 --> 00:10:57,129
这些东西啊格式不像不用把所有的数值

243
00:10:57,129 --> 00:10:59,940
我们现在都可以通过这种方式访问啊

244
00:10:59,940 --> 00:11:01,799
你只要点一下都能都能出来

245
00:11:01,799 --> 00:11:04,080
因为我们知道这这就是脚本里面的属性

246
00:11:04,080 --> 00:11:05,259
对不对啊

247
00:11:05,259 --> 00:11:06,759
group我们后面常用啊

248
00:11:06,759 --> 00:11:08,470
但是我们后面具体再说

249
00:11:08,470 --> 00:11:09,940
这时候我们先不管它

250
00:11:09,940 --> 00:11:12,600
然后这里面有个颜色颜色的话

251
00:11:12,620 --> 00:11:14,120
那如果你要设置颜色

252
00:11:14,120 --> 00:11:20,710
你就可以类似点node.color就等于一个c c.color啊

253
00:11:20,710 --> 00:11:23,169
你这里可以直接用这个cc点

254
00:11:23,169 --> 00:11:26,399
它里面提供了一个color

255
00:11:26,399 --> 00:11:30,080
然后这个color里面呢有很多颜色啊

256
00:11:30,080 --> 00:11:31,429
你比如说蓝色

257
00:11:31,429 --> 00:11:32,360
黑色呀

258
00:11:32,360 --> 00:11:36,080
什么的绿色你看红色这里面都有啊

259
00:11:36,080 --> 00:11:36,860
黄色啊

260
00:11:36,860 --> 00:11:39,220
这里你可以直接给它颜色啊

261
00:11:39,220 --> 00:11:43,679
那一般这个我们不怎么会给他颜色啊

262
00:11:43,679 --> 00:11:47,159
一般就是用它这个本身的这个图片颜色显示

263
00:11:47,159 --> 00:11:51,759
比如说偶尔顶多就是比如说如果你要表示一个图片被人攻击了

264
00:11:51,759 --> 00:11:52,450
对不对

265
00:11:52,450 --> 00:11:56,200
有时候你就会让这个图片闪红一下啊

266
00:11:56,200 --> 00:11:58,600
你就可以把它的颜色设置为红啊

267
00:11:58,600 --> 00:11:59,139
设置为红

268
00:11:59,139 --> 00:12:01,299
过一会儿再设置回来就行了啊

269
00:12:01,299 --> 00:12:03,759
那这些咱们都完了再说啊

270
00:12:03,960 --> 00:12:06,480
那么总之目前就是先这样了

271
00:12:06,480 --> 00:12:09,990
那么这里有一个很重要的就是它作为一个节点

272
00:12:09,990 --> 00:12:11,279
它是可以开关的

273
00:12:11,279 --> 00:12:12,759
我们知道一个节点

274
00:12:13,259 --> 00:12:16,620
他这里这个开关属性我们是可以调的

275
00:12:16,620 --> 00:12:20,980
我们在这里比如说节点的一个开关

276
00:12:21,340 --> 00:12:22,570
怎样设置开关

277
00:12:22,570 --> 00:12:26,590
其实就是this.node.active

278
00:12:26,590 --> 00:12:29,679
就是它的开关是为true还是为false

279
00:12:29,679 --> 00:12:32,500
we force就证明我们把这个节点关了

280
00:12:32,500 --> 00:12:33,789
我们可以试一下

281
00:12:33,789 --> 00:12:34,779
把节点关了

282
00:12:34,779 --> 00:12:35,799
我们看一下

283
00:12:36,320 --> 00:12:39,259
那在这里我们发现不了任何东西啊

284
00:12:39,259 --> 00:12:40,220
发现不了任何东西

285
00:12:40,220 --> 00:12:43,000
我们在这边也看不到它被关了

286
00:12:43,000 --> 00:12:43,360
对不对

287
00:12:43,360 --> 00:12:45,399
也看不到什么节点被关了

288
00:12:45,399 --> 00:12:48,100
所以我们为了看得清楚一点

289
00:12:48,100 --> 00:12:50,500
我们这样我们放一个地面

290
00:12:50,500 --> 00:12:51,220
地面在这里

291
00:12:51,220 --> 00:12:51,700
对不对

292
00:12:51,700 --> 00:12:53,700
运行你肯定能看到这个地面

293
00:12:54,679 --> 00:12:56,840
那把地面加上这个脚本

294
00:12:56,840 --> 00:12:58,039
加上咱们的这个脚本

295
00:12:58,039 --> 00:13:01,860
因为脚本我们就让这个节点关掉了啊

296
00:13:02,120 --> 00:13:04,279
运行大家发现是黑的了

297
00:13:04,279 --> 00:13:05,360
没有那个地面了

298
00:13:05,360 --> 00:13:05,840
对不对

299
00:13:05,840 --> 00:13:07,440
没这个地面了啊

300
00:13:07,440 --> 00:13:08,700
你不要看这边啊

301
00:13:08,700 --> 00:13:11,580
这边显示的就是游戏运行前的一些属性

302
00:13:11,580 --> 00:13:13,220
它并不会实时刷新

303
00:13:13,240 --> 00:13:15,519
你看这里他还他还画着功能

304
00:13:15,519 --> 00:13:16,029
对不对

305
00:13:16,029 --> 00:13:20,379
但实际上我们现在真正跑起来的游戏已经看不到地面了

306
00:13:20,379 --> 00:13:24,279
那就是因为实际上它已经被我们给关掉了啊

307
00:13:25,500 --> 00:13:27,090
这是节点开关

308
00:13:27,090 --> 00:13:31,059
那么如果比如说我们有组件

309
00:13:31,299 --> 00:13:34,000
我们的组件也有开关

310
00:13:34,000 --> 00:13:37,559
就是我们知道我们在这边的组件唉

311
00:13:37,559 --> 00:13:40,620
比如说这个test他这个组件也有个开关

312
00:13:40,620 --> 00:13:41,250
对不对

313
00:13:41,250 --> 00:13:44,320
我想对这个组件进行开关怎么办

314
00:13:44,340 --> 00:13:50,320
this首先this在这里就代表了我们的test组件啊

315
00:13:50,320 --> 00:13:52,029
this就代表了test组件

316
00:13:52,029 --> 00:13:53,940
那么它的开关怎么办

317
00:13:54,000 --> 00:13:57,419
ea不就代表了当前组件是否开关

318
00:13:57,419 --> 00:13:59,879
也就是代表了这个勾是否画上

319
00:13:59,879 --> 00:14:04,039
就是这个组件是否使用上面这个就相当于这个节点整体的开关

320
00:14:04,039 --> 00:14:07,500
这个是一个组件额外的一个开关啊

321
00:14:07,500 --> 00:14:09,320
这个就是组件的开关

322
00:14:12,220 --> 00:14:13,360
组件开关啊

323
00:14:13,360 --> 00:14:16,090
就这样两两种开关搞清楚啊

324
00:14:16,090 --> 00:14:17,759
那么这里注意啊

325
00:14:17,759 --> 00:14:19,919
比如说我现在你看我test

326
00:14:19,919 --> 00:14:21,299
我能对我设置

327
00:14:21,299 --> 00:14:22,769
我能对no的设置

328
00:14:22,769 --> 00:14:24,659
如果我想拿到这个精灵

329
00:14:24,659 --> 00:14:26,539
对精灵组件设置怎么办

330
00:14:26,679 --> 00:14:29,940
那这就涉及到怎样获取组件了

331
00:14:32,220 --> 00:14:34,200
获取组件

332
00:14:34,200 --> 00:14:36,120
怎样获取组件

333
00:14:36,120 --> 00:14:37,559
获取组件非常简单

334
00:14:37,559 --> 00:14:40,220
直接在脚本里面

335
00:14:40,220 --> 00:14:42,220
this.get combent

336
00:14:42,899 --> 00:14:44,159
get confident

337
00:14:44,159 --> 00:14:46,159
就是你要获取什么组件

338
00:14:46,159 --> 00:14:50,360
然后在这里confident就填后面的括号

339
00:14:50,360 --> 00:14:51,799
填写组件的类型

340
00:14:51,799 --> 00:14:55,500
比如说c c点这里注意填写的是组件类型

341
00:14:55,500 --> 00:14:57,779
比如说这个类型是精灵类型

342
00:14:57,779 --> 00:15:01,610
你括号c6 点精灵

343
00:15:01,610 --> 00:15:04,820
那么它有一个返回值来

344
00:15:05,559 --> 00:15:08,080
那么这个spirit啊

345
00:15:08,080 --> 00:15:09,970
就是你得到的精灵组件

346
00:15:09,970 --> 00:15:13,919
你就可以对它spirit就可以进行操作了

347
00:15:13,919 --> 00:15:16,259
诶你又可以做精灵的一些操作了

348
00:15:16,259 --> 00:15:17,639
你也可以把精灵给开了

349
00:15:17,639 --> 00:15:19,259
把精灵这个组件给关了

350
00:15:19,259 --> 00:15:19,919
对不对

351
00:15:19,940 --> 00:15:22,100
这个就是获取组件的方式啊

352
00:15:22,100 --> 00:15:23,779
这就是获取组件的方式

353
00:15:23,779 --> 00:15:26,659
当然你可以如果有多个精灵

354
00:15:26,659 --> 00:15:29,659
你可以用这个这个返回值就一个数组啊

355
00:15:29,659 --> 00:15:32,149
就是如果当前一个节点就加了好多精灵

356
00:15:32,149 --> 00:15:34,960
你就可以获取负数的这样的组件

357
00:15:35,840 --> 00:15:41,559
还有一种获取方式是你可以this.get confident in children

358
00:15:42,379 --> 00:15:45,399
这个意思就是如果我有子物体

359
00:15:45,399 --> 00:15:50,200
那么我这个就是从子物体里面去得到什么类型的组件

360
00:15:50,200 --> 00:15:52,100
比如说cc点

361
00:15:52,279 --> 00:15:55,299
那么这个意思就是如果我有子物体

362
00:15:55,299 --> 00:16:00,070
我要在我的子物体里面去拿到精灵类型的组件啊

363
00:16:00,070 --> 00:16:04,399
也就是说如果我现在有个子物体啊

364
00:16:04,399 --> 00:16:05,600
我这个脚本在这儿呢

365
00:16:05,600 --> 00:16:08,659
那我会在我的子物体里面去找精灵组件

366
00:16:08,659 --> 00:16:09,840
并且把它返回

367
00:16:09,840 --> 00:16:10,620
那么注意啊

368
00:16:10,620 --> 00:16:11,879
像这些获取东西的

369
00:16:11,879 --> 00:16:13,049
如果你获取不到

370
00:16:13,049 --> 00:16:14,940
一般都是大啊

371
00:16:14,940 --> 00:16:16,019
就你获取失败了

372
00:16:16,019 --> 00:16:18,299
一般都是空啊

373
00:16:18,299 --> 00:16:19,679
或者是没获取到

374
00:16:20,159 --> 00:16:23,879
那么这就是咱们目前这个this.node啊

375
00:16:23,879 --> 00:16:27,179
它的一个常用的一些方法嗯

376
00:16:27,179 --> 00:16:28,259
或者属性啊

377
00:16:28,259 --> 00:16:30,240
我们现在先把这些搞定啊

378
00:16:30,240 --> 00:16:33,200
基本上节点就没什么问题了啊

379
00:16:33,639 --> 00:16:35,360
那我们这节课就这么多


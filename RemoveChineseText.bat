@echo off
echo Batch Rename Tool - Remove Chinese Auto-Generated Text
echo ========================================
echo.

powershell.exe -Command "& {Get-ChildItem -File | Where-Object {$_.Name -match '中文.*自动生成'} | ForEach-Object {$old=$_.Name; $new=$old -replace ' 中文（自动生成）',''; if($old -ne $new){if(Test-Path $new){Write-Host 'SKIP: '$old}else{Rename-Item $_.FullName $new; Write-Host 'DONE: '$old}}}}"

echo.
echo Operation completed!
pause

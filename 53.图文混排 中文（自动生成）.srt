1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,439 --> 00:00:13,759
ok这节课我们来讲一下附文本啊

3
00:00:13,759 --> 00:00:17,120
副文本其实就是用来做图文混排的啊

4
00:00:17,120 --> 00:00:18,800
那我们什么是副文本呢

5
00:00:18,800 --> 00:00:21,579
我们在这里又见创建一个渲染节点

6
00:00:21,820 --> 00:00:23,500
我们上节课说了文本

7
00:00:23,500 --> 00:00:25,460
那么下面其实还有个副文本

8
00:00:25,480 --> 00:00:27,519
我们可以选择创建一个副文本

9
00:00:27,519 --> 00:00:30,100
当然实际上我们创建的东西在这里都有

10
00:00:30,100 --> 00:00:31,780
你可以直接从这里拖过来

11
00:00:31,780 --> 00:00:33,219
也是一样的啊

12
00:00:33,219 --> 00:00:34,299
那么拖进来以后

13
00:00:34,299 --> 00:00:35,979
大家就可以看到所谓副本本

14
00:00:35,979 --> 00:00:38,619
你看虽然是一串字符

15
00:00:38,619 --> 00:00:41,350
但是它的这个表现形式是不同的

16
00:00:41,350 --> 00:00:43,359
比如说这里的颜色就是不同的

17
00:00:43,359 --> 00:00:44,039
对不对

18
00:00:44,039 --> 00:00:45,780
那么我们可以看到啊

19
00:00:45,780 --> 00:00:50,079
右边哎这里就变成了富文本啊

20
00:00:50,079 --> 00:00:55,869
那这个副文本string里面就填写你要显示的内容啊

21
00:00:55,869 --> 00:00:56,960
那么

22
00:01:00,520 --> 00:01:05,260
比如说我们在这里啊要显示一个我是红色啊

23
00:01:05,260 --> 00:01:06,980
我们把它原本的删了

24
00:01:07,000 --> 00:01:09,430
那么我们在这里我是红色

25
00:01:09,430 --> 00:01:14,349
我们怎样才能让这个红色两个字真正显示成红色呢

26
00:01:14,349 --> 00:01:15,579
很简单

27
00:01:16,280 --> 00:01:21,420
那么这里我们需要一个标签啊

28
00:01:21,420 --> 00:01:23,040
把它包围起来

29
00:01:23,040 --> 00:01:27,030
那这个东西实际上如果你之前对html了解的话

30
00:01:27,030 --> 00:01:28,579
应该很容易理解

31
00:01:28,579 --> 00:01:30,780
那首先啊这个标签是这样的

32
00:01:33,379 --> 00:01:36,290
我们创建一个

33
00:01:36,290 --> 00:01:38,340
文本吧

34
00:01:44,120 --> 00:01:50,219
那么比如说啊一个标签正常是由前标签和后标签组成的

35
00:01:50,239 --> 00:01:52,760
这个name代表的是标签的名称

36
00:01:52,760 --> 00:01:56,239
后面呢一定要有一个和它对应的啊

37
00:01:56,239 --> 00:01:57,560
标签结束

38
00:01:57,700 --> 00:02:00,819
然后中间呢就填写你包含起来的内容

39
00:02:00,819 --> 00:02:03,459
那么这个name就是你标签的名称

40
00:02:03,459 --> 00:02:05,719
那么标签一般都是成对出现的

41
00:02:05,739 --> 00:02:08,229
如果这个标签不需要成对出现

42
00:02:08,229 --> 00:02:12,310
那么它是需要用一个a斜杠结尾的

43
00:02:12,310 --> 00:02:14,780
那这个标签也是一个完整的标签

44
00:02:14,780 --> 00:02:17,860
所以实际上我们这里就用了标签的一个形式

45
00:02:18,020 --> 00:02:20,419
那么标签里面也是可以带属性的

46
00:02:20,419 --> 00:02:21,800
这里可以空个格

47
00:02:21,800 --> 00:02:23,930
可以通过哎

48
00:02:23,930 --> 00:02:27,439
比如说a等于b啊

49
00:02:27,439 --> 00:02:29,000
那么这就是一对属性

50
00:02:29,000 --> 00:02:30,319
a就是属性的名称

51
00:02:30,319 --> 00:02:31,759
b就是属性的值啊

52
00:02:31,759 --> 00:02:33,020
那么注意啊

53
00:02:33,020 --> 00:02:34,740
这是这种标签的形式

54
00:02:34,759 --> 00:02:37,039
我们在这里就可以给它标签的形式

55
00:02:37,039 --> 00:02:39,599
那么首先我们这里要说的就是颜色

56
00:02:39,879 --> 00:02:41,050
颜色的话

57
00:02:41,050 --> 00:02:43,460
名字就是个color啊

58
00:02:43,460 --> 00:02:45,409
color等于个什么呢

59
00:02:45,409 --> 00:02:46,639
那么这里注意啊

60
00:02:46,639 --> 00:02:49,699
这是人家副文本里面自己的一个写法啊

61
00:02:49,699 --> 00:02:53,939
color可以直接等于后面就要跟随你的颜色

62
00:02:53,960 --> 00:02:55,939
颜色是16进制颜色啊

63
00:02:55,939 --> 00:02:58,159
这个你可以从网上去找啊

64
00:02:58,159 --> 00:03:00,099
或者自己给个单色

65
00:03:03,000 --> 00:03:06,750
那么在这里实际上这个16进制颜色嗯

66
00:03:06,750 --> 00:03:08,520
就是这个三原色啊

67
00:03:08,520 --> 00:03:10,520
那么第一个就是红色

68
00:03:10,539 --> 00:03:13,900
那么每个颜色的值是从0~255的

69
00:03:13,900 --> 00:03:15,400
翻译成16进制啊

70
00:03:15,400 --> 00:03:19,539
这就是其实就是把020~250这个数值翻译成16进制

71
00:03:19,539 --> 00:03:20,860
就两个数来表示

72
00:03:20,860 --> 00:03:22,479
所以是三个颜色

73
00:03:22,479 --> 00:03:24,939
用来就表示了我们所有的这个颜色

74
00:03:24,939 --> 00:03:27,039
那么这个数值如果大家不清楚的话

75
00:03:27,039 --> 00:03:29,830
大家从网上搜索16进制颜色

76
00:03:29,830 --> 00:03:31,689
那么颜色就都出来

77
00:03:31,689 --> 00:03:33,539
那么红色

78
00:03:36,000 --> 00:03:38,189
包围起来对不对啊

79
00:03:38,189 --> 00:03:39,659
你也可以换个颜色

80
00:03:39,659 --> 00:03:42,810
比如说零零ff 00绿色

81
00:03:42,810 --> 00:03:43,560
对不对

82
00:03:43,560 --> 00:03:46,580
0000ff蓝色

83
00:03:46,580 --> 00:03:52,069
那红色加蓝色就是ff 0 ff就变成这种紫色了

84
00:03:52,069 --> 00:03:53,479
对不对啊

85
00:03:53,479 --> 00:03:56,360
那么这个就是颜色除了颜色啊

86
00:03:56,360 --> 00:03:57,560
符文本还支持什么

87
00:03:57,560 --> 00:04:02,699
比如说大号字体

88
00:04:03,840 --> 00:04:09,349
那么这个大号字体这个大字我们可以让它变得真正的大起来

89
00:04:09,349 --> 00:04:10,280
怎么怎么变

90
00:04:10,280 --> 00:04:13,580
就是size size啊

91
00:04:13,580 --> 00:04:15,080
等于一个字体

92
00:04:15,080 --> 00:04:16,879
比如说我们默认字体是40

93
00:04:16,879 --> 00:04:20,670
我们给他一个60诶

94
00:04:20,670 --> 00:04:23,339
把这个大给它包围起来

95
00:04:24,160 --> 00:04:27,699
那么这时候大家注意这个大是不是很明显

96
00:04:27,699 --> 00:04:31,199
很明显就已经变得非常非常大了啊

97
00:04:31,699 --> 00:04:34,220
是不是这个字体ok啊

98
00:04:34,220 --> 00:04:37,819
那么我们继续还有很多种字体

99
00:04:37,819 --> 00:04:41,300
比如说斜体啊

100
00:04:41,300 --> 00:04:44,720
那么斜体的话我们给个i啊

101
00:04:44,720 --> 00:04:46,100
这个简单一个a

102
00:04:46,100 --> 00:04:50,120
然后中间包含的内容就会变成斜体

103
00:04:50,120 --> 00:04:51,709
你看斜着了吧

104
00:04:51,709 --> 00:04:54,259
斜体啊以及什么呀

105
00:04:54,259 --> 00:04:56,779
带下划线的是u

106
00:04:58,620 --> 00:05:00,269
u

107
00:05:00,269 --> 00:05:03,759
这是个下划线

108
00:05:04,759 --> 00:05:06,680
你看是不是下划线就出来了

109
00:05:06,680 --> 00:05:07,220
很有意思

110
00:05:07,220 --> 00:05:08,000
对不对

111
00:05:08,480 --> 00:05:10,310
那么在这里啊

112
00:05:10,310 --> 00:05:11,120
还有一个

113
00:05:11,120 --> 00:05:14,240
那么这些大家一定要记录下来啊

114
00:05:14,240 --> 00:05:17,839
记录下来常用的这个符文本还有什么呀

115
00:05:17,839 --> 00:05:21,339
比如说描边啊

116
00:05:21,339 --> 00:05:23,500
描边其实也是挺常用的啊

117
00:05:23,500 --> 00:05:24,579
描边是挺常用的

118
00:05:24,579 --> 00:05:30,519
在这里我们可以outline说描边

119
00:05:30,519 --> 00:05:34,720
以前刚才呃我们写了这么多种是吧

120
00:05:34,720 --> 00:05:35,680
我先撤销回来

121
00:05:35,680 --> 00:05:37,500
我先说另外一个东西啊

122
00:05:38,740 --> 00:05:40,240
那么大家没有想过

123
00:05:40,240 --> 00:05:42,920
为什么一个标签能能有这么多内容

124
00:05:42,920 --> 00:05:44,899
实际上它一个富文本的标签

125
00:05:44,899 --> 00:05:46,939
就等于把多个标签组合起来了

126
00:05:46,939 --> 00:05:48,500
比如说你看我点我是

127
00:05:48,500 --> 00:05:50,209
其实这是一个小标签

128
00:05:50,209 --> 00:05:51,350
就是我试

129
00:05:51,350 --> 00:05:53,089
然后还点紫色

130
00:05:53,089 --> 00:05:56,019
你看就就就是单独的一个label

131
00:05:56,019 --> 00:05:56,680
叫做红色

132
00:05:56,680 --> 00:05:57,759
是紫色的

133
00:05:57,759 --> 00:06:02,180
所以实际上每一个不同的里面的这个内容

134
00:06:02,180 --> 00:06:04,519
它是给你单个标签显示出来的

135
00:06:04,519 --> 00:06:06,620
只是他给我们拼接起来了啊

136
00:06:06,620 --> 00:06:07,519
我我们不用管

137
00:06:07,519 --> 00:06:12,699
我们只管在这个最大的这个这一层里面编写这个副文本就好了啊

138
00:06:12,699 --> 00:06:13,389
注意这个

139
00:06:13,389 --> 00:06:15,519
那接下来我要描边

140
00:06:15,699 --> 00:06:17,439
我要给描边这两个字

141
00:06:17,439 --> 00:06:20,019
描边怎么描呢

142
00:06:20,759 --> 00:06:24,329
描边的话叫做outline

143
00:06:24,329 --> 00:06:27,160
右边也是个ou

144
00:06:27,220 --> 00:06:30,279
那那实际上它就已经有描边效果了

145
00:06:30,279 --> 00:06:31,480
但是我们现在看不出来

146
00:06:31,480 --> 00:06:33,360
我们可以改个描边的颜色

147
00:06:33,379 --> 00:06:34,639
描边颜色怎么改

148
00:06:34,639 --> 00:06:35,899
就是用属性方式

149
00:06:35,899 --> 00:06:38,180
color等于一个红色

150
00:06:38,579 --> 00:06:41,459
你看描的边是不是就出来了

151
00:06:41,459 --> 00:06:43,379
那么这个就是一个描边效果

152
00:06:43,379 --> 00:06:45,279
你还可以用什么绿色呀

153
00:06:46,319 --> 00:06:47,939
对吧等等啊

154
00:06:47,939 --> 00:06:49,649
其他的颜色你都可以去用

155
00:06:49,649 --> 00:06:51,720
那么这个就是一个描边啊

156
00:06:51,720 --> 00:06:52,939
也是很有用的

157
00:06:53,259 --> 00:06:57,160
还有一个有时候我们的字体是可以点击的

158
00:06:57,160 --> 00:06:59,379
点击是可以调事件的啊

159
00:06:59,379 --> 00:07:02,779
比如说我们这里先创建一个新的脚本

160
00:07:05,079 --> 00:07:07,689
脚本的名字就随意吧

161
00:07:07,689 --> 00:07:10,420
然后我们把这个脚本挂到他的身上来

162
00:07:12,220 --> 00:07:13,680
打开这个脚本

163
00:07:13,899 --> 00:07:17,350
比如说我这里脚本里面给他一个方法啊

164
00:07:17,350 --> 00:07:19,639
叫做test测试的意思

165
00:07:20,000 --> 00:07:23,180
康熙路点debug

166
00:07:26,120 --> 00:07:30,339
点击了只要一掉test就会打印

167
00:07:30,339 --> 00:07:31,959
点击了非常简单

168
00:07:31,959 --> 00:07:33,459
是不是非常简单

169
00:07:33,459 --> 00:07:37,819
那么在这里我们回来我们怎样调这个方法呢

170
00:07:37,819 --> 00:07:40,939
都就通过这个标签里面的内容去调用

171
00:07:40,939 --> 00:07:45,529
比如说点击调用

172
00:07:45,529 --> 00:07:48,079
那么我们希望让这两个点击

173
00:07:48,079 --> 00:07:51,259
让这个点击这两个字可以真正的被点击

174
00:07:51,259 --> 00:07:51,980
怎么办

175
00:07:51,980 --> 00:07:53,279
很简单

176
00:07:53,459 --> 00:07:55,560
二包围起来

177
00:07:55,560 --> 00:07:59,579
点击on就是事件是不是在我们代码

178
00:07:59,579 --> 00:08:03,279
那么在这里面on就是可以被点击那么一样的

179
00:08:03,279 --> 00:08:04,720
其实就是给他加个时间

180
00:08:04,720 --> 00:08:07,180
然后点击以后会掉什么方法

181
00:08:07,180 --> 00:08:09,740
调cr c k click

182
00:08:10,079 --> 00:08:13,019
然后叫test test方法

183
00:08:13,019 --> 00:08:18,899
那么调的这个方法必须在我当前这个呃组件上面挂载着

184
00:08:18,899 --> 00:08:20,399
啊就必须在这个脚本里面

185
00:08:20,399 --> 00:08:22,860
你不要说哎别的组件啊

186
00:08:22,860 --> 00:08:25,319
就别的这个这个节点上面挂了一个脚本

187
00:08:25,319 --> 00:08:26,699
我能不能从这调用啊

188
00:08:26,699 --> 00:08:27,300
那个调不了

189
00:08:27,300 --> 00:08:28,959
我只能调到自己身上

190
00:08:28,980 --> 00:08:30,899
所以哎他这里看了

191
00:08:30,899 --> 00:08:32,639
我们要调一个test方法

192
00:08:32,639 --> 00:08:35,960
他就在整个属性面板里面找找

193
00:08:35,960 --> 00:08:37,759
看看哪个脚本里面有test的方法

194
00:08:37,759 --> 00:08:39,169
就是找到我们这个脚本了

195
00:08:39,169 --> 00:08:40,639
那我们来试一下

196
00:08:43,080 --> 00:08:45,120
那我们首先点击这个调用

197
00:08:45,120 --> 00:08:47,039
我们发现没有点点击

198
00:08:47,039 --> 00:08:51,879
你看是不是就出来了哎所以就很好用

199
00:08:51,879 --> 00:08:55,539
那么为了更体现出我们这个点击是可以被点击的

200
00:08:55,539 --> 00:08:57,730
我们可以进行嵌套使用

201
00:08:57,730 --> 00:09:00,639
也就说他这些是支持嵌套的

202
00:09:00,639 --> 00:09:01,899
比如说给它变颜色

203
00:09:01,899 --> 00:09:04,200
color等于红色

204
00:09:05,379 --> 00:09:07,659
然后再on的后

205
00:09:07,659 --> 00:09:08,419
外面

206
00:09:10,379 --> 00:09:11,789
我们进行一个嵌套

207
00:09:11,789 --> 00:09:14,100
这样的话就是它既改变了颜色

208
00:09:14,100 --> 00:09:15,269
也可以被我们点击

209
00:09:15,269 --> 00:09:16,419
我们再试一下

210
00:09:18,240 --> 00:09:19,649
调用不管用

211
00:09:19,649 --> 00:09:21,450
点击你看就管用

212
00:09:21,450 --> 00:09:23,799
是不是它是可以嵌套使用的

213
00:09:25,379 --> 00:09:30,600
ok再来说个更高级的图文混排啊

214
00:09:30,600 --> 00:09:32,519
就是图片和文字要混合起来

215
00:09:32,519 --> 00:09:35,340
比如说这个文字中间我们可以放上图片

216
00:09:35,340 --> 00:09:37,230
那么这个东西怎么去做

217
00:09:37,230 --> 00:09:40,049
首先我们需要一个图集

218
00:09:40,049 --> 00:09:44,080
那么图集我们就用之前那个之前我们做的啊

219
00:09:44,080 --> 00:09:46,080
这个愤怒小鸟这个突击

220
00:09:46,100 --> 00:09:49,220
比如说我们要加载这个地面land啊

221
00:09:49,220 --> 00:09:52,659
这个地面啊就是这是一个地面吗

222
00:09:52,659 --> 00:09:55,929
然后地面把这个地面真正显示出来啊

223
00:09:55,929 --> 00:09:56,679
好不好

224
00:09:56,679 --> 00:09:58,210
那么我们就这样去做

225
00:09:58,210 --> 00:09:59,500
在这里怎么去做

226
00:09:59,500 --> 00:10:04,240
我们首先是这是一个地面吗

227
00:10:04,820 --> 00:10:09,320
然后这个地面后面我们显示一个图片

228
00:10:09,320 --> 00:10:11,580
然后妈显示在这个图片的后面

229
00:10:11,600 --> 00:10:14,480
那这样的话就把图片和文字混合排在一起了

230
00:10:14,480 --> 00:10:15,159
对不对

231
00:10:15,179 --> 00:10:16,110
怎么做

232
00:10:16,110 --> 00:10:19,679
首先要把这个图集拖到这个位置啊

233
00:10:19,679 --> 00:10:23,370
意思是我们要和这个图集里面的内容混合呃

234
00:10:23,370 --> 00:10:25,960
就是进行一个混合这个布局了

235
00:10:26,659 --> 00:10:31,259
然后在这里地面后面这里要放图集

236
00:10:31,259 --> 00:10:33,580
我们这里就要写个image

237
00:10:33,639 --> 00:10:36,220
然后这个是单节点的

238
00:10:36,220 --> 00:10:37,659
所以直接斜杠就行了

239
00:10:37,659 --> 00:10:39,159
但是它是有属性的

240
00:10:39,159 --> 00:10:46,059
所以在image后面空格src src指的就是名称啊

241
00:10:46,059 --> 00:10:49,460
你要加载的这个图图片名称我们叫做land

242
00:10:49,460 --> 00:10:50,929
就是图集里面的名称

243
00:10:50,929 --> 00:10:56,149
那这时候大家就已经可以看到这是一个这个吗

244
00:10:56,149 --> 00:10:58,580
而且它也是可以加时间的啊

245
00:10:58,580 --> 00:11:00,320
就是你可以加第二个属性啊

246
00:11:00,320 --> 00:11:06,799
嗯就是click等于test也是可以的

247
00:11:06,799 --> 00:11:07,639
我们可以试一下

248
00:11:07,639 --> 00:11:08,639
运行一下

249
00:11:09,100 --> 00:11:10,779
你看这是一个地面吗

250
00:11:10,779 --> 00:11:12,580
点一下

251
00:11:12,580 --> 00:11:14,740
又不出来

252
00:11:17,580 --> 00:11:19,620
点一下你看点击了啊

253
00:11:19,620 --> 00:11:20,460
别的就不管用

254
00:11:20,460 --> 00:11:21,580
这个就可以点击

255
00:11:21,580 --> 00:11:23,919
那么这个就是图文混排了啊

256
00:11:23,919 --> 00:11:26,419
我们那个比如说聊天打字

257
00:11:26,419 --> 00:11:27,440
然后加个表情

258
00:11:27,440 --> 00:11:27,919
对不对

259
00:11:27,919 --> 00:11:28,879
表情怎么做啊

260
00:11:28,879 --> 00:11:32,240
就是可以用这种方式来进行去制作

261
00:11:33,019 --> 00:11:33,980
非常方便

262
00:11:33,980 --> 00:11:36,559
是不是做完以后你看它完完全是一体的

263
00:11:36,559 --> 00:11:39,200
但是实际上我们单独点这个图片

264
00:11:39,279 --> 00:11:41,860
你看它它实际上是个精灵啊

265
00:11:41,860 --> 00:11:43,960
它单独实际上是个精灵啊

266
00:11:43,960 --> 00:11:46,620
只是这是个文字是吧

267
00:11:46,620 --> 00:11:47,429
这是个精灵

268
00:11:47,429 --> 00:11:48,539
这又是个文字

269
00:11:48,539 --> 00:11:50,279
他完全给你分开了啊

270
00:11:50,279 --> 00:11:51,980
这个level

271
00:11:52,000 --> 00:11:52,899
然后精灵

272
00:11:52,899 --> 00:11:53,980
然后又是个label

273
00:11:53,980 --> 00:11:55,720
然后最后给我们集合在一起

274
00:11:55,720 --> 00:11:58,440
就是我们的这个符文的

275
00:11:58,460 --> 00:12:03,379
当然我们知道他这个是怎么做的就ok了

276
00:12:03,379 --> 00:12:04,519
我们也不需要去管

277
00:12:04,519 --> 00:12:05,700
不需要去操心

278
00:12:05,720 --> 00:12:07,820
只是我们这我们要注意一点啊

279
00:12:07,820 --> 00:12:09,980
比如说我要把这个富文本一个地儿

280
00:12:09,980 --> 00:12:11,659
你千万不敢这样一选

281
00:12:11,659 --> 00:12:12,980
点一下就去移了

282
00:12:12,980 --> 00:12:16,470
那你实际上把它里面的其中一个部分移走了

283
00:12:16,470 --> 00:12:17,639
对不对啊

284
00:12:17,639 --> 00:12:19,200
那这样就出问题了啊

285
00:12:19,200 --> 00:12:23,039
那你看他就给你混合的这个这个位置就变了

286
00:12:23,039 --> 00:12:24,600
所以要移动符文本

287
00:12:24,600 --> 00:12:27,659
一定要选中这个符文本再去移动啊

288
00:12:27,659 --> 00:12:29,139
这样的话就ok了

289
00:12:29,259 --> 00:12:35,620
那么ok这节课我们就说了这么多嗯


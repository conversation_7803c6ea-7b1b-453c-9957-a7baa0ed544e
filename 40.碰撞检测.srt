1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:08,720 --> 00:00:11,750
ok那么这节课咱们来说一下碰撞

3
00:00:11,750 --> 00:00:17,760
碰撞这些东西其实呃是我们很重要的一个东西啊

4
00:00:17,760 --> 00:00:19,320
因为它太常用了

5
00:00:19,480 --> 00:00:22,839
嗯那么碰撞的话对于我们来说有两种

6
00:00:22,839 --> 00:00:24,579
一种是基于物理系统的

7
00:00:24,579 --> 00:00:27,440
还有一种是普通的不基于物理系统的

8
00:00:27,460 --> 00:00:29,440
那这个就是咱们普通的啊

9
00:00:29,440 --> 00:00:33,799
我们先说我们这个呃没有物理系统的这样的一个碰撞

10
00:00:33,979 --> 00:00:36,380
那么什么什么意思是碰撞的

11
00:00:36,380 --> 00:00:41,270
那当然嗯顾名思义是不是拖两个图片上来

12
00:00:41,270 --> 00:00:44,179
当这两个图片产生相交的时候

13
00:00:44,179 --> 00:00:48,369
那么这两个精灵是不是就会产生碰撞

14
00:00:48,369 --> 00:00:50,259
那我们就应该得到一个消息

15
00:00:50,259 --> 00:00:53,000
说两个精灵发生碰撞了

16
00:00:53,000 --> 00:00:54,560
但是这里注意啊

17
00:00:54,560 --> 00:00:58,609
我们所有看上去产真正产生碰撞的这个东西啊

18
00:00:58,609 --> 00:01:00,200
你不要看着像碰撞

19
00:01:00,200 --> 00:01:01,700
你就认为它碰撞了

20
00:01:02,119 --> 00:01:04,159
真正产生碰撞的啊

21
00:01:04,159 --> 00:01:05,420
并不是他们本身

22
00:01:05,420 --> 00:01:07,760
他们只是把这个内容显示出来了

23
00:01:07,760 --> 00:01:12,620
所以我们在这左边右边有这样一个遮盖关系是没错

24
00:01:12,620 --> 00:01:17,099
但是他并不知道是否碰撞啊

25
00:01:17,099 --> 00:01:21,299
所以说如果我们就是现在想让系统告诉我们是否碰撞了

26
00:01:21,299 --> 00:01:22,379
系统也不知道

27
00:01:22,379 --> 00:01:24,719
那么怎么做才能做到碰撞呢

28
00:01:24,719 --> 00:01:31,459
我们需要在我们想让它检测碰撞的物体身上去添加一个碰撞组件

29
00:01:31,480 --> 00:01:33,459
只有他加了碰撞组件

30
00:01:33,459 --> 00:01:37,159
它才能去检测是否产生的碰撞

31
00:01:37,819 --> 00:01:41,659
那么在这里我们加一个碰撞组件有三种

32
00:01:41,659 --> 00:01:44,379
第一种我们加上以后

33
00:01:44,379 --> 00:01:50,060
我们发现诶仔细看它的周围有这个绿色的线框

34
00:01:50,140 --> 00:01:53,379
那么在这里面有一些内容我们来说一下啊

35
00:01:53,379 --> 00:01:54,400
首先有宽高

36
00:01:54,400 --> 00:01:55,060
宽的话

37
00:01:55,060 --> 00:01:57,319
一般就是它本身我们往大的拉

38
00:01:57,959 --> 00:02:01,659
大家发现是不是这个框就已经变宽了

39
00:02:01,659 --> 00:02:05,260
那么这个啊这个就是它的这个大小

40
00:02:05,260 --> 00:02:09,680
我们一般不去改它就是原本大小宽高都可以去改

41
00:02:09,699 --> 00:02:15,550
还有xy你看可以把整个这个呃偏移啊

42
00:02:15,550 --> 00:02:17,620
把这个当前的碰撞体偏移

43
00:02:17,620 --> 00:02:19,389
那么一般也不要动它

44
00:02:19,389 --> 00:02:25,120
它默认就和我们这个精灵一样大啊

45
00:02:25,120 --> 00:02:29,659
也就是说实际上真正会检测碰撞的就是这个区域啊

46
00:02:29,659 --> 00:02:32,539
而我们这个真正会显示的就是这个精灵

47
00:02:32,539 --> 00:02:33,800
它是不会检测碰撞的

48
00:02:33,800 --> 00:02:36,300
真正检测检测碰撞的是这个方框

49
00:02:36,379 --> 00:02:38,180
那么在他这里面有个tag值

50
00:02:38,180 --> 00:02:40,039
tag值就是无非给他一个标记

51
00:02:40,039 --> 00:02:41,939
比如说我给他标记成一啊

52
00:02:41,939 --> 00:02:42,840
这个无所谓

53
00:02:42,840 --> 00:02:44,340
在这里还没有用

54
00:02:44,340 --> 00:02:46,680
一会儿我们碰撞写代码的时候

55
00:02:46,680 --> 00:02:48,300
你就知道它在哪用了

56
00:02:48,300 --> 00:02:50,099
然后这里有个编辑

57
00:02:50,099 --> 00:02:51,539
如果我们把编辑勾上

58
00:02:51,539 --> 00:02:54,439
大家可以看这个角就有这个小点儿了

59
00:02:54,439 --> 00:02:59,419
这时候我们就可以通过手动拖拽的方式来修改我们的这个碰撞

60
00:02:59,439 --> 00:03:01,539
它的一个数值了啊

61
00:03:01,539 --> 00:03:05,560
一般我们呃需要修改的时候把它勾上

62
00:03:05,560 --> 00:03:06,430
不需要修改

63
00:03:06,430 --> 00:03:07,900
取消它就行了

64
00:03:07,900 --> 00:03:08,620
除此之外

65
00:03:08,620 --> 00:03:09,879
还有一些封装组件

66
00:03:09,879 --> 00:03:13,439
比如说有圆形啊

67
00:03:13,439 --> 00:03:14,039
圆形的话

68
00:03:14,039 --> 00:03:16,919
当然下面这个就是一个半径啊

69
00:03:16,919 --> 00:03:18,330
这是一个圆形的碰撞

70
00:03:18,330 --> 00:03:20,310
其他的内容是一模一样的

71
00:03:20,310 --> 00:03:23,139
还有一个多边形啊

72
00:03:23,139 --> 00:03:25,419
这个用的就相对而言少一些

73
00:03:25,419 --> 00:03:26,620
一般不是圆形

74
00:03:26,620 --> 00:03:28,099
就是方形啊

75
00:03:28,099 --> 00:03:28,759
性能也高

76
00:03:28,759 --> 00:03:29,599
比如多边形

77
00:03:29,599 --> 00:03:30,620
大家其实可以看一下

78
00:03:30,620 --> 00:03:35,319
就是编辑的时候你可以哎拉的就是奇形怪状

79
00:03:35,319 --> 00:03:36,310
对不对啊

80
00:03:36,310 --> 00:03:39,580
拉的奇形怪状也可以去添加点

81
00:03:39,919 --> 00:03:42,139
这现在是五个点

82
00:03:42,139 --> 00:03:44,879
我可以再加一个六个

83
00:03:44,879 --> 00:03:45,449
对不对

84
00:03:45,449 --> 00:03:46,379
是多边形

85
00:03:46,379 --> 00:03:47,099
就是这样

86
00:03:47,099 --> 00:03:50,039
但是它的话由于规则呃

87
00:03:50,039 --> 00:03:52,800
它本身是一个不规则的一个图形

88
00:03:52,800 --> 00:03:55,860
所以用它检测性能消耗稍微高一些啊

89
00:03:55,860 --> 00:03:58,469
所以一般我们在这里就用方形或者圆形

90
00:03:58,469 --> 00:04:00,150
那像这个也是个方形

91
00:04:00,150 --> 00:04:02,449
那我给它写个二

92
00:04:02,449 --> 00:04:04,860
那这时候我们就看出它的用处了

93
00:04:04,879 --> 00:04:07,219
在系统里面当它产生碰撞的时候

94
00:04:07,219 --> 00:04:09,259
他们就拿这个区分啊

95
00:04:09,259 --> 00:04:10,520
你这个碰到的是几

96
00:04:10,539 --> 00:04:11,740
如果大家注意啊

97
00:04:11,740 --> 00:04:12,699
在我们写代码的时候

98
00:04:12,699 --> 00:04:14,560
我们不需要区分你

99
00:04:14,560 --> 00:04:15,879
这也没必要给它

100
00:04:15,879 --> 00:04:16,930
就是你给它零

101
00:04:16,930 --> 00:04:19,279
它也是能检测出来的啊

102
00:04:19,279 --> 00:04:22,250
那么这里只是让我们看起来知道诶

103
00:04:22,250 --> 00:04:24,319
我们碰到几号的了

104
00:04:24,759 --> 00:04:27,279
哎这一会儿我们通过代码输出出来

105
00:04:27,279 --> 00:04:28,420
大家就明白了

106
00:04:28,779 --> 00:04:31,120
首先默认情况下检测不到

107
00:04:31,120 --> 00:04:33,220
当我变成这种情况的时候

108
00:04:33,220 --> 00:04:36,160
这两个是不是应该产生碰撞了啊

109
00:04:36,160 --> 00:04:38,319
就是1号和2号应该产生碰撞了

110
00:04:38,319 --> 00:04:40,220
那我现在去写一个脚本

111
00:04:42,439 --> 00:04:45,889
我把这个脚本给了1号

112
00:04:45,889 --> 00:04:47,319
给了1号

113
00:04:50,339 --> 00:04:51,540
没加上来吗

114
00:04:51,540 --> 00:04:52,480
加上来了

115
00:04:53,899 --> 00:04:58,620
在它里面我要做这个碰撞检测怎么去做啊

116
00:04:58,620 --> 00:05:00,660
在它里面怎么去做这个碰撞检测

117
00:05:00,660 --> 00:05:01,920
很简单啊

118
00:05:01,920 --> 00:05:02,699
很简单

119
00:05:02,699 --> 00:05:05,339
首先我第一步我一定要开启它

120
00:05:05,339 --> 00:05:09,899
默认这个碰撞检测是没有开启的啊

121
00:05:09,899 --> 00:05:10,860
是没有开启的

122
00:05:10,860 --> 00:05:13,980
我们把它开启开启非常简单

123
00:05:13,980 --> 00:05:18,079
c c点导演点get碰撞系统

124
00:05:18,079 --> 00:05:22,250
然后点inable等于true就可以了

125
00:05:22,250 --> 00:05:24,339
非常简单非常简单

126
00:05:25,699 --> 00:05:27,740
那么一旦开始检测

127
00:05:27,740 --> 00:05:30,319
我们就可以先写几个回调啊

128
00:05:30,319 --> 00:05:36,459
首先最常用的u克拉森恩特

129
00:05:38,740 --> 00:05:42,759
这里我们先给其实准确来说两个参数

130
00:05:42,759 --> 00:05:46,149
但是一般嗯第二个参数没太大用啊

131
00:05:46,149 --> 00:05:48,009
因为第二个参数就代表自己

132
00:05:48,009 --> 00:05:50,379
第一个参数就是我碰到的别人啊

133
00:05:50,379 --> 00:05:51,579
所以虽然有这两个参数

134
00:05:51,579 --> 00:05:54,620
但是大家完全可以只写一个

135
00:05:54,620 --> 00:06:01,839
那么这个意思就是产生碰撞会调用一次

136
00:06:01,839 --> 00:06:04,569
那么我们现在先输出一下

137
00:06:04,569 --> 00:06:07,040
看一下有没有产生这个碰撞

138
00:06:08,540 --> 00:06:09,980
碰撞发生

139
00:06:11,839 --> 00:06:15,819
然后我们在这边我们来运行一下

140
00:06:18,620 --> 00:06:20,939
那这时候大家可以看一下啊

141
00:06:21,600 --> 00:06:24,660
碰撞产生了这个碰撞就产生了

142
00:06:24,660 --> 00:06:26,759
就是因为他们俩挨到了

143
00:06:26,759 --> 00:06:29,040
所以在最开始碰撞检测的时候

144
00:06:29,040 --> 00:06:30,959
他直接就检测到这个碰撞

145
00:06:30,959 --> 00:06:33,600
如果你把他俩拉开

146
00:06:34,480 --> 00:06:36,399
然后重新运行

147
00:06:36,399 --> 00:06:41,259
这时候你再去看就没有任何内容了啊

148
00:06:41,259 --> 00:06:44,379
因为这时候他俩是没有挨到

149
00:06:44,379 --> 00:06:46,899
挨到以后才会检测到这个碰撞啊

150
00:06:46,899 --> 00:06:48,779
a到以后才会检测到碰撞

151
00:06:49,060 --> 00:06:52,899
那么碰撞的话我们怎么知道碰到哪个

152
00:06:52,899 --> 00:06:56,680
有时候我希望比如说我知道我碰到的是敌人还是碰到的是子弹

153
00:06:56,680 --> 00:06:57,959
碰到子弹我就死

154
00:06:57,980 --> 00:07:00,439
碰到敌人我我就怎样

155
00:07:00,439 --> 00:07:01,589
是不是啊

156
00:07:01,589 --> 00:07:04,170
就是碰到不同的是我们要有不同的逻辑

157
00:07:04,170 --> 00:07:10,360
那这时候我们就可以给不同的碰撞体赋值不同的这个标签了

158
00:07:10,360 --> 00:07:12,579
比如说你看我们现在碰到的是这个地面

159
00:07:12,579 --> 00:07:13,540
地面是2号

160
00:07:13,540 --> 00:07:14,560
如果有个敌人

161
00:07:14,560 --> 00:07:16,740
我可以给敌人tag值为3号

162
00:07:16,759 --> 00:07:18,980
这样的话我们就可以通过不同的tag值

163
00:07:18,980 --> 00:07:21,100
然后来做不同的逻辑判断了

164
00:07:21,120 --> 00:07:23,959
那我们在这里可以试一下碰撞发生

165
00:07:24,180 --> 00:07:27,980
看一下能不能把other的tag值打出来

166
00:07:30,480 --> 00:07:31,519
运行

167
00:07:33,639 --> 00:07:36,100
碰撞发生二没有问题吧

168
00:07:36,100 --> 00:07:37,540
我们碰到了一个物体

169
00:07:37,540 --> 00:07:38,620
并且碰到的物体

170
00:07:38,620 --> 00:07:40,660
它它的标签是多少是二

171
00:07:40,680 --> 00:07:44,129
那这个标签在这里含义就非常明显了

172
00:07:44,129 --> 00:07:46,339
它就是用来区分你碰到的东西的

173
00:07:46,339 --> 00:07:47,209
对不对

174
00:07:47,209 --> 00:07:48,620
你可以给一个敌人

175
00:07:48,620 --> 00:07:49,519
你再加个敌人

176
00:07:49,519 --> 00:07:50,240
再给个碰撞

177
00:07:50,240 --> 00:07:51,019
给个三

178
00:07:51,019 --> 00:07:53,180
然后这样的话你给它不同的物体

179
00:07:53,180 --> 00:07:56,180
他就知道哎我碰到的是二还是碰到的是三

180
00:07:56,180 --> 00:07:58,279
那咱们就在代码里面通过逻辑

181
00:07:58,279 --> 00:08:00,100
如果碰到二我做什么事

182
00:08:00,100 --> 00:08:01,360
如果碰到三做什么事

183
00:08:01,360 --> 00:08:02,379
就是这样

184
00:08:02,379 --> 00:08:04,699
那么在这里碰撞的话

185
00:08:05,079 --> 00:08:07,329
不光有个产生碰撞

186
00:08:07,329 --> 00:08:09,019
还有两个

187
00:08:11,399 --> 00:08:13,980
state一个是持续啊

188
00:08:13,980 --> 00:08:15,360
碰撞的一个持续

189
00:08:15,360 --> 00:08:16,579
还有一个

190
00:08:19,939 --> 00:08:21,319
碰撞的一个离开

191
00:08:21,319 --> 00:08:22,680
碰撞的一个结束

192
00:08:24,459 --> 00:08:29,019
那么当我们碰撞发生了以后啊

193
00:08:31,980 --> 00:08:35,179
如果我们碰撞持续

194
00:08:35,179 --> 00:08:36,799
如果我们一直在碰撞

195
00:08:36,799 --> 00:08:38,360
那么它就会一直掉

196
00:08:38,360 --> 00:08:39,830
这个碰撞持续方法

197
00:08:39,830 --> 00:08:41,639
如果碰撞结束了

198
00:08:41,658 --> 00:08:45,179
也就是说比如说我们两个物体在游戏逻辑里面

199
00:08:45,179 --> 00:08:46,799
两个物体分开了啊

200
00:08:46,799 --> 00:08:51,730
就两个碰撞框在运行游戏的期间碰撞结束了

201
00:08:51,730 --> 00:08:54,539
那么它就会叫这个方法啊

202
00:08:54,539 --> 00:08:56,159
意思是诶这个是开始碰撞

203
00:08:56,159 --> 00:08:56,879
这是碰撞中

204
00:08:56,879 --> 00:08:58,200
这是碰撞结束

205
00:08:58,200 --> 00:08:59,940
我们在这里碰撞结束

206
00:08:59,940 --> 00:09:00,299
看不见

207
00:09:00,299 --> 00:09:01,580
我们能看一下

208
00:09:04,580 --> 00:09:05,480
持续中

209
00:09:05,480 --> 00:09:07,820
你看一直叫用速度多快

210
00:09:07,820 --> 00:09:08,179
对不对

211
00:09:08,179 --> 00:09:09,879
345678 400 500

212
00:09:09,899 --> 00:09:10,889
很快啊

213
00:09:10,889 --> 00:09:12,779
很快每一帧掉一个

214
00:09:12,779 --> 00:09:13,980
因为我这个帧率很高

215
00:09:13,980 --> 00:09:15,000
我在100多

216
00:09:15,000 --> 00:09:15,600
对不对

217
00:09:15,600 --> 00:09:17,139
我的帧率是在100多

218
00:09:17,419 --> 00:09:22,100
那么ok我们把它停下来

219
00:09:24,879 --> 00:09:28,639
封装持续都刷新成这个工程了

220
00:09:31,379 --> 00:09:36,840
那么现在啊我们知道碰撞主要就是这三个

221
00:09:36,840 --> 00:09:39,659
那么后面还有一个逗号self啊

222
00:09:39,659 --> 00:09:40,559
这里就是我说的

223
00:09:40,559 --> 00:09:44,539
如果啊你这个后面加个cf

224
00:09:44,539 --> 00:09:47,059
你cf.tag值其实就是自己的碰撞体

225
00:09:47,059 --> 00:09:49,730
也就是这个self实际上就是自己的碰撞体

226
00:09:49,730 --> 00:09:54,080
这个是others是你碰到的别人的那个碰撞体啊

227
00:09:54,080 --> 00:09:56,360
那么这个注意一下啊

228
00:09:56,379 --> 00:09:59,559
嗯所以实际上碰撞就是这样去做啊

229
00:09:59,559 --> 00:10:01,120
我们碰撞就可以这样去检测

230
00:10:01,120 --> 00:10:02,740
非常简单非常简单

231
00:10:02,740 --> 00:10:03,759
只有一点

232
00:10:03,759 --> 00:10:05,919
一个是你不要忘了加碰撞器

233
00:10:05,919 --> 00:10:07,419
你不要光写这个没加碰撞器

234
00:10:07,419 --> 00:10:10,559
第二个就是不要忘了开这个碰撞检测啊

235
00:10:10,559 --> 00:10:11,100
就ok了

236
00:10:11,100 --> 00:10:12,480
这是默认的碰撞方法

237
00:10:12,480 --> 00:10:16,179
那后期我们学了这个呃物理效果以后啊

238
00:10:16,179 --> 00:10:17,799
物理有他自己的一套碰撞

239
00:10:17,799 --> 00:10:20,019
那到时候我们再看啊

240
00:10:20,019 --> 00:10:22,179
ok那么我们这节课就这么多


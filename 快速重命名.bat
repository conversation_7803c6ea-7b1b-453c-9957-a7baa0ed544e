@echo off
chcp 65001 >nul
echo ========================================
echo    快速批量重命名工具
echo    删除文件名中的" 中文（自动生成）"
echo ========================================
echo.

powershell -Command "& {$files = Get-ChildItem -File | Where-Object { $_.Name -like '*中文（自动生成）*' }; if ($files.Count -eq 0) { Write-Host '当前目录下没有找到需要重命名的文件' -ForegroundColor Yellow; Read-Host '按回车键退出'; exit }; Write-Host \"找到 $($files.Count) 个文件需要重命名:\" -ForegroundColor Green; Write-Host ''; foreach ($file in $files) { $newName = $file.Name -replace ' 中文（自动生成）', ''; Write-Host \"$($file.Name) -> $newName\" -ForegroundColor Cyan }; Write-Host ''; $confirm = Read-Host '确认要执行重命名操作吗？(y/n)'; if ($confirm -eq 'y' -or $confirm -eq 'Y') { Write-Host ''; Write-Host '开始重命名...' -ForegroundColor Green; $success = 0; $failed = 0; foreach ($file in $files) { try { $newName = $file.Name -replace ' 中文（自动生成）', ''; if (Test-Path $newName) { Write-Host \"[跳过] $($file.Name) - 目标文件已存在\" -ForegroundColor Yellow } else { Rename-Item -Path $file.FullName -NewName $newName; Write-Host \"[成功] $($file.Name)\" -ForegroundColor Green; $success++ } } catch { Write-Host \"[失败] $($file.Name) - $($_.Exception.Message)\" -ForegroundColor Red; $failed++ } }; Write-Host ''; Write-Host \"重命名完成！成功: $success 个，失败: $failed 个\" -ForegroundColor Cyan } else { Write-Host '操作已取消' -ForegroundColor Yellow }; Write-Host ''; Read-Host '按回车键退出' }"

1
00:00:09,460 --> 00:00:10,060
ok啊

2
00:00:10,060 --> 00:00:14,859
那么这节课咱们把这个flappy bird啊

3
00:00:14,859 --> 00:00:16,960
就是这个飞翔的小鸟啊

4
00:00:16,960 --> 00:00:18,429
咱们给它做一下

5
00:00:18,429 --> 00:00:23,618
那么刚好我们就会用上前面的这个嗯物理系统啊

6
00:00:25,178 --> 00:00:27,278
我们都可能会用得上啊

7
00:00:29,289 --> 00:00:30,859
我们新建这个项目

8
00:00:51,899 --> 00:00:55,579
然后啊我们这个项目创建完成以后

9
00:00:55,579 --> 00:00:57,820
导入我们的这个素材

10
00:01:04,000 --> 00:01:07,019
这个素材咱们之前已经见过了啊

11
00:01:07,019 --> 00:01:08,219
我们已经见过了

12
00:01:08,219 --> 00:01:12,799
那么我们在这里ctrl加s保存一下当前的游戏场景

13
00:01:13,319 --> 00:01:18,939
然后我们创建一个新的文件夹来存放我们编写的这个脚本

14
00:01:21,980 --> 00:01:25,209
ok那么接下来我们先看一下啊

15
00:01:25,209 --> 00:01:27,430
那么在这里呃

16
00:01:27,430 --> 00:01:30,640
我们这个图片它是有一个大小的啊

17
00:01:30,640 --> 00:01:31,540
像素大小的

18
00:01:31,540 --> 00:01:37,689
那么这个图片的像素大小是288乘以一个512

19
00:01:37,689 --> 00:01:40,899
这个应该是个我看512啊

20
00:01:41,019 --> 00:01:42,340
那么没错

21
00:01:42,340 --> 00:01:45,329
那么咱们在这里呃

22
00:01:45,329 --> 00:01:47,549
为了这个编写方便一些

23
00:01:47,549 --> 00:01:51,439
所以咱们目前先把这个屏幕的这个大小

24
00:01:52,359 --> 00:01:54,480
这个大家选中canvas

25
00:01:54,480 --> 00:01:58,719
然后把canvas里面的屏幕大小也改成这个288x512

26
00:01:59,879 --> 00:02:03,319
这里啊我们改成288x512

27
00:02:07,540 --> 00:02:08,819
288512

28
00:02:08,819 --> 00:02:11,219
那这样的话我们的屏幕大小啊

29
00:02:11,219 --> 00:02:13,020
目前就是这么大了啊

30
00:02:13,020 --> 00:02:16,550
我们做的游戏大小目前就是这么大的大小

31
00:02:16,550 --> 00:02:21,719
那么接下来我们要做这个背景背景的话

32
00:02:21,719 --> 00:02:24,240
首先如果我们拖到这个场景当中

33
00:02:24,240 --> 00:02:26,969
它的位置是在这样的一个位置啊

34
00:02:26,969 --> 00:02:29,280
那我们肯定要放到我们屏幕的中间

35
00:02:29,280 --> 00:02:30,000
对不对

36
00:02:30,000 --> 00:02:31,509
放到屏幕中间

37
00:02:31,509 --> 00:02:35,979
那么屏幕中间的话就应该是我们看啊

38
00:02:35,979 --> 00:02:37,689
这个屏幕是288

39
00:02:37,689 --> 00:02:40,800
所以这里应该是个144

40
00:02:41,338 --> 00:02:43,199
这边是512啊

41
00:02:43,199 --> 00:02:45,679
所以是256啊

42
00:02:45,679 --> 00:02:47,239
我们就给他一半的宽高

43
00:02:47,239 --> 00:02:49,938
这样的话就是刚好填满我们的这个屏幕

44
00:02:49,938 --> 00:02:50,759
对不对

45
00:02:50,799 --> 00:02:53,019
但是一个背景是不够的

46
00:02:53,019 --> 00:02:53,618
为什么

47
00:02:53,618 --> 00:02:55,239
因为这个背景是会动的

48
00:02:55,239 --> 00:02:57,038
背景是会缓慢移动的

49
00:02:57,038 --> 00:02:58,520
所以我们再来一个

50
00:02:58,739 --> 00:03:02,280
再来一个放到它刚好放在他的右边

51
00:03:02,319 --> 00:03:04,870
这样的话我们要做什么事

52
00:03:04,870 --> 00:03:06,639
背景要缓慢的移动

53
00:03:06,639 --> 00:03:08,259
往左边慢慢地移动

54
00:03:08,259 --> 00:03:09,939
当移动到这个位置的时候

55
00:03:09,939 --> 00:03:13,900
我们瞬间把第一个背景放到这个位置

56
00:03:14,459 --> 00:03:18,460
那么这样的话我们再往左边移动

57
00:03:18,500 --> 00:03:21,389
再往左边移动移动过来以后

58
00:03:21,389 --> 00:03:23,490
再把出去的放到这个位置

59
00:03:23,490 --> 00:03:28,689
这样的话是不是就成了一个循环滚动的这样的一个这样的一个形式了

60
00:03:28,689 --> 00:03:29,439
对不对

61
00:03:29,439 --> 00:03:33,680
那么基本上2d游戏很多这个背景啊

62
00:03:33,680 --> 00:03:35,719
它都是用这种方式去做的啊

63
00:03:35,719 --> 00:03:37,379
都是用这种方式去做的

64
00:03:37,759 --> 00:03:40,639
那么我们先看一下啊

65
00:03:40,639 --> 00:03:41,959
这是第一个图的位置

66
00:03:41,959 --> 00:03:44,808
第二个图的位置我们要准确拼到它的右边

67
00:03:44,808 --> 00:03:48,818
那么应该是多少合适啊

68
00:03:48,818 --> 00:03:49,959
我们先不管第二个图啊

69
00:03:49,959 --> 00:03:51,639
我们先管第一个图啊

70
00:03:51,639 --> 00:03:53,659
那么现在我们先看这里

71
00:03:55,459 --> 00:03:56,900
这里的话

72
00:03:59,599 --> 00:04:03,159
我们有两个背景它都放在最外层了

73
00:04:03,159 --> 00:04:06,639
这个明显在这个逻辑放呃逻辑上面啊

74
00:04:06,639 --> 00:04:08,009
并不是特别的好

75
00:04:08,009 --> 00:04:09,930
所以我们选中第一个图片

76
00:04:09,930 --> 00:04:12,879
在它上面右键创建一个空的节点啊

77
00:04:12,879 --> 00:04:14,919
这样空节点刚好就在中间位置

78
00:04:14,919 --> 00:04:16,750
然后把它拖到最外层

79
00:04:16,750 --> 00:04:18,540
改个名字叫bg

80
00:04:18,899 --> 00:04:22,408
然后把我们的两个背景都拖进来

81
00:04:22,408 --> 00:04:25,399
都拖到它的这个子物体里面来

82
00:04:25,819 --> 00:04:29,379
那么这样的话我们的第一个背景啊

83
00:04:29,379 --> 00:04:32,680
第一个背景它的位置就是零零啊

84
00:04:32,680 --> 00:04:34,399
零零的话刚好在屏幕上

85
00:04:34,399 --> 00:04:38,420
而第二个背景刚好就是这个屏幕的一个大小

86
00:04:38,420 --> 00:04:38,839
对不对

87
00:04:38,839 --> 00:04:40,639
屏幕的大是288

88
00:04:40,639 --> 00:04:43,939
所以第二个背景我们x就给他288

89
00:04:43,939 --> 00:04:47,139
这样的话他们就刚好可以拼接上啊

90
00:04:47,139 --> 00:04:47,918
无缝拼接

91
00:04:47,918 --> 00:04:48,699
对不对

92
00:04:52,680 --> 00:04:56,480
ok那么接下来咱们来做一下这个背景的移动

93
00:04:56,480 --> 00:04:57,680
背景移动的话

94
00:04:57,680 --> 00:05:00,269
我们就要在这个背景上面啊

95
00:05:00,269 --> 00:05:02,279
去编写我们的代码了

96
00:05:02,279 --> 00:05:05,339
那我们在这里创建一个脚本

97
00:05:06,259 --> 00:05:08,319
叫做bg ctrl

98
00:05:09,759 --> 00:05:10,779
打开它

99
00:05:19,019 --> 00:05:19,999
来改造

100
00:05:19,999 --> 00:05:20,869
改个名

101
00:05:20,869 --> 00:05:23,319
bg control

102
00:05:26,379 --> 00:05:30,779
然后在它的里面我们需要添加一些内容

103
00:05:30,779 --> 00:05:33,408
首先啊我们这个移动是要有速度的

104
00:05:33,408 --> 00:05:36,949
我们把可能会修改的内容给它写成属性方面

105
00:05:36,949 --> 00:05:37,728
我们修改

106
00:05:37,728 --> 00:05:40,160
比如说移动有速度啊

107
00:05:40,160 --> 00:05:42,399
那我们写成属性

108
00:05:42,399 --> 00:05:43,480
并且对外开放

109
00:05:43,480 --> 00:05:46,439
这样的话我们就好在面板上面去修改它

110
00:05:47,160 --> 00:05:51,319
因为这里我们给的速度不一定是合适的啊

111
00:05:51,319 --> 00:05:54,980
但是每次修改在代码里面修改不太好啊

112
00:05:54,980 --> 00:05:56,300
我们如果写成属性

113
00:05:56,300 --> 00:05:58,040
就可以在面板上修改

114
00:05:58,040 --> 00:06:03,689
然后每个图片的宽度是多少啊

115
00:06:03,689 --> 00:06:04,860
这个宽度啊

116
00:06:04,860 --> 00:06:09,670
咱们刚才老的去计算是不是老的去看老的去啊

117
00:06:09,670 --> 00:06:11,930
手枪是288啊

118
00:06:11,930 --> 00:06:14,689
但是这样的话也是这个不太好

119
00:06:14,689 --> 00:06:17,089
万一你比如说哪一次你敲错了是吧

120
00:06:17,089 --> 00:06:18,649
敲成这个啊

121
00:06:18,649 --> 00:06:20,209
比如说287啊

122
00:06:20,209 --> 00:06:21,199
或者敲成别的

123
00:06:21,199 --> 00:06:23,569
那这个循环起来可能就会有裂缝啊

124
00:06:23,569 --> 00:06:28,040
它拼接那块就会这个露馅了啊

125
00:06:28,040 --> 00:06:30,319
而且如果我们这个背景换张图

126
00:06:30,319 --> 00:06:32,449
如果它的宽度改变的话

127
00:06:32,449 --> 00:06:34,550
我们也可以很容易的去改变它

128
00:06:34,550 --> 00:06:40,619
就可以达到我们的效果嗯所以像这种呃可能会更改的

129
00:06:40,619 --> 00:06:42,538
尤其是这种数字类的东西啊

130
00:06:42,538 --> 00:06:44,519
我们写成属性是最好的

131
00:06:44,699 --> 00:06:48,480
那么在这边我们要让它往左边一直移动对吧

132
00:06:48,480 --> 00:06:50,779
那移动当然要写在update里了

133
00:06:52,319 --> 00:06:55,060
我们要让谁移动啊

134
00:06:55,060 --> 00:06:57,879
我们这个脚本是加在谁身上的

135
00:06:57,879 --> 00:06:59,620
加在这个负节点身上的

136
00:06:59,620 --> 00:07:02,740
所以我是要让我的两个子节点往左边移动

137
00:07:02,740 --> 00:07:08,980
所以这边我们就应该用for循环bg of

138
00:07:08,980 --> 00:07:13,300
我们就应该便利一下this.node点求证

139
00:07:16,500 --> 00:07:18,319
那么在这里啊

140
00:07:18,319 --> 00:07:20,959
我们这个bg就是我们的这两个背景了

141
00:07:20,959 --> 00:07:21,500
对不对

142
00:07:21,500 --> 00:07:23,649
我们两个背景都要让它动起来

143
00:07:23,649 --> 00:07:24,790
往左边动

144
00:07:24,790 --> 00:07:26,319
所以是减等于

145
00:07:26,699 --> 00:07:32,339
那么就等于它的位置再减去一个this.speed啊

146
00:07:32,339 --> 00:07:34,259
这时候你看我们就可以直接用speed

147
00:07:34,259 --> 00:07:37,639
就是每秒乘一个dt啊

148
00:07:37,639 --> 00:07:42,689
那就是每秒往左边移动四像素速度非常慢

149
00:07:42,689 --> 00:07:43,709
对不对

150
00:07:43,709 --> 00:07:46,860
那是因为作为背景而言啊

151
00:07:46,860 --> 00:07:48,209
作为背景而言

152
00:07:48,209 --> 00:07:53,439
这个东西本来就是只是给人一点视差效果

153
00:07:53,439 --> 00:07:53,740
诶

154
00:07:55,360 --> 00:07:57,620
这都没有动啊啊

155
00:07:57,620 --> 00:07:58,759
没有挂载脚本啊

156
00:07:58,759 --> 00:08:05,000
没有挂在脚本选中这个节点背景的移动速度本身就是很慢的啊

157
00:08:05,000 --> 00:08:08,060
背景的存在只是为了有这样一个视差效果啊

158
00:08:08,060 --> 00:08:09,680
背景稍很慢的移动

159
00:08:09,680 --> 00:08:12,550
然后如果我们前面一会儿加上地面以后

160
00:08:12,550 --> 00:08:15,170
地面比背景移动的速度快

161
00:08:15,170 --> 00:08:18,980
这样的一个视差效果就让人感觉到很立体化啊

162
00:08:18,980 --> 00:08:21,579
因为你在现实世界当中就这样了

163
00:08:21,579 --> 00:08:24,720
你在车上在在这个火车上是吧

164
00:08:24,720 --> 00:08:27,600
你看着这个附近的物体移动的很快

165
00:08:27,600 --> 00:08:28,709
但是往远处看

166
00:08:28,709 --> 00:08:30,899
远处感觉移动得非常慢

167
00:08:30,899 --> 00:08:31,740
对不对

168
00:08:32,419 --> 00:08:36,039
唉这时候我们仔细看这个背景缓慢在移动啊

169
00:08:36,039 --> 00:08:37,840
这个就是我们要的效果啊

170
00:08:37,840 --> 00:08:39,158
这个就是要的效果

171
00:08:42,580 --> 00:08:43,620
ok啊

172
00:08:43,919 --> 00:08:46,740
那么这个循环无限循环就做完了啊

173
00:08:46,740 --> 00:08:52,429
那么它这个背景就永远都是这样去循环的了啊啊不对

174
00:08:52,429 --> 00:08:53,840
我们只做了个移动啊

175
00:08:53,840 --> 00:08:55,078
还没做循环

176
00:08:55,940 --> 00:08:56,679
动了一下

177
00:08:56,679 --> 00:08:58,120
我就想着循环做完了

178
00:08:58,120 --> 00:09:04,078
那么如果背景超出屏幕

179
00:09:06,159 --> 00:09:08,399
如果一个背景出了这个屏幕了

180
00:09:08,399 --> 00:09:10,139
我们就应该把它放到哪里

181
00:09:10,139 --> 00:09:11,460
如果出了屏幕了

182
00:09:11,460 --> 00:09:13,740
我就应该把它放到右边的位置上

183
00:09:13,740 --> 00:09:14,470
对不对

184
00:09:14,470 --> 00:09:17,080
那么在这里做这个判断啊

185
00:09:17,080 --> 00:09:28,360
如果背景超出屏幕b g.x小于多少负的this点宽就是小于负的288

186
00:09:28,360 --> 00:09:31,029
那证明他肯定出了这个屏幕了啊

187
00:09:31,029 --> 00:09:33,309
那么如果出了屏幕我就怎么了

188
00:09:33,309 --> 00:09:39,679
我就bg.x自己再加上自己宽度的两倍

189
00:09:39,759 --> 00:09:41,649
一定是乘以二的啊

190
00:09:41,649 --> 00:09:42,759
如果超出屏幕

191
00:09:42,759 --> 00:09:45,070
我就要再加上我的宽度乘以二

192
00:09:45,070 --> 00:09:48,938
因为你想如果我已经出了屏幕了

193
00:09:49,019 --> 00:09:51,240
然后我加一个宽度的话

194
00:09:51,240 --> 00:09:52,980
我加我自身一个宽度的话

195
00:09:52,980 --> 00:09:55,769
那么左边的这个图片其实会到屏幕正中间

196
00:09:55,769 --> 00:09:56,909
这个是不对的

197
00:09:56,909 --> 00:09:59,159
我们要让它刚好在屏幕的最右边

198
00:09:59,159 --> 00:10:01,769
那这样的话就是它要瞬间往右边移

199
00:10:01,769 --> 00:10:03,509
它两个宽度啊

200
00:10:03,509 --> 00:10:05,580
这这么长的距离才可以

201
00:10:05,580 --> 00:10:06,470
对不对

202
00:10:06,470 --> 00:10:07,850
仔细思考一下啊

203
00:10:07,850 --> 00:10:09,409
那这样的话就ok了

204
00:10:09,409 --> 00:10:11,659
这个无限循环就做完了

205
00:10:12,700 --> 00:10:17,789
接下来我们来做这个别的部分

206
00:10:17,789 --> 00:10:20,379
比如说我们先来做这个

207
00:10:21,879 --> 00:10:22,960
我们先做什么

208
00:10:22,960 --> 00:10:24,730
先做这个管道吧

209
00:10:24,730 --> 00:10:26,860
先做一个管道啊

210
00:10:26,860 --> 00:10:28,360
让这个管道动起来

211
00:10:28,360 --> 00:10:29,938
管道和这个地面

212
00:10:30,259 --> 00:10:32,740
那么我们做地面啊

213
00:10:35,019 --> 00:10:38,799
地面地面的话我们拖上来

214
00:10:38,799 --> 00:10:41,120
把它放在这个位置

215
00:10:41,919 --> 00:10:43,419
把它放在这个位置

216
00:10:43,419 --> 00:10:46,179
那么我们用相同的方法去做啊

217
00:10:46,179 --> 00:10:47,559
首先地面拖出来

218
00:10:47,559 --> 00:10:49,299
它跟背景不要放一块

219
00:10:50,240 --> 00:10:52,070
把地面放到下面

220
00:10:52,070 --> 00:10:56,820
那么它我默认把地面放在这样一个位置啊

221
00:10:56,820 --> 00:11:00,330
然后我在地面上面建建一个空的节点

222
00:11:00,330 --> 00:11:00,840
注意啊

223
00:11:00,840 --> 00:11:04,360
这个地面的做法跟这个背景是完全一样的啊

224
00:11:04,360 --> 00:11:06,190
是完全一样的

225
00:11:06,190 --> 00:11:08,659
那只是他们的速度不一样

226
00:11:08,720 --> 00:11:12,259
然后把这个空的节点拿出来取个名字啊

227
00:11:12,259 --> 00:11:13,269
叫做land

228
00:11:13,269 --> 00:11:17,320
然后这个真正的这个地面给它取个名字叫烂的一

229
00:11:17,320 --> 00:11:19,029
我放到它里面

230
00:11:19,029 --> 00:11:23,259
然后还有再要拷贝一份叫做烂的二

231
00:11:23,960 --> 00:11:27,129
这个第二个地面要刚好放在它的右边

232
00:11:27,129 --> 00:11:30,580
这样的话地面是不是也变成无限滚动的了啊

233
00:11:30,580 --> 00:11:32,049
无限循环滚动的了

234
00:11:32,049 --> 00:11:34,570
那么刚好在它右边应该给它多少像素

235
00:11:34,570 --> 00:11:35,990
地面是336

236
00:11:35,990 --> 00:11:39,620
所以我这个烂的二应该是个336

237
00:11:40,940 --> 00:11:44,480
这样的话地面也可以有这个无线滚动效果了啊

238
00:11:44,480 --> 00:11:48,539
那么其实你看这这两个滚动效果是完全一样的

239
00:11:48,539 --> 00:11:51,720
所以脚本我直接就用这个背景的啊

240
00:11:51,720 --> 00:11:52,769
给这个地面

241
00:11:52,769 --> 00:11:56,620
然后无非就是把这个宽度你看本来是288

242
00:11:56,620 --> 00:12:00,779
我现在就就应该给他改成多少336啊

243
00:12:00,779 --> 00:12:02,730
所以在这里336

244
00:12:02,730 --> 00:12:03,659
并且速度的话

245
00:12:03,659 --> 00:12:04,740
你就不能试了

246
00:12:04,740 --> 00:12:05,429
四太慢

247
00:12:05,429 --> 00:12:07,000
比如说给个20

248
00:12:07,720 --> 00:12:09,700
那这样的话地面的速度就快了

249
00:12:09,700 --> 00:12:12,169
所以大家可以看这个属性啊

250
00:12:12,169 --> 00:12:14,870
其实你把很多数值写成属性是很有用的

251
00:12:14,870 --> 00:12:17,899
如果我们没有把这两个数值写成属性

252
00:12:17,899 --> 00:12:20,419
那我要地面也做这个循环滚动

253
00:12:20,419 --> 00:12:22,820
我们还能重新去编写地面的脚本了

254
00:12:22,820 --> 00:12:23,679
对不对

255
00:12:25,899 --> 00:12:28,980
这时候大家可以看是不是这两个都在动

256
00:12:28,980 --> 00:12:31,620
但是这个地面的明显给人感觉快一些

257
00:12:31,620 --> 00:12:33,509
这个就一个立体效果了

258
00:12:33,509 --> 00:12:34,320
对不对

259
00:12:34,320 --> 00:12:35,460
给人一个层次感啊

260
00:12:37,330 --> 00:12:38,470
远的跑得慢

261
00:12:38,470 --> 00:12:41,139
当然实际上这个效果还是有一点慢啊

262
00:12:41,139 --> 00:12:42,979
我们可以让它再快一些

263
00:12:43,519 --> 00:12:44,818
给我50

264
00:12:48,519 --> 00:12:49,500
对吧啊

265
00:12:49,500 --> 00:12:50,940
这个效果就不错了啊

266
00:12:50,940 --> 00:12:52,169
这个效果就不错了

267
00:12:52,169 --> 00:12:56,759
那么它的上面可能会有这个嗯管道啊

268
00:12:56,759 --> 00:12:58,139
它的上面会有管道

269
00:12:58,139 --> 00:13:00,899
那管道的话咱们先不管啊

270
00:13:00,899 --> 00:13:05,080
咱们先写咱们的这个嗯小鸟好了啊

271
00:13:05,080 --> 00:13:06,129
先写咱们的小鸟

272
00:13:06,129 --> 00:13:09,759
那么我们来关了他回来

273
00:13:11,019 --> 00:13:13,419
那么小鸟我们拖上来

274
00:13:14,980 --> 00:13:16,360
先把地面收起来

275
00:13:16,360 --> 00:13:18,139
把小鸟拖上来

276
00:13:18,700 --> 00:13:21,220
小鸟比如说默认就在这个位置啊

277
00:13:21,220 --> 00:13:22,299
就在这个位置

278
00:13:22,299 --> 00:13:24,159
那么改个名

279
00:13:24,159 --> 00:13:27,720
不要叫什么零杠零了就叫bird啊

280
00:13:27,720 --> 00:13:29,820
然后给小鸟去编写一个脚本

281
00:13:31,019 --> 00:13:33,019
就叫bird control

282
00:13:36,419 --> 00:13:38,279
挂载到我们小鸟身上

283
00:13:38,279 --> 00:13:39,360
打开这个脚本

284
00:13:40,559 --> 00:13:41,759
改一下名

285
00:13:45,159 --> 00:13:46,740
那么对于小鸟而言

286
00:13:46,740 --> 00:13:50,879
大家去想小鸟这个精灵身上需要有什么东西

287
00:13:50,879 --> 00:13:52,559
首先它是有重力的

288
00:13:53,200 --> 00:13:54,600
那么说到他有重力

289
00:13:54,600 --> 00:13:56,279
它和地面会产生碰撞

290
00:13:56,279 --> 00:13:58,818
所以地面实际上也是怎样的

291
00:13:58,840 --> 00:14:02,019
地面也应该有这个物理效果

292
00:14:02,019 --> 00:14:08,320
所以我们在这里先给地面去加一个盒子的碰撞体啊

293
00:14:08,320 --> 00:14:09,759
不能用这个碰撞啊

294
00:14:10,899 --> 00:14:13,539
加一个物理的盒子的碰撞体

295
00:14:13,600 --> 00:14:14,679
那么这样的话

296
00:14:14,679 --> 00:14:15,759
因为你没有加钢体

297
00:14:15,759 --> 00:14:17,500
它会自动给你加上一个缸体

298
00:14:17,500 --> 00:14:19,690
但是一定要给它改成静态的

299
00:14:19,690 --> 00:14:21,159
你要不给它改成静态的

300
00:14:21,159 --> 00:14:22,799
这个地面会掉下去了

301
00:14:22,799 --> 00:14:24,240
第二个地面也一样

302
00:14:24,240 --> 00:14:26,700
去加一个盒子啊

303
00:14:26,700 --> 00:14:27,720
盒子碰撞体

304
00:14:27,720 --> 00:14:30,120
然后把这个缸体改成静态的

305
00:14:30,120 --> 00:14:33,759
这样的话当小鸟调到它地面上的话

306
00:14:33,759 --> 00:14:34,960
它就不会掉下去了

307
00:14:34,960 --> 00:14:37,698
他就会真的掉在地面上啊

308
00:14:38,519 --> 00:14:41,720
ok那么这个小鸟

309
00:14:44,980 --> 00:14:48,610
小鸟本身肯定也有这个物理物理组件

310
00:14:48,610 --> 00:14:52,220
那我们用圆的啊

311
00:14:52,220 --> 00:14:53,389
我们用圆的

312
00:14:53,389 --> 00:14:54,559
那么圆的话

313
00:14:54,559 --> 00:15:03,820
我们这个半径比如说给他的15 一半还算是比较合适是吧啊

314
00:15:03,820 --> 00:15:05,259
就差不多就用它吧

315
00:15:05,259 --> 00:15:06,688
15啊

316
00:15:06,688 --> 00:15:07,889
比如说小鸟啊

317
00:15:07,889 --> 00:15:08,969
有一个碰撞啊

318
00:15:08,969 --> 00:15:12,149
它的这个圆是半径为15的这样的一个圆

319
00:15:12,149 --> 00:15:15,450
那么它呢它的物理特性就是静态呃

320
00:15:15,450 --> 00:15:16,440
就是动态的

321
00:15:16,440 --> 00:15:20,120
并且我们要勾上这一项

322
00:15:20,120 --> 00:15:24,799
因为我们可能会写脚本去判断它有没有和其他物体产生碰撞

323
00:15:24,799 --> 00:15:25,740
对不对

324
00:15:25,980 --> 00:15:27,059
那么现在运行下

325
00:15:27,059 --> 00:15:29,100
看看这个鸟会不会掉下来

326
00:15:29,100 --> 00:15:30,149
还不会

327
00:15:30,149 --> 00:15:32,700
那是因为我们需要开启什么呀

328
00:15:32,700 --> 00:15:35,019
我们在鸟身上开启一下吧

329
00:15:36,539 --> 00:15:38,100
unload

330
00:15:38,100 --> 00:15:40,889
我们要开启这个物理的这个功能

331
00:15:40,889 --> 00:15:45,419
开物理功能必须在这个unload里面去写啊

332
00:15:52,080 --> 00:15:54,740
ok那么就这样的

333
00:15:54,740 --> 00:15:58,938
然后我们到这边来站运行

334
00:15:59,100 --> 00:16:01,440
哎你看小姐已经掉上面了

335
00:16:01,440 --> 00:16:03,269
但是中下面还有一点空隙

336
00:16:03,269 --> 00:16:05,219
那就是给的碰撞体

337
00:16:05,279 --> 00:16:06,990
15还是有点大

338
00:16:06,990 --> 00:16:08,100
可以再小一点

339
00:16:08,100 --> 00:16:09,090
比如说13

340
00:16:09,090 --> 00:16:10,719
这个就差不多了

341
00:16:12,899 --> 00:16:14,100
啊这就差不多了

342
00:16:14,100 --> 00:16:15,480
刚好掉到这个地面上

343
00:16:15,480 --> 00:16:17,639
对不对啊

344
00:16:17,639 --> 00:16:21,620
差不多那么ok啊

345
00:16:23,799 --> 00:16:26,759
我们这个把物理功能开启

346
00:16:29,059 --> 00:16:32,539
检测物理应该是物理引擎开启

347
00:16:34,720 --> 00:16:37,389
接下来我们这个小鸟会往下掉

348
00:16:37,389 --> 00:16:40,179
我们现在做的就是我们按下鼠标

349
00:16:40,179 --> 00:16:42,100
这个小鸟会飞一下

350
00:16:42,100 --> 00:16:43,000
对不对

351
00:16:43,000 --> 00:16:47,120
那么也就是说我们这个小鸟本身有一个飞的功能啊

352
00:16:47,120 --> 00:16:48,799
飞的这样的一个功能

353
00:16:48,799 --> 00:16:52,179
那么这个功能啊我们怎么做

354
00:16:52,759 --> 00:16:53,600
很简单

355
00:16:53,600 --> 00:16:56,539
每一次只要我们点一下这个小鸟

356
00:16:56,539 --> 00:17:02,299
我们让它掉fly fly的话就是让这个this我们得到它自己的缸体

357
00:17:02,360 --> 00:17:03,559
得到他的钢铁

358
00:17:03,559 --> 00:17:08,618
钢铁就是cc.regi得到它的这个钢琴组件

359
00:17:08,618 --> 00:17:10,419
给他一个什么呀

360
00:17:10,680 --> 00:17:14,099
线性的一个速度向上的一个速度啊

361
00:17:14,099 --> 00:17:16,859
这个速度我们是这个x不用给

362
00:17:16,859 --> 00:17:18,750
因为小鸟x是不动的

363
00:17:18,750 --> 00:17:19,859
y的话会给

364
00:17:19,859 --> 00:17:21,460
比如说给个140

365
00:17:23,079 --> 00:17:25,779
那么每一次我们只要调这个fly方法

366
00:17:25,779 --> 00:17:27,430
小鸟就会向上飞一下

367
00:17:27,430 --> 00:17:30,079
是不是就是飞的方法

368
00:17:33,619 --> 00:17:34,299
ok啊

369
00:17:35,440 --> 00:17:39,069
那么我们这个鼠标点击事件

370
00:17:39,069 --> 00:17:40,509
我们在哪里监听啊

371
00:17:40,509 --> 00:17:41,950
我们不能在鸟身上监听

372
00:17:41,950 --> 00:17:42,369
为什么

373
00:17:42,369 --> 00:17:44,349
因为在小鸟身上监听的话

374
00:17:44,349 --> 00:17:46,500
我们只有点到这个鸟

375
00:17:46,500 --> 00:17:48,660
那么这个鸟才会往天上飞

376
00:17:48,660 --> 00:17:49,680
那这个太困难了

377
00:17:49,680 --> 00:17:50,099
对不对

378
00:17:50,099 --> 00:17:51,269
点到小鸟

379
00:17:51,269 --> 00:17:54,660
所以我们肯定是随便在一个背景处一点

380
00:17:54,660 --> 00:17:56,430
这个都可以让这个小鸟飞

381
00:17:56,430 --> 00:17:58,230
所以我们这个事件监听啊

382
00:17:58,230 --> 00:17:59,720
可以放到背景里面

383
00:17:59,720 --> 00:18:01,400
放到这个背景里面

384
00:18:01,400 --> 00:18:02,779
那么在这个里面

385
00:18:02,779 --> 00:18:07,700
在start里面我们来做一个点击监听

386
00:18:10,220 --> 00:18:12,029
点击监听呃

387
00:18:12,029 --> 00:18:15,779
我要让我的背景去进行一个监听

388
00:18:15,859 --> 00:18:17,720
那么我现在有两个背景

389
00:18:17,720 --> 00:18:18,380
对不对

390
00:18:18,380 --> 00:18:21,619
两个背景这两个背景都要监听这个事件啊

391
00:18:21,619 --> 00:18:22,500
怎么办

392
00:18:22,539 --> 00:18:28,400
其实大家如果希望永远在这个全屏上都能监测到这个监听

393
00:18:28,400 --> 00:18:32,679
你也可以把这个监听时间写到canvas这个节点上面啊也行

394
00:18:32,679 --> 00:18:35,078
那咱们就写到这两个烂的上面好了

395
00:18:35,078 --> 00:18:36,098
写到这两个

396
00:18:36,098 --> 00:18:37,000
而不是烂的

397
00:18:37,000 --> 00:18:39,039
写到这两个bg上面啊

398
00:18:39,039 --> 00:18:40,119
就这两个背景上面

399
00:18:40,119 --> 00:18:42,460
这样的话就是你不管在哪里

400
00:18:42,460 --> 00:18:43,420
只要你点了背景

401
00:18:43,420 --> 00:18:45,710
这个小鸟就会向上飞啊

402
00:18:45,710 --> 00:18:49,180
那么我们在这里便利我们的两个背景

403
00:18:52,440 --> 00:18:56,960
this.node.children

404
00:18:57,759 --> 00:18:58,180
哎

405
00:18:58,180 --> 00:19:02,200
这两个背景我们都给他点on

406
00:19:02,200 --> 00:19:03,369
开启监听

407
00:19:03,369 --> 00:19:09,619
监听什么cc.node点英文tab.most done啊

408
00:19:09,619 --> 00:19:12,440
鼠标按下我们就会做一件事

409
00:19:12,440 --> 00:19:13,599
做什么事

410
00:19:14,259 --> 00:19:16,569
我们让我们的小鸟飞翔

411
00:19:16,569 --> 00:19:17,500
这时候注意啊

412
00:19:17,500 --> 00:19:21,730
我们你看我就要从这个脚本调这个脚本里面的fly方法

413
00:19:21,730 --> 00:19:23,359
这时候怎么办

414
00:19:23,380 --> 00:19:24,759
我们就需要这样去做

415
00:19:25,839 --> 00:19:28,079
加一个属性是小鸟

416
00:19:30,759 --> 00:19:32,460
at a property

417
00:19:34,539 --> 00:19:37,269
就是bird control啊

418
00:19:37,269 --> 00:19:40,059
因为我们这个bd control是我们编写的脚本

419
00:19:40,059 --> 00:19:41,359
对吧啊

420
00:19:41,359 --> 00:19:43,549
我们这个属性类型就是bt control

421
00:19:43,549 --> 00:19:49,180
然后b i r d bird word control类型默认是none

422
00:19:49,460 --> 00:19:52,160
那这样的话我们在这里有这样一个属性

423
00:19:52,160 --> 00:19:54,410
就是属性就是一个小鸟控制器

424
00:19:54,410 --> 00:19:57,339
然后我们到这边给它关联一下

425
00:19:58,539 --> 00:19:59,619
在背景这里

426
00:19:59,619 --> 00:20:01,180
你看就这样一个bird

427
00:20:01,180 --> 00:20:03,019
我们把小鸟拖过来

428
00:20:04,220 --> 00:20:05,480
小鸟拖过来以后

429
00:20:05,480 --> 00:20:09,539
因为小鸟身上有这个bird ctrl这个组件

430
00:20:10,200 --> 00:20:11,909
bd control这个组件

431
00:20:11,909 --> 00:20:15,088
那所以当我们把小鸟拖到他身上的时候

432
00:20:15,088 --> 00:20:17,128
它会检测而发现这个组件了

433
00:20:17,128 --> 00:20:21,709
它就把小鸟身上的word control这个组件啊放过来了

434
00:20:21,709 --> 00:20:25,220
那么这时候这个也就有了啊

435
00:20:25,220 --> 00:20:27,500
这个bird就是咱们这个bd control

436
00:20:27,500 --> 00:20:29,059
我们就可以在这里干嘛了

437
00:20:29,059 --> 00:20:32,839
调用这个bird点里面有个fly

438
00:20:32,839 --> 00:20:33,799
对不对

439
00:20:34,299 --> 00:20:35,920
哎就可以点一下

440
00:20:35,920 --> 00:20:36,940
点一下背景

441
00:20:36,940 --> 00:20:38,259
让这个小鸟飞一下

442
00:20:38,259 --> 00:20:38,980
注意啊

443
00:20:38,980 --> 00:20:40,339
这个是我们

444
00:20:41,960 --> 00:20:46,619
算算是第一次调用这个跨脚本的这样的一个东西吧

445
00:20:47,299 --> 00:20:50,299
啊我们之前那个飞机大战也这样去调过

446
00:20:50,299 --> 00:20:51,500
应该也这样调过吧

447
00:20:51,500 --> 00:20:52,460
我记着也掉过

448
00:20:52,460 --> 00:20:53,839
也是跨脚本掉过

449
00:20:53,839 --> 00:20:55,099
一定要注意啊

450
00:20:55,099 --> 00:20:57,319
这个是很常用的一种方式啊

451
00:20:57,319 --> 00:21:00,039
所以你跨脚本调用一定要会啊

452
00:21:00,700 --> 00:21:04,759
那么在这里我们运行一下

453
00:21:05,440 --> 00:21:06,339
我们点一下

454
00:21:06,339 --> 00:21:10,759
大家可以看这个小鸟是不是点一下会给上一个速度呀

455
00:21:10,759 --> 00:21:13,799
啊我们速度给的太小了100

456
00:21:14,400 --> 00:21:16,680
不过这里小鸟掉落的也太快了

457
00:21:16,680 --> 00:21:20,319
这样的话我们根本反应反应不过来啊

458
00:21:20,319 --> 00:21:23,740
所以为了让它这个重力让它下落稍微慢点

459
00:21:23,740 --> 00:21:25,990
我们在这里给小鸟的钢铁

460
00:21:25,990 --> 00:21:28,589
这个受重力影响

461
00:21:28,589 --> 00:21:31,619
给它改成一半再试一下

462
00:21:32,180 --> 00:21:34,519
哎这个就算比较合适了

463
00:21:34,519 --> 00:21:39,779
然后向上的这个速度我们给了这个速度

464
00:21:40,960 --> 00:21:42,579
在这里是100是吧

465
00:21:42,579 --> 00:21:44,079
如果你觉得100不合适

466
00:21:44,079 --> 00:21:46,119
你可以给个100

467
00:21:46,119 --> 00:21:48,138
200数是多了150

468
00:21:51,740 --> 00:21:53,480
哎你看这个就比较合适了

469
00:21:53,480 --> 00:21:54,920
对不对啊

470
00:21:54,920 --> 00:21:56,900
那这样的话就是点一下相应就飞一下

471
00:21:56,900 --> 00:21:58,299
点一下飞一下

472
00:22:03,180 --> 00:22:04,319
ok啊

473
00:22:05,920 --> 00:22:08,380
那么接下来我们就去编写小鸟

474
00:22:08,380 --> 00:22:10,240
完了我们就要编写这个管道了

475
00:22:10,240 --> 00:22:10,779
对不对

476
00:22:10,779 --> 00:22:11,950
编写管道了

477
00:22:11,950 --> 00:22:14,539
来我们来写管道部分

478
00:22:17,160 --> 00:22:19,680
那么这个管道的话啊

479
00:22:19,680 --> 00:22:20,700
其实小鸟还没完

480
00:22:20,700 --> 00:22:22,779
但是我们这个碰撞的呃

481
00:22:22,779 --> 00:22:24,460
碰撞部分我们最后写啊

482
00:22:24,460 --> 00:22:25,420
碰撞部分最后写

483
00:22:25,420 --> 00:22:27,279
因为你不光要和地面碰撞

484
00:22:27,279 --> 00:22:29,049
要和管道也碰撞啊

485
00:22:29,049 --> 00:22:33,750
所以我们要把这个这些能碰撞的物体先全部给它加上来

486
00:22:33,750 --> 00:22:35,609
再说这个小鸟对不对

487
00:22:35,609 --> 00:22:37,859
管道我们来加一下啊

488
00:22:38,519 --> 00:22:41,579
首先加一个向下的管道啊

489
00:22:41,579 --> 00:22:42,839
比如说是这样的

490
00:22:42,839 --> 00:22:45,519
然后再加一个向上的管道

491
00:22:48,019 --> 00:22:50,000
比如说加到它的上面

492
00:22:50,220 --> 00:22:51,480
然后往下移

493
00:22:51,480 --> 00:22:52,859
哎这是上下管道

494
00:22:52,859 --> 00:22:54,660
这个管道应该在这个地面的后面

495
00:22:54,660 --> 00:22:55,859
对不对啊

496
00:22:55,859 --> 00:23:01,559
所以最后我们把把管道拖到这个地面的这个位置就ok了

497
00:23:01,779 --> 00:23:03,549
但是现在我们做调试

498
00:23:03,549 --> 00:23:04,960
所以我们先放到外面

499
00:23:04,960 --> 00:23:06,160
这个没关系啊

500
00:23:06,160 --> 00:23:07,930
然后这个口有点小

501
00:23:07,930 --> 00:23:09,019
是不是

502
00:23:10,400 --> 00:23:14,319
比如说啊这是我们的这个管道嗯

503
00:23:14,819 --> 00:23:16,559
那么对于管道而言啊

504
00:23:16,559 --> 00:23:18,339
我们选中这个管道

505
00:23:18,519 --> 00:23:19,240
一个管

506
00:23:19,240 --> 00:23:20,769
对于一个管道而言

507
00:23:20,769 --> 00:23:23,019
他们也是会和小鸟碰撞的

508
00:23:23,019 --> 00:23:26,839
所以我们要添加碰撞组件box

509
00:23:27,079 --> 00:23:28,460
那么加了一个box

510
00:23:28,460 --> 00:23:28,880
注意啊

511
00:23:31,670 --> 00:23:33,380
那我们还还缺一个下面的

512
00:23:33,380 --> 00:23:34,940
所以我们再加一个

513
00:23:36,759 --> 00:23:39,599
再加一个物理的box

514
00:23:42,059 --> 00:23:45,119
哎那么现在我们就加了两个这个碰撞组件了

515
00:23:45,119 --> 00:23:46,319
但是这两个重合了

516
00:23:46,319 --> 00:23:52,660
所以我们把第二个碰撞向下去进行一个偏移啊

517
00:23:52,660 --> 00:23:56,440
大家可以看把第二个向下去偏移

518
00:23:56,440 --> 00:23:59,859
偏移偏移450吧

519
00:24:01,059 --> 00:24:02,559
450差不多

520
00:24:03,319 --> 00:24:04,039
-450

521
00:24:04,039 --> 00:24:05,089
差不多啊

522
00:24:05,089 --> 00:24:06,319
你要细的话

523
00:24:06,319 --> 00:24:07,680
-452

524
00:24:08,140 --> 00:24:08,859
对不对

525
00:24:08,859 --> 00:24:10,059
这就差不多了

526
00:24:10,059 --> 00:24:13,599
那这样的话两个管道上面都有碰撞体了

527
00:24:13,599 --> 00:24:17,220
那么这里管道也要设置为静态的

528
00:24:17,220 --> 00:24:18,240
要不然他也会掉下去

529
00:24:18,240 --> 00:24:19,180
对不对

530
00:24:20,420 --> 00:24:23,450
然后还有一点就是管堆管道而言

531
00:24:23,450 --> 00:24:26,269
我们有时候比如说如果要做计分

532
00:24:26,269 --> 00:24:28,400
我们要知道我们有没有穿过一个管道

533
00:24:28,400 --> 00:24:29,190
对不对

534
00:24:29,190 --> 00:24:32,940
那这时候我们怎样去判断小鸟有没有穿过一个管道

535
00:24:32,940 --> 00:24:36,579
那么当然我们在这里如果要做这件事的话

536
00:24:38,720 --> 00:24:42,140
我们就要除了这这两个碰撞啊

537
00:24:42,140 --> 00:24:46,519
这两个碰撞我们还要加一个物理碰撞

538
00:24:46,519 --> 00:24:49,759
那么这个物理碰撞我们把editor点开

539
00:24:50,000 --> 00:24:52,398
这个碰撞我们就应该放在这里

540
00:24:57,599 --> 00:25:00,559
这个碰撞就在这里啊

541
00:25:00,559 --> 00:25:02,390
就是说如果我穿过这个位置

542
00:25:02,390 --> 00:25:04,339
我们就可以调我们的碰撞方法

543
00:25:04,339 --> 00:25:07,329
哎我们就知道我们小鸟穿过了一个呃

544
00:25:07,329 --> 00:25:10,390
就穿过了一个这个这个叫什么管道啊

545
00:25:10,390 --> 00:25:12,190
就比如说可以得一分

546
00:25:12,190 --> 00:25:12,609
对不对

547
00:25:12,609 --> 00:25:13,898
就可以得一分

548
00:25:14,039 --> 00:25:19,119
那么在这里默认情况下这个碰撞体啊

549
00:25:19,119 --> 00:25:20,259
我们是穿过去的

550
00:25:20,259 --> 00:25:21,579
因为它是产生碰撞的

551
00:25:21,579 --> 00:25:24,849
但是我们在这里就可以用我们的传感器模式了

552
00:25:24,849 --> 00:25:26,220
把它勾上

553
00:25:26,220 --> 00:25:27,000
勾上以后

554
00:25:27,000 --> 00:25:28,740
我们的小鸟就可以穿过去了啊

555
00:25:28,740 --> 00:25:32,898
并且穿过去我们也可以怎样触发碰撞方法

556
00:25:32,898 --> 00:25:34,699
而且我们在这里可以给他一个tag值

557
00:25:34,699 --> 00:25:36,480
比如说给他个一啊

558
00:25:36,480 --> 00:25:37,740
我们每次发生碰撞

559
00:25:37,740 --> 00:25:39,900
我们去判断碰撞的这个tag值

560
00:25:39,900 --> 00:25:40,740
如果是一

561
00:25:40,740 --> 00:25:43,380
就证明当前小鸟并没有碰到其他物体

562
00:25:43,380 --> 00:25:45,579
只是穿过了这个管道而已啊

563
00:25:47,940 --> 00:25:48,799
ok啊

564
00:25:48,799 --> 00:25:50,960
那么这就是一个管道啊

565
00:25:50,960 --> 00:25:52,339
这就是一个管道

566
00:25:52,339 --> 00:25:55,210
那么我们在这里嗯

567
00:25:55,210 --> 00:25:57,339
管道的话也是会循环的

568
00:25:57,339 --> 00:25:59,839
所以我们在这里创建一个空的节点

569
00:26:02,519 --> 00:26:03,599
创建一个空的节点

570
00:26:03,599 --> 00:26:06,898
然后把管道放到这个空的节点里面来

571
00:26:09,759 --> 00:26:11,180
我看一下啊

572
00:26:19,279 --> 00:26:20,059
空的节点

573
00:26:20,059 --> 00:26:22,940
或者我们直接我们直接放到地面上吧

574
00:26:22,940 --> 00:26:24,740
啊两种方式都可以啊

575
00:26:24,740 --> 00:26:26,299
直接放到我们地面

576
00:26:26,299 --> 00:26:28,130
作为地面的子物体

577
00:26:28,130 --> 00:26:29,809
或者说直接来个空物体

578
00:26:29,809 --> 00:26:31,940
把所有的管道都放这里都可以

579
00:26:31,940 --> 00:26:35,259
我们可以把这个管道直接放到地面

580
00:26:35,259 --> 00:26:37,420
作为地面的子物体啊

581
00:26:37,420 --> 00:26:41,079
作为这个地面的这个子物体啊

582
00:26:41,079 --> 00:26:42,339
这样的话

583
00:26:45,400 --> 00:26:46,480
啊这样的话不行

584
00:26:46,480 --> 00:26:47,319
所以地面组合体

585
00:26:47,319 --> 00:26:49,839
那我就会把这个地面给遮盖住了

586
00:26:49,839 --> 00:26:52,119
那我们还是用这种方式吧

587
00:26:53,920 --> 00:26:57,339
那我们就直接在这里复制一份

588
00:26:57,339 --> 00:27:00,539
复制一份管道放到这个位置

589
00:27:00,759 --> 00:27:02,079
放到这个位置啊

590
00:27:02,079 --> 00:27:06,369
那基本上就是让它保证一个地面上有这样一个管道

591
00:27:06,369 --> 00:27:08,339
一个地面上一个管道

592
00:27:13,019 --> 00:27:14,279
那么两个管道有了

593
00:27:14,279 --> 00:27:16,769
我们就应该让这个管道是不是循环起来

594
00:27:16,769 --> 00:27:17,759
循环起来

595
00:27:17,759 --> 00:27:20,670
怎样循环写脚本啊

596
00:27:20,670 --> 00:27:23,250
管道的循环注意也能用这个背景的

597
00:27:23,250 --> 00:27:25,710
因为背景这个就是用来做循环移动的

598
00:27:25,710 --> 00:27:26,539
对不对

599
00:27:26,539 --> 00:27:29,509
虽然也能到这个也能给它放到管道上面

600
00:27:29,509 --> 00:27:31,039
但是管道除了循环

601
00:27:31,039 --> 00:27:32,509
还有一个特点

602
00:27:32,509 --> 00:27:33,829
就是它每次循环完了

603
00:27:33,829 --> 00:27:37,039
它的高度是会发生这个变化的啊

604
00:27:37,039 --> 00:27:39,289
因为他如果总是这个高度就没意思了

605
00:27:39,289 --> 00:27:41,079
它的高度是会发生变化的

606
00:27:41,079 --> 00:27:42,339
所以说对于管道而言

607
00:27:42,339 --> 00:27:45,579
我们尽量给它单独去写一个脚本

608
00:27:55,980 --> 00:27:57,299
那么在这边啊

609
00:27:57,299 --> 00:28:01,700
我们管道首先写一下apple ctrl

610
00:28:04,279 --> 00:28:05,480
那么我们看一下啊

611
00:28:05,480 --> 00:28:07,990
这边比如说每一个管道

612
00:28:07,990 --> 00:28:09,910
当它往左边移动啊

613
00:28:09,910 --> 00:28:11,319
如果移动到负

614
00:28:11,319 --> 00:28:13,930
你看到-50的时候就出屏幕了

615
00:28:13,930 --> 00:28:17,099
所以我们管道就是如果小雨了

616
00:28:17,099 --> 00:28:19,140
基本上就是个-50啊

617
00:28:19,140 --> 00:28:23,369
小雨了-50我们就可以让它移动两个屏的距离就可以出

618
00:28:23,369 --> 00:28:25,539
就可以到这个位置啊

619
00:28:25,619 --> 00:28:26,940
移动两平距离啊

620
00:28:26,940 --> 00:28:28,619
一定你移动一平就到这儿了

621
00:28:28,619 --> 00:28:29,220
对不对

622
00:28:29,220 --> 00:28:30,119
出了屏幕

623
00:28:30,119 --> 00:28:32,460
移动两瓶的位置就到这里了啊

624
00:28:32,460 --> 00:28:34,119
这是最合适的

625
00:28:37,700 --> 00:28:38,559
ok啊

626
00:28:38,559 --> 00:28:40,240
那么我们来写一下啊

627
00:28:40,240 --> 00:28:42,378
我们来写一下这个管道

628
00:28:45,680 --> 00:28:47,480
首先我们有一个速度

629
00:28:47,480 --> 00:28:49,119
管道的速度

630
00:28:50,960 --> 00:28:53,419
我们也是给他写成一个属性

631
00:28:55,579 --> 00:28:58,309
然后也让他在面板上显示啊

632
00:28:58,309 --> 00:29:01,960
然后在这里开始移动我们for循环

633
00:29:05,779 --> 00:29:09,400
便利一下我的两个管道啊

634
00:29:09,400 --> 00:29:11,980
因为我这个脚本最终是挂载到谁身上的

635
00:29:11,980 --> 00:29:13,480
也是挂载到他身上的

636
00:29:13,480 --> 00:29:14,559
对不对啊

637
00:29:14,559 --> 00:29:15,898
我们已经挂上了

638
00:29:17,339 --> 00:29:23,069
然后在这里首先移动起来

639
00:29:23,069 --> 00:29:26,619
点x减等于z.p

640
00:29:30,319 --> 00:29:31,819
乘以个dt啊

641
00:29:31,819 --> 00:29:32,750
乘以个dt

642
00:29:32,750 --> 00:29:33,740
那么这样的话

643
00:29:33,740 --> 00:29:35,000
首先管道可以移动了

644
00:29:35,000 --> 00:29:36,319
我们现在来运行一下

645
00:29:36,319 --> 00:29:37,299
看一下

646
00:29:38,660 --> 00:29:39,799
这是第一个管道

647
00:29:41,000 --> 00:29:42,140
然后已经可以移动了

648
00:29:42,140 --> 00:29:43,039
这是第二个管道

649
00:29:43,039 --> 00:29:44,019
对不对

650
00:29:46,559 --> 00:29:47,700
然后

651
00:29:49,759 --> 00:29:52,640
我们每一次管道只要出了屏幕啊

652
00:29:52,640 --> 00:29:53,059
出了屏幕

653
00:29:53,059 --> 00:29:54,500
我们刚才算了一下

654
00:29:54,500 --> 00:29:59,579
大概你看就是点x小于-50啊

655
00:29:59,579 --> 00:30:00,779
基本上就算出屏幕了

656
00:30:00,779 --> 00:30:04,859
我们就可以把管道放到我们的屏幕的最右边了

657
00:30:04,859 --> 00:30:07,119
就是加两瓶的位置

658
00:30:07,839 --> 00:30:12,160
那我们就加等一个288x2啊

659
00:30:12,160 --> 00:30:13,390
两平的位置

660
00:30:13,390 --> 00:30:14,940
然后呢

661
00:30:17,039 --> 00:30:21,359
why我们每一次只要就是把它从左边往右边一放

662
00:30:21,359 --> 00:30:24,180
我们也让它的y轴变一个随机数

663
00:30:24,180 --> 00:30:25,799
y轴随机数到多高呢

664
00:30:25,799 --> 00:30:27,000
我们看一下

665
00:30:27,420 --> 00:30:29,519
那么默认的高度啊

666
00:30:29,519 --> 00:30:30,539
默认的高度是零

667
00:30:30,539 --> 00:30:32,880
我们就随机到一个零啊

668
00:30:32,880 --> 00:30:34,740
不是我看一下啊

669
00:30:35,539 --> 00:30:38,000
啊应该我们能选中地面

670
00:30:39,079 --> 00:30:39,920
选中背景

671
00:30:39,920 --> 00:30:41,398
选中这个管道

672
00:30:41,539 --> 00:30:44,779
那这个管道的高度大家可以看默认是450

673
00:30:44,779 --> 00:30:46,250
450的高度

674
00:30:46,250 --> 00:30:50,680
所以我们在这里给他一个450到多少的随机

675
00:30:50,680 --> 00:30:53,380
450~600吧

676
00:30:53,380 --> 00:30:56,319
大概就是450~600这个高度的一个随机

677
00:30:56,319 --> 00:31:00,480
所以在这边450~600的随机数怎么写

678
00:31:00,480 --> 00:31:02,400
max.random

679
00:31:02,400 --> 00:31:05,099
这个random得到一个0~1的随机数

680
00:31:05,099 --> 00:31:08,220
乘以一个450~600

681
00:31:08,220 --> 00:31:09,779
那就是150的区间

682
00:31:09,779 --> 00:31:11,819
那这就是0~150的随机数

683
00:31:11,819 --> 00:31:13,380
再加上一个400

684
00:31:13,380 --> 00:31:16,559
那结果就是400

685
00:31:17,759 --> 00:31:19,700
我们是450是吧

686
00:31:21,200 --> 00:31:25,759
那么这样的话结果就是450~600的一个随机区间区间啊

687
00:31:25,759 --> 00:31:28,339
那这样的话每一次只要从左边到右边

688
00:31:28,339 --> 00:31:29,599
它的高度就会被重置

689
00:31:29,599 --> 00:31:30,440
我们运行一下

690
00:31:30,440 --> 00:31:31,619
看一下效果

691
00:31:32,259 --> 00:31:33,160
小鸟等一下

692
00:31:33,160 --> 00:31:34,960
我们要把它的这个旋转锁定

693
00:31:34,960 --> 00:31:35,559
如果不锁定

694
00:31:35,559 --> 00:31:37,579
你看就会出现这样的情况

695
00:31:39,339 --> 00:31:41,680
哎你看这个已经变了吧

696
00:31:43,480 --> 00:31:44,878
啊已经变了

697
00:31:46,500 --> 00:31:49,339
这连着这两个都很都很高

698
00:31:57,339 --> 00:31:59,140
你看这个又变成很低的了

699
00:31:59,140 --> 00:32:00,490
这就没有问题了啊

700
00:32:00,490 --> 00:32:01,660
没有问题了

701
00:32:01,660 --> 00:32:05,779
然后呃我们接下来把小鸟的

702
00:32:05,779 --> 00:32:09,529
首先小鸟不能让它这个一直转

703
00:32:09,529 --> 00:32:12,200
我们要把它的这个旋转给它锁定了

704
00:32:12,200 --> 00:32:14,909
那这样的话小鸟首先就不会来回转了

705
00:32:14,909 --> 00:32:17,729
其次这个管子不能这样插在地上啊

706
00:32:17,729 --> 00:32:19,720
应该是地面在它之上的

707
00:32:19,720 --> 00:32:23,579
所以我们在这里把地面拖到最下方

708
00:32:24,160 --> 00:32:25,569
这样的话就好一些

709
00:32:25,569 --> 00:32:27,039
大家看

710
00:32:29,920 --> 00:32:31,819
是不是就像那回事了

711
00:32:35,119 --> 00:32:36,919
是不是像那么回事了

712
00:32:41,759 --> 00:32:42,450
ok啊

713
00:32:42,450 --> 00:32:47,930
那么接下来如果比如说我我这个小鸟穿过去

714
00:32:47,930 --> 00:32:50,900
我要debug一个加分啊

715
00:32:50,900 --> 00:32:52,220
debug一个加一

716
00:32:52,220 --> 00:32:54,410
然后这个如果碰到地面了

717
00:32:54,410 --> 00:32:56,849
我要debug一个死亡啊

718
00:32:56,849 --> 00:32:59,099
我们现在就是没有学这个ui

719
00:32:59,099 --> 00:33:01,470
所以我们直接给它打印出来就可以了

720
00:33:01,470 --> 00:33:03,000
那这个东西很简单

721
00:33:03,000 --> 00:33:05,669
我们打开我们这个小本

722
00:33:05,669 --> 00:33:07,579
选中我们这个小鸟

723
00:33:07,859 --> 00:33:10,319
那么因为物理引擎是小鸟这边去做的

724
00:33:10,319 --> 00:33:12,700
所以我们在小鸟这边去监听

725
00:33:14,279 --> 00:33:16,720
那么呃

726
00:33:19,740 --> 00:33:20,640
我们想一想啊

727
00:33:20,640 --> 00:33:21,599
我们想想

728
00:33:21,599 --> 00:33:24,039
那么这个小鸟的话

729
00:33:25,259 --> 00:33:27,660
这边我们已经把物理引擎开启了

730
00:33:27,660 --> 00:33:29,880
所以我们这边应该不需要别的操作了

731
00:33:29,880 --> 00:33:30,900
应该是没有落下

732
00:33:30,900 --> 00:33:33,400
我们直接监听应该就没有问题了

733
00:33:33,400 --> 00:33:35,529
i'm begin contact

734
00:33:35,529 --> 00:33:36,880
这一定要记着啊

735
00:33:36,880 --> 00:33:38,500
里面有三个三是物理的

736
00:33:38,500 --> 00:33:38,859
对不对

737
00:33:38,859 --> 00:33:41,159
第一个我们叫contact

738
00:33:41,359 --> 00:33:46,670
第二个第三个other就可以了

739
00:33:46,670 --> 00:33:50,740
然后我们去判断我们碰到的那个就是other

740
00:33:50,740 --> 00:33:51,940
他的tag值

741
00:33:51,940 --> 00:33:53,829
如果tag值是一

742
00:33:53,829 --> 00:33:56,378
就证明我们要加分了

743
00:33:57,960 --> 00:34:00,680
debug我们就debug一个加分

744
00:34:01,880 --> 00:34:04,099
否则的话就证明碰到其他物体了

745
00:34:04,099 --> 00:34:08,619
我们就让他怎样debug一个死亡

746
00:34:09,818 --> 00:34:11,159
因为现在没有ui

747
00:34:11,159 --> 00:34:13,699
所以我们直接这样打印就ok了

748
00:34:22,818 --> 00:34:23,838
对不对

749
00:34:23,838 --> 00:34:28,400
这样我们把这个控制台调出来

750
00:34:30,159 --> 00:34:31,239
哎真烦

751
00:34:31,239 --> 00:34:33,260
快捷键又不管用了

752
00:34:36,559 --> 00:34:38,019
大家可以看一下

753
00:34:38,838 --> 00:34:40,429
穿过去加分

754
00:34:40,429 --> 00:34:43,800
是不是我碰到管子了就死亡了

755
00:34:44,798 --> 00:34:46,898
每次死亡都和加分一块

756
00:34:50,858 --> 00:34:52,898
你看这次又又碰到了

757
00:34:52,898 --> 00:34:54,139
又死亡了

758
00:34:56,659 --> 00:34:59,418
你看这次就是纯加分没死完了啊

759
00:34:59,418 --> 00:35:01,039
就是我们这个跳过管子了

760
00:35:01,039 --> 00:35:02,139
对不对

761
00:35:03,398 --> 00:35:04,898
你看这次应该也是加分

762
00:35:04,898 --> 00:35:06,878
你看连续两次加分是死亡

763
00:35:06,878 --> 00:35:07,659
ok啊

764
00:35:07,659 --> 00:35:10,628
那么这个就已经没有问题了啊

765
00:35:10,628 --> 00:35:13,659
那么如果咱们学了ui

766
00:35:13,659 --> 00:35:14,500
咱们就可以在这儿

767
00:35:14,500 --> 00:35:15,820
比如说写个积分啊

768
00:35:15,820 --> 00:35:17,079
我们现在是加一分

769
00:35:17,079 --> 00:35:18,639
两分三分四分死亡了

770
00:35:18,639 --> 00:35:19,719
可以出个game over

771
00:35:19,719 --> 00:35:21,500
对不对啊

772
00:35:21,500 --> 00:35:22,579
那就是这样的啊

773
00:35:22,579 --> 00:35:23,869
非常简单

774
00:35:23,869 --> 00:35:27,599
那么这个就这么多吧

775
00:35:33,619 --> 00:35:36,018
我还忘了他忘了一点

776
00:35:36,018 --> 00:35:41,820
这个小鸟我们是可以让他这个加上这个帧动画的啊

777
00:35:41,820 --> 00:35:42,780
加上这个帧动画的

778
00:35:42,780 --> 00:35:45,250
所以我们在这里可以选中bd

779
00:35:45,250 --> 00:35:49,780
然后在这里动画编辑器给它加一个动画

780
00:35:53,679 --> 00:35:55,030
加上一个动画

781
00:35:55,030 --> 00:35:57,159
那这个东西大家就应该好加了

782
00:35:57,159 --> 00:35:58,420
自己去加

783
00:36:01,739 --> 00:36:03,659
四针加上来

784
00:36:06,938 --> 00:36:11,048
比如说我们一秒走八针啊

785
00:36:11,048 --> 00:36:13,148
这个速度基本上就可以

786
00:36:13,148 --> 00:36:14,798
现在我们来运行一下

787
00:36:14,798 --> 00:36:16,159
看一下效果

788
00:36:18,219 --> 00:36:23,420
哎没有让他上来自动这个执行动画啊

789
00:36:24,378 --> 00:36:25,518
选中这个小鸟

790
00:36:25,518 --> 00:36:27,400
首先动画保存好

791
00:36:27,539 --> 00:36:28,349
关闭了

792
00:36:28,349 --> 00:36:29,639
选中这个小鸟

793
00:36:29,639 --> 00:36:31,039
在右边

794
00:36:31,159 --> 00:36:34,280
我们把这个小鸟的动画移上来

795
00:36:34,280 --> 00:36:35,659
让他上来就自动播放

796
00:36:35,659 --> 00:36:38,260
因为他就这一个动画一直在播放

797
00:36:38,398 --> 00:36:40,260
然后我们在这里运行一下

798
00:36:42,378 --> 00:36:44,418
这时候首先小鸟播放动画了

799
00:36:44,418 --> 00:36:45,139
没有问题了

800
00:36:45,139 --> 00:36:46,668
但是只播放了一次

801
00:36:46,668 --> 00:36:50,880
那这里我们要在这里去更改一下这个小鸟的

802
00:36:53,159 --> 00:36:56,760
这个循环模式我们应该给他改成loop啊

803
00:36:56,760 --> 00:37:01,300
让他一直播放这个动画重新运行

804
00:37:02,878 --> 00:37:07,159
这样的话是不是看起来就会好一些了

805
00:37:08,338 --> 00:37:09,378
就是会动的

806
00:37:09,378 --> 00:37:09,858
对不对

807
00:37:09,858 --> 00:37:11,400
会动的呃

808
00:37:11,400 --> 00:37:12,719
但是这里有一个问题啊

809
00:37:12,719 --> 00:37:14,670
就是他这个编辑器有一个bug

810
00:37:14,670 --> 00:37:16,739
就比如说父子关系

811
00:37:16,739 --> 00:37:17,820
你来回调几次

812
00:37:17,820 --> 00:37:18,239
你看啊

813
00:37:18,239 --> 00:37:21,599
比如说我把这个管道从-5题里面移出去

814
00:37:21,599 --> 00:37:23,300
再移进来

815
00:37:23,759 --> 00:37:26,219
这时候你看他身上越来越难

816
00:37:26,219 --> 00:37:27,059
有没有发现

817
00:37:27,059 --> 00:37:28,318
然后把管道移开

818
00:37:28,318 --> 00:37:32,018
你发现管道原来的地方还有一个碰撞体啊

819
00:37:32,018 --> 00:37:34,119
所以实际上这就是引擎的一些bug

820
00:37:34,119 --> 00:37:34,898
这个怎么解决

821
00:37:34,898 --> 00:37:37,840
你把这个引擎关了再打开就好了啊

822
00:37:38,559 --> 00:37:40,179
可能比如说这个版本以后

823
00:37:40,179 --> 00:37:41,019
如果升了级

824
00:37:41,019 --> 00:37:41,920
你再看视频

825
00:37:41,920 --> 00:37:44,280
可能你的就没有这些问题了啊

826
00:37:45,099 --> 00:37:46,059
嗯ok啊

827
00:37:46,059 --> 00:37:49,360
那我们这节课就这么多


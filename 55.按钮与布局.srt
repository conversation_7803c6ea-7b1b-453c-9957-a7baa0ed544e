1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,019 --> 00:00:12,240
ok我们这节课来说两个空间

3
00:00:12,259 --> 00:00:15,439
那么第一个我们来说这个button控件啊

4
00:00:15,439 --> 00:00:16,429
就是这个按钮

5
00:00:16,429 --> 00:00:19,559
第二个咱们来说一下这个布局呃

6
00:00:19,559 --> 00:00:20,460
一个一个来啊

7
00:00:20,460 --> 00:00:21,239
先来按钮

8
00:00:21,239 --> 00:00:22,739
我们把一个按钮拖上来

9
00:00:22,739 --> 00:00:25,260
当然从这边创建也是一样的啊

10
00:00:25,260 --> 00:00:26,219
也是一样的

11
00:00:26,219 --> 00:00:27,480
这是按钮啊

12
00:00:27,480 --> 00:00:28,859
只是在这边麻烦点

13
00:00:28,859 --> 00:00:30,199
我就直接拖过来了

14
00:00:30,320 --> 00:00:32,359
呃那么按钮大家可以看啊

15
00:00:32,359 --> 00:00:36,020
其实控件的组成都是由多个小控件组成的

16
00:00:36,020 --> 00:00:39,509
比如说一个按钮除了一个灰色的背景

17
00:00:39,509 --> 00:00:41,100
它上面还有文字

18
00:00:41,100 --> 00:00:42,689
文字的话就是一个level

19
00:00:42,689 --> 00:00:43,500
对不对

20
00:00:43,500 --> 00:00:45,030
那么对于button而言

21
00:00:45,030 --> 00:00:46,799
这个上面的这个图片呀

22
00:00:46,799 --> 00:00:48,719
你都是完全可以自己去更改的

23
00:00:48,719 --> 00:00:53,939
大家可以看像这些控件基本上都是由这个精灵组件的啊

24
00:00:53,939 --> 00:00:57,719
你在这里完全可以更改为你要的这个图片

25
00:00:57,719 --> 00:00:58,799
这个这个按钮样式

26
00:00:58,799 --> 00:01:00,619
比如说你可以拖进来一个

27
00:01:01,640 --> 00:01:04,099
你呢现在按钮就变成这个样子了

28
00:01:04,099 --> 00:01:05,099
运行一下

29
00:01:05,299 --> 00:01:07,219
这个是可以点的啊

30
00:01:07,219 --> 00:01:08,480
你看我上来以后

31
00:01:08,480 --> 00:01:10,280
它就变成按钮样式了啊

32
00:01:10,280 --> 00:01:11,659
当然这些都是能改的

33
00:01:11,659 --> 00:01:13,459
我们只是现在没有改别的啊

34
00:01:13,459 --> 00:01:14,680
只改了这一个

35
00:01:15,340 --> 00:01:17,680
但是啊在这里啊

36
00:01:17,680 --> 00:01:22,620
这里一般我们嗯不怎么会多在这里改啊

37
00:01:22,620 --> 00:01:24,120
我们的我们的按钮背景

38
00:01:24,120 --> 00:01:26,760
我们发现在这里改了以后只改了一个啊

39
00:01:26,760 --> 00:01:29,920
并没有把它所有的这样式都改了啊

40
00:01:29,920 --> 00:01:30,939
鼠标一放上去

41
00:01:30,939 --> 00:01:32,260
它又变成原来的样式

42
00:01:32,260 --> 00:01:32,920
对不对

43
00:01:32,939 --> 00:01:36,780
所以我们看一下巴特他自己的属性啊

44
00:01:38,099 --> 00:01:42,329
那么首先button在这里啊

45
00:01:42,329 --> 00:01:45,599
那么第一个啊我们不用管啊

46
00:01:45,599 --> 00:01:47,459
这个是他的一个背景节点

47
00:01:47,459 --> 00:01:51,099
说白了这个就是呃一个关联啊

48
00:01:51,099 --> 00:01:52,180
他自己的一个关联

49
00:01:52,180 --> 00:01:53,530
我们这儿不用管它

50
00:01:53,530 --> 00:01:55,650
看这个呃

51
00:01:55,650 --> 00:01:58,500
那么这个代表的是是否交互啊

52
00:01:58,500 --> 00:01:59,939
就是这个按钮是可以点的

53
00:01:59,939 --> 00:02:01,260
如果你把这个关了

54
00:02:01,260 --> 00:02:03,680
这个按钮就成按灰色的了

55
00:02:03,680 --> 00:02:05,269
这时候你鼠标放上来

56
00:02:05,269 --> 00:02:06,980
它是没有任何反应的啊

57
00:02:06,980 --> 00:02:08,960
是意思就是被禁用了啊

58
00:02:08,960 --> 00:02:11,599
就是这个按钮是被禁用了啊

59
00:02:11,599 --> 00:02:13,159
它是不允许使用的

60
00:02:13,159 --> 00:02:17,409
那么在这里这里是一个过渡啊

61
00:02:17,409 --> 00:02:20,409
就是说按钮因为有几种状态啊

62
00:02:20,409 --> 00:02:22,680
首先我们鼠标移到上面

63
00:02:22,879 --> 00:02:25,580
移到上面有一有一个状态啊

64
00:02:25,580 --> 00:02:27,439
它会有一个高亮的状态

65
00:02:27,439 --> 00:02:29,969
意思是我们这个鼠标移上来了啊

66
00:02:29,969 --> 00:02:31,080
点击一个状态

67
00:02:31,080 --> 00:02:32,419
两个状态了是吧

68
00:02:32,439 --> 00:02:34,300
那么我们鼠标出去啊

69
00:02:34,300 --> 00:02:36,599
默认状态是一个三个状态了

70
00:02:36,620 --> 00:02:38,930
我们刚才发现还有一个禁用禁用

71
00:02:38,930 --> 00:02:41,419
那么加上它的话就是四个状态了

72
00:02:41,419 --> 00:02:43,900
所以实际上一个按钮会有四个状态

73
00:02:44,360 --> 00:02:46,620
那么这四个状态的话

74
00:02:46,639 --> 00:02:49,879
我们可以给它不同的图片啊

75
00:02:49,879 --> 00:02:51,650
那么大家可以看一下下面啊

76
00:02:51,650 --> 00:02:53,599
有四种图片啊

77
00:02:53,599 --> 00:02:54,740
有四个地儿放图片

78
00:02:54,740 --> 00:02:57,300
第一个就是普通的图片

79
00:02:57,300 --> 00:03:00,750
比如说我们可以放个小鸟啊

80
00:03:00,750 --> 00:03:03,000
那么比如说这个文本啊

81
00:03:03,000 --> 00:03:04,139
你要不想要了的话

82
00:03:04,139 --> 00:03:06,780
你就把直接把他的这个子节点删了就好了

83
00:03:06,780 --> 00:03:10,750
那么大家可以看这个button是不是再改下宽

84
00:03:10,750 --> 00:03:13,120
就变成没有文字的一个纯butt了

85
00:03:13,120 --> 00:03:13,879
对不对

86
00:03:14,460 --> 00:03:18,300
然后我们把第二个图片放到按下去啊

87
00:03:18,300 --> 00:03:21,229
第三个放到这个呃

88
00:03:21,229 --> 00:03:23,180
就是悬浮状态啊

89
00:03:23,180 --> 00:03:26,900
就是鼠标移到它上面会显示这个图片啊

90
00:03:26,900 --> 00:03:28,939
当我们鼠标点下去的时候

91
00:03:28,939 --> 00:03:30,199
会显示第二个图片

92
00:03:30,199 --> 00:03:31,960
正常显示的是第一个图片

93
00:03:31,960 --> 00:03:33,460
当我们禁用以后

94
00:03:33,460 --> 00:03:35,080
显示的是最后一个图片啊

95
00:03:35,080 --> 00:03:36,550
这是禁用以后的图片

96
00:03:36,550 --> 00:03:41,240
那这样的话运行以后你看当鼠标移上去

97
00:03:41,240 --> 00:03:43,960
它就会变成翅膀向下的这样一个图片

98
00:03:43,960 --> 00:03:45,580
当我按下去的时候

99
00:03:45,580 --> 00:03:48,939
它就会变成鼠这个这个翅膀在中间的状态啊

100
00:03:48,939 --> 00:03:52,319
所以每一个状态它都有不同的图片切换

101
00:03:55,460 --> 00:03:57,979
那么我们不但可以给这种效果

102
00:03:57,979 --> 00:03:59,060
还可以给什么呀

103
00:03:59,060 --> 00:04:00,800
颜色的改变啊

104
00:04:00,800 --> 00:04:05,979
就是我现在不希望就是不同的状态有不同的图片了啊

105
00:04:05,979 --> 00:04:07,919
我希望图片都是一样的

106
00:04:08,460 --> 00:04:10,419
但是颜色不同

107
00:04:10,419 --> 00:04:14,560
比如正常就是纯色按下去变成红色啊

108
00:04:14,560 --> 00:04:16,600
悬浮在上面是蓝色

109
00:04:16,600 --> 00:04:18,920
禁用的是黑色啊

110
00:04:18,920 --> 00:04:24,339
那运行以后你看鼠标上来蓝色点一下红色啊

111
00:04:24,339 --> 00:04:25,420
如果我们给它禁用了

112
00:04:25,420 --> 00:04:28,100
它就变成黑色啊

113
00:04:30,500 --> 00:04:32,839
ok那么这里还有个时间

114
00:04:32,839 --> 00:04:34,129
意思就是过渡时间

115
00:04:34,129 --> 00:04:36,139
大家刚才也看到这个颜色的变

116
00:04:36,139 --> 00:04:38,139
它是嗯渐变

117
00:04:38,139 --> 00:04:40,779
并不是瞬间就变成这个红色的蓝色的啊

118
00:04:40,779 --> 00:04:43,620
它是从默认的颜色逐渐变成红色

119
00:04:43,620 --> 00:04:45,060
逐渐变成蓝色这样

120
00:04:45,060 --> 00:04:47,879
所以这个改变的时间这里可以修改

121
00:04:47,879 --> 00:04:50,220
除了颜色和这个精灵啊

122
00:04:50,220 --> 00:04:51,300
一般就用这俩

123
00:04:51,300 --> 00:04:54,100
还有一个是一个缩放

124
00:04:54,180 --> 00:04:56,100
这里有个缩放的倍数啊

125
00:04:56,100 --> 00:04:57,899
就是允许缩放1.2倍

126
00:04:57,899 --> 00:05:00,300
就是允许变大一点二倍是不是变大

127
00:05:00,300 --> 00:05:02,420
然后时间还是0.1秒

128
00:05:02,420 --> 00:05:03,620
那这个是什么意思

129
00:05:03,620 --> 00:05:06,139
这个就是我们鼠标在上面没反应

130
00:05:06,139 --> 00:05:08,959
但是我们一点击点击点赞

131
00:05:08,959 --> 00:05:10,279
点击点击啊

132
00:05:10,279 --> 00:05:12,259
这就是这样的一个缩放效果啊

133
00:05:12,259 --> 00:05:13,819
也是给我们一个按钮的效果

134
00:05:13,819 --> 00:05:14,519
对不对

135
00:05:16,879 --> 00:05:19,160
ok那么按钮最重要的是什么

136
00:05:19,160 --> 00:05:20,240
就是按钮你点了以后

137
00:05:20,240 --> 00:05:21,560
你要触发什么事件

138
00:05:21,560 --> 00:05:23,939
是不是比如说有一个事件

139
00:05:23,939 --> 00:05:27,360
比如说这个脚本有个事件叫test啊

140
00:05:27,360 --> 00:05:28,319
然后会输出

141
00:05:28,319 --> 00:05:29,139
点击了

142
00:05:29,160 --> 00:05:30,779
我就想把这个事件

143
00:05:30,779 --> 00:05:34,540
就想按钮按一下就会嗯触发这个事件怎么办

144
00:05:34,540 --> 00:05:36,040
首先你要把这个脚本挂了

145
00:05:36,040 --> 00:05:37,000
挂了上来

146
00:05:37,000 --> 00:05:38,040
对不对

147
00:05:38,379 --> 00:05:42,339
其次这里按钮的事件大家可以看默认是零

148
00:05:42,339 --> 00:05:43,240
就是没有时间

149
00:05:43,240 --> 00:05:45,600
我给他个一就是有事件了

150
00:05:45,620 --> 00:05:47,720
然后你要触发谁的事件

151
00:05:47,720 --> 00:05:49,639
我们这里由于脚本就挂在这

152
00:05:49,639 --> 00:05:50,959
挂载到这个button上了

153
00:05:50,959 --> 00:05:53,180
所以我们直接把八层拖上来

154
00:05:53,300 --> 00:05:59,560
就是我们要当我们点击按钮会触发button这个节点上的事件

155
00:05:59,560 --> 00:06:02,759
button节点的哪个组件啊

156
00:06:02,759 --> 00:06:03,959
第二个是哪个组件

157
00:06:03,959 --> 00:06:06,149
我们就选新的这个nescript

158
00:06:06,149 --> 00:06:07,740
是我们这个新的这个组件

159
00:06:07,740 --> 00:06:08,839
对不对啊

160
00:06:08,839 --> 00:06:10,459
就是就是我们自己写的这个脚本

161
00:06:10,459 --> 00:06:14,720
其实就是啊这里是一个学员组件或者说选脚本的地方

162
00:06:14,720 --> 00:06:16,639
这个是个代表哪个节点啊

163
00:06:16,639 --> 00:06:18,319
哪个节点上的哪个脚本

164
00:06:18,540 --> 00:06:21,060
然后最后就是这个角度里面的哪个方法

165
00:06:21,060 --> 00:06:23,569
你看就能找到test啊

166
00:06:23,569 --> 00:06:24,680
如果你要愿意的话

167
00:06:24,680 --> 00:06:26,360
还可以给它加一些参数啊

168
00:06:26,360 --> 00:06:29,660
一般我们这里就呃不需要给参数

169
00:06:29,660 --> 00:06:31,430
这里把你空着就完了啊

170
00:06:31,430 --> 00:06:33,939
那么test运行

171
00:06:35,519 --> 00:06:38,019
当我们现在点击它的时候

172
00:06:39,560 --> 00:06:41,750
点击点击点击点击了

173
00:06:41,750 --> 00:06:44,899
你看是不是就会触发我们的按钮事件了

174
00:06:44,899 --> 00:06:45,680
那么注意啊

175
00:06:45,680 --> 00:06:49,600
所有的这个只要能触发事件的这个组件啊

176
00:06:49,600 --> 00:06:52,120
通通是用这种方式去触发的啊

177
00:06:52,120 --> 00:06:54,639
你就给它添加上一个事件

178
00:06:54,639 --> 00:06:57,920
然后给他关联事件就ok了

179
00:06:58,040 --> 00:07:02,240
那么所有有事件的这个这个这个这个ui控件啊

180
00:07:02,240 --> 00:07:03,970
都是这样的嗯

181
00:07:03,970 --> 00:07:06,100
咱们是用button做了个例子

182
00:07:06,860 --> 00:07:09,439
那么现在button就ok了

183
00:07:09,439 --> 00:07:10,879
我们把ton删了啊

184
00:07:10,879 --> 00:07:12,980
我们现在来用啊

185
00:07:12,980 --> 00:07:13,699
先不杀他

186
00:07:13,699 --> 00:07:14,300
先不杀他

187
00:07:14,300 --> 00:07:15,160
放到这

188
00:07:15,600 --> 00:07:18,139
我们现在来说我们的这个布局

189
00:07:18,500 --> 00:07:20,660
我们把这个layout拖上来

190
00:07:20,660 --> 00:07:24,620
这个是个布局的一个呃控件啊

191
00:07:24,620 --> 00:07:26,480
那么他呢主要就是用来布局的

192
00:07:26,480 --> 00:07:28,639
也就是啊那么它拖上来以后

193
00:07:28,639 --> 00:07:31,100
大家发现它显示一个白色的样式

194
00:07:31,100 --> 00:07:32,779
所以它本身也是个精灵

195
00:07:32,779 --> 00:07:36,379
那么在这里你可以去修改它的内部的钥匙啊

196
00:07:36,379 --> 00:07:38,060
可以去给它填充个颜色呀

197
00:07:38,060 --> 00:07:39,079
或者怎样的

198
00:07:39,540 --> 00:07:42,839
然后在这里既然是个布局啊

199
00:07:42,839 --> 00:07:45,790
我把它我先拉伸一点

200
00:07:45,790 --> 00:07:47,889
那么布局怎么布局

201
00:07:47,889 --> 00:07:50,740
那我肯定要布局我里面的子节点

202
00:07:50,740 --> 00:07:54,540
所以我把这个按钮放到它的里面

203
00:07:54,540 --> 00:07:57,689
哎我现在按钮放到了它的子物体里面

204
00:07:57,689 --> 00:07:58,259
诶

205
00:07:58,259 --> 00:07:59,339
没见布局啊

206
00:07:59,339 --> 00:08:00,980
我在拷贝几个按钮

207
00:08:01,819 --> 00:08:04,259
两个三个

208
00:08:04,259 --> 00:08:06,300
我现在有三个按钮啊

209
00:08:06,300 --> 00:08:09,180
我有三个按钮都是作为我的子物体存

210
00:08:09,180 --> 00:08:10,259
在的啊

211
00:08:10,259 --> 00:08:12,620
都是作为我这个的子物体存在的

212
00:08:13,779 --> 00:08:15,819
但是并没有布局啊

213
00:08:15,819 --> 00:08:16,540
并没有布局

214
00:08:16,540 --> 00:08:17,050
怎么整

215
00:08:17,050 --> 00:08:18,730
我们选中这个layout

216
00:08:18,730 --> 00:08:22,480
在这里大家可以看有一个组件叫layout

217
00:08:22,480 --> 00:08:25,120
所以实际上它之所以叫布局组件

218
00:08:25,120 --> 00:08:28,100
就是因为他自己给我们加了个layout的组件啊

219
00:08:28,100 --> 00:08:29,899
我们创建一个空节点

220
00:08:29,899 --> 00:08:30,529
加个雷

221
00:08:30,529 --> 00:08:34,960
我们我们这个空姐点就变成布局的这样的一个空间了

222
00:08:34,980 --> 00:08:36,330
在这里有个类型

223
00:08:36,330 --> 00:08:39,419
就是你是需要水平布局还是垂直布局

224
00:08:39,419 --> 00:08:42,109
还是按格子方式布局啊

225
00:08:42,109 --> 00:08:44,058
我们可以试一下

226
00:08:44,058 --> 00:08:45,229
现在水平布局来

227
00:08:45,229 --> 00:08:49,419
那大家发现它在水平上就给你去产生了这样一个布局了

228
00:08:49,679 --> 00:08:54,659
然后这里是一个大小大小是呃

229
00:08:54,659 --> 00:08:59,440
是否要按这个默认是大小是不影响的

230
00:08:59,440 --> 00:09:01,840
如果选中第二个container啊

231
00:09:01,840 --> 00:09:03,009
如果选中第二个

232
00:09:03,009 --> 00:09:06,580
那么整个这个布局它的一个宽啊

233
00:09:06,580 --> 00:09:09,690
就会刚好你看和我们这个子呃

234
00:09:09,690 --> 00:09:14,029
三个子物体的这个宽啊进行一个贴合啊

235
00:09:14,029 --> 00:09:19,250
这是说白了就是让副节点啊适配子节点的这样的一个宽

236
00:09:19,250 --> 00:09:24,059
而最后一个是让子节点呃去适应这个副节点的款

237
00:09:24,059 --> 00:09:24,539
什么意思

238
00:09:24,539 --> 00:09:27,570
你看我父节点拉伸啊

239
00:09:27,570 --> 00:09:31,750
子节点它是去进行了一个适配啊

240
00:09:31,750 --> 00:09:33,429
子节点进行了一个适配

241
00:09:33,429 --> 00:09:37,740
所以这两个其实就是第二个就是负节点适应子节点的宽

242
00:09:37,759 --> 00:09:41,840
最后一个就是子节点去适应复节点的一个宽啊

243
00:09:41,840 --> 00:09:45,620
第一个就是呃宽度是不要去影响的啊

244
00:09:45,620 --> 00:09:46,980
只关心布局

245
00:09:47,759 --> 00:09:52,240
那么在这里嗯py是什么意思

246
00:09:52,240 --> 00:09:53,259
它是一个编剧

247
00:09:53,259 --> 00:09:54,700
现在有个左边边距

248
00:09:54,700 --> 00:09:56,049
比如说左边哪个20

249
00:09:56,049 --> 00:09:58,419
你看它离左边边距就有20

250
00:09:58,440 --> 00:10:00,600
右边编剧也能给给个20

251
00:10:00,600 --> 00:10:02,580
但是这里发现右边编剧给了没作用

252
00:10:02,580 --> 00:10:07,360
那是因为我们这里不让它的大小变成自适应了

253
00:10:07,360 --> 00:10:09,429
我们再给它改成呃

254
00:10:09,429 --> 00:10:11,500
让负节点去适应这个子节点

255
00:10:11,500 --> 00:10:14,539
那这时候你就会发现诶

256
00:10:14,539 --> 00:10:17,179
你看左边30

257
00:10:17,179 --> 00:10:19,940
右边30是不是这是一个边距就出来了

258
00:10:19,960 --> 00:10:24,399
那么最后一项是每一个节点之间的一个空隙

259
00:10:24,399 --> 00:10:27,220
现在三个之间精灵虽然高度不一样

260
00:10:27,220 --> 00:10:30,360
但实际上它们是相邻的啊

261
00:10:30,360 --> 00:10:31,919
我们可以给它放到一条线上

262
00:10:31,919 --> 00:10:33,159
大家可以看一下

263
00:10:33,179 --> 00:10:35,460
它们实际上是相邻的

264
00:10:35,460 --> 00:10:38,250
那我们希望让它们之间有点间距怎么办

265
00:10:38,250 --> 00:10:41,179
我们在这里就可以去给它这个属性

266
00:10:41,980 --> 00:10:44,639
给他20诶

267
00:10:44,639 --> 00:10:47,220
你看之间他们就会空20个距离

268
00:10:47,220 --> 00:10:50,700
然后水平是从左到右布局还是从右到左布局

269
00:10:50,700 --> 00:10:52,980
是这个那么有水平了

270
00:10:52,980 --> 00:10:54,840
当然也有垂直改成垂直

271
00:10:54,840 --> 00:10:58,019
大家可以看这就是垂直样式其实是一样的啊

272
00:10:58,019 --> 00:11:00,929
跟水平完全一样的啊

273
00:11:00,929 --> 00:11:02,580
就是一个是垂直垂直的

274
00:11:02,580 --> 00:11:04,000
一个是水平的bug

275
00:11:04,080 --> 00:11:05,700
然后在这里啊

276
00:11:05,700 --> 00:11:09,100
这个编剧就变成上边编剧与下边编剧了

277
00:11:09,139 --> 00:11:10,309
上面连续

278
00:11:10,309 --> 00:11:11,120
下面连续

279
00:11:11,120 --> 00:11:11,809
对不对

280
00:11:11,809 --> 00:11:14,299
还有一个是格子的

281
00:11:15,039 --> 00:11:17,440
格子的是什么意思啊

282
00:11:17,440 --> 00:11:18,970
格子的是什么意思

283
00:11:18,970 --> 00:11:22,240
那么在这里呃

284
00:11:23,399 --> 00:11:24,600
其实很简单

285
00:11:24,600 --> 00:11:29,980
格子的就是就类似于我们的我们的这个玩游戏的里面的那个背包啊

286
00:11:29,980 --> 00:11:31,840
背包就是一格两格三格四格

287
00:11:31,840 --> 00:11:32,950
第一行排满了

288
00:11:32,950 --> 00:11:34,600
然后第二行五格六格

289
00:11:34,600 --> 00:11:35,440
七格八格啊

290
00:11:35,440 --> 00:11:36,740
就这样的一个形式

291
00:11:36,740 --> 00:11:38,539
那我们在这里大家可以看编剧

292
00:11:38,539 --> 00:11:39,940
比如说我们给个零

293
00:11:40,379 --> 00:11:41,669
又给个零

294
00:11:41,669 --> 00:11:42,360
上给个零

295
00:11:42,360 --> 00:11:44,320
我们贴着边儿啊

296
00:11:44,320 --> 00:11:44,860
贴着边

297
00:11:44,860 --> 00:11:47,259
然后空格也给个零啊

298
00:11:47,259 --> 00:11:49,179
那么现在的话

299
00:11:51,059 --> 00:11:53,200
我们重新给他

300
00:11:55,360 --> 00:11:57,519
创建一个

301
00:11:57,519 --> 00:12:02,100
单色的一个精灵啊

302
00:12:02,100 --> 00:12:03,480
我们创建的按钮吧

303
00:12:06,480 --> 00:12:08,399
啊这样看的会清楚一些

304
00:12:08,399 --> 00:12:11,639
因为按钮的话他能看到他的这个外边框啊

305
00:12:11,639 --> 00:12:12,960
它是有点颜色的

306
00:12:12,960 --> 00:12:14,820
在这里比如说你看按钮

307
00:12:14,820 --> 00:12:17,559
我拷贝一个两个

308
00:12:20,500 --> 00:12:21,940
好背的是按钮啊

309
00:12:21,940 --> 00:12:28,059
不是它里面的这个level按钮拷贝一个两个三个四个五个六个

310
00:12:28,059 --> 00:12:28,960
当填满以后

311
00:12:28,960 --> 00:12:29,710
大家看啊

312
00:12:29,710 --> 00:12:31,419
这个grey的效果就出来了

313
00:12:31,419 --> 00:12:32,799
盒子效果就出来了

314
00:12:32,799 --> 00:12:35,320
填满以后他就会往第二行折行

315
00:12:35,320 --> 00:12:37,159
然后第三行第四行

316
00:12:37,159 --> 00:12:38,360
第五行啊

317
00:12:38,360 --> 00:12:39,259
只要放不下一个

318
00:12:39,259 --> 00:12:40,580
他就开始进行折返

319
00:12:40,580 --> 00:12:41,299
这样的话

320
00:12:41,299 --> 00:12:45,940
最后这个布局就会把我们的子节点布成这样的一个格子效果啊

321
00:12:45,940 --> 00:12:49,480
所以这个布局组件就是呃布局的控件啊

322
00:12:49,480 --> 00:12:50,919
就是做这样的一个事了

323
00:12:50,919 --> 00:12:52,720
他要不然就是给你横向布局

324
00:12:52,720 --> 00:12:54,240
要不然就是纵向布局

325
00:12:54,259 --> 00:12:57,440
要不然就是把子物体变成这种格子的这样的一个布

326
00:12:57,440 --> 00:12:57,860
局

327
00:12:59,220 --> 00:13:00,720
那么变成格子布局以后

328
00:13:00,720 --> 00:13:03,299
大家可以看一下里面的属性啊

329
00:13:03,299 --> 00:13:06,840
因为格子布局我们现在是横向满了以后

330
00:13:06,840 --> 00:13:09,809
然后第二行你它多了这样的一个选择

331
00:13:09,809 --> 00:13:11,639
你可以给它改成纵向

332
00:13:11,659 --> 00:13:14,419
改成纵向就变成他布局默认是从上到下

333
00:13:14,419 --> 00:13:15,019
完了以后

334
00:13:15,019 --> 00:13:17,419
然后第二列第三列按列就布局了

335
00:13:17,440 --> 00:13:20,080
然后有编剧四边的边距都有

336
00:13:20,080 --> 00:13:21,639
然后x的间距

337
00:13:21,639 --> 00:13:22,740
y的间距

338
00:13:22,779 --> 00:13:28,100
然后这个垂直和水平的一个对齐的方向啊

339
00:13:28,100 --> 00:13:30,289
现在垂直是从上到下的

340
00:13:30,289 --> 00:13:32,059
水平是从左到右的啊

341
00:13:32,059 --> 00:13:35,879
从左到右从上到下都是没有问题的啊

342
00:13:35,899 --> 00:13:39,320
那么这个就是布局的这样的一个控件啊

343
00:13:39,320 --> 00:13:41,539
大家其实自己拖上来去用一用

344
00:13:41,539 --> 00:13:42,980
大家就明白了啊

345
00:13:43,000 --> 00:13:44,620
你不要光看啊

346
00:13:44,620 --> 00:13:46,539
光看可能只是理解啊

347
00:13:46,539 --> 00:13:48,720
但是到后面自己也就忘了啊

348
00:13:48,720 --> 00:13:50,460
自己一定要用一用啊

349
00:13:50,460 --> 00:13:53,580
那么ok我们这节课就这么多东西


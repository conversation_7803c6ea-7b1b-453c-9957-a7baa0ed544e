1
00:00:09,960 --> 00:00:13,880
ok这节课咱们来说一下场景的一个切换

2
00:00:13,958 --> 00:00:19,170
那我们最开始啊我们创建完这个工程就保存了一个场景叫做game

3
00:00:19,170 --> 00:00:25,179
那么这个场景我们在最开始介绍这个cos的时候已经说过了啊

4
00:00:25,179 --> 00:00:27,640
一个游戏呢可能有很多场景开始见面

5
00:00:27,640 --> 00:00:28,600
可能是个场景

6
00:00:28,600 --> 00:00:29,739
进入新手村

7
00:00:29,739 --> 00:00:31,179
新手村可能是个场景

8
00:00:31,179 --> 00:00:32,619
进入什么襄阳城

9
00:00:32,619 --> 00:00:33,820
襄城可能是个场景

10
00:00:33,820 --> 00:00:35,009
襄城的外面

11
00:00:35,009 --> 00:00:37,590
虾城的郊区打野怪又是个场景啊

12
00:00:37,590 --> 00:00:38,490
如果有个山洞

13
00:00:38,490 --> 00:00:39,450
山洞也是个场景

14
00:00:39,450 --> 00:00:40,079
对不对

15
00:00:40,079 --> 00:00:42,950
一个游戏呢就是有很多场景组成的

16
00:00:42,950 --> 00:00:46,670
那在这里我们最开始一直是在一个场景里面做操作

17
00:00:46,670 --> 00:00:48,558
我还给它取了个名叫game

18
00:00:48,640 --> 00:00:53,560
那如果现在我们创建第二个场景怎么创建

19
00:00:53,560 --> 00:00:55,479
那为了区分这个场景啊

20
00:00:55,479 --> 00:00:57,338
我们在第一个场景里面

21
00:00:59,179 --> 00:01:02,619
创建一个labour吧

22
00:01:02,619 --> 00:01:04,540
精灵下面有一个文字标签

23
00:01:04,540 --> 00:01:05,049
对不对

24
00:01:05,049 --> 00:01:06,790
文字标签啊

25
00:01:06,790 --> 00:01:08,439
这个标签你可以给他写个

26
00:01:08,439 --> 00:01:10,599
这是第一个场景啊

27
00:01:10,599 --> 00:01:11,859
这是第一个场景

28
00:01:11,859 --> 00:01:14,260
然后我们怎样创建第二个场景

29
00:01:14,260 --> 00:01:18,659
在文件选择这里就有一个打开项目新建场景

30
00:01:18,659 --> 00:01:19,560
保存场景

31
00:01:19,560 --> 00:01:21,120
我们点一下新建场景

32
00:01:21,120 --> 00:01:23,459
这时候大家可以看左边就空了

33
00:01:23,459 --> 00:01:24,899
我们再创建

34
00:01:24,920 --> 00:01:28,219
这时候就是在第二个场景里面做操作了啊

35
00:01:28,219 --> 00:01:29,840
我们就叫场景二

36
00:01:31,099 --> 00:01:33,980
然后我们ctrl加s保存一下

37
00:01:34,359 --> 00:01:35,439
保存一下

38
00:01:35,439 --> 00:01:37,959
比如说名称就叫game 2啊

39
00:01:37,959 --> 00:01:39,609
我觉得这个就随便起的名了

40
00:01:39,609 --> 00:01:42,290
这时候大家就发现有两个场景了

41
00:01:42,290 --> 00:01:43,370
game 1 game 2

42
00:01:43,370 --> 00:01:44,930
双击打开这个场景

43
00:01:44,930 --> 00:01:48,530
你看现在场景一和场景二里面的内容是完全不一样的

44
00:01:48,530 --> 00:01:49,189
对不对

45
00:01:49,189 --> 00:01:52,040
你可以编辑单独编辑每一个场景

46
00:01:52,040 --> 00:01:54,500
那一个游戏就是有很多场景组成

47
00:01:54,500 --> 00:01:57,920
然后在这些场景之间干嘛跳转

48
00:01:57,920 --> 00:01:58,430
对不对

49
00:01:58,430 --> 00:01:59,060
跳转

50
00:01:59,060 --> 00:02:02,659
那么比如说我现在想通过代码的方式

51
00:02:02,659 --> 00:02:04,099
那我们现在知道运行起来

52
00:02:04,099 --> 00:02:06,870
比如说现在跑的运行

53
00:02:06,870 --> 00:02:08,729
大家看到一了场景一

54
00:02:08,729 --> 00:02:14,099
我想现在运行起来切换到第二个场景怎么办啊

55
00:02:14,099 --> 00:02:16,819
切换到我们后创建的这个场景怎么办

56
00:02:17,498 --> 00:02:21,849
那么我们就要在这里用代码啊

57
00:02:21,849 --> 00:02:26,020
我们知道这个这个这个代码我们这儿已经挂了一个脚本了

58
00:02:26,020 --> 00:02:28,719
第一个场景里面我们这儿挂了一个test脚本

59
00:02:28,719 --> 00:02:29,919
对不对啊

60
00:02:29,919 --> 00:02:31,539
我还在这个里面去写

61
00:02:31,539 --> 00:02:35,080
比如说上来我们就让他加载

62
00:02:35,080 --> 00:02:36,759
第二个场景

63
00:02:36,759 --> 00:02:39,159
通过代码怎样去加载呢

64
00:02:39,179 --> 00:02:40,318
非常简单

65
00:02:40,318 --> 00:02:41,519
cc点

66
00:02:41,519 --> 00:02:43,139
首先我们找到导演

67
00:02:43,139 --> 00:02:45,659
这个是在咱们讲那个结构的时候

68
00:02:45,659 --> 00:02:48,599
咱们说过像场景这些事儿谁去负责的导演

69
00:02:48,599 --> 00:02:49,300
对不对

70
00:02:49,300 --> 00:02:50,500
导演干嘛

71
00:02:50,500 --> 00:02:52,319
losing

72
00:02:53,979 --> 00:02:55,500
就可以了啊

73
00:02:55,500 --> 00:02:56,250
就可以了

74
00:02:56,250 --> 00:02:59,939
在里面去填写场景的名称

75
00:02:59,939 --> 00:03:01,819
比如我们叫game 2

76
00:03:02,819 --> 00:03:04,400
那我们现在运行一下

77
00:03:04,400 --> 00:03:07,520
看一下是不是一开始运行就加载到二了

78
00:03:07,520 --> 00:03:10,819
你看是不是场景就变成第二个场景了

79
00:03:11,060 --> 00:03:13,789
那么这个就是加载场景的方法

80
00:03:13,789 --> 00:03:15,979
那么它是可以加参数的

81
00:03:15,979 --> 00:03:21,319
它的参数是一个这个这个函数是什么意思啊

82
00:03:21,719 --> 00:03:32,180
这个函数被加载就证明当前已经加载到新的场景理论

83
00:03:34,219 --> 00:03:38,629
那比如说你有这个两个场景是吧

84
00:03:38,629 --> 00:03:41,360
那么你第二个场景如果加载进来

85
00:03:41,360 --> 00:03:43,159
你要做一些事情的话

86
00:03:43,159 --> 00:03:45,710
你可能就会在这里写一个函数

87
00:03:45,710 --> 00:03:48,800
如果加载加载加载完以后加载到第二个场景了

88
00:03:48,800 --> 00:03:53,599
你就会在这儿去写做一些什么什么什么什么什么初始化操作

89
00:03:53,599 --> 00:03:54,169
对不对

90
00:03:54,169 --> 00:03:56,569
就是切换完场景的初始化操作

91
00:03:56,569 --> 00:03:59,449
但是总体而言就是能进到这个里面

92
00:03:59,449 --> 00:04:03,280
执行代码已经证明你切换了新的场景了

93
00:04:04,699 --> 00:04:06,250
那么就是这样的

94
00:04:06,250 --> 00:04:08,439
那么往下啊

95
00:04:08,439 --> 00:04:12,058
往下除了这一个方法啊

96
00:04:12,058 --> 00:04:15,959
这个方法优点是用起来也比较简单是吧

97
00:04:15,959 --> 00:04:17,360
但有一个缺点

98
00:04:17,899 --> 00:04:19,439
缺点是什么

99
00:04:19,980 --> 00:04:21,199
大家注意啊

100
00:04:21,199 --> 00:04:23,300
在咱们这个2d游戏啊

101
00:04:23,300 --> 00:04:25,399
其实对于咱们来说还好

102
00:04:25,399 --> 00:04:30,009
咱们其实用这个我觉得大部分足够了啊

103
00:04:30,009 --> 00:04:31,600
但是对于大型游戏

104
00:04:31,600 --> 00:04:34,519
比如说大家想如果有一些游戏啊

105
00:04:34,519 --> 00:04:36,079
这个这个这个一个场景特别大

106
00:04:36,079 --> 00:04:38,749
比如说3d游戏一个场景里面

107
00:04:38,749 --> 00:04:42,129
可能它的这个加载的这个资源是吧

108
00:04:42,129 --> 00:04:45,560
好几百兆一个场景就很慢啊

109
00:04:45,560 --> 00:04:48,620
那这个加载的话就会导致一个问题

110
00:04:48,620 --> 00:04:50,480
如果一执行这个方法

111
00:04:50,480 --> 00:04:51,800
比如说加载game 2场景

112
00:04:51,800 --> 00:04:53,339
game 2场景特别大

113
00:04:53,500 --> 00:04:54,339
就会怎样

114
00:04:54,339 --> 00:04:55,839
就会一直卡死在这儿

115
00:04:55,839 --> 00:04:57,339
直到卡到什么时候

116
00:04:57,339 --> 00:04:59,259
卡到场景加载完了

117
00:04:59,259 --> 00:05:01,480
我们才能诶调这个方法告诉你

118
00:05:01,480 --> 00:05:02,709
场景加载完了

119
00:05:02,709 --> 00:05:04,720
但这个给人的感觉特别不好

120
00:05:04,720 --> 00:05:07,209
所以很多游戏才有进度条

121
00:05:07,209 --> 00:05:10,259
进度条就是让你去等进度条

122
00:05:10,259 --> 00:05:11,519
就是让你去等

123
00:05:11,519 --> 00:05:13,439
就告诉你现在正在加载中

124
00:05:13,439 --> 00:05:15,100
对不对啊

125
00:05:15,100 --> 00:05:19,120
所以说你这种直接加载小资源还行啊

126
00:05:19,120 --> 00:05:20,019
就小游戏还行

127
00:05:20,019 --> 00:05:21,250
一闪而过啊

128
00:05:21,250 --> 00:05:23,199
大型游戏就不行了

129
00:05:23,199 --> 00:05:25,240
那大型游戏我们要加载资源

130
00:05:25,240 --> 00:05:26,720
怎么样去加载呢

131
00:05:26,720 --> 00:05:28,699
我们就换一个方法

132
00:05:32,019 --> 00:05:33,269
还是用它

133
00:05:33,269 --> 00:05:36,589
但是我们加载之前

134
00:05:36,589 --> 00:05:38,870
我们就要进行一个预加载啊

135
00:05:38,870 --> 00:05:41,089
或者加载我们想加载场景

136
00:05:41,089 --> 00:05:43,370
直接调这个预加载啊

137
00:05:43,370 --> 00:05:44,300
调这个预加载

138
00:05:44,300 --> 00:05:45,379
这个是什么意思啊

139
00:05:46,699 --> 00:05:49,160
game 2预加载

140
00:05:51,839 --> 00:05:54,540
他就是过来加载这个game 2

141
00:05:54,540 --> 00:05:55,800
加载这个场景

142
00:05:55,800 --> 00:05:57,759
加载这个场景的话

143
00:05:59,100 --> 00:06:03,529
function function同样也有一个回调

144
00:06:03,529 --> 00:06:04,759
但是这个回调什么意思

145
00:06:04,759 --> 00:06:08,839
就是当我把这个场景预加载完以后

146
00:06:08,879 --> 00:06:10,259
我调用的方法

147
00:06:10,259 --> 00:06:11,639
这个只是加载完

148
00:06:11,639 --> 00:06:14,589
并不代表切换上面这个是已经切换了

149
00:06:14,589 --> 00:06:16,569
你只是代码执行这的时候

150
00:06:16,569 --> 00:06:18,610
你你现在已经看到第二个场景了

151
00:06:18,610 --> 00:06:20,410
而他能进到这里面

152
00:06:20,410 --> 00:06:25,600
只是证明这个场景加载到内存了

153
00:06:25,600 --> 00:06:28,660
但是还没有用啊

154
00:06:28,660 --> 00:06:29,769
还没有切换

155
00:06:29,769 --> 00:06:31,180
是这样的一个意思

156
00:06:31,180 --> 00:06:34,629
所以在这里面如果我想切换再怎样

157
00:06:34,629 --> 00:06:38,259
你再去进行一个切换啊

158
00:06:38,259 --> 00:06:39,920
你再去进行切换

159
00:06:41,379 --> 00:06:46,910
losing game 2就ok了

160
00:06:46,910 --> 00:06:49,259
我们现在来运行一下

161
00:06:49,879 --> 00:06:52,300
大家可以看是不是切到第二个场景了

162
00:06:52,300 --> 00:06:56,269
当然我们其实对于我们来说感觉不太根本感觉不出来

163
00:06:56,269 --> 00:06:57,360
应该说对不对

164
00:06:57,360 --> 00:06:59,310
但实际上它有这个含义

165
00:06:59,310 --> 00:07:01,439
如果我把这句话注释了

166
00:07:01,439 --> 00:07:03,500
大家可以看到只留一个预加载

167
00:07:04,620 --> 00:07:07,459
那么它其实加载完了第二个场景

168
00:07:07,459 --> 00:07:08,750
它也不会怎样

169
00:07:08,750 --> 00:07:11,029
他也不会说给我们切换啊

170
00:07:11,029 --> 00:07:12,230
它是这样一个意思

171
00:07:12,230 --> 00:07:14,060
但是对于我们来说

172
00:07:14,060 --> 00:07:16,970
其实用cos creator的呃很多啊

173
00:07:16,970 --> 00:07:19,310
很多都是用来做一些小游戏的啊

174
00:07:19,310 --> 00:07:22,910
顶多是这个客户端上的稍微大型一些的游戏啊

175
00:07:22,910 --> 00:07:24,769
那这时候可能会用到瑜伽的

176
00:07:24,769 --> 00:07:27,769
但是还有很多人拿它用来做一些网页游戏

177
00:07:27,769 --> 00:07:29,819
甚至这个微信小游戏啊

178
00:07:29,819 --> 00:07:33,319
这一类有时候就是比如说嗯你就没有必要用这个

179
00:07:33,319 --> 00:07:35,000
你就直接去加载就行了啊

180
00:07:35,000 --> 00:07:36,259
没有必要用这个预加载

181
00:07:37,399 --> 00:07:40,899
那么就是这样一回事啊

182
00:07:40,899 --> 00:07:41,980
就是这样一回事

183
00:07:41,980 --> 00:07:43,779
那么这里有一个问题啊

184
00:07:43,779 --> 00:07:45,100
这里有一个问题

185
00:07:45,519 --> 00:07:46,930
什么问题

186
00:07:46,930 --> 00:07:48,300
比如说

187
00:07:49,920 --> 00:07:57,660
如果有一个这个这个这个如果在场景一里面有一个节点

188
00:07:57,660 --> 00:08:00,600
这个节点叫test节点啊

189
00:08:00,600 --> 00:08:01,680
或者说

190
00:08:03,180 --> 00:08:04,399
留test节点啊

191
00:08:04,399 --> 00:08:04,879
留什么

192
00:08:04,879 --> 00:08:06,259
比如说啊随便一个节点吧

193
00:08:06,259 --> 00:08:07,668
比如说这个land的节点

194
00:08:07,668 --> 00:08:09,528
他在第一个场景有

195
00:08:09,528 --> 00:08:10,309
第二个场景没有

196
00:08:10,309 --> 00:08:10,879
对不对

197
00:08:10,879 --> 00:08:12,468
但是偶尔有需求

198
00:08:12,468 --> 00:08:15,468
我们是希望他一直保存在各个场景中的

199
00:08:15,468 --> 00:08:18,810
就是他在啊这个land在第一个场景有

200
00:08:18,810 --> 00:08:19,709
第二个场景也有

201
00:08:19,709 --> 00:08:20,759
第三个场景也有

202
00:08:20,759 --> 00:08:22,769
那这样的话在这个节点上面

203
00:08:22,769 --> 00:08:25,699
我们就可以比如说保存一些东西啊

204
00:08:25,699 --> 00:08:27,860
你比如说保存到当前这个游戏

205
00:08:27,860 --> 00:08:30,589
一共你玩了多长时间

206
00:08:30,589 --> 00:08:32,570
如果你切个场景重新计算

207
00:08:32,570 --> 00:08:34,070
那那时间就不对了

208
00:08:34,070 --> 00:08:36,679
所以你那个就希望从这个游戏运行开始

209
00:08:36,679 --> 00:08:38,058
这个节点一直在

210
00:08:38,058 --> 00:08:41,889
然后我在上面挂个脚本去一直计算当前玩了多长时间

211
00:08:41,889 --> 00:08:42,669
对不对

212
00:08:42,669 --> 00:08:45,250
这些都是需要有一个节点

213
00:08:45,250 --> 00:08:51,000
这个节点它是作为一个永久性的节点存在的啊

214
00:08:51,000 --> 00:08:53,139
作为一个永久性的节点存在的

215
00:08:53,139 --> 00:08:56,500
那么这个节点我们怎么给他啊

216
00:08:56,500 --> 00:08:57,279
我们怎么给他

217
00:08:57,279 --> 00:08:58,240
其实非常简单

218
00:08:58,240 --> 00:09:00,399
比如说有那样一个节点呃

219
00:09:00,399 --> 00:09:01,570
我这里提前说一下

220
00:09:01,570 --> 00:09:03,359
咱们用到的时候再说啊

221
00:09:04,279 --> 00:09:07,438
那么首先这个节点叫做

222
00:09:09,259 --> 00:09:12,309
比如说就是know this.node node吧

223
00:09:12,309 --> 00:09:16,470
比如说我就想把自己这个节点啊变变成这个什么呀

224
00:09:16,470 --> 00:09:18,929
变成永远不会被销毁的节点

225
00:09:18,929 --> 00:09:21,120
因为你实际上你一切场景

226
00:09:21,120 --> 00:09:23,100
你场景里面的内容就发生变化了

227
00:09:23,100 --> 00:09:24,960
我就希望你就算切到别的场景

228
00:09:24,960 --> 00:09:26,639
我这个节点依然存在

229
00:09:26,639 --> 00:09:29,220
那你就需要this.node啊

230
00:09:29,220 --> 00:09:31,279
那我就保存this.node

231
00:09:31,279 --> 00:09:34,610
那么你就应该用cc.game啊

232
00:09:34,610 --> 00:09:41,840
它里面有一个添加一个应该意思翻译就是一个持久化的一个节点啊

233
00:09:41,840 --> 00:09:44,029
它翻译成这个常驻节点啊

234
00:09:44,029 --> 00:09:48,970
常驻节点意思就是我这里允许加一个节点

235
00:09:48,970 --> 00:09:50,350
只要加了这个节点

236
00:09:50,350 --> 00:09:51,009
从现在开始

237
00:09:51,009 --> 00:09:52,570
这个节点就不会被销毁了

238
00:09:52,570 --> 00:09:56,159
哪怕你换各种切换场景也不会被销毁

239
00:09:56,539 --> 00:09:58,490
当然有添加

240
00:09:58,490 --> 00:10:02,620
就有把它从常驻列表里面移除出去

241
00:10:03,419 --> 00:10:04,879
但是移除出去以后

242
00:10:04,879 --> 00:10:06,309
他还在啊

243
00:10:06,309 --> 00:10:09,580
但是就是说你把它从常驻列表里面移除出去

244
00:10:09,580 --> 00:10:11,169
它还保留在这个场景里

245
00:10:11,169 --> 00:10:13,769
只是如果现在再切换第二个场景

246
00:10:13,769 --> 00:10:14,669
它就没了

247
00:10:14,669 --> 00:10:17,318
你比如说我们有1233个场景

248
00:10:18,340 --> 00:10:19,899
233个场景

249
00:10:19,899 --> 00:10:22,059
第一个场景里面有个this node

250
00:10:22,059 --> 00:10:24,460
你把它加到这个常驻了啊

251
00:10:24,460 --> 00:10:26,029
加到这个常驻节点里了

252
00:10:26,029 --> 00:10:27,830
第二个节点你把他remove掉

253
00:10:27,830 --> 00:10:30,089
那么在第二个节点里面呃

254
00:10:30,089 --> 00:10:31,528
那么在第二个场景里面

255
00:10:31,528 --> 00:10:33,479
this.node还是可以用的啊

256
00:10:33,479 --> 00:10:34,558
就是它是存在的

257
00:10:34,558 --> 00:10:35,818
并没有被删除啊

258
00:10:35,818 --> 00:10:40,860
只是从这个常驻这个这个这个呃常驻节点里面删除了

259
00:10:40,860 --> 00:10:43,350
那么这时候如果我切换到第三个场景

260
00:10:43,350 --> 00:10:44,129
它就会怎样

261
00:10:44,129 --> 00:10:45,009
它就没了

262
00:10:45,009 --> 00:10:47,889
所以这个this.note就只活了一二个场景

263
00:10:47,889 --> 00:10:48,669
对不对

264
00:10:48,669 --> 00:10:49,750
那么注意啊

265
00:10:49,750 --> 00:10:52,100
有这样两个方法呃

266
00:10:52,100 --> 00:10:54,399
用到的时候我们再说啊

267
00:10:55,039 --> 00:10:56,860
ok那么整体而言

268
00:10:56,860 --> 00:11:02,519
这边就是切换场景和切换场景可能会遇到的一些问题啊

269
00:11:02,700 --> 00:11:06,559
那么我们这节课就这么多


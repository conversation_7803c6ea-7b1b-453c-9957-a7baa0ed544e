1
00:00:09,320 --> 00:00:12,509
ok这节课咱们继续看这个动画系统

2
00:00:12,509 --> 00:00:15,390
那么首先上节课咱们做了这个动画

3
00:00:15,390 --> 00:00:17,850
这个动画已经可以正常的这个执行了

4
00:00:17,850 --> 00:00:18,550
对不对

5
00:00:18,550 --> 00:00:23,620
那么在这里我们发现动画呢它会生成这样的一个线

6
00:00:23,620 --> 00:00:26,140
就是针之间它会有连线

7
00:00:26,140 --> 00:00:26,949
对不对

8
00:00:26,949 --> 00:00:28,420
它会有连线

9
00:00:28,800 --> 00:00:33,539
那么在这里这个线其实我们是可以编辑的啊

10
00:00:33,539 --> 00:00:35,960
我们点一下双击一下这个线

11
00:00:35,960 --> 00:00:39,289
这个线啊默认它是这样的一个匀速啊

12
00:00:39,289 --> 00:00:41,840
这个意思就是我们是匀速的一个播放

13
00:00:41,840 --> 00:00:46,049
如果你对它进行了这个线的一个修改

14
00:00:46,049 --> 00:00:49,130
你看可以有不同的修改啊

15
00:00:49,130 --> 00:00:50,509
比如说有淡入效果的

16
00:00:50,509 --> 00:00:51,770
有淡出效果的

17
00:00:51,770 --> 00:00:52,130
对不对

18
00:00:52,130 --> 00:00:53,630
这是淡入的这样的一个线

19
00:00:53,630 --> 00:00:55,479
这是弹出的这样的一个线

20
00:00:56,359 --> 00:00:59,320
那么其实代入就是最开始动作小

21
00:00:59,320 --> 00:01:00,880
后面逐渐变大啊

22
00:01:00,880 --> 00:01:03,329
弹出就是最开始动作比较大

23
00:01:03,329 --> 00:01:05,189
然后到结束的时候动作变小

24
00:01:05,189 --> 00:01:05,760
对不对

25
00:01:05,760 --> 00:01:06,750
以及其他的

26
00:01:06,750 --> 00:01:09,159
比如说你可以自定义画这个线

27
00:01:09,780 --> 00:01:12,420
哎你自定义画一个时间线

28
00:01:12,420 --> 00:01:14,640
那么通过这个曲线啊

29
00:01:14,640 --> 00:01:15,780
通过这个曲线

30
00:01:15,780 --> 00:01:18,480
我们的这个动画效果就会变得不一样

31
00:01:18,480 --> 00:01:21,099
哎比如说这样我保存一下

32
00:01:22,599 --> 00:01:23,849
我保存了

33
00:01:23,849 --> 00:01:27,510
然后我们这个这个这个这个保存关闭

34
00:01:27,510 --> 00:01:29,879
我们在这边运行一下

35
00:01:30,579 --> 00:01:34,530
你看这个旋转最开始的时候很大的一个旋转

36
00:01:34,530 --> 00:01:36,060
后面旋转就少了

37
00:01:36,219 --> 00:01:38,439
然后到最后又有一点旋转

38
00:01:38,439 --> 00:01:42,010
也就是开头和结尾旋转到中间基本上就没有动

39
00:01:42,010 --> 00:01:42,519
对不对

40
00:01:42,519 --> 00:01:43,829
中间为什么没有动

41
00:01:43,829 --> 00:01:47,299
看线中间基本上我都让他呃

42
00:01:47,299 --> 00:01:49,609
基本上就可以说是没有变化了啊

43
00:01:49,609 --> 00:01:51,859
已经可以快在一个水平线上

44
00:01:51,859 --> 00:01:54,150
只有在最开始和结束的时候

45
00:01:54,150 --> 00:01:55,769
这个变化是比较大的

46
00:01:55,769 --> 00:01:56,790
幅度是比较大的

47
00:01:56,790 --> 00:01:57,359
对不对

48
00:01:57,359 --> 00:01:58,959
那这个曲线的话

49
00:02:00,799 --> 00:02:03,159
呃一般也就是像做这种移动啊

50
00:02:03,159 --> 00:02:03,700
旋转啊

51
00:02:03,700 --> 00:02:05,019
这种动画的时候

52
00:02:05,019 --> 00:02:06,579
你要有兴趣的话

53
00:02:06,700 --> 00:02:08,560
你进来可以调一下啊

54
00:02:08,560 --> 00:02:10,430
它的效果可能就会更好

55
00:02:10,430 --> 00:02:12,770
但是一般如果我们做帧动画啊

56
00:02:12,770 --> 00:02:14,030
那个一般都是匀速的

57
00:02:14,030 --> 00:02:16,520
那个我们基本上就不会去调这个了

58
00:02:16,780 --> 00:02:19,719
这个东西大家自己看一看啊

59
00:02:19,719 --> 00:02:21,250
去试一下啊

60
00:02:21,250 --> 00:02:22,719
试一下它预制的啊

61
00:02:22,719 --> 00:02:23,919
试一下它这些预制的

62
00:02:23,919 --> 00:02:25,769
看一下他们的一个效果啊

63
00:02:25,769 --> 00:02:28,109
就ok了

64
00:02:29,039 --> 00:02:31,939
我们再改回成线性的吧

65
00:02:33,139 --> 00:02:35,889
啊这个就是正常的一个动画

66
00:02:35,889 --> 00:02:38,659
那么接下来我们来说一个东西啊

67
00:02:39,239 --> 00:02:44,538
比如说我在他身上挂载一个脚本

68
00:02:44,538 --> 00:02:46,659
比如说我要新建一个脚本

69
00:02:47,259 --> 00:02:50,120
这个脚本我挂载到了他的身上

70
00:02:51,558 --> 00:02:53,860
脚本里面我们写个代码

71
00:03:09,699 --> 00:03:12,080
那么在这边呃

72
00:03:12,080 --> 00:03:15,259
我们在这边啊加一个我们自己的方法

73
00:03:15,259 --> 00:03:19,680
方法就是比如说叫custom啊

74
00:03:19,680 --> 00:03:21,150
我们就叫custom好了

75
00:03:21,150 --> 00:03:23,580
然后custom是干嘛的啊

76
00:03:23,580 --> 00:03:30,099
我们就是debug一下被调用了啊

77
00:03:30,099 --> 00:03:31,990
就就打印一下被调用了

78
00:03:31,990 --> 00:03:35,430
也就是说只要这个custom方法一旦被调用啊

79
00:03:35,430 --> 00:03:36,780
那这个就可以被打印了

80
00:03:36,780 --> 00:03:38,490
那现在我们在哪里调用

81
00:03:38,490 --> 00:03:39,819
我们不在这里调用

82
00:03:39,819 --> 00:03:42,319
我们想通过这个动画系统调用

83
00:03:42,319 --> 00:03:44,060
那么这个怎么怎么做

84
00:03:44,060 --> 00:03:48,960
比如说我希望他每次到了中间这一帧的时候都调用啊

85
00:03:48,960 --> 00:03:50,520
或者中间已经有这个帧了

86
00:03:50,520 --> 00:03:52,439
我们为了区分我们放到这里吧

87
00:03:52,439 --> 00:03:53,879
大概在这个位置的时候

88
00:03:53,879 --> 00:03:56,430
我们会调用这个函数

89
00:03:56,430 --> 00:03:57,919
那么怎么样做

90
00:03:57,919 --> 00:03:59,479
把它放到这里

91
00:03:59,479 --> 00:04:04,400
这时候这个真事件我们就可以使用了啊

92
00:04:04,400 --> 00:04:06,710
我们上面这一盘就这个没有用过

93
00:04:06,710 --> 00:04:08,800
我们点一下这个插入真事件

94
00:04:08,800 --> 00:04:13,419
那这时候大家会看到有这样的一个白色的一个一点东西

95
00:04:13,419 --> 00:04:16,600
意思就是这一帧它是和事件关联的

96
00:04:16,600 --> 00:04:22,678
选中这一帧右键有个编辑在这里就可以去填写我们的

97
00:04:22,678 --> 00:04:24,119
你看方法的一个名称

98
00:04:24,119 --> 00:04:26,459
我们那个叫做custom

99
00:04:27,420 --> 00:04:28,879
如果你有你

100
00:04:28,879 --> 00:04:30,800
你希望给他传递一些属性

101
00:04:30,800 --> 00:04:32,029
你也可以给它属性

102
00:04:32,029 --> 00:04:33,360
什么类型的

103
00:04:33,680 --> 00:04:35,240
我们现在没有

104
00:04:35,240 --> 00:04:37,339
然后如果你有多个函数

105
00:04:37,339 --> 00:04:37,939
你可以加

106
00:04:37,939 --> 00:04:40,800
你看它可以同一时间掉多个函数

107
00:04:41,319 --> 00:04:44,038
那么我们默认就一个啊custom

108
00:04:44,038 --> 00:04:46,769
然后我们保存保存完了关闭它

109
00:04:46,769 --> 00:04:49,259
这时候我们运行

110
00:04:52,978 --> 00:04:55,319
我们上来没有让他播放是吗

111
00:04:55,319 --> 00:04:57,500
我们让他上来默认播放

112
00:05:00,339 --> 00:05:03,399
诶应该是播放

113
00:05:03,399 --> 00:05:04,300
怎么没有播放

114
00:05:04,300 --> 00:05:05,560
我们看一下啊

115
00:05:09,079 --> 00:05:11,439
现在运行一下看看效果

116
00:05:11,439 --> 00:05:12,819
它是在这个位置

117
00:05:12,819 --> 00:05:14,939
这个动画压根就没有播放

118
00:05:15,600 --> 00:05:16,759
为什么没有播放

119
00:05:16,759 --> 00:05:17,600
看一下啊

120
00:05:17,600 --> 00:05:19,240
没有播放

121
00:05:20,779 --> 00:05:22,660
动画再保存一下

122
00:05:24,860 --> 00:05:26,199
啊播放了

123
00:05:26,199 --> 00:05:27,879
那这时候大家可以看一下啊

124
00:05:27,879 --> 00:05:30,279
刚才是没保存输出

125
00:05:30,279 --> 00:05:33,430
你看每次到了大概这个位置

126
00:05:33,430 --> 00:05:36,160
它它就被调那个方法被调用了

127
00:05:36,160 --> 00:05:36,939
它就执行一下

128
00:05:36,939 --> 00:05:37,480
被调用了

129
00:05:37,480 --> 00:05:38,420
被调用了

130
00:05:39,300 --> 00:05:42,259
所以这个就是呃动画事件啊

131
00:05:42,259 --> 00:05:45,319
就是动画到了某一帧的时候会给我们调一个方法

132
00:05:45,319 --> 00:05:46,879
那这个方法也是我们自己写的

133
00:05:46,879 --> 00:05:48,199
这样的话能做到什么

134
00:05:48,199 --> 00:05:50,230
比如说你看在最高处的时候

135
00:05:50,230 --> 00:05:51,759
我们每次有调事件

136
00:05:51,759 --> 00:05:53,769
然后你就可以在事件里面去写

137
00:05:53,769 --> 00:05:57,490
这时候比如说这里有个礼花呀或者怎样的

138
00:05:57,490 --> 00:05:59,110
或者屏幕变个颜色呀

139
00:05:59,110 --> 00:06:01,870
啊就会和这个就可以去写代码

140
00:06:01,870 --> 00:06:03,550
和这个动画进行一个配合了

141
00:06:03,550 --> 00:06:04,420
对不对

142
00:06:06,660 --> 00:06:09,079
那么动画事件是很有用的啊

143
00:06:09,079 --> 00:06:09,829
很有用的

144
00:06:09,829 --> 00:06:12,709
我们常常会用到动画里面的事件啊

145
00:06:12,709 --> 00:06:14,660
因为你光播动画没有用对吧

146
00:06:14,660 --> 00:06:18,930
我们需要在动画的某一个时刻啊去做一些操作

147
00:06:21,509 --> 00:06:22,339
o

148
00:06:25,680 --> 00:06:27,740
啊我们大家就先知道一下啊

149
00:06:27,740 --> 00:06:29,420
还是我们这个用多了

150
00:06:29,420 --> 00:06:31,339
大家自然就习惯这个东西了啊

151
00:06:31,339 --> 00:06:34,079
动画事件嗯

152
00:06:34,839 --> 00:06:37,920
那么我们动画事件也知道了

153
00:06:37,920 --> 00:06:42,699
那么接下来啊这个精灵啊

154
00:06:42,699 --> 00:06:43,540
我不要他了

155
00:06:43,540 --> 00:06:44,500
不让他执行了

156
00:06:44,500 --> 00:06:51,480
我们在这里创建一个我在这里拖上来这样的一个序列帧吧

157
00:06:52,360 --> 00:06:54,160
啊这个针我只有个跑路

158
00:06:54,160 --> 00:06:55,480
还没别的

159
00:06:56,560 --> 00:07:02,360
那么在这里这个人物啊有一个跑步的动画啊

160
00:07:02,360 --> 00:07:06,500
这个是一个类似于看着像是3d了

161
00:07:06,500 --> 00:07:08,720
实际上他是2d是不是啊

162
00:07:08,720 --> 00:07:10,790
视觉3d这样的一个人物

163
00:07:10,790 --> 00:07:16,678
那我希望哎让他的这一组动画播放起来怎么怎么去做啊

164
00:07:16,678 --> 00:07:17,720
怎么去做

165
00:07:19,519 --> 00:07:21,860
那么首先我把它拖出来

166
00:07:24,639 --> 00:07:26,279
动画先关闭了啊

167
00:07:26,279 --> 00:07:28,420
先不要去编辑动画了

168
00:07:30,339 --> 00:07:33,540
我们先往场景里面拖一个我们的这个人物啊

169
00:07:33,540 --> 00:07:35,009
拖一个我们的人物

170
00:07:35,009 --> 00:07:37,800
我们可以让它稍微大一些

171
00:07:38,300 --> 00:07:41,699
怎么说三倍三倍

172
00:07:42,860 --> 00:07:45,100
ok是不是这样的一个人物呀

173
00:07:45,100 --> 00:07:47,019
我们现在啊如果运行

174
00:07:47,019 --> 00:07:50,139
它肯定就是就是一个精灵在这里

175
00:07:50,139 --> 00:07:50,980
对不对

176
00:07:52,680 --> 00:07:54,120
没保存吗

177
00:08:05,199 --> 00:08:08,098
ok那么现在就是一个精灵在这啊

178
00:08:08,098 --> 00:08:11,189
就是一个呃就是这样一个静态的精灵在这

179
00:08:11,189 --> 00:08:14,360
我现在希望让它能跑起来怎么办

180
00:08:15,100 --> 00:08:17,579
那么我们就要给他去做这个动画了

181
00:08:17,579 --> 00:08:18,180
是不是

182
00:08:18,180 --> 00:08:22,040
那么我们在这儿点到动画编辑器给它去加一个动画

183
00:08:23,199 --> 00:08:30,250
加了动画以后点击去叫一个run跑步的一个动画啊

184
00:08:30,250 --> 00:08:31,689
跑步的一个动画

185
00:08:31,689 --> 00:08:35,019
那么跑步动画我们让他上来就跑啊

186
00:08:35,019 --> 00:08:36,120
上来就跑

187
00:08:36,539 --> 00:08:40,590
然后这个跑步动画我们开始编辑怎么样去做这个跑步动画

188
00:08:40,590 --> 00:08:45,129
其实做动画就是对这个当前显示的图片去进行修改

189
00:08:45,129 --> 00:08:48,009
所以在这里加一个属性属性

190
00:08:48,009 --> 00:08:54,979
就是这个cc点点sprite frame这样一个属性

191
00:08:55,320 --> 00:09:00,379
然后我们选中我们所有的这个图片放到这里

192
00:09:00,379 --> 00:09:02,149
放到零零的位置上

193
00:09:02,149 --> 00:09:04,979
那这时候大家去看就可以看到啊

194
00:09:05,299 --> 00:09:06,659
就可以看到

195
00:09:08,580 --> 00:09:14,429
动画播放的时候可以看到是不是这个人物每一帧

196
00:09:14,429 --> 00:09:17,100
每一帧他都切换一个动画啊

197
00:09:17,100 --> 00:09:19,139
但是如果按这种形式去播放的话

198
00:09:19,139 --> 00:09:20,039
它跑的太快了

199
00:09:20,039 --> 00:09:21,279
你看啊

200
00:09:21,279 --> 00:09:22,219
为什么

201
00:09:22,480 --> 00:09:27,279
因为我们现在这个跑步一共有12345678针八针的跑步动画

202
00:09:27,279 --> 00:09:29,019
你一秒播放60帧

203
00:09:29,019 --> 00:09:30,639
这个太快了啊

204
00:09:30,639 --> 00:09:32,259
所以在这里有两种方式修改

205
00:09:32,259 --> 00:09:35,249
要不然你直接在这里去改这个速度倍数啊

206
00:09:35,249 --> 00:09:38,188
要不然的话你可以去修改一秒播放的帧数

207
00:09:38,188 --> 00:09:40,799
比如说我改成一秒播12帧啊

208
00:09:40,799 --> 00:09:45,889
那这样的话去播放是不是感觉就好一些啊

209
00:09:45,889 --> 00:09:46,879
可以再快一点

210
00:09:46,879 --> 00:09:48,110
一秒16帧

211
00:09:48,110 --> 00:09:49,559
一秒16帧

212
00:09:50,399 --> 00:09:55,679
那么我们把这个跑步一般会做成循环的loop

213
00:09:56,000 --> 00:09:56,419
哎

214
00:09:56,419 --> 00:10:01,379
跑起来你看是不是就有这样的一个帧动画了啊

215
00:10:01,379 --> 00:10:03,210
这个人物一直在这儿跑啊

216
00:10:03,210 --> 00:10:04,139
使劲跑使劲跑

217
00:10:04,139 --> 00:10:04,740
对不对

218
00:10:04,740 --> 00:10:06,179
这就是一个真动画啊

219
00:10:06,179 --> 00:10:06,779
一个真动画

220
00:10:06,779 --> 00:10:08,059
我们保存一下

221
00:10:08,840 --> 00:10:10,940
关闭啊

222
00:10:10,940 --> 00:10:12,259
那么震动画做完了以后

223
00:10:12,259 --> 00:10:14,620
我们现在点那个播放按钮

224
00:10:15,480 --> 00:10:19,859
大家可以看是不是在游戏里面这个人物就开始跑起来了

225
00:10:20,539 --> 00:10:23,099
那么ok这是一个动画

226
00:10:23,159 --> 00:10:25,379
而且我们上来就让它默认播放了

227
00:10:25,379 --> 00:10:27,479
那么如果有多个动画怎么办

228
00:10:27,500 --> 00:10:30,379
那么我们在这里做这样一个事啊

229
00:10:30,379 --> 00:10:30,799
很简单

230
00:10:30,799 --> 00:10:38,899
我们就是把它的这个默认动画片段和这个上来让它播放的这个呃

231
00:10:38,899 --> 00:10:41,409
这个这个布尔值给它勾掉

232
00:10:41,409 --> 00:10:42,850
那么现在我们在运行

233
00:10:42,850 --> 00:10:44,409
它肯定就不会执行动画了

234
00:10:44,409 --> 00:10:44,980
对不对

235
00:10:44,980 --> 00:10:48,818
那我们通过代码的方式让他去播放动画

236
00:10:48,980 --> 00:10:51,980
只要通过代码的方式能播放这个run动画

237
00:10:51,980 --> 00:10:53,360
那么就好说了

238
00:10:53,360 --> 00:10:54,929
你可以对这个人物

239
00:10:54,929 --> 00:10:57,090
对这个精灵做好多动画啊

240
00:10:57,090 --> 00:10:57,990
不光有跑步的

241
00:10:57,990 --> 00:10:58,950
有很多动画

242
00:10:58,950 --> 00:11:01,679
最后这里面数组里面有很多像啊

243
00:11:01,679 --> 00:11:04,350
你需要哪个动画就播放哪些哪项就完了

244
00:11:04,350 --> 00:11:05,039
对不对

245
00:11:05,039 --> 00:11:07,379
那我们在这里通过代码来播放这个动画

246
00:11:07,379 --> 00:11:10,179
我们来给他再加一个脚本

247
00:11:13,120 --> 00:11:14,679
叫普列

248
00:11:16,279 --> 00:11:20,139
ctrl加到我们这个run身上

249
00:11:21,840 --> 00:11:23,399
打开这个脚本

250
00:11:26,519 --> 00:11:27,859
打开这个脚本

251
00:11:27,860 --> 00:11:30,860
那么在这边首先我要干嘛

252
00:11:30,860 --> 00:11:34,129
我肯定要得到我的这个动画组件

253
00:11:34,129 --> 00:11:40,379
所以我let你就等于个this.get component啊

254
00:11:40,379 --> 00:11:45,370
然后cc.animation得到动画组件

255
00:11:45,370 --> 00:11:47,230
然后我就可以去干嘛了

256
00:11:47,230 --> 00:11:48,159
播放动画了

257
00:11:48,159 --> 00:11:48,820
对不对

258
00:11:48,820 --> 00:11:53,120
然后在这里播放动画

259
00:11:53,120 --> 00:11:55,519
播放动画你就可以安利点

260
00:11:55,519 --> 00:11:56,600
很简单

261
00:11:56,600 --> 00:11:58,279
你要播放哪个动画就play

262
00:11:58,279 --> 00:12:00,259
然后把动画的名字写上

263
00:12:00,259 --> 00:12:00,919
我们叫run

264
00:12:00,919 --> 00:12:01,799
对不对

265
00:12:01,919 --> 00:12:03,600
就可以了啊

266
00:12:03,600 --> 00:12:05,500
我们来运行一下试一下

267
00:12:06,860 --> 00:12:09,200
你看是不是就播放起来了

268
00:12:09,200 --> 00:12:11,600
所以非常简单非常简单啊

269
00:12:11,600 --> 00:12:14,799
然后在这边除了播放动画还有什么呀

270
00:12:15,159 --> 00:12:18,379
嗯安妮点暂停

271
00:12:18,600 --> 00:12:22,139
安妮点儿继续爱你

272
00:12:22,139 --> 00:12:26,899
点停止这三这个暂停和停止的区别是什么

273
00:12:26,899 --> 00:12:28,759
比如说你播放到第四针了

274
00:12:28,759 --> 00:12:29,870
你暂停了

275
00:12:29,870 --> 00:12:31,860
它就会暂停到第四帧上

276
00:12:31,860 --> 00:12:33,000
然后继续的话

277
00:12:33,000 --> 00:12:36,029
它就会怎样从第第四帧开始继续播放

278
00:12:36,029 --> 00:12:37,629
你要是点了停止

279
00:12:37,629 --> 00:12:39,730
那么就证明这个动画就算播放完了

280
00:12:39,730 --> 00:12:40,870
他下一次再播放

281
00:12:40,870 --> 00:12:42,549
它就从第一帧开始播放啊

282
00:12:42,549 --> 00:12:44,049
就这一点区别啊

283
00:12:44,049 --> 00:12:46,860
那所以这个动画其实用起来非常简单

284
00:12:46,860 --> 00:12:47,700
就这么简单

285
00:12:47,700 --> 00:12:50,279
你用什么你去播放的动画就完了

286
00:12:50,539 --> 00:12:51,320
很简单

287
00:12:51,320 --> 00:12:53,600
是不是啊

288
00:12:53,600 --> 00:12:54,779
那么

289
00:12:56,740 --> 00:13:00,399
主要的就是大家对这个素材啊

290
00:13:00,399 --> 00:13:06,029
对这个素材就是拿进来以后在这儿拖拽到我们这个属性列表里面

291
00:13:06,029 --> 00:13:08,309
然后对这个速度调整啊

292
00:13:08,309 --> 00:13:12,039
一般就是调速度比较麻烦点嗯

293
00:13:13,100 --> 00:13:13,820
怎么说啊

294
00:13:13,820 --> 00:13:16,759
这个速度的话一般2d游戏啊

295
00:13:16,759 --> 00:13:20,279
我调的一般都是顶多就是十几啊

296
00:13:20,279 --> 00:13:21,720
就是比如说12啊

297
00:13:21,720 --> 00:13:24,629
我12是我最常用的一个速度啊

298
00:13:24,629 --> 00:13:29,039
那么如果我去做那种特别小的游戏啊

299
00:13:29,039 --> 00:13:30,720
比如说类似于小霸王啊

300
00:13:30,720 --> 00:13:32,549
就以前玩的小霸王那种游戏

301
00:13:32,549 --> 00:13:35,629
我一般可能甚至都会调到八哈哈

302
00:13:35,629 --> 00:13:38,570
所以这个速度也是每秒播放几帧啊

303
00:13:38,570 --> 00:13:40,220
这个基本上你调好

304
00:13:40,220 --> 00:13:41,990
然后你就可以看效果啊

305
00:13:41,990 --> 00:13:46,679
效果调好以后使用起来其实非常简单啊

306
00:13:46,679 --> 00:13:49,139
使用起来其实是非常简单的

307
00:13:49,779 --> 00:13:53,159
那么这个动画系统啊就就这么多内容

308
00:13:53,159 --> 00:13:55,828
那么大家还是下去多练习一下

309
00:13:55,828 --> 00:13:57,509
那么在这里啊

310
00:13:57,509 --> 00:13:59,129
这个按钮我们好像没点过

311
00:13:59,129 --> 00:14:02,729
我们在这里是可以切换当前这个人物有的动画的啊

312
00:14:02,729 --> 00:14:04,649
比如说现在就一个跑步啊

313
00:14:04,649 --> 00:14:07,470
我们如果在这里给这个人物添加一个新的动画

314
00:14:07,470 --> 00:14:09,399
比如说叫站立

315
00:14:09,399 --> 00:14:11,710
那这时候你就可以去切换啊

316
00:14:11,710 --> 00:14:14,409
你是编辑这个人物的跑步动画还是站立动画

317
00:14:14,409 --> 00:14:15,909
跑步还是站立

318
00:14:15,909 --> 00:14:18,899
是不是你又可以编辑多个动画了

319
00:14:19,440 --> 00:14:23,100
然后你也会发现人物的这边就会有一个跑步

320
00:14:23,100 --> 00:14:24,179
一个战力了啊

321
00:14:25,500 --> 00:14:27,120
呃很简单啊

322
00:14:27,120 --> 00:14:31,480
那我们这节课就这么多啊


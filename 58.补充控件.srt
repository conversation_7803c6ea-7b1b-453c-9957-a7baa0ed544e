1
00:00:03,339 --> 00:00:04,280
<该字幕由算法自动生成> 冲冲冲冲

2
00:00:09,060 --> 00:00:13,089
ok啊咱们这节课来说一下呃

3
00:00:13,089 --> 00:00:14,380
下一个组件啊

4
00:00:14,380 --> 00:00:15,130
slider

5
00:00:15,130 --> 00:00:17,019
那么这个组件是干嘛的

6
00:00:17,019 --> 00:00:20,219
这个组件其实特别的常见啊

7
00:00:20,239 --> 00:00:21,800
那么它就是滑动组件

8
00:00:21,800 --> 00:00:23,980
我们把它拖上来运行看一下

9
00:00:24,420 --> 00:00:26,280
那么它就是这样一个组件啊

10
00:00:26,280 --> 00:00:27,600
左边是一个最小值

11
00:00:27,600 --> 00:00:29,160
最右边是一个最大值

12
00:00:29,160 --> 00:00:32,899
我们可以从左到右进行一个滑动啊

13
00:00:33,539 --> 00:00:36,509
那么这个就是咱们的这个空间

14
00:00:36,509 --> 00:00:39,570
那么这个slider的话非常简单啊

15
00:00:39,570 --> 00:00:42,060
我们可以看一下他呢

16
00:00:42,060 --> 00:00:43,679
首先下面一个底层

17
00:00:43,679 --> 00:00:44,100
对不对

18
00:00:44,100 --> 00:00:44,759
就这一条

19
00:00:44,759 --> 00:00:46,380
然后上面还有一个圆球

20
00:00:46,460 --> 00:00:49,460
然后的话对于它的这个整个组件

21
00:00:49,460 --> 00:00:50,659
我们看一下它的属性

22
00:00:50,659 --> 00:00:52,380
一共也没几条啊

23
00:00:52,380 --> 00:00:55,649
首先有一个你是水平放的还是垂直放的啊

24
00:00:55,649 --> 00:00:57,000
那默认是水平

25
00:00:57,000 --> 00:00:57,899
如果是垂直的话

26
00:00:57,899 --> 00:00:59,840
你要把它拉伸成垂直的

27
00:00:59,859 --> 00:01:04,239
然后在这边的话有一个进度啊

28
00:01:04,239 --> 00:01:06,040
这是当前默认的一个进度

29
00:01:06,040 --> 00:01:07,540
默认是0.5在中间

30
00:01:07,540 --> 00:01:09,459
那么它的最小其实是零

31
00:01:09,459 --> 00:01:10,459
最大是一

32
00:01:10,459 --> 00:01:14,159
也就是说每一个slider它的范围都是从0~1

33
00:01:14,180 --> 00:01:17,060
如果比如说你需要控制一个从0~100的

34
00:01:17,060 --> 00:01:21,400
那你就先唉先拿一个slider给他拿到

35
00:01:21,400 --> 00:01:22,599
结果从0~1

36
00:01:22,599 --> 00:01:25,060
然后拿到的结果乘以100就ok了

37
00:01:25,060 --> 00:01:25,799
对不对

38
00:01:25,939 --> 00:01:28,099
那么这里有一个事件啊

39
00:01:28,099 --> 00:01:29,900
这个事件如果你需要的话

40
00:01:29,900 --> 00:01:31,370
你自己加一个事件

41
00:01:31,370 --> 00:01:32,780
事件的话很简单

42
00:01:32,780 --> 00:01:34,400
什么时候调用事件呢

43
00:01:34,840 --> 00:01:39,780
只要你用户点中这个小球进行了滑动啊

44
00:01:39,780 --> 00:01:43,219
只要滑动也就是实际上是这个数值啊

45
00:01:43,219 --> 00:01:45,530
这个数值只要发生了变化

46
00:01:45,530 --> 00:01:47,750
那么它呢就会调用这个方法

47
00:01:47,750 --> 00:01:51,379
你就可以在这个方法里面拿到当前的数值

48
00:01:51,400 --> 00:01:55,659
那么咱们这几节课一直说控件也没有用过脚本

49
00:01:55,659 --> 00:01:58,359
实际上用脚本的话非常简单啊

50
00:01:58,359 --> 00:02:01,980
比如说你拿到一个脚本啊

51
00:02:01,980 --> 00:02:03,420
脚本你获得这个组件

52
00:02:03,420 --> 00:02:07,019
这个组件的属性名称就是咱们前面这些名称啊

53
00:02:07,019 --> 00:02:08,538
基本上是没有变化的

54
00:02:08,538 --> 00:02:10,919
比如说你get confident slider

55
00:02:10,939 --> 00:02:12,199
cc.slide

56
00:02:12,199 --> 00:02:13,699
然后括号是吧

57
00:02:13,699 --> 00:02:14,930
先获取这个组件

58
00:02:14,930 --> 00:02:18,840
然后拿到组件点progress就可以拿到这个数值了对吧

59
00:02:18,840 --> 00:02:21,659
你可以每一次在在关联一个事件

60
00:02:21,659 --> 00:02:26,120
每一次在这个事件里面把当前的这个进度给输出一下啊

61
00:02:26,139 --> 00:02:27,819
呃自己试一试

62
00:02:27,819 --> 00:02:28,780
非常简单啊

63
00:02:28,780 --> 00:02:29,949
非常简单

64
00:02:29,949 --> 00:02:33,680
那么除了这个啊

65
00:02:33,680 --> 00:02:35,479
这个这个组件非常简单

66
00:02:35,479 --> 00:02:38,300
然后咱们再说一个是这样的一个组件啊

67
00:02:38,300 --> 00:02:39,699
这个组件是什么

68
00:02:40,139 --> 00:02:42,310
大家可以看啊

69
00:02:42,310 --> 00:02:46,060
这个其实就是一个呃怎么说开关是吧

70
00:02:46,060 --> 00:02:48,219
类似于开关这样的一个组件啊

71
00:02:48,219 --> 00:02:50,349
类似于开关的组件呃

72
00:02:50,349 --> 00:02:52,560
只是呃他这样的

73
00:02:52,560 --> 00:02:55,259
它一般这种组件会和lb连起来用

74
00:02:55,259 --> 00:02:56,580
比如说左边有个对号

75
00:02:56,580 --> 00:02:57,900
右边你来个label

76
00:02:57,900 --> 00:02:58,860
然后放上

77
00:02:58,860 --> 00:03:04,460
比如说男再来个再来一个这个图个下面再来个女啊

78
00:03:04,460 --> 00:03:05,300
就是二选一

79
00:03:05,300 --> 00:03:06,520
对不对啊

80
00:03:06,520 --> 00:03:08,259
那么这个组件也非常简单

81
00:03:08,259 --> 00:03:09,580
首先大家选中它

82
00:03:09,580 --> 00:03:10,919
可以看一下右边

83
00:03:11,020 --> 00:03:15,280
那么在右边的话他的这一组属性啊

84
00:03:15,280 --> 00:03:16,719
首先它有一个动画

85
00:03:16,719 --> 00:03:18,580
这个动画是什么情况

86
00:03:18,580 --> 00:03:19,840
是因为我们现在点它

87
00:03:19,840 --> 00:03:21,400
大家发现它有一个缩放的动画

88
00:03:21,400 --> 00:03:22,159
对不对

89
00:03:22,159 --> 00:03:23,539
它有一个缩放动画

90
00:03:23,539 --> 00:03:26,240
那么这个缩放动画就是在这里去设定的

91
00:03:26,240 --> 00:03:29,490
你是缩放的还是改变这个背景图片

92
00:03:29,490 --> 00:03:32,039
还是改变颜色和按钮一模一样啊

93
00:03:32,039 --> 00:03:33,419
这个和按钮一模一样

94
00:03:33,419 --> 00:03:36,680
然后有这个动画的时间都缩放的倍数

95
00:03:36,699 --> 00:03:41,259
然后我们在这里主要知道的就是这个这个是默认是否选中的啊

96
00:03:41,259 --> 00:03:42,759
比如说比如说你看这两个

97
00:03:42,759 --> 00:03:45,400
我们要把它做一组二选一啊

98
00:03:45,400 --> 00:03:47,500
如果要做一组二选一的话

99
00:03:47,500 --> 00:03:49,599
你肯定要一个呃

100
00:03:49,599 --> 00:03:51,639
只能有一个是默认选中的啊

101
00:03:51,639 --> 00:03:52,569
就it's check

102
00:03:52,569 --> 00:03:54,639
这个默认只能有一个选中的

103
00:03:54,639 --> 00:03:56,419
但是这时候又有问题了

104
00:03:56,900 --> 00:03:58,879
如果现在比如说是这样的情况

105
00:03:58,879 --> 00:04:01,650
那我选上面大家发现这行不对

106
00:04:01,650 --> 00:04:02,939
这完全不是二选一

107
00:04:02,939 --> 00:04:03,689
对不对

108
00:04:03,689 --> 00:04:05,039
如果大家注意啊

109
00:04:05,039 --> 00:04:08,639
你要单独去使用这个这个组件的话

110
00:04:08,680 --> 00:04:11,590
无非就是用它一个布尔值代表一个开关

111
00:04:11,590 --> 00:04:16,300
如果你想把它多个这样的开关组件用成一组啊

112
00:04:16,300 --> 00:04:17,980
怎么样去用啊

113
00:04:17,980 --> 00:04:18,399
怎么去用

114
00:04:18,399 --> 00:04:19,240
其实非常简单

115
00:04:19,240 --> 00:04:23,139
首先你需要一个空的这样的一个组件啊

116
00:04:23,139 --> 00:04:25,689
比如说我们创建一个空的节点啊

117
00:04:25,689 --> 00:04:28,720
然后把他们俩都放到空节点的下面

118
00:04:31,040 --> 00:04:33,980
把这两个都作为空节点的子节点

119
00:04:33,980 --> 00:04:38,540
然后在这个空节点上面我们加一个组件嗯

120
00:04:38,540 --> 00:04:43,019
加一个组件叫做tube content的

121
00:04:43,019 --> 00:04:45,360
就是这个算是它的一个容器啊

122
00:04:45,360 --> 00:04:46,620
切换组件的一个容器

123
00:04:46,620 --> 00:04:47,939
只要加上以后

124
00:04:47,959 --> 00:04:49,759
我们在运行

125
00:04:49,779 --> 00:04:51,370
这时候大家看一下

126
00:04:51,370 --> 00:04:54,639
如果你有多个这样的组件的话啊

127
00:04:54,639 --> 00:04:57,079
我们只能同时选择一个

128
00:04:59,939 --> 00:05:03,360
那这就成了类似于单选组件这个这个样式的

129
00:05:03,360 --> 00:05:04,110
对不对

130
00:05:04,110 --> 00:05:09,139
那么其实呃我们下一个组件啊

131
00:05:09,139 --> 00:05:10,279
下一个组件拖上来

132
00:05:10,279 --> 00:05:10,819
大家翻一下

133
00:05:10,819 --> 00:05:15,360
你看这个组件其实就是咱们刚才的那个样式

134
00:05:15,360 --> 00:05:19,500
只不过他又把那个勾换成了小圆圈啊

135
00:05:19,500 --> 00:05:24,480
然后他的这个这个组件和前面这个组件这俩组件完全是一样的

136
00:05:24,480 --> 00:05:25,379
完全是一样的

137
00:05:25,379 --> 00:05:29,000
只是呃这个默认已经给你把一组弄好了啊

138
00:05:29,000 --> 00:05:31,060
里面有三个图片组件啊

139
00:05:31,060 --> 00:05:33,319
也就是说它里面包括三个图表组件

140
00:05:33,339 --> 00:05:35,019
然后最外层一个空节点

141
00:05:35,019 --> 00:05:37,120
空间点加了这样一个图和cs的

142
00:05:37,120 --> 00:05:37,689
对不对

143
00:05:37,689 --> 00:05:39,180
我们运行看一下

144
00:05:39,180 --> 00:05:40,500
你看跟刚才的效果

145
00:05:40,500 --> 00:05:41,160
一模一样

146
00:05:41,160 --> 00:05:43,259
他无非就是把里面的图片换啊

147
00:05:43,259 --> 00:05:44,839
他无非就是把图片换

148
00:05:48,480 --> 00:05:52,500
啊那么这三个组件啊非常简单啊

149
00:05:52,500 --> 00:05:56,240
这三个组件非常简单呃

150
00:05:56,240 --> 00:05:58,879
那么我们还剩最后两个

151
00:05:58,879 --> 00:06:02,100
其实这两个比如说我们拿上来一个video啊

152
00:06:02,100 --> 00:06:03,329
拿上来一个video

153
00:06:03,329 --> 00:06:05,160
那么这个组件怎样去用啊

154
00:06:05,160 --> 00:06:06,800
它是用来播放视频的

155
00:06:06,800 --> 00:06:09,139
它支持两种播放视频

156
00:06:09,139 --> 00:06:10,399
一个是本地的

157
00:06:10,399 --> 00:06:11,300
一个是远程的

158
00:06:11,300 --> 00:06:12,470
如果是远程的

159
00:06:12,470 --> 00:06:15,019
你在这里把远程的地址输进来

160
00:06:15,040 --> 00:06:17,060
然后就可以播放了啊

161
00:06:17,060 --> 00:06:21,139
然后他在代码里面的使用方法跟我们之前的那个音频播放

162
00:06:21,139 --> 00:06:22,220
那个组件一样

163
00:06:22,220 --> 00:06:24,990
拿到它直接play播放就行了啊

164
00:06:24,990 --> 00:06:27,360
或者是你先给他设置这个url

165
00:06:27,360 --> 00:06:29,199
远程的url再去play

166
00:06:29,220 --> 00:06:31,800
呃当然你也可以在面板上直接设置

167
00:06:31,800 --> 00:06:32,939
这是远程的

168
00:06:32,939 --> 00:06:34,889
如果你设置本地的话

169
00:06:34,889 --> 00:06:37,379
大家发现这里有个clip

170
00:06:37,699 --> 00:06:40,319
是不是跟咱们那个音频一样啊

171
00:06:40,319 --> 00:06:41,100
完全一样

172
00:06:41,100 --> 00:06:44,189
也就是说你左边这次拖进来的不是一个mp 3了

173
00:06:44,189 --> 00:06:46,350
你比如说你可以拖进来一个mp 4

174
00:06:46,350 --> 00:06:48,480
把mp 4放在这里啊

175
00:06:48,480 --> 00:06:49,439
拖到这个位置

176
00:06:49,439 --> 00:06:50,490
然后运行

177
00:06:50,490 --> 00:06:51,779
那么它就会播放

178
00:06:51,779 --> 00:06:55,240
但是实际上它播放了起来延迟很高

179
00:06:55,240 --> 00:06:57,420
就是说你这个一点了播放以后

180
00:06:57,800 --> 00:07:03,699
你在网页上面你可能得等好久一会儿你才能看到它开始播放啊

181
00:07:03,699 --> 00:07:06,800
所以说这种像视频这个东西呃

182
00:07:06,800 --> 00:07:09,139
能就是能少用就少用啊

183
00:07:09,139 --> 00:07:11,839
尤其有时候我们像这种做这种网页游戏啊

184
00:07:11,839 --> 00:07:13,100
或者微信小游戏啊

185
00:07:13,100 --> 00:07:15,139
你没必要给他播放很大一个视频

186
00:07:15,139 --> 00:07:15,769
对不对

187
00:07:15,769 --> 00:07:17,779
这是当前播放的视频呃

188
00:07:17,779 --> 00:07:21,000
这个这个时间音量是否静音啊

189
00:07:21,000 --> 00:07:21,750
这几个

190
00:07:21,750 --> 00:07:23,220
然后这个我们知道一下

191
00:07:23,220 --> 00:07:26,529
就是是否是全屏播放嗯

192
00:07:26,529 --> 00:07:29,439
这这里它有一个默认已经画上勾的啊

193
00:07:29,439 --> 00:07:31,620
它是保持宽高比啊

194
00:07:31,620 --> 00:07:33,000
如果不保持宽高比

195
00:07:33,000 --> 00:07:34,620
你随便拖拽这个视频

196
00:07:34,620 --> 00:07:35,699
它就会变形啊

197
00:07:35,699 --> 00:07:37,259
你要是拉上勾

198
00:07:37,259 --> 00:07:38,259
选上这个勾

199
00:07:38,259 --> 00:07:41,920
那么它们就会保持它原本的一个宽高啊

200
00:07:41,920 --> 00:07:44,759
比如说它是四比三的或者是16比九的

201
00:07:44,759 --> 00:07:47,879
它就会按照它原本的比例去播放多余的部分

202
00:07:47,879 --> 00:07:51,600
就显示黑边了啊嗯

203
00:07:52,839 --> 00:07:56,860
那么这个东西啊基本上不会太多去用啊

204
00:07:56,860 --> 00:07:57,970
不会太多去用

205
00:07:57,970 --> 00:08:00,189
大家知道知道就ok了啊

206
00:08:00,189 --> 00:08:03,120
然后这里还有一个最后一个啊

207
00:08:03,120 --> 00:08:05,939
也是用起用的相对而言比较少的

208
00:08:05,939 --> 00:08:06,720
大家可以看一下

209
00:08:06,720 --> 00:08:08,639
它就是一个web view啊

210
00:08:08,639 --> 00:08:09,819
是一个网页

211
00:08:09,819 --> 00:08:13,240
在这里右边啊事件我们不用管它

212
00:08:13,240 --> 00:08:14,920
这个事件基本上很少用啊

213
00:08:14,920 --> 00:08:17,560
那就是一加载它就会掉这个事件

214
00:08:17,560 --> 00:08:20,079
那么在这里也有一个地址啊

215
00:08:20,079 --> 00:08:20,740
默认的地址

216
00:08:20,740 --> 00:08:22,420
比如说我们给他一个地址

217
00:08:22,699 --> 00:08:24,680
比如说百度点4m

218
00:08:24,680 --> 00:08:26,600
我们给一个百度的一个url

219
00:08:26,600 --> 00:08:30,379
这时候我们在运行稍停一下

220
00:08:30,379 --> 00:08:36,960
大家可以看他这个页面就会自动给我们加载这个网页了啊

221
00:08:36,960 --> 00:08:39,399
也就是说你写的程序啊

222
00:08:39,418 --> 00:08:42,159
嗯如果哪里需要

223
00:08:42,159 --> 00:08:43,480
比如说打开个网页啊

224
00:08:43,480 --> 00:08:46,570
然后你又不想打开系统的那个浏览器的话

225
00:08:46,570 --> 00:08:49,220
你就可以加这样一个组件啊

226
00:08:49,220 --> 00:08:50,120
加这样一个组件

227
00:08:50,120 --> 00:08:51,659
加了这个组件以后

228
00:08:52,220 --> 00:08:56,419
就可以在这个组件上面去看咱们的这个网页了啊

229
00:08:56,419 --> 00:08:58,159
这这个就是这个网页啊

230
00:08:58,159 --> 00:09:02,539
所以说实际上现在很多浏览器都是这样做的啊

231
00:09:02,539 --> 00:09:03,860
我可以跟大家去说

232
00:09:03,860 --> 00:09:06,860
其实浏览器的内核啊是最重要的

233
00:09:06,860 --> 00:09:07,919
内核很少

234
00:09:07,940 --> 00:09:11,500
但是很多浏览器全是拿不来别人的内核

235
00:09:11,500 --> 00:09:12,159
自己去用

236
00:09:12,159 --> 00:09:12,940
然后自己封装

237
00:09:12,940 --> 00:09:16,000
一下外壳实际上就跟呃怎么说啊

238
00:09:16,000 --> 00:09:18,220
就是挂羊皮卖狗肉这样的

239
00:09:18,220 --> 00:09:19,139
对不对

240
00:09:19,980 --> 00:09:25,820
那么实际上这些东西啊呃呃整个这个ui部分

241
00:09:25,820 --> 00:09:29,389
你默认你把button a label呀

242
00:09:29,389 --> 00:09:33,340
然后这个editor slider sco you

243
00:09:33,340 --> 00:09:36,759
然后把这几个啊先给他练熟

244
00:09:36,759 --> 00:09:38,200
因为这是最最常用的

245
00:09:38,200 --> 00:09:42,799
然后到后面可能哎你会用上这个图格是吧

246
00:09:42,799 --> 00:09:45,830
然后这这几个你先搞得清清楚楚的

247
00:09:45,830 --> 00:09:48,559
然后这个基本上ui你就没什么问题了

248
00:09:48,559 --> 00:09:50,059
然后比如说用到不常用的

249
00:09:50,059 --> 00:09:52,480
比如说用到啊视频播放啊

250
00:09:52,480 --> 00:09:53,649
或者用到网页了

251
00:09:53,649 --> 00:09:56,830
其实你可以完全在再去研究啊

252
00:09:56,830 --> 00:10:00,059
但实际上大家也发现它本身东西也很简单啊

253
00:10:00,299 --> 00:10:02,419
嗯ok啊

254
00:10:02,419 --> 00:10:03,919
不需要自己太强迫自己

255
00:10:03,919 --> 00:10:05,360
每一个都要记得熟熟的

256
00:10:05,360 --> 00:10:06,860
它有什么功能

257
00:10:06,860 --> 00:10:07,639
怎样去用啊

258
00:10:07,639 --> 00:10:08,659
ui没必要这样

259
00:10:08,659 --> 00:10:11,259
其实这个东西呃多练一练啊

260
00:10:11,259 --> 00:10:12,340
把常用的练会了

261
00:10:12,340 --> 00:10:13,720
而不常用的真的忘了

262
00:10:13,720 --> 00:10:16,240
然后到时候用到了再去啊

263
00:10:16,240 --> 00:10:17,820
再去研究它啊

264
00:10:17,840 --> 00:10:22,059
ok那咱们这节课就说了这么多


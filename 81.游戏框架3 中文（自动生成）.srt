1
00:00:09,560 --> 00:00:11,419
ok我们这节课啊

2
00:00:11,419 --> 00:00:14,330
简单的拿上一节课的这个框架

3
00:00:14,330 --> 00:00:16,699
然后咱们来做一个例子啊

4
00:00:16,699 --> 00:00:21,120
比如说我们这里有一个角色啊

5
00:00:21,120 --> 00:00:23,339
这个角色当我点击这个角色的时候

6
00:00:23,339 --> 00:00:25,000
他的血量就会减少

7
00:00:25,059 --> 00:00:28,420
那么血量的减少我们肯定就是有ui的部分了

8
00:00:28,420 --> 00:00:32,100
我们在这里创建一个空的节点啊

9
00:00:32,100 --> 00:00:33,390
是ui啊

10
00:00:33,390 --> 00:00:36,579
那么所有ui的东西我们都放到它的下面

11
00:00:36,579 --> 00:00:39,219
那么我们做的是血量部分

12
00:00:39,219 --> 00:00:43,179
所以下面再创建一个血量呃

13
00:00:43,179 --> 00:00:44,799
对于血量而言

14
00:00:44,799 --> 00:00:49,799
我们这里这个ui你可以名字全一点

15
00:00:52,679 --> 00:00:55,768
manager ui manager ui的一个管理器

16
00:00:55,768 --> 00:00:57,719
在下面有个hp啊

17
00:00:57,719 --> 00:00:59,729
有一个hp的一个控制器

18
00:00:59,729 --> 00:01:03,450
然后他呢就是负责所有的这个血量相关的事情

19
00:01:03,450 --> 00:01:07,359
在hp下面我们就是来个level

20
00:01:09,819 --> 00:01:14,310
第一个labour呢我们就是hp冒号

21
00:01:14,310 --> 00:01:15,700
再来一个

22
00:01:18,480 --> 00:01:20,870
再来一个hp label

23
00:01:20,870 --> 00:01:24,099
这个label放在这里

24
00:01:24,099 --> 00:01:25,420
默认是个100

25
00:01:27,239 --> 00:01:29,540
然后当我点这个方块的时候

26
00:01:29,540 --> 00:01:31,118
这个血量就会减少

27
00:01:31,439 --> 00:01:33,269
很简单的一个逻辑啊

28
00:01:33,269 --> 00:01:36,420
这个就是我们的普遍就是我们方块这个怎么去做

29
00:01:36,420 --> 00:01:38,799
首先正常情况下我们大家是怎么做的

30
00:01:38,799 --> 00:01:40,420
大家一定会写一个脚本

31
00:01:40,420 --> 00:01:42,909
是player脚本挂载到这个玩家上面

32
00:01:42,909 --> 00:01:47,019
然后在上面去获取诶

33
00:01:47,019 --> 00:01:50,209
大家可能去获取一下这个这个关联一下这个label

34
00:01:50,209 --> 00:01:52,370
然后之后点一下这个方块

35
00:01:52,370 --> 00:01:53,930
就让这个label数字改变

36
00:01:53,930 --> 00:01:54,530
对不对

37
00:01:54,530 --> 00:02:00,870
但是这样的话就相当于我们直接从玩家脚本调用到这个hp脚本了

38
00:02:00,870 --> 00:02:04,519
那这个逻辑当然并不是很好的啊

39
00:02:04,519 --> 00:02:06,019
就是我说的小游戏可以

40
00:02:06,019 --> 00:02:07,040
大型游戏的话

41
00:02:07,040 --> 00:02:09,199
如果我们都按这种方式去交互

42
00:02:09,199 --> 00:02:12,039
那最后那那游戏就没法去做了啊

43
00:02:12,039 --> 00:02:14,439
那交互就变成网网状了啊

44
00:02:14,439 --> 00:02:15,289
就太乱了

45
00:02:15,289 --> 00:02:18,590
所以接下来我们就用我们这种消息的方式来做一下

46
00:02:18,590 --> 00:02:20,840
我们看一下我这个框架能不能用啊

47
00:02:20,840 --> 00:02:23,659
那首先在这里我们要写第一个脚本

48
00:02:24,000 --> 00:02:26,159
第一个脚本就是我们的玩家脚本

49
00:02:27,479 --> 00:02:28,740
不are control

50
00:02:30,520 --> 00:02:33,979
那这个玩家脚本在这里有什么操作

51
00:02:35,838 --> 00:02:37,780
首先作为玩家脚本

52
00:02:38,679 --> 00:02:41,519
我们在这里先看一下我们的消息啊

53
00:02:41,519 --> 00:02:42,359
我们的消息

54
00:02:42,359 --> 00:02:46,870
我们现在的消息我们就用这个ui消息里面的刷新血量啊

55
00:02:46,870 --> 00:02:50,039
也就是说我们现在来实现这一组啊

56
00:02:50,039 --> 00:02:51,780
这一大类里面的这一小类

57
00:02:52,800 --> 00:02:58,860
那么在我们的这个pyer control里面啊

58
00:02:58,860 --> 00:03:03,120
在player control里面我们做一个点击事件

59
00:03:03,120 --> 00:03:06,539
每次点击我们就希望让他血量减少

60
00:03:06,539 --> 00:03:08,719
那this.note.on

61
00:03:12,098 --> 00:03:16,879
然后在二里面cc.note点英文tab点

62
00:03:17,319 --> 00:03:25,349
貌似大鼠标点击点击了以后点击的事件里面我们要干嘛

63
00:03:25,349 --> 00:03:27,090
血量减少

64
00:03:27,090 --> 00:03:28,379
这时候注意啊

65
00:03:28,379 --> 00:03:32,239
我们血量减少的话就不能是啊

66
00:03:32,239 --> 00:03:35,359
在这里再写个lab和外界关联就不能那样了

67
00:03:35,359 --> 00:03:36,438
我们直接发消息

68
00:03:36,438 --> 00:03:37,579
发消息就很简单

69
00:03:37,579 --> 00:03:40,778
直接用message center啊

70
00:03:40,778 --> 00:03:44,870
用消息中心点发送一个自定义的消息

71
00:03:44,870 --> 00:03:48,080
然后在里面三个参数咱们写的还记得吧

72
00:03:48,080 --> 00:03:50,449
三个参数第一个参数是个大类型

73
00:03:50,449 --> 00:03:52,900
就是我们要发个什么类型的消息

74
00:03:53,000 --> 00:03:58,699
message type.type ui就是ui类型的消息

75
00:03:58,699 --> 00:03:59,959
发一个ui类型的消息

76
00:03:59,959 --> 00:04:01,699
你看咱们虽然这个是人物

77
00:04:01,699 --> 00:04:03,378
角色人物其实不属于ui

78
00:04:03,378 --> 00:04:03,889
对不对

79
00:04:03,889 --> 00:04:06,580
但是咱们现在就发了一个ui的消息

80
00:04:07,438 --> 00:04:11,959
ui的消息小类呢就是命令到底是什么啊

81
00:04:11,959 --> 00:04:13,609
小类我们就是叫命令

82
00:04:13,609 --> 00:04:19,019
那就是message type.ui的这个命令啊

83
00:04:19,019 --> 00:04:20,459
刷新血量的命令

84
00:04:20,459 --> 00:04:22,620
那么再往下第三个参数

85
00:04:22,620 --> 00:04:24,000
我们需要给一个参数

86
00:04:24,000 --> 00:04:26,589
这个参数我们比如说给个数字

87
00:04:26,589 --> 00:04:29,529
数字就代表一次血量减少多少啊

88
00:04:29,529 --> 00:04:32,899
发个消息就希望这一次血量减少十滴血啊

89
00:04:32,899 --> 00:04:33,740
我满过100滴

90
00:04:33,740 --> 00:04:34,220
对不对

91
00:04:34,220 --> 00:04:35,569
这次减少十滴血

92
00:04:35,569 --> 00:04:37,550
那我们现在就发了这样一个消息

93
00:04:37,550 --> 00:04:39,709
那这样的话大家也会发现运行一下

94
00:04:39,709 --> 00:04:41,060
现在肯定没有任何作用

95
00:04:41,060 --> 00:04:44,238
因为他这个发了消息以后也就完事儿了

96
00:04:44,238 --> 00:04:45,139
没人去接收它

97
00:04:45,139 --> 00:04:46,038
对不对

98
00:04:46,038 --> 00:04:50,519
那么这样的话玩家这边就完成了啊

99
00:04:50,519 --> 00:04:52,259
玩家这边我们就写完了

100
00:04:52,259 --> 00:04:58,149
接下来我们来写一下这个管理类啊

101
00:04:58,149 --> 00:05:00,529
就是这个管理类呃

102
00:05:00,529 --> 00:05:02,120
那么这对于这个管理类而言

103
00:05:02,120 --> 00:05:07,579
我们首先来创建一个脚本叫ui manager

104
00:05:09,699 --> 00:05:11,999
把这个ui manager拖上来

105
00:05:13,678 --> 00:05:14,699
打开它

106
00:05:16,420 --> 00:05:18,259
首先名字改了

107
00:05:19,740 --> 00:05:20,959
首先名字改了

108
00:05:20,959 --> 00:05:22,279
然后在下边

109
00:05:22,279 --> 00:05:26,100
首先static instance

110
00:05:28,139 --> 00:05:29,600
ui manager

111
00:05:30,620 --> 00:05:33,220
我们先把它写成一个单例

112
00:05:35,079 --> 00:05:35,920
on load

113
00:05:35,920 --> 00:05:39,019
我们最好在unload里面啊

114
00:05:39,019 --> 00:05:41,720
单例如果我们要把一个类型写成单例

115
00:05:41,720 --> 00:05:43,660
尽量在unload里面啊

116
00:05:44,899 --> 00:05:48,899
然后先是先执行父类的onload方法

117
00:05:50,319 --> 00:05:53,279
只要是子类继承父类啊

118
00:05:53,279 --> 00:05:56,339
我们我们自己写的子类继承父类的啊

119
00:05:56,339 --> 00:05:59,040
我们尽量把这个给写上

120
00:06:00,540 --> 00:06:03,740
然后在这边他们继承也要改一下

121
00:06:03,740 --> 00:06:05,800
继承于我们的manager base

122
00:06:07,120 --> 00:06:09,480
manager base就是管理类的子类的

123
00:06:09,480 --> 00:06:15,038
然后在这里ui manager.instance等于this

124
00:06:15,038 --> 00:06:17,670
这样的话当前我们就成一个单例类了啊

125
00:06:17,670 --> 00:06:18,689
我外界调用的话

126
00:06:18,689 --> 00:06:21,810
直接ui manager.instance就能调用这个对象了

127
00:06:21,810 --> 00:06:22,560
对不对

128
00:06:22,560 --> 00:06:25,439
然后当前我们接收的消息类型

129
00:06:28,680 --> 00:06:30,019
接收的消息类型

130
00:06:30,019 --> 00:06:31,040
我们就重写一下

131
00:06:31,040 --> 00:06:33,699
set message type这个方法就可以了

132
00:06:34,519 --> 00:06:38,678
return一个message type

133
00:06:38,678 --> 00:06:40,598
点type ui啊

134
00:06:40,598 --> 00:06:45,660
就我们这个这个管理类接收的消息类型是type ui这个类型的

135
00:06:45,660 --> 00:06:46,860
忘了这个方法的

136
00:06:46,860 --> 00:06:49,319
你去看一下咱们这个脚本怎么写的啊

137
00:06:49,639 --> 00:06:52,970
ok这样的话ui manager这边也写完了

138
00:06:52,970 --> 00:06:58,788
再下来我们就去写一个血量的控制器

139
00:06:58,788 --> 00:07:02,860
血量控制脚本叫做hp control啊

140
00:07:02,860 --> 00:07:04,420
这就是最下面一层了

141
00:07:04,420 --> 00:07:06,160
这就是最下面一层

142
00:07:07,639 --> 00:07:13,480
相当于我们hu这个这个这个hp control是在这里的

143
00:07:14,980 --> 00:07:18,180
然后ui manager是在这里的

144
00:07:19,819 --> 00:07:22,939
啊我们这两个脚本就是在这两个位置上的

145
00:07:24,420 --> 00:07:27,680
然后这个uh p ctrl的话

146
00:07:27,680 --> 00:07:28,860
我们打开它

147
00:07:29,439 --> 00:07:32,019
首先hp control

148
00:07:35,199 --> 00:07:36,959
我们在这个里面

149
00:07:36,959 --> 00:07:37,978
我们比如说血量

150
00:07:37,978 --> 00:07:39,209
我们就写在这个里面吧

151
00:07:39,209 --> 00:07:42,579
hp number类型的满共有100滴血

152
00:07:45,600 --> 00:07:52,759
首先我们要让当前的我们要把这个hb ctrl让ui manager去管理

153
00:07:52,759 --> 00:07:53,180
对不对

154
00:07:53,180 --> 00:07:55,439
所以我们要注册一下

155
00:07:58,040 --> 00:08:02,660
注册为ui的消息接收者

156
00:08:03,199 --> 00:08:05,139
怎么注册为ui的消息接收者

157
00:08:05,139 --> 00:08:06,309
就是ui manager

158
00:08:06,309 --> 00:08:08,269
点instance

159
00:08:08,269 --> 00:08:10,939
拿到ui manager这个对象啊

160
00:08:10,939 --> 00:08:11,750
管理类

161
00:08:11,750 --> 00:08:15,500
然后注册一下接受者

162
00:08:15,500 --> 00:08:18,480
然后把自己放到这里就可以了

163
00:08:21,959 --> 00:08:23,939
resist this

164
00:08:24,879 --> 00:08:26,939
我这边没写参数吗

165
00:08:26,939 --> 00:08:28,740
ui manager

166
00:08:31,740 --> 00:08:37,279
而不是原manager找这个这个这个这个这个叫做继承manager base

167
00:08:37,279 --> 00:08:38,139
在这里

168
00:08:41,039 --> 00:08:42,740
写了啊

169
00:08:42,740 --> 00:08:46,940
那有可能就是咱们的这个这里没改啊

170
00:08:46,940 --> 00:08:49,299
类型不匹配在这里

171
00:08:50,399 --> 00:08:52,129
component base啊

172
00:08:52,129 --> 00:08:56,129
我们要改一下这个继承的类继承与component base啊

173
00:08:56,129 --> 00:08:58,139
这个类这样的话就没问题了

174
00:08:58,139 --> 00:08:59,370
就接收完了

175
00:08:59,370 --> 00:09:04,839
然后我们就可以实现这个方法呢叫做receive message

176
00:09:10,179 --> 00:09:12,339
这就是接收到的消息

177
00:09:15,059 --> 00:09:19,820
啊你必须你必须这个注册为消息接收者才能接收到消息

178
00:09:19,820 --> 00:09:23,259
然后我们先写一个方法叫做改变血量的方法

179
00:09:25,480 --> 00:09:28,889
比如说叫做change hp

180
00:09:28,889 --> 00:09:30,269
然后改多少呢

181
00:09:30,269 --> 00:09:31,879
给他一个参数

182
00:09:31,899 --> 00:09:37,539
然后在这里this.hp减等于这个传过来的参数

183
00:09:37,720 --> 00:09:41,690
然后我们改变这个level上面的文字

184
00:09:41,690 --> 00:09:44,210
lor是我的第二个子节点

185
00:09:44,210 --> 00:09:47,359
所以我就可以点children

186
00:09:49,039 --> 00:09:54,899
第二个字节点就是一get得到我们的cc.level组件

187
00:09:55,500 --> 00:09:56,779
得到组件以后

188
00:09:56,779 --> 00:10:01,389
dx正属性就等于一个z点

189
00:10:01,389 --> 00:10:08,419
hp加上一个就把这个是我们的这个number类型

190
00:10:08,419 --> 00:10:09,139
加上字符串

191
00:10:09,139 --> 00:10:10,639
就变成字符串类型了

192
00:10:10,639 --> 00:10:12,259
转成字符串类型以后

193
00:10:12,259 --> 00:10:14,299
复制给我们的level就可以了

194
00:10:15,899 --> 00:10:21,990
ok这样的话我们改变血量方法就算完成了

195
00:10:21,990 --> 00:10:24,059
那么改变血量什么时候用呢

196
00:10:24,059 --> 00:10:27,099
那么就看我们接受什么时候接收到消息了

197
00:10:27,120 --> 00:10:30,450
首先掉下super.receive

198
00:10:30,450 --> 00:10:32,519
只要是复写的方法啊

199
00:10:32,519 --> 00:10:33,960
只要是复写的方法

200
00:10:33,960 --> 00:10:35,698
你都要掉下super

201
00:10:37,100 --> 00:10:38,899
然后在这里我们判断

202
00:10:40,700 --> 00:10:42,958
如果我们接收到的

203
00:10:45,080 --> 00:10:46,899
命令命令

204
00:10:48,460 --> 00:10:51,730
如果接收到的命令是刷新血量的命令

205
00:10:51,730 --> 00:10:52,899
我们在干嘛

206
00:10:52,899 --> 00:10:54,639
我们就直接刷新血量

207
00:10:54,639 --> 00:10:56,350
但是这里注意一下啊

208
00:10:56,350 --> 00:11:00,330
得到参数就是这个message

209
00:11:00,330 --> 00:11:02,759
我们传过来了一个第三个参数

210
00:11:02,759 --> 00:11:03,899
传过来一个参数

211
00:11:03,899 --> 00:11:04,529
对不对

212
00:11:04,529 --> 00:11:06,200
就是减的血量

213
00:11:06,200 --> 00:11:07,519
减的血量是多少

214
00:11:07,519 --> 00:11:09,769
我们要拿到这个数字

215
00:11:09,769 --> 00:11:13,299
我们给它强转为number类型

216
00:11:13,299 --> 00:11:16,269
尖括号强转就是message.continent

217
00:11:16,269 --> 00:11:17,980
默认这个参数是any类型

218
00:11:17,980 --> 00:11:18,279
对不对

219
00:11:18,279 --> 00:11:20,429
我们给它强转成number类型

220
00:11:20,429 --> 00:11:22,769
然后转成number类型以后

221
00:11:22,769 --> 00:11:25,889
我们再调用this点称之hp

222
00:11:25,889 --> 00:11:28,649
把number给它传过去

223
00:11:28,649 --> 00:11:31,779
那这个我们就要一个number类型

224
00:11:33,539 --> 00:11:36,960
那这样的话就ok了就ok了

225
00:11:36,960 --> 00:11:38,850
我们可以来试一下啊

226
00:11:38,850 --> 00:11:41,419
那我们在这里运行一下

227
00:11:41,779 --> 00:11:44,360
然后打开我们这边啊

228
00:11:44,360 --> 00:11:46,220
这个这个调试在这里可以不用打开

229
00:11:46,220 --> 00:11:46,820
我们点一下

230
00:11:46,820 --> 00:11:47,690
这里就会改变

231
00:11:47,690 --> 00:11:50,198
点一下没改变

232
00:11:50,720 --> 00:11:51,740
我们看一下啊

233
00:11:51,740 --> 00:11:53,330
我们这我们来检查一下

234
00:11:53,330 --> 00:11:55,519
点一下这里应该是会改变

235
00:11:55,519 --> 00:11:55,940
对不对

236
00:11:55,940 --> 00:11:57,419
但是这里没改变

237
00:11:57,419 --> 00:12:01,740
那我们就要去检查一下我们的脚本了

238
00:12:02,759 --> 00:12:07,159
首先我们先看一下脚本都有没有正常挂载上来

239
00:12:07,159 --> 00:12:08,599
玩家挂载上来

240
00:12:08,940 --> 00:12:10,379
血量没有挂载上

241
00:12:10,379 --> 00:12:14,789
你看血量脚本挂载上ui manager挂在上

242
00:12:14,789 --> 00:12:16,409
ok我们再运行一下

243
00:12:16,409 --> 00:12:17,519
点一下

244
00:12:18,320 --> 00:12:19,820
这就没有问题了吧

245
00:12:19,820 --> 00:12:21,919
点一下你看血量就会减少

246
00:12:21,919 --> 00:12:23,179
点一下血量减少

247
00:12:23,179 --> 00:12:25,220
虽然是个很简单的例子

248
00:12:25,220 --> 00:12:30,639
但是实际上已经是这样的一个正常的这样的一个结构了啊

249
00:12:30,639 --> 00:12:33,979
就是相当于我们这边有个玩家了

250
00:12:34,500 --> 00:12:36,750
然后在玩家呢我们做了一个点击

251
00:12:36,750 --> 00:12:39,360
然后他们发了一个消息啊

252
00:12:39,360 --> 00:12:40,500
消息给消息中心

253
00:12:40,500 --> 00:12:41,759
消息中心给了ui

254
00:12:41,759 --> 00:12:43,960
ui呢给了什么了

255
00:12:43,960 --> 00:12:46,000
给了这个血量了

256
00:12:47,019 --> 00:12:48,159
你还给了血量

257
00:12:48,159 --> 00:12:49,960
就消息有这样的一个过程啊

258
00:12:49,960 --> 00:12:51,240
有这样一个过程

259
00:12:52,879 --> 00:12:57,539
ok啊那么咱们就说这么多啊

260
00:12:57,539 --> 00:12:58,649
咱们就说这么多

261
00:12:58,649 --> 00:13:00,419
知道了这一个例子以后啊

262
00:13:00,419 --> 00:13:04,519
大家对这个呃框架大概有个了解就可以了啊

263
00:13:04,519 --> 00:13:05,509
大概有个了解

264
00:13:05,509 --> 00:13:08,620
那么如果以后自己的这个项目啊

265
00:13:08,620 --> 00:13:12,340
要做这个比较麻烦一点的项目的时候啊

266
00:13:12,340 --> 00:13:14,200
你就可以去尝试一下

267
00:13:14,200 --> 00:13:16,360
看看能不能用上这个框架啊

268
00:13:16,360 --> 00:13:17,679
不用的话可以做出来

269
00:13:17,679 --> 00:13:21,110
但是用的话你会感觉更加的好做啊

270
00:13:21,110 --> 00:13:24,409
当然你如果去你自己的公司的话啊

271
00:13:24,409 --> 00:13:27,799
每个公司可能有自己的一套一套框架

272
00:13:27,799 --> 00:13:31,500
这个框架并不是说都是用的这样的一套啊

273
00:13:31,500 --> 00:13:33,000
这只是其中的一套啊

274
00:13:33,000 --> 00:13:35,710
所以说你先研究会这个东西

275
00:13:35,710 --> 00:13:37,629
然后自己去哪个公司

276
00:13:37,629 --> 00:13:40,029
然后再去学习公司里面的啊

277
00:13:40,029 --> 00:13:42,350
呃这个这个框架啊

278
00:13:42,350 --> 00:13:44,210
这就是说程序员一直在学习

279
00:13:44,210 --> 00:13:45,080
永远停不了

280
00:13:45,080 --> 00:13:46,009
你换一个公司

281
00:13:46,009 --> 00:13:50,419
你就会发现代码风格啊啊用的框架呀可能就不一样了啊

282
00:13:50,860 --> 00:13:55,120
ok那我们这节课就这么多

